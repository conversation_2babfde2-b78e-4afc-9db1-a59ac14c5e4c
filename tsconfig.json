{"compilerOptions": {"target": "es2022", "lib": ["dom", "es2022", "esnext.asynciterable"], "module": "es2022", "moduleResolution": "node", "baseUrl": "./src/", "outDir": "./dist", "sourceMap": true, "removeComments": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowJs": true, "typeRoots": ["./src/types", "./node_modules/@types"]}, "include": ["./src/**/*.ts", "./src/**/*.d.ts", "./eslint.config.js"], "exclude": ["node_modules"]}