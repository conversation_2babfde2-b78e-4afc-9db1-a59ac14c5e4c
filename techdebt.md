# Tech Debt

## Integration Testing
** Integration tests are in high flux for a few PRs, please 
ignore them until this message is deleted. **
* Move diff back to devDependencies since integration is being moved out of src.
* Check `jest-diff` and possible `vitest` alternatives.
* Don't use path.join for url paths (use `url = new Url()` pattern)
* Endpoints like systemMaintainer/find may have magic disappearing and reappearing
  properties, need to confirm this by testing multiple records.

## Census: note/delete
* Test mocks are returning `number[]` instead of `number`
* Might need to mock deeper in the `wmDelete` mock.

## Census: page/dataExchangeFind
* Uncovered test/branch on line 98 `src/resources/census-core-v2/dataExchange/find.ts`
* Swagger docs enum needs fixing

## Utils: dataExchange
* Uncovered test/branch on lines 175, 347, 479

## CI/CD:
* Setup a github pipeline to run lint, test, and docker build steps when commits
  are pushed to pull request.

## GitHub Configuration Checks and Updates
* Review and update the pull request checklist template setup in GitHub.
* Squash merges should be enforced.
* Pull requests with outstanding feedback should not be allowed to be merged.

## Libraries
### Endpoint Setup
* Add support for `<PgData>` to `endpointSetup`.

### Value to Null Utilities
* `splitOrNull`:
  * ```
    // Auto-detect and normalize newlines if present
    const normalizedInput = input.includes('\n') ? input.replace(/\n/g, ' ') : input;
    ```

## Naming.
* Rename `Barf` class.
