# Tech Debt
Notated by: `(--requester, --requester)`

## Integration Testing
** Integration tests are in high flux for a few PRs, please
ignore them until this message is deleted. **
* Check `jest-diff` and possible `vitest` alternatives. (--ecrocco)
* Webmethods requests with invalid URLs do not output at all, which results
  in a `response.json()` parse error, ala

 `/page/systemMaintainer//?id=%7B023BE457-783E-413e-99A2-FB52C55A1616%7D`

  (--hrivera).


## Endpoint Handlers
### Census: note/add
* Remove email functionality in a separate ticket. (--rmelton, --ecrocco)
* Refactor LDAP lookup loop, there is a bug with the way it is returning. (--rmelton)
### Census: note/delete
* Test mocks are returning `number[]` instead of `number`. (--rmelton)
* Might need to mock deeper in the `wmDelete` mock. (--rmelton)


## Census: page/dataExchangeFind
* Uncovered test/branch on line 98 `src/resources/census-core-v2/dataExchange/find.ts`
* Swagger docs enum needs fixing

## Utils: dataExchange
* Uncovered test/branch on lines 175, 347, 479

## CI/CD:
* Set up a GitHub pipeline to run lint, test, and docker build steps when commits
  are pushed to pull request.  (--rmelton)


## GitHub Configuration Checks and Updates
* Review and update the pull request checklist template setup in GitHub. (--ecrocco, --rmelton)
* Squash merges should be enforced. (--hrivera)
* Pull requests with outstanding feedback should not be allowed to be merged. (--hrivera)


## Libraries
### Endpoint Setup
* Add support for `<PgData>` to `endpointSetup`. (--ecrocco)

### Value to Null Utilities
* `splitOrNull`: (--ecrocco)
  ```
  // Auto-detect and normalize newlines if present
  const normalizedInput = input.includes('\n') ? input.replace(/\n/g, ' ') : input;
  ```


## Devops
* System timezones should be set to EST / EDT, the webmethods server is using this now. (--hrivera)


## Database Queries
* queryViewTyped:
  * Move orderBy and distinct to an optional options parameter. (--hrivera)
* DATETIME columns are being mangled by webmethods.
  * Webmethods: `"createdOn": "2025-06-28T20:44:32.082Z"`
  * Typescript: `"createdOn": "2025-06-28T20:44:32.820Z"`

  (--hrivera)


--------------------------------



# Completed
Notated by: `(--requester, --requester), (--fixer)`

## Integration Testing
* Don't use `path.join` for url paths (use `url = new Url()` pattern).
  * Fixed. (--rmelton), (--hrivera)
* Endpoints like systemMaintainer/find may have magic
  disappearing and reappearing properties, need to confirm
  this by testing multiple records.  Follow up with Don.
  * Added to docs/webmethods-knowledge.md. (--hrivera), (--hrivera)
* Move diff back to devDependencies since integration is being moved out of src.
  * Fixed. (--hrivera), (--hrivera)

## Naming.
* Rename `Barf` class.
  * Renamed to `UnitTestForcedExceptionError`. (--ecrocco), (--hrivera)

## Devops
* Remove clean and clean-images from Makefile. (--rmelton), (--hrivera)
