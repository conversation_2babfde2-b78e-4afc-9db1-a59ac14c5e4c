# Support more than one local project.
COMPOSE_PROJECT_NAME ?= 'base_api'

build:
	docker compose -f docker-compose-prod.yml build

prod-up:
	docker compose -f docker-compose-prod.yml up -d

prod-up-debug:
	docker compose -f docker-compose-prod.yml --progress=plain up -d

prod-down:
	docker compose -f docker-compose-prod.yml down --rmi 'local'

prod-logs:
	docker compose -f docker-compose-prod.yml logs -f

dev-integration:
	npx tsx src/integration/main.ts

dev-up:
	docker compose -f docker-compose.yml up -d

dev-rebuild:
	docker compose -f docker-compose.yml up --build -d

dev-down:
	docker compose -f docker-compose.yml down --rmi 'local'

dev-logs:
	docker compose -f docker-compose.yml logs -f

dev-shell:
	docker exec -it ${COMPOSE_PROJECT_NAME}-api /bin/bash

clean:
	docker compose -f docker-compose-prod.yml down -v
	docker compose -f docker-compose.yml down -v
	docker builder prune -f
	docker image prune -f

clean-images:
	docker rmi $$(docker images -q) -f

test:
	npm run test:coverage

test-and-build:
	npm run test:coverage
	make build

pre-push:
	npm run lint:src
	npm run lint:tests
	make test-and-build
