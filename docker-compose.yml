services:
  api-base:
    build:
      context: .
      target: "dev"
    volumes:
      - ./:/opt/base_api
    entrypoint: ["/opt/docker/entry-point.sh"]
    ports:
      - "3000:3000"
    env_file:
      - path: ./.env
    networks:
      - api-base
    depends_on:
      - pg
      # - sparxea

  pg:
    image: postgres:17-alpine
    environment:
      POSTGRES_DB: base-api
      POSTGRES_USER: pguser
      POSTGRES_PASSWORD: pgpass
    ports:
      - "5432:5432"
    networks:
      - api-base

  # Testing SparxEA locally
  # sparxea:
  #   image: mcr.microsoft.com/mssql/server:2019-latest
  #   environment:
  #     ACCEPT_EULA: "Y"
  #     SA_PASSWORD: "SuperSecret1"
  #   ports:
  #     - 1433:1433
  #   networks:
  #     - api-base

networks:
  api-base:
