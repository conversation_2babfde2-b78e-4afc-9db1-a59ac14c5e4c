ARG BUILDER=prod

FROM node:23.11.1-alpine3.21 AS base

FROM base AS dev
RUN mkdir -p /opt/docker
COPY ./docker/entry-point.sh /opt/docker/entry-point.sh

EXPOSE 3000

FROM base AS builder
RUN mkdir -p /opt/base_api/src

COPY ./src /opt/base_api/src
COPY ./package.json /opt/base_api/package.json
COPY ./tsconfig.json /opt/base_api/tsconfig.json

WORKDIR /opt/base_api

RUN npm install && npm run build

FROM base AS prod
RUN mkdir -p /opt/base_api/dist
RUN mkdir -p /opt/base_api/config
COPY --from=builder /opt/base_api/dist /opt/base_api/dist
COPY ./config /opt/base_api/config
COPY ./package.json /opt/base_api/package.json
WORKDIR /opt/base_api
RUN npm install
