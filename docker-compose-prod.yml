name: base_api
services:
  api-base:
    container_name: api
    build:
      context: .
      target: "prod"
    command: ["npm", "run", "start"]
    ports:
      - "3000:3000"
    env_file:
      - path: ./.env
    networks:
      - api-base
    depends_on:
      - pg

  pg:
    image: postgres:17-alpine
    container_name: pg
    environment:
      POSTGRES_DB: base-api
      POSTGRES_USER: pguser
      POSTGRES_PASSWORD: pgpass
    ports:
      - 5432:5432
    networks:
      - api-base
networks:
  api-base:
