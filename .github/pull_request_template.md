<!-- Please go over all the following points. If you're unsure about any of these, don't hesitate to ask. Note everything that is commented is only visible in edit or raw view.

- I have tagged as reviewers individuals who must review the changes as well as those who should be aware of these changes
 -->

# Description

Please include a summary of the change. Please also include relevant motivation and context.

## Related JIRA Issue(s)

<!-- These should be links to the User Story.-->

- List JIRA issues

### Related PR dependencies

<!-- These should be links to the PR. Replace text below with N/A if not applicable -->

- List any related PR dependencies that are required for this change.

## Pull Request Checklist

- [ ] I have followed the branching strategy and branch naming convention "jiraissue" (all lowercase).
  - Example: seas-1234
  - If there is no Jira issue (User Story), should there be? If not, branch name should be in the format "shortdesription" (all lowercase)
  - Example: ui-hotfix
- [ ] The PR title is meaningful and in the format "jiraissue: title" (JIRA issue in all caps and title in proper case).
  - Example: SEAS-1234: Fix Budget and Contracts, Contract List duplicates issue
- [ ] I have verified the commits in the PR reflect only commits that I have made and/or related to changes in this PR
- [ ] I have verified the files change only reflect files that were changed and/or related to change in this PR
- [ ] I have linted the library by running `npm run lint` and resolved all linting errors
- [ ] I have created tests for any new components I have created
- [ ] I have run `make build` and have verified all endpoints are working
- [ ] Documentation has been updated

# Testing

<!-- Please also list any relevant details for your test configuration including test environment, any environmental or other configurations. -->

Please describe the tests that you ran to verify your changes. Provide instructions so we can reproduce. Please provide screenshots with captions, if applicable.
