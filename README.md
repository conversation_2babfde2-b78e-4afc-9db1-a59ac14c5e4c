# cms-api-base - Node + Typescript
This repo contains the System API Platform (SAP) replacement for webMethods.

## ENV Variables
| Variable | Description | Type | Example |
| --- | --- | --- | --- |
| `PORT` | Port used for the API | `number` | `3000` |
| `LDAP_SECRET` | The secret string for the LDAP credentials in AWS Secrets Manager | `string` | `dev/SAPLDAPCredentals` |

### DB Variables
The databases and session managers are controlled by name variables in the env file. For example, if your declare the following in `main.ts`:

```js
const dsName = 'SAMPLE';
// Data Source
const pgData = new PgData({ app, name: dsName });
pgData.initDb();

// Session manager
enablePgSession(app, dsName);
```

The variable name you would provide will start with is `SAMPLE`. Ex.: `SAMPLE_DB_USER="test"`

These variables must be added to a secret AWS Secret Manager and add the secret string to `SAMPLE_DB_SECRET`
| Variable | Description | Type | Example |
| --- | --- | --- | --- |
| `{NAME}_DB_USER` | The user name to connect to the database | `string` | `user` |
| `{NAME}_DB_PASS` | The password to connect to the database | `string` | `password` |
| `{NAME}_HOST` | The hostname for the data source | `string ` | `localhost` |
| `{NAME}_PORT` | The port to connect ot the data source | `number` | `5432`|
| `{NAME}_DB `| The name of the database | `string` | `base` |
| `{NAME}_SESSION_SECRET` | The secret phrase needed for your session manager | `string` | `super$3cr3t` |
| `{NAME}_SESSION_TABLE` | The database table where session information is kept | `string` | `sessions` |
| `{NAME}_COOKIE_NAME` | The name prefixed to the cookie returned(WIP) | `string` | `core` |

# Development
During initial setup and before starting the server the first time, you must create an `.env` file or copy the sample env file provided by running `cp .env.example .env`. Run `npm run dev` to start the API with changes watched. Changes are watched via `nodemon` and are controlled via the `nodemon.json` config.

# Starting the server
The server can be started by running `npm run start`

## Testing
Run `npm run test` to run the testing suite.

### Coverage
Running `npm test:coverage` will bring up the coverage report for the entire suite

### Running tests or coverage on a single file
The commands above also work for a single file. In order to run them on a single file, chain the location of the file you would like to test or check coverage on.

Example: `npm run test src\main.ts` or `npm run test:coverage src\main.ts`
