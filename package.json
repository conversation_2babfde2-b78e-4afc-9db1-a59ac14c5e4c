{"name": "cms-api-base", "version": "1.0.0", "main": "dist/main.js", "type": "module", "scripts": {"build": "npx tsc --project tsconfig.json", "config": "npx tsc --showConfig", "start": "vite-node dist/src/main.js", "dev": "nodemon --legacy-watch -w src src/main.ts", "integration": "vite-node -c integration/vite.config.js integration/main.ts", "lint": "eslint \"src/**/*.ts\" \"tests/**/*.ts\" \"integration/**/*.ts\"", "lint:cached": "eslint --cache \"src/**/*.ts\" \"tests/**/*.ts\" \"integration/**/*.ts\"", "format": "eslint \"src/**/*.ts\" --fix", "test": "vitest --run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "typecheck": "npm run typecheck:integration && npm run typecheck:src", "typecheck:integration": "npx tsc --project integration/tsconfig.json --noEmit", "typecheck:src": "npx tsc --project tsconfig.json --noEmit", "typecheck:tests": "npx tsc --project tests/tsconfig.json --noEmit"}, "keywords": [], "author": "CMS", "license": "All Rights Reserved", "description": "", "dependencies": {"@aws-sdk/client-secrets-manager": "^3.758.0", "axios": "^1.8.3", "bunyan": "^1.8.15", "config": "^3.3.12", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "csrf": "^3.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-session": "^1.18.1", "fast-xml-parser": "^5.0.9", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "ldapts": "^7.3.1", "lodash": "^4.17.21", "morgan": "^1.10.0", "pg": "^8.14.0", "sequelize": "^6.37.6", "swagger-autogen": "^2.23.7", "swagger-ui-dist": "^5.21.0", "swagger-ui-express": "^5.0.1", "tedious": "^18.6.1", "uuid": "^11.1.0", "vite-node": "^3.1.3"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@swc/core": "^1.11.9", "@swc/helpers": "^0.5.15", "@types/bunyan": "^1.8.11", "@types/chai": "^5.2.0", "@types/config": "^3.3.5", "@types/connect-pg-simple": "^7.0.3", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.16", "@types/mocha": "^10.0.10", "@types/node": "^22.13.10", "@types/sinon": "^17.0.4", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.8", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitest/coverage-v8": "^3.2.4", "chai": "^5.2.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-plugin-import": "^2.31.0", "jest-diff": "^30.0.2", "msw": "^2.7.3", "nodemon": "^3.1.10", "p-map": "^7.0.3", "regenerator-runtime": "^0.14.1", "sinon": "^19.0.2", "supertest": "^7.0.0", "typescript": "^5.8.2", "vitest": "^3.0.8", "yargs": "^18.0.0"}}