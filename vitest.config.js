import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
    test: {
        globals: true,
        typecheck: {
            tsconfig: './tests/tsconfig.json',
        },
    },
    resolve: {
        alias: [
            {
                find: /^src\/(.*)/,
                replacement: path.resolve(process.cwd(), 'src/$1'),
            },
            {
                find: /^tests\/(.*)/,
                replacement: path.resolve(process.cwd(), 'tests/$1'),
            },
        ],
    },
});
