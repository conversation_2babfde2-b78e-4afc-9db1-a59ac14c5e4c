// Modules.
import dotenv from 'dotenv';

// Custom.
import { harnessCensus } from './harness';
import authenticate from './lib';
import config from './config';
import { processPackage } from './runner';

/**
 * Main entrypoint.
 */
const main = async () => {
  dotenv.config();

  const auth = await authenticate(config);

  await processPackage(config, auth, harnessCensus);
};

(async () => {
  await main();
})();
