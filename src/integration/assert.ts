// Modules.
import { diff } from 'jest-diff';

// Types.
class AssertionError extends Error {}

function p(v: string | null): void {
  process.stdout.write(`${v ?? ''}\n`);
}

const assert = (condition: boolean, message: string) => {
  if (!condition) {
    throw new AssertionError(message);
  }
};

const assertDiff = (a: object, b: object, message: string = 'object mismatch') => {
  const d = diff(a, b);

  if (!d) {
    throw new AssertionError('diff failed');
  }

  if (d && !d.includes('Compared values have no visual difference.')) {
    p(d);
    throw new AssertionError(message);
  }
};

const assertSuccess = (response: Response) => {
  assert(response.status === 200, 'status is not 200');
};

const diffCheck = (a: object, b: object): boolean => {
  const d = diff(a, b);

  return d !== null && !d.includes('Compared values have no visual difference.');
};

export {
  assert,
  assertDiff,
  assertSuccess,
  diffCheck,
};
