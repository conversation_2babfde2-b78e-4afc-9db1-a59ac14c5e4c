interface IAuthentication {
  jwt: string;
  cookie: string;
}

interface IConfig {
  environments: {
    [K in 'local' | 'dev' | 'impl' | 'prod']: {
      url: string;
    };
  };
}

interface IHeaders {
  [key: string]: string;
}

interface IIntegrationHarnessCall {
  method: 'get' | 'post' | 'put' | 'delete';
  path: string;
  body?: object;
  query?: Record<string, string>;
  headers?: Record<string, string>;
  skip?: boolean;
  callback?: (headers: IHeaders, body: object) => void;
  validate(response: Response): Promise<void>;
  validate(response: Response, body: IWebmethodsResponse): Promise<void>;
}

interface IIntegrationHarnessRoute {
  path: string;
  calls: IIntegrationHarnessCall[];
}

interface IIntegrationHarnessPackage {
  name: string;
  path: string;
  routes: IIntegrationHarnessRoute[];
}

interface IWebmethodsResponse {
  result: string;
  message: string | string[];
}

export {
  IAuthentication,
  IConfig,
  IHeaders,
  IIntegrationHarnessCall,
  IIntegrationHarnessPackage,
  IIntegrationHarnessRoute,
  IWebmethodsResponse,
};
