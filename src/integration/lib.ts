// Core.
import fs from 'fs';

// Custom.
import { IAuthentication, IConfig } from './types';

const cachedTokenRead = () => {
  const pwd = process.cwd();

  if (!fs.existsSync(`${pwd}/.cache/integration_jwt_header`)) {
    return null;
  }

  process.stdout.write('using cached auth\n');
  return JSON.parse(
    fs.readFileSync(`${pwd}/.cache/integration_jwt_header`, 'utf8'),
  );
};

const cachedTokenWrite = (auth: IAuthentication) => {
  const pwd = process.cwd();

  if (!auth.jwt || !auth.cookie) {
    throw new Error('cachedTokenWrite: auth is invalid');
  }

  process.stdout.write(`caching auth to ${pwd}/.cache/integration_jwt_header\n`);
  if (!fs.existsSync(`${pwd}/.cache`)) {
    fs.mkdirSync(`${pwd}/.cache`);
  }
  fs.writeFileSync(`${pwd}/.cache/integration_jwt_header`, JSON.stringify(auth));
};

const authenticate = async (config: IConfig) => {
  const cached = cachedTokenRead();
  if (cached) {
    return cached;
  }

  const body = {
    username: process.env.INTEGRATION_USERNAME,
    password: process.env.INTEGRATION_PASSWORD,
  };

  const response = await fetch(`${config.environments.local.url}/gateway/LDAP/1.0/authenticate`, {
    method: 'post',
    body: JSON.stringify(body),
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const auth: IAuthentication = {
    jwt: response.headers.get('x-set-jwt') ?? '',
    cookie: response.headers.get('set-cookie')?.split(';')[0] ?? '',
  };

  if (!response.ok || !auth.jwt || !auth.cookie) {
    throw new Error('Unable to authenticate');
  }

  cachedTokenWrite(auth);

  return auth;
};

export default authenticate;
