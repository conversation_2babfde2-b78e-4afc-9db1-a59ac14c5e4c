// noinspection SpellCheckingInspection

// Custom.
import {
  SystemsListItem,
  SystemsListResponse,
} from 'resources/census-core-v2/systems/find';
import {
  assert, assertDiff, assertSuccess, diffCheck,
} from './assert';
import { IIntegrationHarnessPackage, NOTE_PAGE_NAME } from './types';
import {
  SystemPropertyQueryParams,
  SystemPropertyResponse,
} from '../resources/census-core-v2/systemProperty/find';

const harnessCensus: IIntegrationHarnessPackage = {
  name: 'census',
  path: '/gateway/System%20Census%20Core%20API/2.0.0',
  routes: [
    {
      path: '/',
      calls: [
        {
          method: 'get',
          path: '/',
          validate: async (response: Response) => {
            const body = await response.json();
            assertSuccess(response);
            assert(body.message === 'CENSUS Core v2 root', 'message mismatch');
          },
        },
      ],
    },
    {
      path: '/note',
      calls: [
        {
          method: 'post',
          path: '/',
          // @todo-integration Need to decide on names here, with multiple devs
          //                   hard to tell which belongs to whom, perhaps
          //                   environment variables could be used. --hrivera
          body: {
            Notes: [{
              systemId: '{11111111-2222-3333-4444-555555555555}',
              pageName: NOTE_PAGE_NAME,
              userId: 'T3ST',
              userFirst: 'Henry',
              userLast: 'Rivera',
              userRole: 'Reviewer',
              note: 'This is a test note.',
            }],
            EmailFlags: {
              notifyReviewer: false,
              notifyRespondent: false,
              includeHistory: false,
            },
          },
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          callback: (headers: Headers, _body: object) => ({
            INSERTED_IDS: headers.get('INSERTED_IDS'),
            AFFECTED_ROWS: headers.get('AFFECTED_ROWS'),
          }),
          validate: async (response: Response): Promise<void> => {
            assertSuccess(response);
          },
        },
        {
          method: 'get',
          path: '/',
          query: {},
          validate: async (response: Response): Promise<void> => {
            const body = await response.json();

            assertSuccess(response);
            assert(body.Notes[0].systemId === '{11111111-2222-3333-4444-555555555555}', 'expected system id 11111111-2222-3333-4444-555555555555');
            assert(body.Notes[0].pageName === NOTE_PAGE_NAME, `expected page name ${NOTE_PAGE_NAME}`);
            assert(body.Notes[0].userId === 'T3ST', 'expected user id T3ST');
            assert(body.Notes[0].userFirst === 'Henry', 'expected user first name Henry');
            assert(body.Notes[0].userLast === 'Rivera', 'expected user last name Rivera');
            assert(body.Notes[0].userRole === 'Reviewer', 'expected user role Reviewer');
            assert(body.Notes[0].note === 'This is a test note.', 'expected note This is a test note.');
          },
        },
        {
          // @todo-integration Nearly ready to implement delete, but not quite yet. --hrivera
          method: 'delete',
          path: '/',
          skip: true,
          validate: async (response: Response) => {
            assertSuccess(response);
            assert(false, 'test not implemented');
          },
        },
      ],
    },
    {
      path: '/page/systemsList',
      calls: [
        {
          method: 'get',
          path: '/',
          validate: async (response: Response): Promise<void> => {
            const body = await response.json();
            const original: SystemsListResponse = {
              pageName: 'SystemsList',
              count: 1,
              SystemsList: [
                {
                  id: '{85B1AEFF-CB34-43cc-A99C-8D094CDB89F1}',
                  nextVersionId: '',
                  previousVersionId: '',
                  ictObjectId: '{85B1AEFF-CB34-43cc-A99C-8D094CDB89F1}',
                  uuid: '',
                  name: 'Knowledge Management Platform',
                  description: 'The Knowledge Management Platform (KMP) is a platform aimed at delivering AI products and services to the organization.',
                  version: '25',
                  acronym: 'KMP',
                  objectState: 'Active',
                  status: '',
                  belongsTo: '',
                  businessOwnerOrg: 'Division of Technical Engineering and Architecture',
                  businessOwnerOrgComp: 'OIT',
                  systemMaintainerOrg: 'Division of Technical Engineering and Architecture',
                  systemMaintainerOrgComp: 'OIT',
                  qaReviewerAssignmentId: '{C201E288-5B9D-446F-AE1B-D083C8B60E45}',
                  qaReviewerFirstName: 'Texi',
                  qaReviewerLastName: 'Rodriguez-Villarreal',
                  qaReviewerUserName: 'RD8U',
                  daReviewerAssignmentId: '{2FC97A95-707E-4BD5-8F61-09AE707FC774}',
                  daReviewerFirstName: 'Aditya',
                  daReviewerLastName: 'Sharma',
                  daReviewerUserName: 'SAHL',
                },
              ],
            };

            // Ensure the example document is found in the list.
            let foundMatch = false;
            body.SystemsList.forEach((system: SystemsListItem) => {
              if (diffCheck(system, original.SystemsList[0])) {
                foundMatch = true;
              }
            });

            assertSuccess(response);
            assert(foundMatch, 'system not found');
          },
        },
      ],
    },
    {
      path: '/systemProperty',
      calls: [
        {
          method: 'get',
          path: '/',
          query: {
            systemId: '{0029DABB-C64B-4a18-BB2D-0714A8AEAD62}',
            propertyName: 'CMS UUID',
          } as SystemPropertyQueryParams,
          validate: async (response: Response): Promise<void> => {
            const body = await response.json();
            const original: SystemPropertyResponse = {
              propertyValue: '21F129D2-3314-44AE-AAB9-CE210DF411D5',
            };

            assertSuccess(response);
            assertDiff(body, original);
          },
        },
      ],
    },
  ],
};

// noinspection JSUnusedGlobalSymbols
const harnessAll: IIntegrationHarnessPackage[] = [harnessCensus];

export {
  harnessAll,
  harnessCensus,
};
