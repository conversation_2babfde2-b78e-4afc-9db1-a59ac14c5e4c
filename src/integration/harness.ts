// noinspection SpellCheckingInspection

// Custom.
import { SystemsListItem, SystemsListResponse } from 'resources/census-core-v2/systems/find';
import {
  assert, assertDiff, assertSuccess, diffCheck,
} from './assert';
import { NoteFindResponse } from '../resources/census-core-v2/note/find';
import { IHeaders, IIntegrationHarnessPackage } from './types';

const harnessCensus: IIntegrationHarnessPackage = {
  name: 'census',
  path: '/gateway/System%20Census%20Core%20API/2.0.0',
  routes: [
    {
      path: '/',
      calls: [
        {
          method: 'get',
          path: '/',
          validate: async (response: Response) => {
            const body = await response.json();
            assertSuccess(response);
            assert(body.message === 'CENSUS Core v2 root', 'message mismatch');
          },
        },
      ],
    },
    {
      path: '/note',
      calls: [
        {
          method: 'get',
          path: '/',
          query: {
            id: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
            pageName: 'BusinessOwnerBasicInformation',
          },
          validate: async (response: Response): Promise<void> => {
            const body = await response.json();
            const og: NoteFindResponse = {
              count: 1,
              Notes: [
                {
                  noteId: '3998',
                  systemId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
                  pageName: 'BusinessOwnerBasicInformation',
                  userId: 'ACPS',
                  userFirst: 'Matthew',
                  userLast: 'Artz',
                  userRole: 'Reviewer',
                  note: 'test note',
                  createdOn: '2024-09-11T15:24:10.257Z',
                },
              ],
            };

            assertSuccess(response);
            assertDiff(body, og);
          },
        },
        {
          method: 'post',
          path: '/',
          skip: true,
          callback: (headers: IHeaders, body: object) => {
            process.stdout.write(JSON.stringify(headers, null, 2));
            process.stdout.write(JSON.stringify(body, null, 2));
          },
          validate: async (response: Response): Promise<void> => {
            assertSuccess(response);
            assert(false, 'test not implemented');
          },
        },
        {
          method: 'delete',
          path: '/',
          skip: true,
          validate: async (response: Response) => {
            assertSuccess(response);
            assert(false, 'test not implemented');
          },
        },
      ],
    },
    {
      path: '/page/systemsList',
      calls: [
        {
          method: 'get',
          path: '/',
          validate: async (response: Response): Promise<void> => {
            const body = await response.json();
            const og: SystemsListResponse = {
              pageName: 'SystemsList',
              count: 1,
              SystemsList: [
                {
                  id: '{85B1AEFF-CB34-43cc-A99C-8D094CDB89F1}',
                  nextVersionId: '',
                  previousVersionId: '',
                  ictObjectId: '{85B1AEFF-CB34-43cc-A99C-8D094CDB89F1}',
                  uuid: '',
                  name: 'Knowledge Management Platform',
                  description: 'The Knowledge Management Platform (KMP) is a platform aimed at delivering AI products and services to the organization.',
                  version: '25',
                  acronym: 'KMP',
                  objectState: 'Active',
                  status: '',
                  belongsTo: '',
                  businessOwnerOrg: 'Division of Technical Engineering and Architecture',
                  businessOwnerOrgComp: 'OIT',
                  systemMaintainerOrg: 'Division of Technical Engineering and Architecture',
                  systemMaintainerOrgComp: 'OIT',
                  qaReviewerAssignmentId: '{C201E288-5B9D-446F-AE1B-D083C8B60E45}',
                  qaReviewerFirstName: 'Texi',
                  qaReviewerLastName: 'Rodriguez-Villarreal',
                  qaReviewerUserName: 'RD8U',
                  daReviewerAssignmentId: '{2FC97A95-707E-4BD5-8F61-09AE707FC774}',
                  daReviewerFirstName: 'Aditya',
                  daReviewerLastName: 'Sharma',
                  daReviewerUserName: 'SAHL',
                },
              ],
            };

            // Ensure the example document is found in the list.
            let foundMatch = false;
            body.SystemsList.forEach((system: SystemsListItem) => {
              if (diffCheck(system, og.SystemsList[0])) {
                foundMatch = true;
              }
            });

            assertSuccess(response);
            assert(foundMatch, 'system not found');
          },
        },
      ],
    },
  ],
};

// noinspection JSUnusedGlobalSymbols
const harnessAll: IIntegrationHarnessPackage[] = [harnessCensus];

export {
  harnessAll,
  harnessCensus,
};
