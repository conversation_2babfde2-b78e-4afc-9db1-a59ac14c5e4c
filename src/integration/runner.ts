import Route from './route';
import {
  IAuthentication,
  IConfig,
  IIntegrationHarnessCall,
  IIntegrationHarnessPackage,
  IIntegrationHarnessRoute,
  NOTE_PAGE_NAME,
} from './types';

const p = (...args: string[]) => {
  const m = args.map((a: string) => a.toString()).join(' ');
  process.stdout.write(`${m}\n`);
};

const pi = (m: string, i: number = 0) => {
  process.stdout.write(`${'\t'.repeat(i)}${m}\n`);
};

const processCall = async (
  config: IConfig,
  auth: IAuthentication,
  call: IIntegrationHarnessCall,
  routePath: string,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  lastResult: any = null,
): Promise<object | null> => {
  pi(`processing call ${call.method.toUpperCase()} ${routePath}${call.path}`, 2);

  const requestOptions: RequestInit = {
    method: call.method,
    headers: {
      'x-jwt-key': auth.jwt,
      Cookie: auth.cookie,
      'Content-Type': 'application/json',
    },
  };

  if (call.method !== 'get' && call.body) {
    requestOptions.body = JSON.stringify(call.body);
  }

  const baseUrl = Route.getUrl(
    config.environments.local.url,
    routePath,
    call.path,
  ).replace(/\/$/, '');

  let query = call?.query ?? {};

  if (routePath.endsWith('/test') && call.method === 'get') {
    process.stdout.write(JSON.stringify(lastResult, null, 2));
  }

  if (routePath.endsWith('/note') && call.method === 'get') {
    // const last = JSON.parse(lastResult.INSERTED_IDS);
    query = {
      ...query,
      id: '{11111111-2222-3333-4444-555555555555}',
      pageName: NOTE_PAGE_NAME,
    };
  }

  const url = (query && Object.keys(query).length > 0)
    ? `${baseUrl}?${new URLSearchParams(query).toString()}`
    : baseUrl;

  if (call.skip) {
    pi(`skipping call ${call.method} ${url.replace('http://localhost:3000', '').replace('http:/localhost:3000', '')}`, 3);
    return null;
  }

  const request = new Request(url, requestOptions);
  const response = await fetch(url, request);

  let returnValue = {};

  if (call.callback) {
    const body = await response.json();
    returnValue = call.callback(response.headers, body);
  }

  const debug = [
    `code=${response.status}`,
    `method=${call.method.toUpperCase()}`,
    `url=${baseUrl}`.replace('http://localhost:3000', '').replace('http:/localhost:3000', ''),
    `status=${response.status}`,
    `content-type=${response.headers.get('content-type')}`,
    `content-length=${response.headers.get('content-length')}`,
  ];
  pi(debug.join(' '), 3);

  await call.validate(response);

  if (call.callback) {
    return returnValue;
  }

  return null;
};

const processRoute = async (
  config: IConfig,
  auth: IAuthentication,
  route: IIntegrationHarnessRoute,
  packagePath: string,
): Promise<void> => {
  pi('-'.repeat(80), 1);
  pi(`processing route ${packagePath}${route.path}`, 1);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let lastResult: any = null;

  // eslint-disable-next-line no-restricted-syntax
  for (const call of route.calls) {
    // eslint-disable-next-line no-await-in-loop
    const result = await processCall(
      config,
      auth,
      call,
      `${packagePath}${route.path}`,
      lastResult,
    );

    if (result) {
      lastResult = result;
    } else {
      lastResult = null;
    }
  }

  // await route.calls.reduce(async (promise, call) => {
  //   await promise;
  //   return processCall(config, auth, call, `${packagePath}${route.path}`);
  // }, Promise.resolve());
};

const processPackage = async (
  config: IConfig,
  auth: IAuthentication,
  harnessPackage: IIntegrationHarnessPackage,
): Promise<void> => {
  p(`processing package ${harnessPackage.name} (${harnessPackage.path})`);

  await harnessPackage.routes.reduce(async (promise, route) => {
    await promise;
    return processRoute(config, auth, route, harnessPackage.path);
  }, Promise.resolve());
};

export {
  processCall,
  processPackage,
  processRoute,
};
