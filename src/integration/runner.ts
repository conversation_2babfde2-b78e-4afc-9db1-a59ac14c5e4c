import Route from './route';
import {
  IAuthentication,
  IConfig,
  IIntegrationHarnessCall,
  IIntegrationHarnessPackage,
  IIntegrationHarnessRoute,
} from './types';

const p = (...args: string[]) => {
  const m = args.map((a: string) => a.toString()).join(' ');
  process.stdout.write(`${m}\n`);
};

const pi = (m: string, i: number = 0) => {
  process.stdout.write(`${'\t'.repeat(i)}${m}\n`);
};

const processCall = async (
  config: IConfig,
  auth: IAuthentication,
  call: IIntegrationHarnessCall,
  routePath: string,
): Promise<void> => {
  pi(`processing call ${call.method.toUpperCase()} ${routePath}${call.path}`, 2);

  const requestOptions: RequestInit = {
    method: call.method,
    headers: {
      'x-jwt-key': auth.jwt,
      Cookie: auth.cookie,
      'Content-Type': 'application/json',
    },
  };

  if (call.method !== 'get' && call.body) {
    requestOptions.body = JSON.stringify(call.body);
  }

  const baseUrl = Route.getUrl(
    config.environments.local.url,
    routePath,
    call.path,
  ).replace(/\/$/, '');

  const url = (call.query && Object.keys(call.query).length > 0)
    ? `${baseUrl}?${new URLSearchParams(call.query).toString()}`
    : baseUrl;

  if (call.skip) {
    pi(`skipping call ${call.method} ${url}`, 3);
    return;
  }

  const request = new Request(url, requestOptions);
  const response = await fetch(url, request);

  const debug = [
    `code=${response.status}`,
    `method=${call.method.toUpperCase()}`,
    `url=${baseUrl}`,
    `status=${response.status}`,
    `content-type=${response.headers.get('content-type')}`,
    `content-length=${response.headers.get('content-length')}`,
  ];
  pi(debug.join(' '), 3);

  await call.validate(response);
};

const processRoute = async (
  config: IConfig,
  auth: IAuthentication,
  route: IIntegrationHarnessRoute,
  packagePath: string,
): Promise<void> => {
  pi('-'.repeat(80), 1);
  pi(`processing route ${packagePath}${route.path}`, 1);

  await route.calls.reduce(async (promise, call) => {
    await promise;
    return processCall(config, auth, call, `${packagePath}${route.path}`);
  }, Promise.resolve());
};

const processPackage = async (
  config: IConfig,
  auth: IAuthentication,
  harnessPackage: IIntegrationHarnessPackage,
): Promise<void> => {
  p(`processing package ${harnessPackage.name} (${harnessPackage.path})`);

  await harnessPackage.routes.reduce(async (promise, route) => {
    await promise;
    return processRoute(config, auth, route, harnessPackage.path);
  }, Promise.resolve());
};

export {
  processCall,
  processPackage,
  processRoute,
};
