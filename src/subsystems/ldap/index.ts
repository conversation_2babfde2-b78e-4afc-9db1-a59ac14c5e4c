import { Client } from 'ldapts';
import {
  get,
  isString,
  isArray,
  isError,
  isNull,
} from 'lodash';
import {
  LDAPFilter,
  LDAPAttributes,
  LDAPLoginValues,
  LDAPPersonMap,
  User,
  CMSApp,
  SparxUserData,
} from '../../types';
import {
  ldapBase,
  getCredentials,
} from '../../utils/auth';

const bindPerson = async (app: CMSApp, loginValues: LDAPLoginValues) => {
  const {
    clientOptions,
  } = getCredentials();

  if (get(clientOptions, 'url', 'undefined').includes('undefined')) {
    return new Error('Invalid Admin Credentials');
  }

  const client = new Client(clientOptions);

  try {
    // bind user to ldap
    await client.bind(`uid=${String(loginValues.username).toUpperCase()},ou=people,${ldapBase}`, loginValues.password);
  } catch (err) {
    app.logger.error({ message: `User bind Error: ${get(err, 'message', 'Unknown bind error')}` });
    return new Error('Unable to bind to the user');
  } finally {
    client.unbind();
  }

  return 'success';
};

const queryPersons = async (
  app: CMSApp,
  filter: LDAPFilter,
  attributes: LDAPAttributes,
  jobCode?: string,
): Promise<User[] | Error> => {
  const persons: User[] = [];
  const {
    ldapAdmin,
    ldapAdminPass,
    clientOptions,
  } = getCredentials();

  if (!ldapAdmin || !ldapAdminPass || get(clientOptions, 'url', 'undefined').includes('undefined')) {
    return new Error('Invalid Admin Credentials');
  }

  const adminClient = new Client(clientOptions);

  try {
    await adminClient.bind(`uid=${ldapAdmin},ou=System Accounts,${ldapBase}`, ldapAdminPass);
    const { searchEntries } = await adminClient.search(`ou=people,${ldapBase}`, {
      filter,
      attributes,
    });

    searchEntries.forEach((item) => {
      persons.push(item as User);
    });
  } catch (err) {
    app.logger.error({ error: err });
    return new Error('Unable to find persons with supplied filter');
  } finally {
    adminClient.unbind();
  }

  if (jobCode) {
    const hasJobCode = persons.every((person) => {
      const memberships = get(person, 'ismemberof');
      if (!memberships) {
        return false;
      }

      if (!isString(memberships) && !isArray(memberships)) {
        return false;
      }

      if (isString(memberships) && !memberships.includes(jobCode)) {
        return false;
      }

      if (isArray(memberships) && !memberships.some((str) => str.includes(jobCode))) {
        return false;
      }

      return true;
    });

    if (!hasJobCode) {
      return new Error('Unable to find job code for persons');
    }
  }

  return persons;
};

const person = async (app: CMSApp, personData: LDAPPersonMap): Promise<User[] | Error> => {
  const {
    firstName,
    lastName,
    commonName,
    email,
    telephone,
  } = personData;

  if (Object.values(personData).every(isNull)) {
    return new Error('No items provided for the filter');
  }

  if (!isString(firstName) && !isNull(firstName)) {
    return new Error('LDAP searches using first name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
  }

  if (!isString(lastName) && !isNull(lastName)) {
    return new Error('LDAP searches using last name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
  }

  if (!isString(commonName) && !isNull(commonName)) {
    return new Error('LDAP searches using common name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
  }

  if (!isString(email) && !isNull(email)) {
    return new Error('LDAP search using email did not contain a valid email pattern. Please try again.');
  }

  let filter: string = '';
  if (firstName) {
    filter += `(givenName=${firstName})`;
  }

  if (lastName) {
    filter += `(sn=${lastName})`;
  }

  if (commonName) {
    filter += `(cn=${commonName})`;
  }

  if (email) {
    filter += `(mail=${email})`;
  }

  if (telephone) {
    filter += `(telephoneNumber=${telephone})`;
  }

  filter = `(&${filter})`;
  const attributes = ['cn', 'mail', 'givenName', 'sn', 'telephoneNumber', 'uid', 'ismemberof'];
  const personResult = await queryPersons(app, filter, attributes);

  if (isError(personResult)) {
    app.logger.error(personResult);
    return personResult;
  }

  return personResult;
};

const personIds = async (app: CMSApp, id: string) => {
  const filter = `uid=${id}`;
  const attributes = ['cn', 'mail', 'givenName', 'sn', 'telephoneNumber', 'uid', 'ismemberof'];
  const user = await queryPersons(app, filter, attributes);
  if (isError(user)) {
    app.logger.error(user);
    return user;
  }
  return user;
};

const mapSinglePerson = (personData: SparxUserData) => {
  const {
    id,
    firstName,
    lastName,
    phone,
    email,
    userName,
  } = personData;

  const mappedData = {
    id,
    userName,
    firstName,
    lastName,
    phone,
    email,
  };

  return mappedData;
};

const mapPerson = (personData: SparxUserData[]) => {
  const mappedPersons: LDAPPersonMap[] = [];
  personData.forEach((singlePerson) => {
    const mappedSingle = mapSinglePerson(singlePerson);
    mappedPersons.push(mappedSingle);
  });

  return mappedPersons;
};

export {
  queryPersons,
  bindPerson,
  person,
  personIds,
  mapSinglePerson,
  mapPerson,
};
