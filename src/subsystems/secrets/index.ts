import {
  isError,
  isEmpty,
  isArray,
  isString,
  get,
} from 'lodash';
import { getSecret as getAWSSecret, getBulkSecrets } from '../../utils/aws';
import { CMSApp, SecretStoreItem } from '../../types';

const secretStore: SecretStoreItem[] = [];

const getSecret = async (app: CMSApp, secretString: string) => {
  const existingSecret = secretStore.find((item) => item.secretString === secretString);
  if (existingSecret) {
    return existingSecret.secrets;
  }

  const secrets = await getAWSSecret(app, secretString);
  if (isError(secrets)) {
    return secrets;
  }

  secretStore.push({
    secretString,
    secrets,
  });

  return secrets;
};

const getSecretStringFromEnv = (app: CMSApp, envStrings: string | string[]): string[] | Error => {
  if (isArray(envStrings) && !envStrings.every(isString)) {
    return new Error('The provided environment variables are not an array of strings');
  }
  let env: string[] = [];
  if (isArray(envStrings)) {
    env = envStrings;
  } else {
    env = [envStrings];
  }

  const secretStrings: string[] = [];
  const secretStringError = new Error('');
  env.forEach((item) => {
    const secretString = get(process, `env.${item}`);
    if (!secretString) {
      secretStringError.message = `Unable to get secret string from ${item} environment variable`;
      app.logger.error({ error: secretStringError });
      return;
    }

    secretStrings.push(secretString);
  });

  if (!isEmpty(secretStringError.message)) {
    return secretStringError;
  }

  return secretStrings;
};

const initSecrets = async (app: CMSApp, secrets: string[]): Promise<void | Error> => {
  if (isEmpty(secrets)) {
    throw new Error('Secrets cannot be empty when initializing the secret subsystem');
  }
  const secretResponse = await getBulkSecrets(app, secrets);
  if (isError(secretResponse)) {
    throw secretResponse;
  }

  secretResponse.forEach((item) => secretStore.push(item));
};

export {
  getSecret,
  getSecretStringFromEnv,
  initSecrets,
};
