import {
  isError,
  isEmpty,
} from 'lodash';
import {
  CMSApp,
  User,
  GlobalVariableBody,
} from '../../types';
import {
  getDb,
  GLOBAL_VARIABLES,
} from '../../utils/db-helpers';
import PgData from '../../data-sources/pg';
import {
  tryAsync,
} from '../../utils/general';
import {
  nowUtcFormatted,
} from '../../utils/time';

const findGlobalVariable = async (app: CMSApp, globalName: string) => {
  // Get Core DB
  if (isEmpty(app)) {
    return new Error('Application cannot be blank');
  }

  if (isEmpty(globalName)) {
    return new Error('Global name is required');
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const [findError, findGlobalData] = await tryAsync(db.findOne(GLOBAL_VARIABLES, {
    globalName,
  }));

  if (findError || isError(findGlobalData)) {
    app.logger.error({ error: findError || findGlobalData });
    return findError || findGlobalData;
  }

  return findGlobalData;
};

const createGlobalVariable = async (app: CMSApp, user: User, global: GlobalVariableBody) => {
  // Get Core DB
  if (isEmpty(app)) {
    return new Error('Application cannot be blank');
  }

  if (isEmpty(user)) {
    return new Error('Invalid user');
  }

  if (isEmpty(global)) {
    return new Error('Global body cannot be blank');
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const findGlobalData = await findGlobalVariable(app, global.globalName);

  if (!isEmpty(findGlobalData)) {
    return new Error('Global variable already exists');
  }

  const [createError, createGlobalData] = await tryAsync(db.insertOne(GLOBAL_VARIABLES, {
    globalName: global.globalName,
    globalValue: global.globalValue,
    createdBy: user.uid,
    createdDate: nowUtcFormatted(),
    updatedBy: user.uid,
    updatedDate: nowUtcFormatted(),
  }));

  if (createError || isError(createGlobalData)) {
    app.logger.error({ error: createError || createGlobalData });
    return createError || createGlobalData;
  }
  return true;
};

const updateGlobalVariable = async (app: CMSApp, user: User, global: GlobalVariableBody) => {
  if (isEmpty(app)) {
    return new Error('Application cannot be blank');
  }

  if (isEmpty(user)) {
    return new Error('Invalid user');
  }

  if (isEmpty(global)) {
    return new Error('Global body cannot be blank');
  }

  // Get Core DB
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const findGlobalData = await findGlobalVariable(app, global.globalName);

  if (isEmpty(findGlobalData)) {
    return new Error('Global variable does not exist');
  }

  const [updateError, updateGlobalData] = await tryAsync(db.updateOne(GLOBAL_VARIABLES, {
    globalValue: global.globalValue,
    updatedBy: user.uid,
    updatedDate: nowUtcFormatted(),
  }, {
    globalName: global.globalName,
  }));

  if (updateError || isError(updateGlobalData) || updateGlobalData === 'failed') {
    app.logger.error({ error: updateError || updateGlobalData });
    return updateError || updateGlobalData;
  }

  return true;
};

const removeGlobalVariable = async (app: CMSApp, global: string) => {
  if (isEmpty(app)) {
    return new Error('Application cannot be blank');
  }

  if (isEmpty(global)) {
    return new Error('Global body cannot be blank');
  }

  // Get Core DB
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const findGlobalData = await findGlobalVariable(app, global);

  if (isEmpty(findGlobalData)) {
    return new Error('Global variable does not exist');
  }

  const [deleteError, deleteGlobalData] = await tryAsync(
    db.deleteOne(GLOBAL_VARIABLES, { globalName: global }),
  );

  if (deleteError || isError(deleteGlobalData)) {
    app.logger.error({ error: deleteError || deleteGlobalData });
    return deleteError || deleteGlobalData;
  }
  return true;
};

export {
  findGlobalVariable,
  createGlobalVariable,
  updateGlobalVariable,
  removeGlobalVariable,
};
