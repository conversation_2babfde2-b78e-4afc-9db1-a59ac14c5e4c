import {
  isError,
  isEmpty,
  get,
  isPlainObject,
} from 'lodash';
import { XMLParser } from 'fast-xml-parser';
import MssqlData from '../../data-sources/mssql';
import {
  CMSApp,
  SparxPackage,
} from '../../types';
import { getSecret } from '../secrets';
import { getDb, SPARX_PACKAGE_HIERARCHY } from '../../utils/db-helpers';
import { tryAsync } from '../../utils/general';
import { postRequest, getRequest, deleteRequest } from '../../utils/http';
import {
  getDuration,
  addToUTC,
  nowUtcFormatted,
} from '../../utils/time';

let tokenExpireTime = '1900-01-01T01:01:01.001Z';
let token = '';
let apiUrlBase = '';

const getToken = async (app: CMSApp) => {
  const expiration = getDuration(tokenExpireTime, null, 'seconds');
  if (isError(expiration)) {
    return new Error('Unable to verify token expiration');
  }

  if (expiration > 2 && !isEmpty(token)) {
    return token;
  }

  const sparxSecretString = get(app, 'config.systems.sparxea.apiSecretString');
  if (!sparxSecretString) {
    return new Error('Unable to retrieve Sparx API Secret String');
  }
  const secrets = await getSecret(app, sparxSecretString);
  if (isError(secrets) || !isPlainObject(secrets)) {
    const secretsErrorMsg = 'Error getting SparxEA API secrets';
    app.logger.error({ message: secretsErrorMsg, error: secrets });
    return new Error(secretsErrorMsg);
  }

  const sparxUser = get(Object.keys(secrets), '[0]');
  const sparxPass = get(secrets, sparxUser);
  if (!sparxUser || !sparxPass) {
    return new Error('Unable to connect to SparxEA the credentials are not configured correctly');
  }

  const requestOpts = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  const response = await postRequest(app, `${apiUrlBase}/CMS_Dev_Model/oslc/am/login/`, `uid=${sparxUser};pwd=${sparxPass}`, requestOpts);
  if (isError(response)) {
    app.logger.error({ error: response });
    return new Error('An error occurred requesting a token');
  }

  const parser = new XMLParser();
  const parsedResponse = parser.parse(get(response, 'data', ''));
  const newToken = get(parsedResponse, 'rdf:RDF.ss:login.ss:useridentifier');
  if (!newToken) {
    return new Error('Unable to retrieve token from SparxEA response');
  }

  const newExpiration = addToUTC(nowUtcFormatted(), 15, 'minutes') as string;
  if (isError(newExpiration)) {
    return new Error('An error occurred updating the token expiration');
  }

  tokenExpireTime = newExpiration;
  token = newToken;
  return token;
};

const getTokenExpiration = () => tokenExpireTime;

const getQuery = async (app: CMSApp, id: string) => {
  const systemToken = await getToken(app);
  if (isError(systemToken)) {
    return systemToken;
  }

  const baseUrl = `${apiUrlBase}/CMS_Dev_Model/oslc/am/resource/`;
  const [error, response] = await tryAsync(getRequest(app, `${baseUrl}${encodeURIComponent(id)}?useridentifier=${encodeURIComponent(systemToken)}`));
  if (error || isError(response)) {
    app.logger.error(error || response);
    return new Error('An error occurred getting the object');
  }

  return get(response, 'data', '');
};

const postQuery = async (app: CMSApp, body: string | object) => {
  const [error, response] = await tryAsync(postRequest(app, `${apiUrlBase}/CMS_Dev_Model/oslc/am/cf/resource/`, body));
  if (error || isError(response)) {
    app.logger.error(error || response);
    return new Error('An error occurred creating the object');
  }

  return get(response, 'data', '');
};

const deleteQuery = async (app: CMSApp, id: string) => {
  const systemToken = await getToken(app);
  if (isError(systemToken)) {
    return systemToken;
  }

  const baseUrl = `${apiUrlBase}/CMS_Dev_Model/oslc/am/resource/`;
  const [error, response] = await tryAsync(deleteRequest(app, `${baseUrl}${encodeURIComponent(id)}?useridentifier=${encodeURIComponent(systemToken)}`));
  if (error || isError(response)) {
    app.logger.error(error || response);
    return new Error('An error occurred deleting the object');
  }

  return get(response, 'data', '');
};

let packages: SparxPackage[] = [];
const packageNames: string[] = [];

const getPackages = () => packages;
const getPackageNames = () => packageNames;

const getSparxPackages = async (app: CMSApp): Promise<SparxPackage[] | Error> => {
  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return db;
  }
  const [error, result] = await tryAsync(db.queryView(SPARX_PACKAGE_HIERARCHY, ['Package_GUID', 'Package_Name']));
  const resultPackages: SparxPackage[] | Error = get(result, '[0]', []);
  if (error) {
    return error;
  }

  if (isError(result)) {
    return result;
  }

  if (isEmpty(resultPackages)) {
    return new Error('Package results empty');
  }

  return resultPackages;
};

const initSparx = async (app: CMSApp) => {
  const sparxPackages = await getSparxPackages(app);
  if (isError(sparxPackages)) {
    throw sparxPackages;
  }

  packages = sparxPackages;
  packages.forEach((item) => {
    packageNames.push(item.Package_Name);
  });

  const sparxApiSecret = get(app, 'config.systems.sparxea.apiSecretString');
  if (!sparxApiSecret) {
    throw new Error('Unable to retrieve Sparx API Secret value');
  }

  const sparxApiSecrets = await getSecret(app, sparxApiSecret);
  if (isError(sparxApiSecrets)) {
    throw sparxApiSecrets;
  }

  const baseUrl = get(sparxApiSecrets, 'api-url');
  if (!baseUrl) {
    throw new Error('Unable to retrieve Sparx API base URL');
  }

  apiUrlBase = baseUrl;
};

const getPackageParentId = (id: string) => get(packages.find((item) => item.Package_Name === id), 'Package_GUID');

export {
  getToken,
  getTokenExpiration,
  getQuery,
  postQuery,
  deleteQuery,
  getPackages,
  getPackageNames,
  getSparxPackages,
  initSparx,
  getPackageParentId,
};
