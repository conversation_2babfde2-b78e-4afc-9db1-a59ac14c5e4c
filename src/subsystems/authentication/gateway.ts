import { Request } from 'express';
import Jo<PERSON> from 'joi';
import { isError } from 'lodash';
import { v4 } from 'uuid';
import { CMSApp, User } from '../../types';
import { getDb, GATEWAY } from '../../utils/db-helpers';
import PgData from '../../data-sources/pg';
import { tryAsync } from '../../utils/general';
import { nowUtcFormatted } from '../../utils/time';

const validate = async (req: Request, app: CMSApp, key: string): Promise<true | Error> => {
  const schema = Joi.string().required().uuid();
  const { error } = schema.validate(key);
  if (error) {
    return new Error('Invalid key');
  }

  // Get Core DB
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const [critError, keyData] = await tryAsync(db.findOne(GATEWAY, {
    key,
  }));

  if (critError || isError(keyData)) {
    return critError || keyData as Error;
  }

  // check DB for key
  if (!keyData) {
    return new Error('Gateway key not found');
  }

  req.authMethod = 'gateway';
  return true;
};

const createKey = async (app: CMSApp, user: User) => {
  // Get Core DB
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const newKey = v4();

  const [critError, keyData] = await tryAsync(db.insertOne(GATEWAY, {
    key: newKey,
    createdBy: user.uid,
    createdDate: nowUtcFormatted(),
  }));

  if (critError || isError(keyData)) {
    return critError || keyData;
  }

  return newKey;
};

const removeKey = async (app: CMSApp, key: string) => {
  // Get Core DB
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return db;
  }

  const [critError, keyData] = await tryAsync(db.deleteOne(GATEWAY, {
    key,
  }));

  if (critError || isError(keyData)) {
    return critError || keyData;
  }

  return true;
};

export {
  validate,
  createKey,
  removeKey,
};
