import { Request } from 'express';
import { isEmpty } from 'lodash';
import { AuthHeaders, CMSApp } from '../../types';
import { validate as gatewayValidate } from './gateway';
import { validate as jwtValidate } from './jwt';

const authentication = async (
  req: Request,
  app: CMSApp,
  headers: AuthHeaders,
): Promise<true | Error> => {
  const {
    gatewayKey,
    jwtKey,
  } = headers;

  // Validate gatewayKey
  if (!isEmpty(gatewayKey)) {
    return gatewayValidate(req, app, gatewayKey);
  }

  // Validate jwtKey
  if (!isEmpty(jwtKey)) {
    return jwtValidate(req, jwtKey);
  }

  return new Error('No authentication keys provided to authenticate');
};

export default authentication;
