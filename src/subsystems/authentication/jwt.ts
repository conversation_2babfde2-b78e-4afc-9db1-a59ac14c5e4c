import CSRF from 'csrf';
import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import Joi from 'joi';
import {
  get,
  set,
  result,
  isEmpty,
  isError,
} from 'lodash';
import { queryPersons } from '../ldap';
import { SecureJWT, User } from '../../types';

let tokens: CSRF;
const secrets = {
  csrf: '',
  jwt: '',
};

const initAuthSecrets = () => {
  tokens = new CSRF();
  const csrfSecret = tokens.secretSync();
  const jwtSecret = tokens.secretSync();
  secrets.csrf = csrfSecret;
  secrets.jwt = jwtSecret;
};

const addSessionTokensToReq = (req: Request, _: Response, next: NextFunction) => {
  set(req, 'session.csrf.secret', secrets.csrf);
  set(req, 'session.jwt.secret', secrets.jwt);
  next();
};

const getJwt = (req: Request, id: string) => {
  const jwtSecret = get(req, 'session.jwt.secret');
  const csrfSecret = get(req, 'session.csrf.secret');
  if (!jwtSecret || !csrfSecret) {
    return new Error('Unable to retrieve secrets');
  }

  const idSchema = Joi.string().alphanum().min(4).max(4);
  const { error } = idSchema.validate(id);
  if (error) {
    return error;
  }

  const jwtSigned = jwt.sign({
    csrf: tokens.create(csrfSecret),
    id,
  }, jwtSecret, {
    subject: get(req, 'sessionID'),
    audience: 'session',
  });

  return jwtSigned;
};

const setJwt = (res: Response, token: string) => {
  res.set('X-Set-JWT', token);
};

const validate = async (req: Request, token: string): Promise<true | Error> => {
  const app = get(req, 'systemApp');
  if (!app) {
    return new Error('Unable to retrieve system application');
  }

  const jwtSecret = result(req, 'session.jwt.secret', '');
  let jwtToken: string | SecureJWT;

  try {
    jwtToken = jwt.verify(token, jwtSecret, {
      subject: get(req, 'sessionID'),
      audience: 'session',
    }) as SecureJWT;
  } catch (error) {
    const destroyError = new Error();
    req.session.destroy((err) => {
      if (err) {
        destroyError.message = err.message;
      }
    });

    if (!isEmpty(destroyError.message)) {
      return destroyError;
    }
    app.logger.error({ error });
    return new Error('Web token could not be verified');
  }

  if (!tokens.verify(result(req, 'session.csrf.secret', ''), jwtToken.csrf)) {
    return new Error('The csrf security token is invalid');
  }

  const filter = `(uid=${jwtToken.id})`;
  const attributes = ['cn', 'mail', 'givenName', 'sn', 'telephoneNumber', 'uid', 'ismemberof'];
  const user = await queryPersons(app, filter, attributes);
  if (isError(user)) {
    return user;
  }

  const foundUser: User | undefined = user.find((item) => jwtToken.id === item.uid);
  if (!foundUser) {
    return new Error('Unable to retrieve user data');
  }

  req.authMethod = 'jwt';
  req.user = foundUser;
  return true;
};

export {
  initAuthSecrets,
  addSessionTokensToReq,
  getJwt,
  setJwt,
  validate,
};
