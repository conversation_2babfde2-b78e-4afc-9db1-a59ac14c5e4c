import {
  Request,
  Response,
  NextFunction,
} from 'express';
import { get, isError } from 'lodash';
import authentication from '../subsystems/authentication';
import { checkIfRouteIsPublic } from '../utils/resources';

const checkIfAuthorized = async (req: Request, res: Response, next: NextFunction) => {
  const reqPath = get(req, 'path', '');
  const reqMethod = get(req, 'method', '').toLowerCase();
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const isPublic = checkIfRouteIsPublic(app, reqPath, reqMethod);
  if (isPublic) {
    return next();
  }

  // x-Gateway-APIKey
  const gatewayKey = req.get('x-Gateway-APIKey') || '';

  // x-jwt-key
  const jwtKey = req.get('x-jwt-key') || '';

  if ((!gatewayKey && !jwtKey) || (gatewayKey && jwtKey)) {
    return res.status(401).send({ error: 'Unauthorized access - keys' });
  }

  const validAuth: true | Error = await authentication(req, app, {
    gatewayKey,
    jwtKey,
  });

  if (isError(validAuth)) {
    app.logger.error({ error: validAuth });
    return res.status(401).send({ error: 'Unauthorized access - auth' });
  }

  return next();
};

export default checkIfAuthorized;
