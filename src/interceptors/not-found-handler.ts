import {
  RequestHandler,
  Request,
  Response,
  NextFunction,
} from 'express';
import { get } from 'lodash';
import { CMSApp } from '../types';

const formatDate = (date: Date) => date.toLocaleDateString('en-US', {
  month: 'short',
  day: 'numeric',
  year: 'numeric',
});

const formatTime = (date: Date) => date.toLocaleTimeString('en-US', {
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

/**
 * Convert the path to a service name used in the original webmethods 404 responses.
 */
const getWebmethodsServiceName = (v: string): string => {
  if (v.startsWith('/gateway/LDAP')) {
    return 'LDAP';
  }
  if (v.startsWith('/gateway/CEDAR%20Core%20API')) {
    return 'CEDAR Core API';
  }
  if (v.startsWith('/gateway/CEDARIntake')) {
    return 'CEDARIntake';
  }
  if (v.startsWith('/gateway/System%20Census%20Core%20API')) {
    return 'System Census Core API';
  }
  if (v.startsWith('/gateway')) {
    return 'null';
  }

  // /health and /sparx return HTML pages in Webmethods.

  return 'Unknown Service';
};

// noinspection FunctionWithMultipleLoopsJS
const notFoundHandler: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  if (res.headersSent) {
    return next();
  }

  const app: CMSApp | undefined = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'An error occurred retrieving the system application' });
  }

  const path: string | undefined = get(req, 'path');
  if (!path) {
    return res.status(500).send({ error: 'An error occurred retrieving the route path' });
  }

  const method: string | undefined = get(req, 'method');
  if (!method) {
    return res.status(500).send({ error: 'An error occurred retrieving the route method' });
  }

  let isRegisteredRoute: boolean = false;
  app.resources.registry.forEach((item) => {
    const itemPath = item.path;
    const itemPathParts = itemPath.split('/');
    const itemMethod = item.method.toLowerCase();
    const pathParts = path.split('/');

    if (method.toLowerCase() === itemMethod && itemPathParts.length === pathParts.length) {
      let isSamePath: boolean = true;
      itemPathParts.forEach((value, idx) => {
        if (!isSamePath) {
          return;
        }
        if (value.indexOf(':') === -1 && value !== pathParts[idx]) {
          isSamePath = false;
        }
      });

      if (isSamePath) {
        isRegisteredRoute = true;
      }
    }
  });

  if (isRegisteredRoute) {
    return next();
  }

  const now = new Date();
  const clientIP = req.ip ?? req.socket.remoteAddress ?? 'unknown';
  const url = req.originalUrl || req.url;
  let requestedPath = req.originalUrl || req.url;
  const serviceName = getWebmethodsServiceName(requestedPath);
  const timeStr = formatTime(now);
  const dateStr = formatDate(now);

  const pathFilter = [
    '/gateway/LDAP/1.0',
    '/gateway/CEDAR%20Core%20API/2.0.0',
    '/gateway/System%20Census%20Core%20API/2.0.0',
    '/gateway/CEDARIntake/1.0',
    '/gateway',
    '/health',
    '/sparx',
  ];

  const hasGatewayPrefix = requestedPath.indexOf('/gateway') !== -1;
  const knownServicePaths = [
    '/gateway/LDAP/1.0',
    '/gateway/CEDAR%20Core%20API/2.0.0',
    '/gateway/System%20Census%20Core%20API/2.0.0',
    '/gateway/CEDARIntake/1.0',
  ];
  const hasKnownService = knownServicePaths.some((p) => requestedPath.indexOf(p) !== -1);

  const isGateway = hasGatewayPrefix && !hasKnownService;

  pathFilter.forEach((p) => {
    if (requestedPath.indexOf(p) === -1) {
      return;
    }
    requestedPath = requestedPath.replace(p, '/');
  });

  requestedPath = requestedPath.replace(/^\/+(.*)$/, '/$1');

  const errorMessage = isGateway
    ? `Error Message: Service not found: ${url}. `
    : `Error Message: Resource ${requestedPath} not found. `;

  const output = [
    'API Gateway encountered an error. ',
    errorMessage,
    `Request Details: Service - ${serviceName}, Operation - null, `,
    `Invocation Time:${timeStr}, Date:${dateStr},  `,
    `Client IP - ${clientIP}, User - Default and Application:null`,
  ];

  return res.status(404).send({ Exception: output.join('') });
};

export {
  formatDate,
  formatTime,
};

export default notFoundHandler;
