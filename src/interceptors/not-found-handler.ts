import {
  RequestHandler,
  Request,
  Response,
  NextFunction,
} from 'express';
import { get } from 'lodash';
import { CMSApp } from '../types';

const notFoundHandler: RequestHandler = (req: Request, res: Response, next: NextFunction) => {
  if (res.headersSent) {
    return next();
  }

  const app: CMSApp | undefined = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'An error occurred retrieving the system application' });
  }

  const path: string | undefined = get(req, 'path');
  if (!path) {
    return res.status(500).send({ error: 'An error occurred retrieving the route path' });
  }

  const method: string | undefined = get(req, 'method');
  if (!method) {
    return res.status(500).send({ error: 'An error occurred retrieving the route method' });
  }

  let isRegisteredRoute: boolean = false;
  app.resources.registry.forEach((item) => {
    const itemPath = item.path;
    const itemPathParts = itemPath.split('/');
    const itemMethod = item.method.toLowerCase();
    const pathParts = path.split('/');

    if (method.toLowerCase() === itemMethod && itemPathParts.length === pathParts.length) {
      let isSamePath: boolean = true;
      itemPathParts.forEach((value, idx) => {
        if (!isSamePath) {
          return;
        }
        if (value.indexOf(':') === -1 && value !== pathParts[idx]) {
          isSamePath = false;
        }
      });

      if (isSamePath) {
        isRegisteredRoute = true;
      }
    }
  });

  if (isRegisteredRoute) {
    return next();
  }

  return res.status(404).send({ error: 'Not Found' });
};

export default notFoundHandler;
