import dayjs, { Dayjs, ManipulateType } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import duration from 'dayjs/plugin/duration';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { isString, isNumber, isNaN } from 'lodash';

dayjs.extend(utc);
dayjs.extend(duration);
dayjs.extend(customParseFormat);

const defaultFormat = 'YYYY-MM-DDTHH:mm:ss.SSS[Z]';
const nowUtc = () => dayjs().utc();

const getFormatString = (format?: string) => {
  let dtFormat = defaultFormat;
  if (format && isString(format)) {
    dtFormat = format;
  }

  return dtFormat;
};

const nowUtcFormatted = (format?: string) => {
  const dtFormat = getFormatString(format);

  return nowUtc().format(dtFormat);
};

const isDayJs = (time: string | Dayjs | Date) => {
  if (dayjs.isDayjs(time)) {
    return true;
  }

  return false;
};

const formatDate = (date: string | Date | Dayjs, format?: string) => {
  const dtFormat = getFormatString(format);
  if (isDayJs(date)) {
    const typedDate = date as Dayjs;
    typedDate.format(dtFormat);
  }

  const dt = dayjs(date);
  if (!dt.isValid()) {
    return new Error('Invalid date');
  }

  return dt.format(dtFormat);
};

const formatDateOrNull = (date: string | Date | Dayjs, format?: string): string | null => {
  const dtFormat = getFormatString(format);
  if (isDayJs(date)) {
    const typedDate = date as Dayjs;
    typedDate.format(dtFormat);
  }

  const dt = dayjs(date);
  if (!dt.isValid()) {
    return null;
  }

  return dt.format(dtFormat);
};

const getDuration = (
  start: string | Date | Dayjs,
  end: string | Date | Dayjs | null = null,
  durationFormat: ManipulateType = 'milliseconds',
): number | Error => {
  let startInit = start;
  if (!isString(startInit) && dayjs.isDayjs(startInit)) {
    startInit = startInit.format(defaultFormat);
  }
  const startDt = dayjs(startInit, defaultFormat);
  if (!startDt.isValid()) {
    return new Error('Invalid start date');
  }

  let milliseconds: number;
  if (end) {
    let endInit = end;
    if (!isString(endInit) && dayjs.isDayjs(endInit)) {
      endInit = endInit.format(defaultFormat);
    }
    const endDt = dayjs(endInit, defaultFormat);
    if (!endDt.isValid()) {
      return new Error('Invalid end date');
    }

    milliseconds = startDt.diff(endDt, durationFormat);
  } else {
    const now = dayjs(nowUtcFormatted(), defaultFormat);
    milliseconds = startDt.diff(now, durationFormat);
  }

  if (isNaN(milliseconds)) {
    return new Error('Unable to get millisecond duration');
  }

  return milliseconds;
};

const addToUTC = (
  currentTime: string | Date,
  amount: number,
  time: ManipulateType,
  returnString: boolean = true,
  format?: string,
): string | Error | Dayjs => {
  const currentDt = dayjs(currentTime);
  if (!currentDt.isValid()) {
    return new Error('Invalid date');
  }

  const updatedTime = currentDt.utc().add(amount, time);
  if (returnString) {
    const dtFormat = getFormatString(format);
    return updatedTime.format(dtFormat);
  }

  return updatedTime;
};

const sleep = <T = void>(
  ms: number,
  func: (resolve: (value: T | Promise<T>) => void) => void = (resolve) => resolve(undefined as T),
): Promise<T> => (
    new Promise<T>((resolve) => {
      setTimeout(() => func(resolve), ms);
    })
  );

const timeFromNowUtc = (amount: number, unit: ManipulateType) => {
  if (!isNumber(amount) || !isString(unit)) {
    return new Error('Invalid amount or unit');
  }

  return nowUtc().add(amount, unit);
};

const isValidFormat = (date: string, format: string): boolean => (
  dayjs(date, format, true).isValid()
);

export {
  defaultFormat,
  nowUtc,
  getFormatString,
  nowUtcFormatted,
  isDayJs,
  formatDate,
  formatDateOrNull,
  getDuration,
  addToUTC,
  sleep,
  timeFromNowUtc,
  isValidFormat,
};
