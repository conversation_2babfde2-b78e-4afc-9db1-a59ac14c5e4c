import {
  get,
  isError,
  isString,
  isEmpty,
  unset,
} from 'lodash';
import Jo<PERSON> from 'joi';
import { SPARX_SYSTEM_DATAEXCHANGE } from '../db-helpers';
import MssqlData from '../../data-sources/mssql';
import { strToBoolOrNull, tryAsync } from '../general';
import { Where, OrObject, CMSApp } from '../../types';

// Define local types as required
export interface DbPageDataExchangeStatus {
  SYSTEM_SURVEY_EXCHANGE_STATUS_ID: number;
  EXCHANGE_ID: string;
  NEW_EXCHANGE_FLAG: boolean | null;
  ORIGINAL_RECEIVER_ID: string | null;
  ORIGINAL_SENDER_ID: string | null;
  RECEIVER_REPORTED_DELETE_FLAG: boolean | null;
  SENDER_REPORTED_DELETE_FLAG: boolean | null;
  RECEIVER_STATUS: string | null;
  RECEIVER_LAST_SUBMIT_DATETIME: string | null;
  SENDER_STATUS: string | null;
  SENDER_LAST_SUBMIT_DATETIME: string | null;
  RECEIVER_QA_STATUS: string | null;
  SENDER_QA_STATUS: string | null;
}

export interface PageDataExchangeStatus {
  exchangeId: string | null;
  systemId: string | null;
  systemStatus: string | null;
  partnerId: string | null;
  partnerStatus: string | null;
  reviewerStatus: string | null;
  direction: 'receiver' | 'sender' | null;
  // deleted is in Designer, but not in the actual Webmethods
  // deleted: boolean | null;
}

// Helper function to map DB results to API response format, mimicking Webmethods logic
export const convertDbToApiStatus = (
  dbStatus: DbPageDataExchangeStatus,
  requestSystemId: string,
): PageDataExchangeStatus => {
  const apiStatus: PageDataExchangeStatus = {
    exchangeId: dbStatus.EXCHANGE_ID,
    systemId: null,
    systemStatus: null,
    partnerId: null,
    partnerStatus: null,
    reviewerStatus: null,
    direction: null,
    // deleted is in Designer, but not in the actual Webmethods
    // deleted: null,
  };

  // Webmethods logic determines "direction" based on whether the input systemId
  // matches ORIGINAL_RECEIVER_ID or ORIGINAL_SENDER_ID from the DB record.
  if (dbStatus.ORIGINAL_RECEIVER_ID === requestSystemId) {
    // System in question is the receiver
    apiStatus.systemId = dbStatus.ORIGINAL_RECEIVER_ID;
    apiStatus.systemStatus = dbStatus.RECEIVER_STATUS;
    apiStatus.partnerId = dbStatus.ORIGINAL_SENDER_ID;
    apiStatus.partnerStatus = dbStatus.SENDER_STATUS;
    apiStatus.reviewerStatus = dbStatus.RECEIVER_QA_STATUS;
    apiStatus.direction = 'receiver';
    // deleted is in Designer, but not in the actual Webmethods
    // apiStatus.deleted = dbStatus.RECEIVER_REPORTED_DELETE_FLAG;
  } else if (dbStatus.ORIGINAL_SENDER_ID === requestSystemId) {
    // System in question is the sender
    apiStatus.systemId = dbStatus.ORIGINAL_SENDER_ID;
    apiStatus.systemStatus = dbStatus.SENDER_STATUS;
    apiStatus.partnerId = dbStatus.ORIGINAL_RECEIVER_ID;
    apiStatus.partnerStatus = dbStatus.RECEIVER_STATUS;
    apiStatus.reviewerStatus = dbStatus.SENDER_QA_STATUS;
    apiStatus.direction = 'sender';
    // deleted is in Designer, but not in the actual Webmethods
    // apiStatus.deleted = dbStatus.SENDER_REPORTED_DELETE_FLAG;
  }
  // If the requestSystemId doesn't match either, the original Webmethods flow
  // implies it wouldn't be returned by the specific JDBC adapters for receiver/sender,
  // or it would default to sender if found in the 'both' query but wasn't receiver.
  // For 'both' query, we check receiver first, then sender.
  return apiStatus;
};

const dataExchangeStatusFindUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  direction: 'receiver' | 'sender' | 'both',
) => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db)) {
    app.logger.error({ error: db });
    return new Error('Database Unavailable');
  }

  if (!systemId || !isString(systemId)) {
    const error = new Error('The systemId parameter is required and must be a string');
    app.logger.error({ error });
    return error;
  }

  // 'both' or any other value defaults to 'both'
  const whereClause: Where = {
    where: {
      operation: {
        column: 'ORIGINAL_RECEIVER_ID',
        operator: '=',
        value: systemId,
      },
    },
    or: [
      {
        operation: {
          column: 'ORIGINAL_SENDER_ID',
          operator: '=',
          value: systemId,
        },
      },
    ],
  };

  if (direction === 'receiver') {
    whereClause.where = {
      operation: {
        column: 'ORIGINAL_RECEIVER_ID',
        operator: '=',
        value: systemId,
      },
    };
    delete whereClause.or;
  }
  if (direction === 'sender') {
    whereClause.where = {
      operation: {
        column: 'ORIGINAL_SENDER_ID',
        operator: '=',
        value: systemId,
      },
    };
    delete whereClause.or;
  }

  const [queryError, queryResult] = await tryAsync(
    db.queryView(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_STATUS',
      [
        'SYSTEM_SURVEY_EXCHANGE_STATUS_ID',
        'EXCHANGE_ID',
        'NEW_EXCHANGE_FLAG',
        'ORIGINAL_RECEIVER_ID',
        'ORIGINAL_SENDER_ID',
        'RECEIVER_REPORTED_DELETE_FLAG',
        'SENDER_REPORTED_DELETE_FLAG',
        'RECEIVER_STATUS',
        'RECEIVER_LAST_SUBMIT_DATETIME',
        'SENDER_STATUS',
        'SENDER_LAST_SUBMIT_DATETIME',
        'RECEIVER_QA_STATUS',
        'SENDER_QA_STATUS',
      ],
      whereClause,
    ),
  );

  if (queryError || isError(queryResult)) {
    app.logger.error({ error: queryError || queryResult });
    return new Error('There was an error fetching the query');
  }

  if (!queryResult || !Array.isArray(queryResult)) {
    app.logger.error({ error: new Error('Unable to get result from query') });
    return new Error('Unable to get result from query');
  }

  const statusRaw: DbPageDataExchangeStatus[] = queryResult[0] as DbPageDataExchangeStatus[];
  const exchangeStatus: PageDataExchangeStatus[] = statusRaw.map(
    (dbItem) => convertDbToApiStatus(dbItem, systemId),
  );

  return {
    count: exchangeStatus.length,
    ExchangeStatus: exchangeStatus,
  };
};

const getDataExchangeListUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  direction: 'receiver' | 'sender' | 'both',
  version: string = '',
) => {
  const baseSchema = Joi.string();

  const allowedDirections = [
    'both',
    'receiver',
    'sender',
  ];

  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db)) {
    app.logger.error({ error: db });
    return new Error('Database Unavailable');
  }

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\' and \'direction\'');
    app.logger.error({ error });
    return error;
  }

  if (!isString(direction) || isEmpty(direction)) {
    const error = new Error('Please provide required parameters \'systemId\' and \'direction\'');
    app.logger.error({ error });
    return error;
  }

  if (!allowedDirections.includes(direction)) {
    const error = new Error('The provided direction is not valid.');
    app.logger.error({ error });
    return error;
  }

  // just console logging for now
  // eslint-disable-next-line no-console
  console.log({ version });

  const paramOrs: OrObject[] | null = [];

  const byIdWhere: Where = {
    where: {},
    or: [],
  };

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return new Error('The system ID is not valid');
  }

  if (direction === 'sender') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'receiver') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'both') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
    paramOrs.push({
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    });
    byIdWhere.or = paramOrs;
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Description" as exchangeDescription',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert to string array
    '"Exchange Frequency" as connectionFrequency',
    // Convert to boolean
    '"IE Agreement" as dataExchangeAgreement',
    // Convert to boolean
    '"Exchange includes Beneficiary Address Data" as containsBeneficiaryAddress',
    // Convert to string array
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert to boolean
    '"Address Data Editable" as isAddressEditable',
    // Convert to boolean
    '"Exchange Contains PII" as containsPii',
    // Convert to boolean
    '"Exchange Contains PHI" as containsPhi',
    // Convert to boolean
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert to boolean
    '"Exchange Includes Banking Data" as containsBankingData',
    // Convert to boolean
    '"Exchange Supports Mailing to Beneficiaries" as isBeneficiaryMailingFile',
    // Convert to boolean
    '"Data Shared via API" as sharedViaApi',
    '"API Ownership" as apiOwnership',
    // Convert to array of object
    '"Type of Data" as typeOfDataName',
    '"Type of Data ID" as typeOfDataId',
    '"Number of Records Exchanged" as numOfRecords',
    '"Exchange Format" as dataFormat',
    '"Exchange Format Other" dataFormatOther',
    // Convert to boolean
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert to string array
    '"Exchange CUI Type" as exchangeCUIType',
    // Convert to boolean
    '"Exchange Connection Authenticated" as exchangeConnectionAuthenticated',
    // Convert to string array
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], byIdWhere));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('Unable to retrieve exchange by system ID');
  }

  const exchangeResults = get(viewResult, '[0]', []).map((item: ExchangeListResult) => {
    const connectionFrequency = get(item, 'connectionFrequency', '') as string;
    const containsBeneficiaryAddress = get(item, 'containsBeneficiaryAddress', null);
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const isAddressEditable = get(item, 'isAddressEditable', null);
    const containsPii = get(item, 'containsPii', null);
    const containsPhi = get(item, 'containsPhi', null);
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null);
    const containsBankingData = get(item, 'containsBankingData', null);
    const sharedViaApi = get(item, 'sharedViaApi', null);
    const typeOfDataName = get(item, 'typeOfDataName', null) as string | null;
    const typeOfDataId = get(item, 'typeOfDataId', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null);
    const exchangeCUIType = get(item, 'exchangeCUIType', null) as string | null;
    const exchangeConnectionAuthenticated = get(item, 'exchangeConnectionAuthenticated', null);
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (!isEmpty(connectionFrequency)) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    const formattedTypeOfData: object[] = [];
    if ((!isEmpty(typeOfDataName)) && (!isEmpty(typeOfDataId))) {
      formattedTypeOfData.push({
        id: typeOfDataId,
        name: typeOfDataName,
      });
    }

    let formattedExchangeCUIType: string[] = [];
    if (exchangeCUIType) {
      formattedExchangeCUIType = exchangeCUIType.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    // deleting these as they're converted
    unset(item, 'typeOfDataId');
    unset(item, 'typeOfDataName');

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      containsBeneficiaryAddress: strToBoolOrNull(containsBeneficiaryAddress),
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      isAddressEditable: strToBoolOrNull(isAddressEditable),
      containsPii: strToBoolOrNull(containsPii),
      containsPhi: strToBoolOrNull(containsPhi),
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      containsBankingData: strToBoolOrNull(containsBankingData),
      sharedViaApi: strToBoolOrNull(sharedViaApi),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeCUIType: formattedExchangeCUIType,
      exchangeConnectionAuthenticated: strToBoolOrNull(exchangeConnectionAuthenticated),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return {
    count: exchangeResults.length,
    Exchanges: exchangeResults,
  };
};

const getDataExchangeByIdUtil = async (
  app: CMSApp,
  db: MssqlData,
  id: string,
) => {
  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));

  if (error) {
    app.logger.error({ error });
    return new Error('Invalid object id');
  }

  const where = {
    where: {
      operation: {
        column: 'Connection GUID',
        operator: '=',
        value: id,
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert string to string array after call
    '"Exchange Frequency" as connectionFrequency',
    // Convert string to string array after call
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert Yes/No to boolean after call
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert string to string array after call
    '"Type of Data" as typeOfData',
    // Convert Yes/No to boolean after call
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert string to string array after call
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('Unable to retrieve exchange by system ID');
  }

  const exchangeResults = get(viewResult, '[0]', []).map((item: object) => {
    const connectionFrequency = get(item, 'connectionFrequency', null) as string | null;
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null) as string | null;
    const typeOfData = get(item, 'typeOfData', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null) as string | null;
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (connectionFrequency) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    let formattedTypeOfData: string[] = [];
    if (typeOfData) {
      formattedTypeOfData = typeOfData.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return get(exchangeResults, '[0]');
};

export {
  dataExchangeStatusFindUtil,
  getDataExchangeListUtil,
  getDataExchangeByIdUtil,
};
