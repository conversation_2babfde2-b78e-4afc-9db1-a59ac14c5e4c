import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../data-sources/mssql';
import {
  CMSApp,
  AndObject,
  Where,
  ContractResult,
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
} from '../../types';
import {
  SPARX_SYSTEM_CONTRACT_FULL_TBL,
  SPARX_CONTRACT,
  SP_INSERT_CONTRACT_LIST,
} from '../db-helpers';
import {
  iterableToArray,
  tryAsync,
  boolToStr,
} from '../general';

export const contractListUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  query: Record<string, string>,
): Promise<ContractResult | Error > => {
  const baseSchema = Joi.string();
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db || isError(db)) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  let isKeywordSearch = false;
  let isSystemIdSearch = false;
  const paramAnd: AndObject[] = [];
  let sparxQuery: string = SPARX_SYSTEM_CONTRACT_FULL_TBL;

  const searchParams = new URLSearchParams(query);
  const keys = iterableToArray(searchParams.keys());
  if (keys.includes('systemId') && !isEmpty(systemId)) {
    const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
    if (systemIdVal.error) {
      app.logger.error({ error: systemIdVal.error });
      return new Error(systemIdVal.error.details[0].message);
    }
    isSystemIdSearch = true;
  }

  if (keys.includes('systemId') && systemId === '') {
    isSystemIdSearch = true;
  }

  // keyword
  const keyword = `%${get(query, 'keyword', null) as string | null}%`;
  if (keyword && keyword !== '%null%') {
    isKeywordSearch = true;
    // makes `isSystemIdSearch` false again if there's a keyword.
    isSystemIdSearch = false;
    sparxQuery = SPARX_CONTRACT;
    if (keyword === '%%' || keyword === null) {
      const error = new Error('The keyword is not valid.');
      app.logger.error({ error });
      return error;
    }
  }

  // POPStartDate
  const POPStartDate = get(query, 'POPStartDate', null) as string | null;
  if (POPStartDate) {
    paramAnd.push({
      operation: {
        column: 'POP Start Date',
        operator: '=',
        value: POPStartDate,
      },
    });
  }

  // POPEndDate
  const POPEndDate = get(query, 'POPEndDate', null) as string | null;
  if (POPEndDate) {
    paramAnd.push({
      operation: {
        column: 'POP End Date',
        operator: '=',
        value: POPEndDate,
      },
    });
  }

  // contractName
  const contractName = get(query, 'contractName', null) as string | null;
  if (contractName) {
    paramAnd.push({
      operation: {
        column: 'Contract Name',
        operator: '=',
        value: contractName,
      },
    });
  }

  let where: Where | undefined;

  if (isSystemIdSearch && !isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId || '',
        },
      },
      and: paramAnd,
    };
  }

  if (isKeywordSearch && !isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Contract Name',
          operator: 'LIKE',
          value: keyword,
        },
      },
      and: paramAnd,
    };
  }

  if (isSystemIdSearch && isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId,
        },
      },
    };
  }

  if (isKeywordSearch && isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Contract Name',
          operator: 'LIKE',
          value: keyword,
        },
      },
    };
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(sparxQuery, [
    '"Sparx Contract GUID" as id',
    '"Contract Number" as parentAwardId',
    '"Order Number" as awardId',
    '"Contract Amount" as Cost',
    '"Contract Name" as ContractName',
    '"POP Start Date" as POPStartDate',
    '"POP End Date" as POPEndDate',
    '"Order Number" as OrderNumber',
    '"Project Title" as ProjectTitle',
    '"Product Service Description" as ProductServiceDescription',
    '"Service Provided" as ServiceProvided',
    '"Contract Number" as ContractNumber',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    if (isKeywordSearch) {
      const error = new Error('Unable to retrieve the contract list with keyword.');
      app.logger.error({ error });
      return error;
    }
    if (isSystemIdSearch) {
      const error = new Error('Unable to retrieve the contract list with system id.');
      app.logger.error({ error });
      return error;
    }
  }
  const contracts = get(viewResult, '[0]', []);

  if (isKeywordSearch && isEmpty(paramAnd)) {
    return {
      Contracts: contracts,
    };
  }

  return {
    count: contracts.length,
    Contracts: contracts,
  };
};

// Interface for the stored procedure response
interface ContractAddResponse {
  NewObjects: {
    GUID: string;
  };
  Count?: number;
}

// Internal types for constructing the JSON payload for the stored procedure
interface SparxContractDeliverableValues {
  architectureelement?: string; // Maps to systemId
  contract?: string; // Maps to input.id (from ContractRequest)
  cms_application_delivery_org?: string; // Maps to input.contractADO ('Yes'/'No')
  name?: string; // Derived from input.id and input.systemId
  ContractNumber?: string;
  IsDeliveryOrg?: string;
  OrderNumber?: string;
  ProductServiceDescription?: string;
  ProjectTitle?: string;
  ServiceProvided?: string;
  parentAwardId?: string;
  awardId?: string;
  description?: string;
  POPStartDate?: string;
  POPEndDate?: string;
  contractName?: string;
}

interface SparxObject {
  ClassName: string;
  Id: string;
  Values: SparxContractDeliverableValues;
}

interface SparxRelation {
  FromId: string; // Temporary ID
  Property: string;
  ToRef: string; // Actual Sparx GUID (e.g., systemId, contractId)
}

interface SparxUpdateRequestPayload {
  CurrentProfile: string;
  Objects: SparxObject[];
  Relations: SparxRelation[];
}

// Define local types as required
// Based on CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/docTypes/Contract/node.ndf
export interface ContractRequest {
  id?: string; // This is the ID of an existing contract in Sparx that this deliverable relates to.
  // Not explicitly used in WM flow, assume new GUID generated by SP
  // contractDeliverableId?: string;
  ContractNumber?: string;
  IsDeliveryOrg?: string; // Expects "Yes" or "No" for boolean conversion if applicable
  OrderNumber?: string;
  ProductServiceDescription?: string;
  ProjectTitle?: string;
  ServiceProvided?: string;
  parentAwardId?: string;
  contractADO?: string; // Yes/No for 'cms_application_delivery_org'
  awardId?: string;
  description?: string;
  systemId?: string; // Architecture element ID
  POPStartDate?: string;
  POPEndDate?: string;
  contractName?: string;
}

export interface ContractAddResult {
  success: boolean;
  guid?: string;
  count?: number;
  error?: string;
}

/**
 * Executes SparX contract operation for adding contracts
 * @param app - The CMS application instance
 * @param db - The database instance
 * @param contractsToAdd - Array of contracts to add
 * @returns Promise<ContractAddResult | Error>
 */
export const contractAddUtil = async (
  app: CMSApp,
  db: MssqlData,
  contractsToAdd: ContractRequest[],
): Promise<ContractAddResult | Error> => {
  try {
    const updateRequest: SparxUpdateRequestPayload = {
      CurrentProfile: 'API User',
      Objects: [],
      Relations: [],
    };

    // Validate all contracts first before processing
    const invalidContract = contractsToAdd.find(
      (contract) => !contract.id || typeof contract.id !== 'string' || !contract.systemId || typeof contract.systemId !== 'string',
    );
    if (invalidContract) {
      const index = contractsToAdd.indexOf(invalidContract);
      if (!invalidContract.id || typeof invalidContract.id !== 'string') {
        return new Error(`Missing or invalid 'id' for contract at index ${index}.`);
      }
      return new Error(`Missing or invalid 'systemId' for contract at index ${index}.`);
    }

    // Process all contracts
    contractsToAdd.forEach((contract, index) => {
      // Use the index as a temporary ID for the object within the JSON payload.
      const currentIterationId = String(index);

      // Map ContractRequest fields to SparxContractDeliverableValues
      const deliverableValues: SparxContractDeliverableValues = {
        // ID of the existing contract in Sparx (validated above)
        contract: contract.id!,
        // ID of the system (architecture element) (validated above)
        architectureelement: contract.systemId!,
        // Convert 'Yes'/'No' to 'true'/'false' or handle as is
        cms_application_delivery_org: contract.contractADO
          ? boolToStr(contract.contractADO === 'Yes')
          : undefined,
        name: `${contract.id}|${contract.systemId}`, // Derived name
        ContractNumber: contract.ContractNumber,
        IsDeliveryOrg: contract.IsDeliveryOrg,
        OrderNumber: contract.OrderNumber,
        ProductServiceDescription: contract.ProductServiceDescription,
        ProjectTitle: contract.ProjectTitle,
        ServiceProvided: contract.ServiceProvided,
        parentAwardId: contract.parentAwardId,
        awardId: contract.awardId,
        description: contract.description,
        POPStartDate: contract.POPStartDate,
        POPEndDate: contract.POPEndDate,
        contractName: contract.contractName,
      };

      // Create SparxObject for the ContractDeliverable
      updateRequest.Objects.push({
        ClassName: 'ContractDeliverable',
        Id: currentIterationId,
        Values: deliverableValues,
      });

      // Create relations
      updateRequest.Relations.push(
        {
          FromId: currentIterationId,
          Property: 'architectureelement',
          ToRef: contract.systemId!,
        },
        {
          FromId: currentIterationId,
          Property: 'contract',
          ToRef: contract.id!,
        },
      );
    });

    const jsonInput = JSON.stringify(updateRequest);

    const queryParams: StoredProcedureParam[] = [{
      name: 'result',
      type: 'nvarchar',
      param: 'max',
    }];

    const queryData: StoredProcedureData[] = [{
      name: 'jsonInput',
      value: jsonInput,
    }, {
      name: 'jsonOutput',
      value: 'result',
      isOutput: true,
    }];

    const queryResults: StoredProcedureQuery[] = [{
      resultKey: 'result',
      paramName: 'result',
    }];

    const [insertError, insertResult] = await tryAsync(
      db.queryStoredProcedures(SP_INSERT_CONTRACT_LIST, queryParams, queryData, queryResults),
    );

    if (insertError || isError(insertResult)) {
      app.logger.error({
        error: insertError || insertResult,
        message: 'Database stored procedure call failed',
        payload: updateRequest,
      });
      return new Error(`Database insert failed: ${insertError?.message ?? 'Unknown DB error'}`);
    }

    const rows = insertResult as Array<Array<{ queryStatus: number; result: string }>>;
    const firstRow = rows?.[0][0] ?? {};
    const status = firstRow?.queryStatus ?? -1;

    if (status !== 0) {
      app.logger.error({ error: firstRow, message: 'Stored procedure returned non-success status' });
      return new Error('Stored procedure returned non-zero status');
    }

    const rowResult = firstRow?.result ?? '';
    if (!rowResult) {
      const error = new Error('The stored procedure did not yield a result');
      app.logger.error({ error });
      return error;
    }

    // Parse the JSON result directly
    let parsed: ContractAddResponse;
    try {
      parsed = JSON.parse(rowResult);
    } catch (parseError) {
      const error = new Error('Invalid Sparx results from stored procedure');
      app.logger.error({ error: parseError });
      return error;
    }

    if (!parsed || !parsed.NewObjects) {
      const error = new Error('Invalid Sparx results from stored procedure');
      app.logger.error({ error });
      return error;
    }

    // Extract GUIDs from the parsed result
    const newObjects = parsed.NewObjects;
    let newGuids: string = '';

    // Designer shows this endpoint should handle multiple requests,
    // but only replies with a single GUID
    if (newObjects?.GUID) {
      newGuids = newObjects.GUID;
    }

    return {
      success: true,
      guid: newGuids,
      count: contractsToAdd.length,
    };
  } catch (error) {
    app.logger.error({
      error: error as Error,
      message: 'Unexpected error in executeSparxContractOperation',
    });
    return error as Error;
  }
};
