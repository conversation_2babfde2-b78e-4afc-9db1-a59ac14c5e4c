import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../data-sources/mssql';
import {
  CMSApp,
  AndObject,
  Where,
  ContractResult,
} from '../../types';
import {
  SPARX_SYSTEM_CONTRACT_FULL_TBL,
  SPARX_CONTRACT,
} from '../db-helpers';
import {
  iterableToArray,
  tryAsync,
} from '../general';

const contractListUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
  query: Record<string, string>,
): Promise<ContractResult | Error > => {
  const baseSchema = Joi.string();
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db || isError(db)) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  let isKeywordSearch = false;
  let isSystemIdSearch = false;
  const paramAnd: AndObject[] = [];
  let sparxQuery: string = SPARX_SYSTEM_CONTRACT_FULL_TBL;

  const searchParams = new URLSearchParams(query);
  const keys = iterableToArray(searchParams.keys());
  if (keys.includes('systemId') && !isEmpty(systemId)) {
    const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
    if (systemIdVal.error) {
      app.logger.error({ error: systemIdVal.error });
      return new Error(systemIdVal.error.details[0].message);
    }
    isSystemIdSearch = true;
  }

  if (keys.includes('systemId') && systemId === '') {
    isSystemIdSearch = true;
  }

  // keyword
  const keyword = `%${get(query, 'keyword', null) as string | null}%`;
  if (keyword && keyword !== '%null%') {
    isKeywordSearch = true;
    // makes `isSystemIdSearch` false again if there's a keyword.
    isSystemIdSearch = false;
    sparxQuery = SPARX_CONTRACT;
    if (keyword === '%%' || keyword === null) {
      const error = new Error('The keyword is not valid.');
      app.logger.error({ error });
      return error;
    }
  }

  // POPStartDate
  const POPStartDate = get(query, 'POPStartDate', null) as string | null;
  if (POPStartDate) {
    paramAnd.push({
      operation: {
        column: 'POP Start Date',
        operator: '=',
        value: POPStartDate,
      },
    });
  }

  // POPEndDate
  const POPEndDate = get(query, 'POPEndDate', null) as string | null;
  if (POPEndDate) {
    paramAnd.push({
      operation: {
        column: 'POP End Date',
        operator: '=',
        value: POPEndDate,
      },
    });
  }

  // contractName
  const contractName = get(query, 'contractName', null) as string | null;
  if (contractName) {
    paramAnd.push({
      operation: {
        column: 'Contract Name',
        operator: '=',
        value: contractName,
      },
    });
  }

  let where: Where | undefined;

  if (isSystemIdSearch && !isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId || '',
        },
      },
      and: paramAnd,
    };
  }

  if (isKeywordSearch && !isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Contract Name',
          operator: 'LIKE',
          value: keyword,
        },
      },
      and: paramAnd,
    };
  }

  if (isSystemIdSearch && isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId,
        },
      },
    };
  }

  if (isKeywordSearch && isEmpty(paramAnd)) {
    where = {
      where: {
        operation: {
          column: 'Contract Name',
          operator: 'LIKE',
          value: keyword,
        },
      },
    };
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(sparxQuery, [
    '"Sparx Contract GUID" as id',
    '"Contract Number" as parentAwardId',
    '"Order Number" as awardId',
    '"Contract Amount" as Cost',
    '"Contract Name" as ContractName',
    '"POP Start Date" as POPStartDate',
    '"POP End Date" as POPEndDate',
    '"Order Number" as OrderNumber',
    '"Project Title" as ProjectTitle',
    '"Product Service Description" as ProductServiceDescription',
    '"Service Provided" as ServiceProvided',
    '"Contract Number" as ContractNumber',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    if (isKeywordSearch) {
      const error = new Error('Unable to retrieve the contract list with keyword.');
      app.logger.error({ error });
      return error;
    }
    if (isSystemIdSearch) {
      const error = new Error('Unable to retrieve the contract list with system id.');
      app.logger.error({ error });
      return error;
    }
  }
  const contracts = get(viewResult, '[0]', []);

  if (isKeywordSearch && isEmpty(paramAnd)) {
    return {
      Contracts: contracts,
    };
  }

  return {
    count: contracts.length,
    Contracts: contracts,
  };
};

export default contractListUtil;
