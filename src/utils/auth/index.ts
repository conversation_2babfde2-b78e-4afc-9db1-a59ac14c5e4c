import { ClientOptions } from 'ldapts';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import { getSecret } from '../../subsystems/secrets';
import { CMSApp } from '../../types';

let ldapServer: string | undefined;
let ldapPort: number | string | undefined;
let ldapProtocol: string | undefined;
let ldapAdmin: string | undefined;
let ldapAdminPass: string | undefined;
const ldapBase: string = 'dc=cms,dc=hhs,dc=gov';

const initLdap = async (app: CMSApp) => {
  const ldapSecretString = get(app, 'config.systems.auth.secretString');
  if (!isString(ldapSecretString) || isEmpty(ldapSecretString)) {
    return new Error('Unable to retrieve LDAP Secret configuration');
  }

  const secrets = await getSecret(app, ldapSecretString);
  if (isError(secrets)) {
    return secrets;
  }

  const secretLdapServer = get(secrets, 'ldap_url');
  const secretLdapAdmin = get(secrets, 'ldap_user');
  const secretLdapAdminPass = get(secrets, 'ldap_pass');

  if (!secretLdapServer || !secretLdapAdmin || !secretLdapAdminPass) {
    return new Error('Invalid credentials');
  }

  ldapServer = secretLdapServer;
  ldapAdmin = secretLdapAdmin;
  ldapAdminPass = secretLdapAdminPass;
  ldapPort = get(secrets, 'ldap_port', 11636);
  ldapProtocol = get(secrets, 'ldap_protocol', 'ldaps');

  return true;
};

const getCredentials = () => {
  const ldapUrl: string = `${ldapProtocol}://${ldapServer}:${ldapPort}`;

  const clientOptions: ClientOptions = {
    url: ldapUrl,
    tlsOptions: {
      rejectUnauthorized: false,
    },
  };

  return {
    ldapAdmin,
    ldapAdminPass,
    clientOptions,
  };
};

export {
  ldapBase,
  getCredentials,
  initLdap,
};
