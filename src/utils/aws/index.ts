import {
  SecretsManagerClient,
  DescribeSecretCommand,
  GetSecretValueCommand,
  BatchGetSecretValueCommand,
} from '@aws-sdk/client-secrets-manager';
import {
  get,
  isString,
  isEmpty,
  forEach,
  isArray,
  isError,
  attempt,
} from 'lodash';
import { tryAsync } from '../general';
import { CMSApp, SecretItems, SecretStoreItem } from '../../types';

const getAccessKeys = () => {
  const accessKeyId = get(process, 'env.AWS_ACCESS_KEY_ID');
  const secretAccessKey = get(process, 'env.AWS_SECRET_ACCESS_KEY');
  const sessionToken = get(process, 'env.AWS_SESSION_TOKEN');

  if (!accessKeyId || !secretAccessKey || !sessionToken) {
    return new Error('Unable to find all required AWS credentials');
  }

  return {
    accessKeyId,
    secretAccessKey,
    sessionToken,
  };
};

const getSecret = async (app: CMSApp, secretPath: string): Promise<SecretItems | Error> => {
  const accessKeys = getAccessKeys();
  if (isError(accessKeys)) {
    return accessKeys;
  }

  const {
    accessKeyId,
    secretAccessKey,
    sessionToken,
  } = accessKeys;

  // describe secret
  const client = new SecretsManagerClient({
    region: 'us-east-1',
    credentials: {
      accessKeyId,
      secretAccessKey,
      sessionToken,
    },
  });

  if (!isString(secretPath) || isEmpty(secretPath)) {
    return new Error('The secret should be a string and not empty');
  }

  const describeInput = {
    SecretId: secretPath,
  };
  const describeCommand = new DescribeSecretCommand(describeInput);
  const [describeError, describeResponse] = await tryAsync(client.send(describeCommand));
  if (describeError || isError(describeResponse)) {
    app.logger.error({ error: describeError || describeResponse });
    return new Error('Unable to retrieve secret details');
  }
  const versions = get(describeResponse, 'VersionIdsToStages', {});
  if (isEmpty(versions)) {
    return new Error('Unable to retrieve secret versions');
  }

  const versionStage = 'AWSCURRENT';
  let latestVersionId = '';
  forEach(versions, (value: string[], key: string) => {
    if (isArray(value) && value.includes(versionStage)) {
      latestVersionId = key;
    }
  });
  if (isEmpty(latestVersionId)) {
    return new Error('Unable to find current AWS version');
  }

  const input = {
    SecretId: secretPath,
    VersionId: latestVersionId,
    VersionStage: versionStage,
  };
  const secretCommand = new GetSecretValueCommand(input);
  const secretResponse = await client.send(secretCommand);

  // get secret values
  const secrets = get(secretResponse, 'SecretString', '');
  if (!isString(secrets) || isEmpty(secrets)) {
    return new Error('Unable to retrieve secret string');
  }

  const parsedSecrets = attempt(JSON.parse.bind(null, secrets)) as SecretItems;
  if (isError(parsedSecrets)) {
    app.logger.error({ error: parsedSecrets });
    return new Error('Unable to parse secret string');
  }

  return parsedSecrets;
};

const getBulkSecrets = async (app: CMSApp, secrets: string[]) => {
  if (isEmpty(secrets)) {
    return new Error('No secrets provided');
  }

  const accessKeys = getAccessKeys();
  if (isError(accessKeys)) {
    return accessKeys;
  }

  const {
    accessKeyId,
    secretAccessKey,
    sessionToken,
  } = accessKeys;

  const client = new SecretsManagerClient({
    region: 'us-east-1',
    credentials: {
      accessKeyId,
      secretAccessKey,
      sessionToken,
    },
  });

  const command = new BatchGetSecretValueCommand({
    SecretIdList: secrets,
  });

  const response = await client.send(command);
  const errors = get(response, 'Errors', []);
  if (!isEmpty(errors)) {
    app.logger.error({ errors });
    return new Error('An error occurred while retrieving the secrets');
  }

  const secretResponse = get(response, 'SecretValues', []);
  const parsedSecrets: SecretStoreItem[] = [];
  const parsedSecretError = new Error('');
  secretResponse.forEach((item) => {
    const rawSecret = get(item, 'SecretString', '');
    const secretString = get(item, 'Name', '');
    if (!secretString) {
      app.logger.error({ error: new Error('Unable to retrieve secret name') });
      parsedSecretError.message = 'Unable to retrieve secret from AWS';
      return;
    }

    const parsedSecret = attempt(JSON.parse.bind(null, rawSecret)) as SecretItems;
    if (isError(parsedSecret)) {
      app.logger.error({ error: parsedSecret });
      parsedSecretError.message = 'Unable to retrieve secret from AWS';
      return;
    }

    parsedSecrets.push({
      secretString,
      secrets: parsedSecret,
    });
  });

  if (!isEmpty(parsedSecretError.message)) {
    return parsedSecretError;
  }

  return parsedSecrets;
};

export {
  getAccessKeys,
  getSecret,
  getBulkSecrets,
};
