import { isError } from 'lodash';
import { CMSApp, XmlParamsObject } from '../../types';
import { getToken, getPackageParentId } from '../../subsystems/sparxea';

const createPostXMLPerson = async (app: CMSApp, paramsObject: XmlParamsObject) => {
  if (!app) {
    return new Error('Unable to retrieve system application');
  }

  const token = await getToken(app);
  if (isError(token)) {
    app.logger.error({ error: token });
    return token;
  }

  const parentResourceIdentifier = await getPackageParentId('Person');
  if (isError(parentResourceIdentifier)) {
    app.logger.error({ error: parentResourceIdentifier });
    return parentResourceIdentifier;
  }

  const {
    title = '',
    type = '',
    resourceType = '',
    stereotype = '',
  } = paramsObject;
  return (`<?xml version="1.0"?><rdf:RDF xmlns:oslc_am="http://open-services.net/ns/am#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"><oslc_am:Resource><dcterms:title>${title}</dcterms:title><dcterms:type>${type}</dcterms:type><ss:resourcetype>${resourceType}</ss:resourcetype><ss:parentresourceidentifier>${parentResourceIdentifier !== '' ? `pk_${parentResourceIdentifier}` : ''}</ss:parentresourceidentifier><ss:useridentifier>${token}</ss:useridentifier><ss:stereotype><ss:stereotypename><ss:name>${stereotype}</ss:name></ss:stereotypename></ss:stereotype></oslc_am:Resource></rdf:RDF>`);
};

export {
  // eslint-disable-next-line import/prefer-default-export
  createPostXMLPerson,
};
