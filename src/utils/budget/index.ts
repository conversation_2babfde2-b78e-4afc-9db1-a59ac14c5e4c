import {
  get,
  isError,
  isEmpty,
  isString,
} from 'lodash';
import MssqlData from '../../data-sources/mssql';
import {
  Where,
  OrObject,
  BudgetFindQueryData,
  CMSApp,
  FoundGlobalVariableBody,
  BudgetResult,
} from '../../types';
import {
  SPARX_SYSTEM_BUDGET_PROJECT,
  SPARX_BUDGET_PROJECT,
} from '../db-helpers';
import {
  findGlobalVariable,
} from '../../subsystems/global-variables/util';
import {
  strToBool,
  tryAsync,
} from '../general';

const getData = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  whereObj?: Where,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(
    SPARX_SYSTEM_BUDGET_PROJECT,
    [
      '"Sparx BudgetProject GUID" as id',
      '"OFM Project ID" as projectId',
      '"Sparx System GUID" as systemId',
      '"Budget ProjectName" as projectTitle',
      '"Funding" as funding',
      '"Connection GUID" as fundingId',
    ],
    whereObj,
  ));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an issue fetching information from the database');
  }

  const budgets = get(viewResult, '[0]', []).map((item: object) => ({ ...item, FiscalYear: yearGlobal }));

  return {
    count: budgets.length,
    Budgets: budgets,
  };
};

const findData = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  queryData: BudgetFindQueryData,
): Promise<Error | BudgetResult> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const isProjectId = (!isEmpty(queryData.projectId));
  const isProjectTitle = (!isEmpty(queryData.projectTitle));
  const isSystemId = (!isEmpty(queryData.systemId));

  // Order goes projectId, projectTitle, then systemId.
  if (isProjectId && !isProjectTitle && !isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && isProjectTitle && !isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Budget ProjectName',
            operator: 'LIKE',
            value: queryData.projectTitle,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && !isProjectTitle && isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Sparx System GUID',
            operator: 'LIKE',
            value: queryData.systemId,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && isProjectTitle && isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Budget ProjectName',
            operator: 'LIKE',
            value: queryData.projectTitle,
          },
        },
        {
          operation: {
            column: 'Sparx System GUID',
            operator: 'LIKE',
            value: queryData.systemId,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget ProjectName',
          operator: '=',
          value: queryData.projectTitle,
        },
      },
    };

    if (isSystemId) {
      whereObj.or = [];
      const sId = {
        operation: {
          column: 'Sparx System GUID',
          operator: 'LIKE',
          value: queryData.systemId,
        },
      };
      whereObj.or.push(sId as OrObject);
    }
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: queryData.systemId,
        },
      },
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  return getData(app, db, yearGlobal);
};

const getDataIdsOnly = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  getDataWhere: Where,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_BUDGET_PROJECT, [
    '"Sparx Budget Project GUID" as id',
    '"Project ID" as projectId',
    '"Budget Project Name" as projectTitle',
  ], getDataWhere));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an issue fetching information from the database');
  }
  const budgets = get(viewResult, '[0]', []).map((item: object) => ({ ...item, FiscalYear: yearGlobal }));

  return {
    count: `${budgets.length}`,
    Budgets: budgets,
  };
};

const findDataIdsOnly = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  queryData: BudgetFindQueryData,
): Promise<Error | BudgetResult> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const queryProjectId = queryData.projectId;
  const queryProjectTitle = queryData.projectTitle;

  if (!queryProjectId && queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget Project Name',
          operator: 'LIKE',
          value: queryProjectTitle,
        },
      },
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  if (queryProjectId && !queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Project ID',
          operator: 'LIKE',
          value: queryProjectId,
        },
      },
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  if (queryProjectId && queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget Project Name',
          operator: 'LIKE',
          value: queryProjectTitle,
        },
      },
      or: [
        {
          operation: {
            column: 'Project ID',
            operator: 'LIKE',
            value: queryProjectId,
          },
        },
      ],
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  return {
    count: 0,
  };
};

const budgetFindUtil = async (
  app: CMSApp,
  db: MssqlData,
  query: Record<string, string>,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const queryData: BudgetFindQueryData = {
    systemId: '',
    projectTitle: '',
    projectId: '',
  };

  const yearGlobalVariableData: FoundGlobalVariableBody = await findGlobalVariable(app, 'cedar.api.budget.year') as FoundGlobalVariableBody;
  if (isError(yearGlobalVariableData)) {
    app.logger.error({ error: yearGlobalVariableData });
    return yearGlobalVariableData;
  }

  const yearGlobal = yearGlobalVariableData.globalValue;

  // projectId
  const projectId = get(query, 'projectId') as string;
  if (isString(projectId) && isEmpty(projectId)) {
    const error = new Error('The project ID is not valid');
    app.logger.error({ error });
    return error;
  }
  if (projectId) {
    queryData.projectId = projectId;
  }

  // Project Title
  const projectTitle = get(query, 'projectTitle') as string;
  if (isString(projectTitle) && isEmpty(projectTitle)) {
    const error = new Error('The project title is not valid');
    app.logger.error({ error });
    return error;
  }
  if (projectTitle) {
    queryData.projectTitle = projectTitle;
  }

  // systemId
  const systemId = get(query, 'systemId') as string;
  if (isString(systemId) && isEmpty(systemId)) {
    const error = new Error('The system ID is not valid');
    app.logger.error({ error });
    return error;
  }
  if (systemId) {
    queryData.systemId = systemId;
  }

  // onlyIds / idsOnly
  const onlyIdsParamVal = get(query, 'onlyIds', '') as string;
  const onlyIds = strToBool(onlyIdsParamVal);

  const idsOnlyParamVal = get(query, 'idsOnly', '') as string;
  const idsOnly = strToBool(idsOnlyParamVal);

  if (onlyIds || idsOnly) {
    const onlyIdsResult = await findDataIdsOnly(app, db, yearGlobal, queryData);
    if (isError(onlyIdsResult)) {
      app.logger.error({ error: onlyIdsResult });
      return new Error('There was an issue fetching information from the database');
    }
    return onlyIdsResult;
  }

  const dataResult = await findData(app, db, yearGlobal, queryData);
  if (isError(dataResult)) {
    app.logger.error({ error: dataResult });
    return new Error('There was an issue fetching information from the database');
  }

  return dataResult;
};

export {
  getData,
  budgetFindUtil,
  findData,
  getDataIdsOnly,
  findDataIdsOnly,
};
