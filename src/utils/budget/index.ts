import {
  get,
  isError,
  isEmpty,
  isString,
} from 'lodash';
import MssqlData from '../../data-sources/mssql';
import {
  Where,
  OrObject,
  BudgetFindQueryData,
  CMSApp,
  FoundGlobalVariableBody,
  BudgetResult,
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
} from '../../types';
import {
  SPARX_SYSTEM_BUDGET_PROJECT,
  SPARX_BUDGET_PROJECT,
  SP_INSERT_SYSTEMBUDGET_JSON,
} from '../db-helpers';
import {
  findGlobalVariable,
} from '../../subsystems/global-variables/util';
import {
  strToBool,
  tryAsync,
} from '../general';

const getData = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  whereObj?: Where,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(
    SPARX_SYSTEM_BUDGET_PROJECT,
    [
      '"Sparx BudgetProject GUID" as id',
      '"OFM Project ID" as projectId',
      '"Sparx System GUID" as systemId',
      '"Budget ProjectName" as projectTitle',
      '"Funding" as funding',
      '"Connection GUID" as fundingId',
    ],
    whereObj,
  ));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an issue fetching information from the database');
  }

  const budgets = get(viewResult, '[0]', []).map((item: object) => ({ ...item, FiscalYear: yearGlobal }));

  return {
    count: budgets.length,
    Budgets: budgets,
  };
};

const findData = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  queryData: BudgetFindQueryData,
): Promise<Error | BudgetResult> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const isProjectId = (!isEmpty(queryData.projectId));
  const isProjectTitle = (!isEmpty(queryData.projectTitle));
  const isSystemId = (!isEmpty(queryData.systemId));

  // Order goes projectId, projectTitle, then systemId.
  if (isProjectId && !isProjectTitle && !isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && isProjectTitle && !isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Budget ProjectName',
            operator: 'LIKE',
            value: queryData.projectTitle,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && !isProjectTitle && isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Sparx System GUID',
            operator: 'LIKE',
            value: queryData.systemId,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectId && isProjectTitle && isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: queryData.projectId,
        },
      },
      or: [
        {
          operation: {
            column: 'Budget ProjectName',
            operator: 'LIKE',
            value: queryData.projectTitle,
          },
        },
        {
          operation: {
            column: 'Sparx System GUID',
            operator: 'LIKE',
            value: queryData.systemId,
          },
        },
      ],
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget ProjectName',
          operator: '=',
          value: queryData.projectTitle,
        },
      },
    };

    if (isSystemId) {
      whereObj.or = [];
      const sId = {
        operation: {
          column: 'Sparx System GUID',
          operator: 'LIKE',
          value: queryData.systemId,
        },
      };
      whereObj.or.push(sId as OrObject);
    }
    return getData(app, db, yearGlobal, whereObj);
  }

  if (isSystemId) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: queryData.systemId,
        },
      },
    };
    return getData(app, db, yearGlobal, whereObj);
  }

  return getData(app, db, yearGlobal);
};

const getDataIdsOnly = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  getDataWhere: Where,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_BUDGET_PROJECT, [
    '"Sparx Budget Project GUID" as id',
    '"Project ID" as projectId',
    '"Budget Project Name" as projectTitle',
  ], getDataWhere));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an issue fetching information from the database');
  }
  const budgets = get(viewResult, '[0]', []).map((item: object) => ({ ...item, FiscalYear: yearGlobal }));

  return {
    count: `${budgets.length}`,
    Budgets: budgets,
  };
};

const findDataIdsOnly = async (
  app: CMSApp,
  db: MssqlData,
  yearGlobal: string,
  queryData: BudgetFindQueryData,
): Promise<Error | BudgetResult> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const queryProjectId = queryData.projectId;
  const queryProjectTitle = queryData.projectTitle;

  if (!queryProjectId && queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget Project Name',
          operator: 'LIKE',
          value: queryProjectTitle,
        },
      },
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  if (queryProjectId && !queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Project ID',
          operator: 'LIKE',
          value: queryProjectId,
        },
      },
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  if (queryProjectId && queryProjectTitle) {
    const whereObj: Where = {
      where: {
        operation: {
          column: 'Budget Project Name',
          operator: 'LIKE',
          value: queryProjectTitle,
        },
      },
      or: [
        {
          operation: {
            column: 'Project ID',
            operator: 'LIKE',
            value: queryProjectId,
          },
        },
      ],
    };
    return getDataIdsOnly(app, db, yearGlobal, whereObj);
  }

  return {
    count: 0,
  };
};

const budgetFindUtil = async (
  app: CMSApp,
  db: MssqlData,
  query: Record<string, string>,
): Promise<BudgetResult | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  const queryData: BudgetFindQueryData = {
    systemId: '',
    projectTitle: '',
    projectId: '',
  };

  const yearGlobalVariableData: FoundGlobalVariableBody = await findGlobalVariable(app, 'cedar.api.budget.year') as FoundGlobalVariableBody;
  if (isError(yearGlobalVariableData)) {
    app.logger.error({ error: yearGlobalVariableData });
    return yearGlobalVariableData;
  }

  const yearGlobal = yearGlobalVariableData.globalValue;

  // projectId
  const projectId = get(query, 'projectId') as string;
  if (isString(projectId) && isEmpty(projectId)) {
    const error = new Error('The project ID is not valid');
    app.logger.error({ error });
    return error;
  }
  if (projectId) {
    queryData.projectId = projectId;
  }

  // Project Title
  const projectTitle = get(query, 'projectTitle') as string;
  if (isString(projectTitle) && isEmpty(projectTitle)) {
    const error = new Error('The project title is not valid');
    app.logger.error({ error });
    return error;
  }
  if (projectTitle) {
    queryData.projectTitle = projectTitle;
  }

  // systemId
  const systemId = get(query, 'systemId') as string;
  if (isString(systemId) && isEmpty(systemId)) {
    const error = new Error('The system ID is not valid');
    app.logger.error({ error });
    return error;
  }
  if (systemId) {
    queryData.systemId = systemId;
  }

  // onlyIds / idsOnly
  const onlyIdsParamVal = get(query, 'onlyIds', '') as string;
  const onlyIds = strToBool(onlyIdsParamVal);

  const idsOnlyParamVal = get(query, 'idsOnly', '') as string;
  const idsOnly = strToBool(idsOnlyParamVal);

  if (onlyIds || idsOnly) {
    const onlyIdsResult = await findDataIdsOnly(app, db, yearGlobal, queryData);
    if (isError(onlyIdsResult)) {
      app.logger.error({ error: onlyIdsResult });
      return new Error('There was an issue fetching information from the database');
    }
    return onlyIdsResult;
  }

  const dataResult = await findData(app, db, yearGlobal, queryData);
  if (isError(dataResult)) {
    app.logger.error({ error: dataResult });
    return new Error('There was an issue fetching information from the database');
  }

  return dataResult;
};

// SparX operation types
export interface ProjectArchValues {
  cms_funding?: string;
  changecategory?: string;
  changecomments?: string;
  comments?: string;
  object?: string;
  project?: string;
  sag_imp_id?: string;
  samplerecordforusecases?: string;
  type?: string;
}

export interface SparxObject {
  ClassName: string;
  Id: string;
  Values: ProjectArchValues;
  RefStr?: string;
  GenericAttributes?: unknown[];
}

export interface SparxRelation {
  FromId: string;
  Property: string;
  ToRef: string;
  FromRef?: string;
  ToId?: string;
}

export interface SparxUpdateRequest {
  CurrentProfile: string;
  APICulture?: string;
  Objects: SparxObject[];
  Relations: SparxRelation[];
}

export interface BudgetRequest {
  FiscalYear?: string;
  FundingSource?: string;
  id?: string; // OFM budget internal ID in system of record
  Name?: string;
  projectId: string; // OFM budget project ID in system of record
  systemId?: string; // System which this budget funds
  projectTitle?: string; // Title of this project
  fundingId?: string; // Cross-reference ID for relationship between budget project and application
  funding?: string; // Description of the allocation of this budget to the system
}

/**
 * Executes a SparX budget operation by creating the necessary SparX objects and relations,
 * then executing the stored procedure.
 * @param app - The CMS application instance
 * @param db - The database connection
 * @param budgets - Array of budget requests to process
 * @returns Promise that resolves to success status and count, or Error if failed
 */
const executeSparxBudgetOperation = async (
  app: CMSApp,
  db: MssqlData,
  budgets: BudgetRequest[],
): Promise<{ success: boolean; count: number } | Error> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return db || new Error('Database Unavailable');
  }

  if (!budgets || !Array.isArray(budgets) || budgets.length === 0) {
    return new Error('No budgets provided');
  }

  const sparxObjects: SparxObject[] = [];
  const sparxRelations: SparxRelation[] = [];

  budgets.forEach((budget, index) => {
    const currentObjectId = String(index + 1);

    const projectArchValues: ProjectArchValues = {
      cms_funding: budget.funding,
    };

    sparxObjects.push({
      ClassName: 'ProjectArch',
      Id: currentObjectId,
      Values: projectArchValues,
    });

    // Relation for 'project'
    sparxRelations.push({
      FromId: currentObjectId,
      Property: 'project',
      ToRef: budget.projectId,
    });

    // Relation for 'object' (assuming this maps to systemId based on Webmethods trace)
    if (budget.systemId) {
      sparxRelations.push({
        FromId: currentObjectId,
        Property: 'object',
        ToRef: budget.systemId,
      });
    }
  });

  const sparxUpdateRequest: SparxUpdateRequest = {
    CurrentProfile: 'API User',
    Objects: sparxObjects,
    Relations: sparxRelations,
  };

  const jsonInput = JSON.stringify(sparxUpdateRequest);

  const queryParams: StoredProcedureParam[] = [
    { name: 'RETURN_VALUE', type: 'int' }, // This seems to be the output for success/failure
  ];
  const queryData: StoredProcedureData[] = [
    { name: 'jsonInput', value: jsonInput },
  ];
  const queryResults: StoredProcedureQuery[] = [
    { resultKey: 'queryStatus', paramName: 'RETURN_VALUE', wrapAsParam: true },
  ];

  const [spError, spResult] = await tryAsync(
    db.queryStoredProcedures(SP_INSERT_SYSTEMBUDGET_JSON, queryParams, queryData, queryResults),
  );

  if (spError || isError(spResult)) {
    app.logger.error({
      error: spError || spResult,
      message: 'Database stored procedure execution failed',
      payload: sparxUpdateRequest,
    });
    // Return the original error to preserve it for the caller
    return spError || (spResult as Error);
  }

  const queryStatus: number = get(spResult, '[0][0].queryStatus', -1);

  if (queryStatus !== 0) { // Webmethods flow checks for RETURN_VALUE == 0 for success
    app.logger.error({
      message: 'Stored procedure returned non-zero status.',
      queryStatus,
      spResult,
      payload: sparxUpdateRequest,
    });
    return new Error('Stored procedure returned non-zero status');
  }

  return { success: true, count: budgets.length };
};

export {
  getData,
  budgetFindUtil,
  findData,
  getDataIdsOnly,
  findDataIdsOnly,
  executeSparxBudgetOperation,
};
