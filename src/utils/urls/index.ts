import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';
import {
  tryAsync,
  strToBoolOrNull,
} from '../general';
import MssqlData from '../../data-sources/mssql';
import {
  SPARX_SYSTEM_URL,
} from '../db-helpers';
import { CMSApp } from '../../types';

const cedarUrlsUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
) => {
  const baseSchema = Joi.string();
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return new Error('Database Unavailable');
  }

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\'');
    app.logger.error({ error });
    return error;
  }

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return new Error('The system ID is not valid');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_URL, [
    '"Sparx URL GUID" as urlId',
    '"URLLink" as address',
    '"URL API Endpoint" as isApiEndpoint',
    '"URL API AWF" as isBehindWebApplicationFirewall',
    '"Provides Version Code Repository Access" as isVersionCodeRepository',
    '"Hosting Environment" as urlHostingEnv',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: systemId,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an error fetching the URLs');
  }

  const urlSummary = get(viewResult, '[0]', []).map((item: object) => {
    const isApiEndpoint = get(item, 'isApiEndpoint', null) as string | null;
    const isBehindWebApplicationFirewall = get(item, 'isBehindWebApplicationFirewall', null) as string | null;
    const isVersionCodeRepository = get(item, 'isVersionCodeRepository', null) as string | null;

    return {
      ...item,
      isApiEndpoint: strToBoolOrNull(isApiEndpoint),
      isBehindWebApplicationFirewall: strToBoolOrNull(isBehindWebApplicationFirewall),
      isVersionCodeRepository: strToBoolOrNull(isVersionCodeRepository),
    };
  });

  if (urlSummary.length === 0) {
    return [];
  }

  return urlSummary;
};

const censusUrlsUtil = async (app: CMSApp, db: MssqlData, systemId: string) => {
  const baseSchema = Joi.string();
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return new Error('Database Unavailable');
  }

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\'');
    app.logger.error({ error });
    return error;
  }

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return new Error('The system ID is not valid');
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_URL, [
    '"Sparx URL GUID" as urlId',
    '"URLLink" as link',
    '"URL API Endpoint" as urlApiEndpoint',
    '"URL API AWF" as urlApiWaf',
    '"Provides Version Code Repository Access" as providesVerCodeAccess',
    '"Hosting Environment" as urlHostingEnv',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: systemId,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an error fetching the URLs');
  }

  const urlSummary = get(viewResult, '[0]', []).map((item: object) => {
    const isApiEndpoint = get(item, 'urlApiEndpoint', null) as string | null;
    const isBehindWebApplicationFirewall = get(item, 'urlApiWaf', null) as string | null;
    const isVersionCodeRepository = get(item, 'providesVerCodeAccess', null) as string | null;

    return {
      ...item,
      urlApiEndpoint: strToBoolOrNull(isApiEndpoint),
      urlApiWaf: strToBoolOrNull(isBehindWebApplicationFirewall),
      providesVerCodeAccess: strToBoolOrNull(isVersionCodeRepository),
    };
  });

  if (urlSummary.length === 0) {
    return [];
  }

  return urlSummary;
};

export {
  cedarUrlsUtil,
  censusUrlsUtil,
};
