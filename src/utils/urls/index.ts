import { get, isEmpty, isError } from 'lodash';
import { EndpointQueryParams } from 'src/utils/express/responses';
import {
  tryAsync,
  strToBoolOrNull,
} from 'src/utils/general';
import MssqlData from 'src/data-sources/mssql';
import { SPARX_SYSTEM_URL } from 'src/utils/db-helpers';
import { CMSApp } from 'src/types';

interface UrlFindCedarQueryParams extends EndpointQueryParams {
  id: string;
}

interface UrlFindCensusQueryParams extends EndpointQueryParams {
  systemId: string;
}

const cedarUrlsUtil = async (
  app: CMSApp,
  db: MssqlData,
  systemId: string,
) => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return new Error('Database Unavailable');
  }

  // Webmethods allows passing nothing.  This is hard (impossible) to do
  // with the id coming in via path parameter, but might as well check.
  // All tests so far resulted in 404 errors.
  if (typeof systemId === 'undefined') {
    return [];
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_URL, [
    '"Sparx URL GUID" as urlId',
    '"URLLink" as address',
    '"URL API Endpoint" as isApiEndpoint',
    '"URL API AWF" as isBehindWebApplicationFirewall',
    '"Provides Version Code Repository Access" as isVersionCodeRepository',
    '"Hosting Environment" as urlHostingEnv',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: systemId,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an error fetching the URLs');
  }

  const urlSummary = get(viewResult, '[0]', []).map((item: object) => {
    const isApiEndpoint = get(item, 'isApiEndpoint', null) as string | null;
    const isBehindWebApplicationFirewall = get(item, 'isBehindWebApplicationFirewall', null) as string | null;
    const isVersionCodeRepository = get(item, 'isVersionCodeRepository', null) as string | null;

    return {
      ...item,
      isApiEndpoint: strToBoolOrNull(isApiEndpoint),
      isBehindWebApplicationFirewall: strToBoolOrNull(isBehindWebApplicationFirewall),
      isVersionCodeRepository: strToBoolOrNull(isVersionCodeRepository),
    };
  });

  if (urlSummary.length === 0) {
    return [];
  }

  return urlSummary;
};

const censusUrlsUtil = async (app: CMSApp, db: MssqlData, systemId: string) => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    app.logger.error({ error: db || new Error('Database Unavailable') });
    return new Error('Database Unavailable');
  }

  // Webmethods allows passing nothing.
  if (typeof systemId === 'undefined' || isEmpty(systemId)) {
    return [];
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_URL, [
    '"Sparx URL GUID" as urlId',
    '"URLLink" as link',
    '"URL API Endpoint" as urlApiEndpoint',
    '"URL API AWF" as urlApiWaf',
    '"Provides Version Code Repository Access" as providesVerCodeAccess',
    '"Hosting Environment" as urlHostingEnv',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: systemId,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an error fetching the URLs');
  }

  const urlSummary = (viewResult as unknown[])[0] as unknown[];

  if (urlSummary.length === 0) {
    return [];
  }

  return urlSummary;
};

export {
  UrlFindCedarQueryParams,
  UrlFindCensusQueryParams,
  cedarUrlsUtil,
  censusUrlsUtil,
};
