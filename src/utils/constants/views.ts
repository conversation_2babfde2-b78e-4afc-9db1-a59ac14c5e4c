/**
 * Simple enum representing view names.
 *
 * Allows for tracking query types used by endpoints.
 */
enum SparxSupportViews {
  Sparx_System = 'Sparx_System',
  Sparx_System_API_Category = 'Sparx_System_API_Category',
  Sparx_System_RecordsManagementBucket_Census = 'Sparx_System_RecordsManagementBucket_Census',
  Sparx_System_Software_Full = 'Sparx_System_Software_Full',
  Sparx_System_URL = 'Sparx_System_URL',
}

export {
  // eslint-disable-next-line import/prefer-default-export
  SparxSupportViews,
};
