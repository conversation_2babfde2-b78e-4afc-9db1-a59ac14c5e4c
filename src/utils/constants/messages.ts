enum Messages {
  app_invalid = 'Unable to get the application from request',
  db_connection_invalid = 'Database connection invalid',
  db_query_general_error = 'Error querying database',
  db_query_result_missing = 'Unable to get result from query',
  db_query_stored_procedure_error = 'Error executing stored procedure',
  db_query_view_error = 'Error querying view',
  db_insert_error = 'Error inserting records',
  db_update_error = 'Error updating records',
  db_delete_error = 'Error deleting records',
  db_unavailable = 'Database Unavailable',
  internal_server_error = 'Internal Server Error',
  sp_json_parse_error = 'Error parsing JSON from stored procedure result',
}

export default Messages;
