import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import MssqlData from '../../data-sources/mssql';
import {
  CMSApp,
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
  InsertPersonSubDataSP,
  InsertPersonSPResult,
} from '../../types';
import { validateStringParams } from '../validation';
import { SP_INSERT_PERSON } from '../db-helpers';
import { tryAsync } from '../general';

const formatQueryData = (
  app: CMSApp,
  data: InsertPersonSubDataSP,
): StoredProcedureData[] | Error => {
  if (isEmpty(app)) {
    return new Error('No app was passed into the parameters');
  }

  const queryData: StoredProcedureData[] = [];
  const emailParam = get(data, 'email', null);
  const email = validateStringParams(emailParam);

  if (isError(email)) {
    app.logger.error({ error: email });
    return email;
  }
  if (email) {
    queryData.push({
      name: 'Email',
      value: email,
    });
  }

  const externalSourceParam = get(data, 'externalSource', null);
  const externalSource = validateStringParams(externalSourceParam);
  if (isError(externalSource)) {
    app.logger.error({ error: externalSource });
    return externalSource;
  }
  if (externalSource) {
    queryData.push({
      name: 'ExternalSource',
      value: externalSource,
    });
  }

  const firstNameParam = get(data, 'firstName', null);
  const firstName = validateStringParams(firstNameParam);
  if (isError(firstName)) {
    app.logger.error({ error: firstName });
    return firstName;
  }
  if (firstName) {
    queryData.push({
      name: 'FirstName',
      value: firstName,
    });
  }

  const lastNameParam = get(data, 'lastName', null);
  const lastName = validateStringParams(lastNameParam);
  if (isError(lastName)) {
    app.logger.error({ error: lastName });
    return lastName;
  }
  if (lastName) {
    queryData.push({
      name: 'LastName',
      value: lastName,
    });
  }

  const phoneParam = get(data, 'phone', null);
  const phone = validateStringParams(phoneParam);
  if (isError(phone)) {
    app.logger.error({ error: phone });
    return phone;
  }
  if (phone) {
    queryData.push({
      name: 'Phone',
      value: phone,
    });
  }

  const techNameParam = get(data, 'techName', null);
  const techName = validateStringParams(techNameParam);
  if (isError(techName)) {
    app.logger.error({ error: techName });
    return techName;
  }
  if (techName) {
    queryData.push({
      name: 'TechName',
      value: techName,
    });
  }

  const usernameParam = get(data, 'userName', null);
  const username = validateStringParams(usernameParam);
  if (isError(username)) {
    app.logger.error({ error: username });
    return username;
  }
  if (username) {
    queryData.push({
      name: 'UserName',
      value: username,
    });
  }

  const guidParam = get(data, 'GUID', null);
  const guid = validateStringParams(guidParam);
  if (isError(guid)) {
    app.logger.error({ error: guid });
    return guid;
  }
  if (guid) {
    queryData.push({
      name: 'GUID',
      value: guid,
    });
  }

  return queryData;
};

const InsertUserPromise = async (
  app: CMSApp,
  db: MssqlData,
  storedProcedure: string,
  params?: StoredProcedureParam[],
  data?: StoredProcedureData[],
  resultQueries?: StoredProcedureQuery[],
) => {
  const [error, response] = await tryAsync(db.queryStoredProcedures(
    storedProcedure,
    params,
    data,
    resultQueries,
  ));

  if (error || isError(response)) {
    app.logger.error({ error: error || response });
    return error || response;
  }
  return response;
};

const createPromise = async (
  app: CMSApp,
  db: MssqlData,
  person: InsertPersonSubDataSP,
) => {
  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];

  // goes through the data and if there's an error it returns it. otherwise, provides an array
  // of formatted keys and values
  const queryData = formatQueryData(app, person);
  if (isError(queryData)) {
    return queryData;
  }

  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  const response = await InsertUserPromise(
    app,
    db,
    SP_INSERT_PERSON,
    queryParams,
    queryData,
    queryResults,
  );

  if (isError(response)) {
    app.logger.error({ error: response });
    return response;
  }

  if (isEmpty(response)) {
    const error = new Error('There was an error inserting a person in the database');
    app.logger.error({ error });
    return error;
  }

  const arr: InsertPersonSPResult[] = get(response, '[0]', []);
  // the type is an array with all of the roles/people, and the status tacked on at the very end

  const queryObjEntry: InsertPersonSPResult = arr[arr.length - 1];
  const { queryStatus } = queryObjEntry;
  // the last entry in the array has the queryStatus
  if (queryStatus === 1) {
    const error = new Error('There was an error inserting a person in the database');
    app.logger.error({ error });
    return error;
  }

  return true;
};

const insertPersonSP = async (
  app: CMSApp,
  db: MssqlData,
  personData: InsertPersonSubDataSP[],
): Promise<boolean | Error | Error[]> => {
  if (!app) {
    return new Error('Unable to get the application from parameters');
  }

  if (!db) {
    const error = new Error('Database not in parameters');
    app.logger.error({ error });
    return error;
  }

  if (!personData || isEmpty(personData)) {
    const error = new Error('Unable to get person data from parameters');
    app.logger.error({ error });
    return error;
  }

  const promises: Promise<true | Error>[] = [];
  personData.forEach((person) => {
    const promise = createPromise(app, db, person);
    promises.push(promise);
  });

  const results = await Promise.all(promises)
    .catch((error) => error);

  if (results.some(isError)) {
    const errors = results.filter(isError);
    if (errors.length === 1) {
      const error = errors[0];
      app.logger.error({ error });
      return error;
    }

    app.logger.error({ error: errors });
    return errors;
  }

  return true;
};

export {
  formatQueryData,
  InsertUserPromise,
  createPromise,
  insertPersonSP,
};
