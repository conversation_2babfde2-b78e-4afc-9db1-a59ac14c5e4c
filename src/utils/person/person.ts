import { Request, Response } from 'express';
import { get, isError } from 'lodash';
import { person } from '../../subsystems/ldap';

const personSearch = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  // Check for queries
  const firstName = get(req, 'query.first_name', '') as string;
  const lastName = get(req, 'query.last_name', '') as string;
  const commonName = get(req, 'query.commonName', '') as string;
  const email = get(req, 'query.email', '') as string;
  const telephone = get(req, 'query.telephone', '') as string;
  const personData = {
    firstName,
    lastName,
    commonName,
    email,
    telephone,
  };

  const personResult = await person(app, personData);
  if (isError(personResult)) {
    app.logger.error(personResult);
    if (personResult.message === 'No items provided for the filter') {
      return res.status(400).send({ message: 'No items provided for the filter' });
    }
    return res.status(500).send({ message: 'Failed' });
  }

  return res.status(200).send(personResult);
};

export default personSearch;
