import Joi from 'joi';

const validateStringParams = (param: string | null): string | Joi.ValidationError => {
  const paramSchema = Joi.string().allow(null, '');
  const {
    error,
    value,
  } = paramSchema.validate(param);

  if (error) {
    return error;
  }
  return value;
};

export {
  // TODO: Build out validation util file
  // eslint-disable-next-line import/prefer-default-export
  validateStringParams,
};
