import { isArray, isEmpty } from 'lodash';
import Jo<PERSON>, { ValidationResult } from 'joi';
import { CMSApp, ResourceConfig } from '../../types';

const prependPathToResources = (path: string, resources: ResourceConfig[]) => {
  if (!isArray(resources)) {
    return new Error('Resources must be an array of resource configs');
  }

  if (isEmpty(path) || isEmpty(resources)) {
    return resources;
  }

  return resources.map((item) => {
    let newPath = path;
    if (item.path !== '/') {
      newPath = `${path}${item.path}`;
    }

    if (item.path !== '/' && path === '/') {
      newPath = item.path;
    }

    return {
      ...item,
      path: newPath,
    };
  });
};

const validateResourceConfig = (resourceConfig: ResourceConfig): ValidationResult => {
  const schema = Joi.object({
    name: Joi.string().required(),
    path: Joi.string().allow('').required(),
    method: Joi.string().valid('delete', 'get', 'options', 'patch', 'post', 'put').required(),
    resource: Joi.function().required(),
    public: Joi.boolean(),
  });

  return schema.validate(resourceConfig);
};

const checkIfRouteIsPublic = (app: CMSApp, path: string, method: string): boolean => {
  const foundRoute = app.resources.publicRoutes.find((item) => (
    item.path === path && item.method === method
  ));

  if (!foundRoute) {
    return false;
  }

  return true;
};

export {
  prependPathToResources,
  validateResourceConfig,
  checkIfRouteIsPublic,
};
