import Joi from 'joi';
import {
  get,
  isError,
  isString,
  attempt,
  isEmpty,
} from 'lodash';
import { mapPerson } from '../../subsystems/ldap';
import MssqlData from '../../data-sources/mssql';
import {
  SparxUserData,
  CMSApp,
  UnformattedSparxUserData,
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
  MappedSparxUserResults,
  SparxResults,
} from '../../types';
import { SP_GET_USERLIST } from '../db-helpers';
import { tryAsync } from '../general';

const getUsersSP = async (
  app: CMSApp,
  db: MssqlData,
  sparxUserData: SparxUserData,
): Promise<UnformattedSparxUserData[] | Error> => {
  const validateParams = (param: string | null) => {
    const paramSchema = Joi.string().allow(null, '');
    const {
      error,
      value,
    } = paramSchema.validate(param);

    if (error) {
      app.logger.error({ error });
      return error;
    }

    return value;
  };

  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];
  const queryData: StoredProcedureData[] = [{
    name: 'Outputjson',
    value: 'result',
    isOutput: true,
  }];
  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  const usernameParam = get(sparxUserData, 'userName', null) as string | null;
  const username = validateParams(usernameParam);
  if (isError(username)) {
    app.logger.error({ error: username });
    return username;
  }
  if (username) {
    queryData.push({
      name: 'username',
      value: username,
    });
  }

  const firstNameParam = get(sparxUserData, 'firstName', null) as string | null;
  const firstName = validateParams(firstNameParam);
  if (isError(firstName)) {
    app.logger.error({ error: firstName });
    return firstName;
  }
  if (firstName) {
    queryData.push({
      name: 'firstName',
      value: firstName,
    });
  }

  const lastNameParam = get(sparxUserData, 'lastName', null) as string | null;
  const lastName = validateParams(lastNameParam);
  if (isError(lastName)) {
    app.logger.error({ error: lastName });
    return lastName;
  }
  if (lastName) {
    queryData.push({
      name: 'lastName',
      value: lastName,
    });
  }

  const phoneParam = get(sparxUserData, 'phone', null) as string | null;
  const phone = validateParams(phoneParam);
  if (isError(phone)) {
    app.logger.error({ error: phone });
    return phone;
  }
  if (phone) {
    queryData.push({
      name: 'phone',
      value: phone,
    });
  }

  const emailParam = get(sparxUserData, 'email', null) as string | null;
  const email = validateParams(emailParam);
  if (isError(email)) {
    app.logger.error({ error: email });
    return email;
  }
  if (email) {
    queryData.push({
      name: 'email',
      value: email,
    });
  }

  const [findSparxUserError, findSparxUserResults] = await tryAsync(
    db.queryStoredProcedures(SP_GET_USERLIST, queryParams, queryData, queryResults),
  ) as [Error | null, MappedSparxUserResults[][] | Error];

  if (findSparxUserError) {
    app.logger.error({ error: findSparxUserError });
    return findSparxUserError;
  }

  if (isError(findSparxUserResults)) {
    app.logger.error({ error: findSparxUserResults });
    return findSparxUserResults;
  }

  const mappedResults = findSparxUserResults[0][1] || '';
  if (!mappedResults) {
    const error = new Error('Unable to get mappedResults from query');
    app.logger.error({ error });
    return error;
  }

  const findSparxUserQueryStatus: number | null = mappedResults.queryStatus;
  if (findSparxUserQueryStatus !== 0) {
    const error = new Error('Status of the query was invalid');
    app.logger.error({ error });
    return error;
  }

  const findSparxUserResult = mappedResults.result;

  if (!findSparxUserResult) {
    const error = new Error('Unable to get findSparxUserResult from query');
    app.logger.error({ error });
    return error;
  }

  let parsedObject = findSparxUserResult;
  if (isString(parsedObject)) {
    parsedObject = attempt(JSON.parse.bind(null, parsedObject));
    if (isError(parsedObject)) {
      app.logger.error({ error: parsedObject });
      return parsedObject;
    }
  }
  return get(parsedObject, 'Objects', []);
};

const findSparxUser = async (
  app: CMSApp,
  db: MssqlData,
  sparxUserData: SparxUserData,
): Promise<Error | SparxResults> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db) {
    const error = new Error('Database Unavailable');
    app.logger.error({ error });
    return error;
  }

  const foundSparxUser = await getUsersSP(app, db, sparxUserData);

  if (isError(foundSparxUser)) {
    app.logger.error({ error: foundSparxUser });
    return foundSparxUser;
  }

  if (isEmpty(foundSparxUser)) {
    return {
      count: 0,
      users: [],
    };
  }

  const formattedFoundSparxUser = foundSparxUser.map((user) => ({
    ...user.Values,
    id: user.id,
  }));

  const mappedSparxUser = mapPerson(formattedFoundSparxUser);
  return {
    count: mappedSparxUser.length,
    users: mappedSparxUser,
  };
};

export {
  getUsersSP,
  findSparxUser,
};
