import {
  get,
  set,
  isEmpty,
  isError,
  isArray,
  isPlainObject,
  isString,
} from 'lodash';
import PgData from '../../data-sources/pg';
import {
  AndObject,
  BetweenObject,
  CMSApp,
  GroupedObject,
  LikeObject,
  OperationObject,
  OrObject,
  ParamsObject,
  Where,
  WhereObject,
  WhereResult,
} from '../../types';
import { tryAsync } from '../general';
import DataSource from '../../data-sources/data-source';
import globalVariables from '../../data-sources/pg/seeds/globalVariables.json';
import apiKeys from '../../data-sources/pg/seeds/apiKeys.json';
import intakeSchemas from '../../data-sources/pg/seeds/intakeSchemas.json';

// Models
const SESSIONS = 'sessions';
const GATEWAY = 'gateway';
const GLOBAL_VARIABLES = 'globalVariables';
const INTAKE_SCHEMA = 'intakeSchemas';

// Views
const SPARX_PACKAGE_HIERARCHY = 'Sparx_Package_Hierarchy';
const SPARX_EASI_SYSTEM = 'Sparx_EASi_System';
const SPARX_ATO_TBL = 'CEDAR_API.Sparx_ATO_Tbl';
const SPARX_ATO_FULL_TBL = 'CEDAR_API.Sparx_System_ATO_Full_Tbl';
const SPARX_BUDGET_PROJECT = 'Sparx_BudgetProject';
const SPARX_SYSTEM_BUDGET_PROJECT = 'Sparx_System_BudgetProject';
const SPARX_API_GET_ROLE = 'Sparx_API_Get_Role';
const DBO_SPARX_ROLE = 'dbo.Sparx_Role';
const DBO_SPARX_SYSTEM_URL = 'dbo.Sparx_System_URL';
const SPARX_SYSTEM_DATAEXCHANGE = 'Sparx_System_DataExchange';
const SPARX_SYSTEM_CONTRACT_FULL_TBL = 'CEDAR_API.Sparx_System_Contract_Full_Tbl';
const SPARX_CONTRACT = 'Sparx_Contract';
const SPARX_SYSTEM_ANNUAL_COST = 'Sparx_System_Annual_Cost';
const SPARX_ATO_THREAT = 'Sparx_ATO_Threat';
const SPARX_SYSTEM_URL = 'Sparx_System_URL';
const SPARX_SYSTEM_DATACENTER_FULL = 'Sparx_System_DataCenter_Full';
const SPARX_SYSTEM_SOFTWARE_FULL = 'Sparx_System_Software_Full';
const SPARX_SYSTEM = 'Sparx_System';
const SPARX_SYSTEM_API_CATEGORY = 'Sparx_System_API_Category';
const SYSTEM_SURVEY_REVIEWERS = 'CEDAR_Support.System_Census.SYSTEM_SURVEY_REVIEWERS';
const SPARX_SYSTEM_BUSINESS_OWNER_CENSUS = 'Sparx_Support.dbo.Sparx_System_BusinessOwner_Census';
const SPARX_ORGANIZATION = 'Sparx_Organization';

// Stored Procedures
const SP_GET_SYSTEM_LIST = 'SP_Get_SystemList';
const SP_GET_SYSTEM_LIST_ROLE = 'SP_Get_SystemListRole';
const SP_GET_CONTRACT_LIST = 'SP_Get_ContractList';
const SP_INSERT_OBJECTROLE = 'SP_Insert_ObjectRole_json';
const SP_GET_USERLIST = 'SP_Get_UserList';
const SP_GET_EASIINTAKE_BY_STATUS_PAGINATED = 'SP_Get_EASiIntake_By_Status_Paginated';
const SP_INSERT_PERSON = 'SP_Insert_Person';
const SP_INSERT_INTAKE = 'SP_Insert_CEDAR_Intake_Request';
const SP_INSERT_SYSTEMBUDGET_JSON = 'SP_Insert_SystemBudget_json';
const SP_INSERT_CONTRACT_LIST = 'SP_Insert_SystemContract_json';

// CEDAR API Tables
const SPARX_DATACENTER_TBL = 'CEDAR_API.Sparx_DataCenter_Tbl';
const INTAKE_REQUEST = 'CEDAR_API.INTAKE_REQUEST';

const getDb = <D extends DataSource>(app: CMSApp, name: string): D | Error => {
  const source = get(app, `config.systems.${name}.dataSource`);
  if (!source) {
    return new Error(`Unable to find system in configuration for ${name}`);
  }

  const db = get(app, `dataSources.${source}`);
  if (!db) {
    return new Error(`Unable to find data source in configuration for ${source}`);
  }

  if (!(db instanceof DataSource)) {
    return new Error(`Data source ${source} is not a valid DataSource instance`);
  }

  return db as D;
};

const checkEmpty = (item: string | number | string[] | number[]) => (
  (isString(item) && isEmpty(item)) || (isArray(item) && isEmpty(item))
);

const handleOperation = (operation: OperationObject, result: WhereResult, prepend: string = ''): Error | boolean => {
  const column = get(operation, 'column', '');
  const operator = get(operation, 'operator', '');
  const value = get(operation, 'value', '');

  if (checkEmpty(column) || checkEmpty(operator) || checkEmpty(value)) {
    return new Error('The "operator" object is missing a required field');
  }

  if (operator === 'IN') {
    if (!Array.isArray(value)) {
      return new Error('The "IN" operator requires an array value');
    }

    if (value.length === 0) {
      // Handle empty array case - typically this should return no results
      set(result, 'query', `${result.query}${prepend}1=0`);
      return true;
    }

    const nextId = Object.keys(result.params).length + 1;
    const placeholders = value.map((_, index) => `:opParams${nextId}_${index}`).join(', ');
    set(result, 'query', `${result.query}${prepend}(${column} IN (${placeholders}))`);

    // Set each value as a separate parameter
    value.forEach((val, index) => {
      set(result, `params[opParams${nextId}_${index}]`, val);
    });

    return true;
  }

  const nextId = Object.keys(result.params).length + 1;
  set(result, 'query', `${result.query}${prepend}"${column}" ${operator} :opParams${nextId}`);
  set(result, `params[opParams${nextId}]`, value);

  return true;
};

const handleBetween = (between: BetweenObject, result: WhereResult, prepend: string = ''): Error | boolean => {
  const column = get(between, 'column', '');
  const start = get(between, 'start', '');
  const stop = get(between, 'stop', '');

  if (checkEmpty(column) || checkEmpty(start) || checkEmpty(stop)) {
    return new Error('The "between" object is missing a required field');
  }

  set(result, 'query', `${result.query}${prepend}("${column}" BETWEEN ${start} AND ${stop})`);
  return true;
};

const handleLike = (like: LikeObject, result: WhereResult, prepend: string = ''): Error | boolean => {
  const column = get(like, 'column', '');
  const value = get(like, 'value', '');

  if (checkEmpty(column) || checkEmpty(value)) {
    return new Error('The "like" object is missing a required field');
  }

  const nextId = Object.keys(result.params).length + 1;
  set(result, 'query', `${result.query}${prepend}("${column}" LIKE :opParams${nextId})`);
  set(result, `params[opParams${nextId}]`, value);

  return true;
};

const processWhereObject = (
  obj: WhereObject | AndObject | OrObject,
  result: WhereResult,
  prepend?: string,
) => {
  const amount = Object.keys(obj).length;
  if (amount !== 1) {
    return new Error('The "where" object must have only have one option');
  }

  const operation: OperationObject | undefined = get(obj, 'operation');
  const between: BetweenObject | undefined = get(obj, 'between');
  const like: LikeObject | undefined = get(obj, 'like');

  if (operation) {
    const whereOpResult = handleOperation(operation, result, prepend);
    if (isError(whereOpResult)) {
      return whereOpResult;
    }
  }

  if (between) {
    const betweenResult = handleBetween(between, result, prepend);
    if (isError(betweenResult)) {
      return betweenResult;
    }
  }

  if (like) {
    const likeResult = handleLike(like, result, prepend);
    if (isError(likeResult)) {
      return likeResult;
    }
  }

  return true;
};

const handleAnd = (and: AndObject[], result: WhereResult): Error | boolean => {
  if (!isArray(and) || isEmpty(and)) {
    return new Error('The "and" array is empty or invalid');
  }

  const validateAnd = and.every((item) => Object.keys(item).length === 1);
  if (!validateAnd) {
    return new Error('The "and" array objects can only have 1 option per object');
  }

  const andError = new Error();
  for (let i: number = 0; i < and.length; i += 1) {
    const processedAnd = processWhereObject(and[i], result, ' AND ');
    if (isError(processedAnd)) {
      andError.message = processedAnd.message;
      break;
    }
  }

  if (!isEmpty(andError.message)) {
    return andError;
  }

  return true;
};

const handleOr = (or: OrObject[], result: WhereResult): Error | boolean => {
  if (!isArray(or) || isEmpty(or)) {
    return new Error('The "or" array is empty or invalid');
  }

  const validateOr = or.every((item) => Object.keys(item).length === 1);
  if (!validateOr) {
    return new Error('The "or" array objects can only have 1 option per object');
  }

  const orError = new Error();
  for (let i: number = 0; i < or.length; i += 1) {
    const processedOr = processWhereObject(or[i], result, ' OR ');
    if (isError(processedOr)) {
      orError.message = processedOr.message;
      break;
    }
  }

  if (!isEmpty(orError.message)) {
    return orError;
  }

  return true;
};

const handleGrouped = (grouped: GroupedObject, result: WhereResult) => {
  const validateGrouped = Object.keys(grouped).length;
  if (validateGrouped !== 1) {
    return new Error('The "grouped" object must have only have one option');
  }

  const groupedResult: WhereResult = {
    query: '',
    params: {
      ...result.params,
    },
  };

  const and = get(grouped, 'and', []);
  if (!isEmpty(and)) {
    const processedAnd = handleAnd(and, groupedResult);
    if (isError(processedAnd)) {
      return processedAnd;
    }

    const resultAndQuery = groupedResult.query.substring(5);
    set(result, 'query', `${result.query} AND (${resultAndQuery})`);
    set(result, 'params', groupedResult.params);
  }

  const or = get(grouped, 'or', []);
  if (!isEmpty(or)) {
    const processedOr = handleOr(or, groupedResult);
    if (isError(processedOr)) {
      return processedOr;
    }

    const resultOrQuery = groupedResult.query.substring(4);
    set(result, 'query', `${result.query} OR (${resultOrQuery})`);
    set(result, 'params', groupedResult.params);
  }

  return true;
};

const processWhere = (whereObj: Where): Error | WhereResult => {
  // Handle WHERE statement
  const where = get(whereObj, 'where', {});
  // Check if where is available
  if (!isPlainObject(where) || isEmpty(where)) {
    return new Error('The "where" object is required and must be an object');
  }

  const result: { query: string, params: ParamsObject } = {
    query: '',
    params: {},
  };

  // Handle the initial WHERE statement
  const processedWhere = processWhereObject(where, result, ' WHERE ');
  if (isError(processedWhere)) {
    return processedWhere;
  }

  // Handle AND statement
  const and = get(whereObj, 'and', []);
  if (isArray(and) && !isEmpty(and)) {
    const processedAnd = handleAnd(and, result);
    if (isError(processedAnd)) {
      return processedAnd;
    }
  }

  // Handle OR statement
  const or = get(whereObj, 'or', []);
  if (isArray(or) && !isEmpty(or)) {
    const processedOr = handleOr(or, result);
    if (isError(processedOr)) {
      return processedOr;
    }
  }

  // Handle grouped
  const grouped = get(whereObj, 'grouped', {});
  if (isPlainObject(grouped) && !isEmpty(grouped)) {
    const processedGrouped = handleGrouped(grouped, result);
    if (isError(processedGrouped)) {
      return processedGrouped;
    }
  }

  return result;
};

const seedDb = async (app: CMSApp): Promise<true | Error> => {
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    app.logger.error(db);
    return db;
  }

  const seeds = [{
    key: GLOBAL_VARIABLES,
    value: globalVariables,
  }, {
    key: GATEWAY,
    value: apiKeys,
  }, {
    key: INTAKE_SCHEMA,
    value: intakeSchemas,
  }];

  const results = await Promise.all(seeds.map(async (seed) => {
    app.logger.info({ info: `Currently truncating: ${seed.key}` });

    const [dropTableError, dropTable] = await tryAsync(db.truncate(seed.key));
    if (dropTableError || isError(dropTable) || dropTable !== 0) {
      app.logger.error({ error: `Unable to truncate ${seed.key}`, result: dropTableError || dropTable });
      return new Error(`Unable to truncate ${seed.key}`);
    }
    app.logger.info({ info: `Truncate complete for: ${seed.key}` });

    const [seedError, seedResults] = await tryAsync(db.seedDb(seed.key, seed.value, {}));
    app.logger.info({ info: `Currently seeding: ${seed.key}` });
    if (seedError || isError(seedResults) || seedResults === 'failed') {
      app.logger.error({ error: `Unable to seed ${seed.key}`, result: seedError || seedResults });
      return new Error(`Unable to seed ${seed.key}`);
    }
    app.logger.info({ info: `Seeding complete for ${seed.key}: seeded ${(seedResults as number[]).length} item(s)` });
    return true;
  }));

  if (results.some((seed) => isError(seed))) {
    app.logger.error({ error: results });
    return new Error(`There was an error while seeding: ${results}`);
  }

  return true;
};

export {
  SESSIONS,
  GATEWAY,
  GLOBAL_VARIABLES,
  INTAKE_SCHEMA,
  SPARX_PACKAGE_HIERARCHY,
  SPARX_EASI_SYSTEM,
  SPARX_ATO_TBL,
  SPARX_ATO_FULL_TBL,
  SPARX_BUDGET_PROJECT,
  SPARX_SYSTEM_BUDGET_PROJECT,
  SPARX_SYSTEM_CONTRACT_FULL_TBL,
  SPARX_SYSTEM_DATAEXCHANGE,
  SPARX_SYSTEM_ANNUAL_COST,
  SPARX_API_GET_ROLE,
  SPARX_SYSTEM_URL,
  SPARX_SYSTEM_DATACENTER_FULL,
  SPARX_DATACENTER_TBL,
  SYSTEM_SURVEY_REVIEWERS,
  SPARX_SYSTEM_BUSINESS_OWNER_CENSUS,
  SPARX_ORGANIZATION,
  SP_GET_SYSTEM_LIST,
  SP_GET_SYSTEM_LIST_ROLE,
  SP_INSERT_OBJECTROLE,
  SP_GET_CONTRACT_LIST,
  SP_GET_USERLIST,
  SP_INSERT_PERSON,
  SP_INSERT_INTAKE,
  SP_INSERT_SYSTEMBUDGET_JSON,
  SPARX_CONTRACT,
  DBO_SPARX_ROLE,
  DBO_SPARX_SYSTEM_URL,
  INTAKE_REQUEST,
  SPARX_ATO_THREAT,
  SPARX_SYSTEM_SOFTWARE_FULL,
  SPARX_SYSTEM,
  SPARX_SYSTEM_API_CATEGORY,
  SP_GET_EASIINTAKE_BY_STATUS_PAGINATED,
  SP_INSERT_CONTRACT_LIST,
  getDb,
  checkEmpty,
  handleOperation,
  handleBetween,
  handleLike,
  processWhereObject,
  handleAnd,
  handleOr,
  handleGrouped,
  processWhere,
  seedDb,
};
