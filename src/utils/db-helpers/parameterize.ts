/**
 * Type for passing order by parameters as an argument.
 */
type ParameterizeOrderByConfig = Record<string, ParameterizeDirections>;

/**
 * Valid ORDER BY directions.
 */
enum ParameterizeDirections {
  asc = 'ASC',
  desc = 'DESC',
}

/**
 * Valid built-in functions for MSSQL.
 */
enum ParameterizedFunctions {
  // noinspection SpellCheckingInspection
  GETUTCDATE = 'GETUTCDATE()',
}

/**
 * Represents values that can be passed as literals when
 * needed in parameterized queries.
 */
type ParameterizeLiterals = Record<string, ParameterizedFunctions>;

/**
 * Represents the values needed when adding order by
 * to a parameterized query.
 *
 * Sequelize requires the replacements to be in a key => value format.
 */
interface ParameterOrderByResult {
  query: string;
  params: Record<string, string>;
}

/**
 * Provides functions for creating parameters in SQL query clauses.
 */
class Parameterize {
  public static readonly validColumnRegex: RegExp = /^\[?[a-zA-Z_][a-zA-Z0-9_ ]*]?$/;

  /**
   * Constructs the VALUES clause of an SQL statement with values replaced
   * with parameters.
   */
  public static insert(
    columns: string[],
    values: unknown[][],
    literals: ParameterizeLiterals = {},
    prefix: string = 'p',
  ): string {
    columns.forEach((name) => {
      if (!this.validColumnRegex.test(name)) {
        throw new Error(`Parameterize::insert: invalid or unsafe column name: ${name}`);
      }
    });

    const result = values.map((_, rowIndex) => {
      const mapped: string[] = columns.map((name, colIndex) => {
        if (literals[name] && this.isParameterFunction(literals[name])) {
          return name;
        }

        const paramNumber = rowIndex * columns.length + colIndex + 1;
        return `:${prefix}${paramNumber}`;
      });

      return ['(', mapped.join(', '), ')'].join('');
    });

    return result.join(', ');
  }

  /**
   * Constructs a simple ORDER BY clause with parameterized column names.
   *
   * Returns SQL output like: ORDER BY :o1 ASC, :o2 DESC
   */
  public static orderBy(
    orderBy: ParameterizeOrderByConfig,
    prefix: string = 'o',
  ): ParameterOrderByResult {
    Object.keys(orderBy).forEach((column) => {
      if (!this.validColumnRegex.test(column)) {
        throw new Error(`Parameterize::orderBy: invalid or unsafe column name: ${column}`);
      }
    });

    const params: Record<string, string> = {};
    const orderByClauses = Object.entries(orderBy).map(([column, direction], index) => {
      const paramName = `:${prefix}${index + 1}`;

      params[`${prefix}${index + 1}`] = column;
      return `${paramName} ${direction}`;
    });

    return {
      query: `ORDER BY ${orderByClauses.join(', ')}`,
      params,
    };
  }

  private static isParameterFunction(
    name: string,
  ): boolean {
    const functions = Object.values(ParameterizedFunctions);

    // Ensure the function name is valid during runtime.
    if (!functions.includes(name as ParameterizedFunctions)) {
      throw new Error(`Parameterize: invalid parameter function: ${name}`);
    }

    return functions.includes(name as ParameterizedFunctions);
  }
}

export default Parameterize;

export {
  ParameterizedFunctions,
  ParameterizeDirections,
  ParameterizeLiterals,
  ParameterizeOrderByConfig,
};
