/**
 * Utility type to create an object with enum keys and unknown value.
 */
type CreateColumnsFromEnum<E, T = unknown> = {
  [K in keyof E]: T;
};

// noinspection JSUnusedGlobalSymbols
/**
 * Utility type to create an object with enum keys and value of any type.
 */
type CreateObjectFromEnum<T extends Record<string, unknown>, V> = {
  [K in keyof T]: V;
};

/**
 * Uses the keys from an enum to build a list of columns (for queries).
 */
const getColumnsFromEnum = (
  e: Record<string, string>,
): string[] => Object.keys(e).map((k) => `[${k}]`);

/**
 * Takes a column map enum and uses it to construct an object for an interface.
 */
const mapEnumToInterface = <
  I extends Record<string, unknown>,
>(
    enumMapping: Record<string, keyof I>,
    record: Record<string, unknown>[],
  ): I[] => {
  const result = [] as I[];

  const mapper = (r: Record<string, unknown>) => {
    const row = {} as I;
    Object.entries(enumMapping).forEach(
      ([columnName, propertyName]) => {
        if (r[columnName] !== undefined) {
          row[propertyName] = r[columnName] as I[keyof I];
        }
      },
    );

    return row;
  };

  record.forEach((r) => {
    const row = mapper(r);
    result.push(row);
  });

  return result;
};

export {
  CreateColumnsFromEnum,
  CreateObjectFromEnum,
  getColumnsFromEnum,
  mapEnumToInterface,
};
