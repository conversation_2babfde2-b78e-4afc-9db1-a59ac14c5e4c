import {
  get,
  isError,
  isEmpty,
  omit,
} from 'lodash';
import MssqlData from '../../data-sources/mssql';
import {
  Where,
  AndObject,
  RawDeploymentDataCenter,
  CMSApp,
} from '../../types';
import {
  SPARX_SYSTEM_DATACENTER_FULL,
} from '../db-helpers';
import {
  tryAsync,
} from '../general';

const getDeploymentWhere = (
  systemId: string,
  status: string,
  deploymentType: string,
  state: string,
) => {
  const hasSystemId = !isEmpty(systemId);
  const secondaryParams = [status, deploymentType, state];
  const secondaryKeys = ['Relationship Status', 'Environment', 'State'];
  const secondaryParamsEmpty = secondaryParams.every((param) => isEmpty(param));

  // if there are params, systemId HAS to be there
  if (!secondaryParamsEmpty) {
    if (!hasSystemId) {
      return new Error('System ID is required if other parameters are provided');
    }

    const paramAnd: AndObject[] = [];
    const where: Where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId,
        },
      },
      and: paramAnd,
    };

    secondaryParams.forEach((param, idx) => {
      if (!isEmpty(param)) {
        paramAnd.push({
          operation: {
            column: secondaryKeys[idx],
            operator: '=',
            value: param,
          },
        });
      }
    });

    return where;
  }

  if (hasSystemId && secondaryParamsEmpty) {
    return {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId,
        },
      },
    };
  }

  // if no params are given or when all else fails, return all
  return undefined;
};

const deploymentListUtil = async (
  app: CMSApp,
  db: MssqlData,
  query: Record<string, string>,
) => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (isError(db) || !db) {
    const dbError = new Error('Database Unavailable');
    app.logger.error({ error: db || dbError });
    return db || dbError;
  }

  // required-ish
  const systemId = get(query, 'systemId', '');
  const status = get(query, 'status', '');
  const deploymentType = get(query, 'deploymentType', '');
  const state = get(query, 'state', '');

  const where = getDeploymentWhere(systemId, status, deploymentType, state);

  if (isError(where)) {
    app.logger.error({ error: where });
    return where;
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATACENTER_FULL, [
    '"Connection GUID" as id',
    '"Connection Name" as name',
    '"Description" as description',
    '"Environment" as deploymentType',
    '"Sparx System GUID" as systemId',
    '"System Name" as systemName',
    '"Version" as systemVersion',
    '"DisplaySystemStatus" as status',
    '"DisplaySystemState" as state',
    '"StartDate" as startDate',
    '"EndDate" as endDate',
    '"Connection GUID" as deploymentElementId',
    '"Contractor Name" as contractorName',
    '"Production Data Use Flag" as hasProductionData',
    '"Hot Site" as isHotSite',
    '"System Server Software Replicated" as replicatedSystemElements',
    '"WAN Type" as wanType',
    '"WAN Type - Other" as wanTypeOther',
    '"Hosted on Cloud" as movingToCloud',
    '"Cloud Migrated Date" as movingToCloudDate',
    '"Users Requiring Multifactor Authentication" as usersRequiringMFA',
    '"Other Special Users" as otherSpecialUsers',
    '"Network Encryption" as networkEncryption',
    '"AWS Enclave" as awsEnclave',
    '"AWS Enclave Other" as awsEnclaveOther',
    '"Sparx DataCenter GUID" as DataCenterId',
    '"DataCenter Name" as DataCenterName',
    '"Version" as DataCenterVersion',
    '"Data Center Type" as DataCenterDescription',
    '"DisplayDataCenterStatus" as DataCenterStatus',
    '"DisplayDataCenterState" as DataCenterState',
    '"StartDate" as DataCenterStartDate',
    '"EndDate" as DataCenterEndDate',
    '"Address Line 1" as DataCenterAddress1',
    '"Address Line 2" as DataCenterAddress2',
    '"City" as DataCenterCity',
    '"State" as DataCenterAddressState',
    '"Zip Code" as DataCenterZip',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return new Error('There was an error retrieving the deployment list from the database');
  }

  const deployments = get(viewResult, '[0]', []).map((deployment: RawDeploymentDataCenter) => {
    const dataCenterObj = {
      id: deployment.DataCenterId,
      name: deployment.DataCenterName,
      version: deployment.DataCenterVersion,
      description: deployment.DataCenterDescription,
      status: deployment.DataCenterStatus,
      state: deployment.DataCenterState,
      startDate: deployment.DataCenterStartDate,
      endDate: deployment.DataCenterEndDate,
      address1: deployment.DataCenterAddress1,
      address2: deployment.DataCenterAddress2,
      city: deployment.DataCenterCity,
      addressState: deployment.DataCenterAddressState,
      zip: deployment.DataCenterZip,
    };
    const dataCenterKeys = [
      'DataCenterId',
      'DataCenterName',
      'DataCenterVersion',
      'DataCenterDescription',
      'DataCenterStatus',
      'DataCenterState',
      'DataCenterStartDate',
      'DataCenterEndDate',
      'DataCenterAddress1',
      'DataCenterAddress2',
      'DataCenterCity',
      'DataCenterAddressState',
      'DataCenterZip',
    ];
    const formattedDeployment = omit(deployment, dataCenterKeys);
    return {
      ...formattedDeployment,
      DataCenter: dataCenterObj,
    };
  });
  return {
    count: deployments.length,
    Deployments: deployments,
  };
};

export {
  getDeploymentWhere,
  deploymentListUtil,
};
