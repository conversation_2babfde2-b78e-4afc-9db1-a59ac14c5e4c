import { CreateColumnsFromEnum } from 'src/utils/db-helpers/mappers';

// Note, the names must be unique between the map type and the row type.

// Database to Object Mapping
// --------------------------

export enum SparxSystemApiCategoryMap {
  // noinspection SpellCheckingInspection
  'API Cateogry' = 'category', // Yes, misspelled.
}

export type TSparxSystemApiCategoryRow = CreateColumnsFromEnum<typeof SparxSystemApiCategoryMap>;

export enum SoftwareProductMap {
  // noinspection SpellCheckingInspection,JSUnusedGlobalSymbols
  'Software Name' = 'software_name',
  'Sparx Software GUID' = 'softwareProductId',
  'Technopedia Release ID' = 'technopedia_id',
  'Vendor Name' = 'vendor_name',
  'Category' = 'technopedia_category',
  'Used as API Gateway' = 'api_gateway_use',
  'Used for AI Capabilities' = 'provides_ai_capability',
  'Purchased Under Enterprise License Agreement' = 'ela_purchase',
  'ELA Vendor ID' = 'ela_vendor_id',
  'Software ELA Organization' = 'ela_organization',
  'System Software Connection GUID' = 'systemSoftwareConnectionGuid',
  'Software SoftwareCategory Connection GUID' = 'softwareCatagoryConnectionGuid', // Yes, misspelled.
  'Software Vendor Connection GUID' = 'softwareVendorConnectionGuid',
}

export type TSoftwareProductRow = CreateColumnsFromEnum<typeof SoftwareProductMap>;

export enum SparxSystemMap {
  // noinspection SpellCheckingInspection,JSUnusedGlobalSymbols
  'API Developed' = 'apisDeveloped',
  'API Description Published' = 'apiDescPublished',
  'API Description Location' = 'apiDescPubLocation',
  'API Data Area' = 'apiDataArea',
  'API Accessibility' = 'apisAccessibility',
  'Does the API use FHIR' = 'apiFHIRUse',
  'Does the API use FHIR Other' = 'apiFHIRUseOther',
  'System has API Gateway' = 'systemHasApiGateway',
  'API Has Portal' = 'apiHasPortal',
  'Uses AI Technology' = 'usesAiTech',
  'AI Project Life Cycle Stage' = 'developmentStage',
  'AI Solution Category' = 'aiSolnCatg',
  'AI Solution Category Other' = 'aiSolnCatgOther', // Yes, strange spelling.
}
export type TSparxSystemColumns = CreateColumnsFromEnum<typeof SparxSystemMap>;

// Intermediate Objects
// --------------------

export interface ISoftwareRecord extends Record<string, unknown> {
  api_gateway_use: boolean,
  ela_organization?: string,
  ela_purchase?: string,
  ela_vendor_id?: string,
  provides_ai_capability: boolean,
  software_name: string,
  // noinspection SpellCheckingInspection
  softwareCatagoryConnectionGuid: string, // Yes, misspelled.
  softwareProductId: string,
  softwareVendorConnectionGuid: string,
  systemSoftwareConnectionGuid: string,
  technopedia_category?: string,
  technopedia_id: string,
  vendor_name: string,

}

// Endpoint Responses
// ------------------

export interface ISoftwareProductResponse extends Record<string, unknown> {
  apisDeveloped: string,
  apiDescPublished: string,
  apiDescPubLocation: string,
  apiDataArea?: string[],
  apiAccessibility: string,
  apiFHIRUse: string,
  apiFHIRUseOther: string,
  systemHasApiGateway: boolean | null
  apiHasPortal: string | boolean | null
  usesAiTech: string,
  developmentStage: string,
  aiSolnCatg?: string[],
  // noinspection SpellCheckingInspection
  aiSolnCatgOther: string, // Yes, misspelled.
  softwareProducts?: ISoftwareRecord[],
}
