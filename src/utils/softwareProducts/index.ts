import {
  get,
  isError,
} from 'lodash';
import MssqlData from '../../data-sources/mssql';
import {
  SPARX_SYSTEM_SOFTWARE_FULL,
  SPARX_SYSTEM,
  SPARX_SYSTEM_API_CATEGORY,
} from '../db-helpers';
import {
  strToBool,
  tryAsync,
} from '../general';
import {
  CMSApp,
  RawCategoryObj,
  RawSystemResult,
  RawSoftwareObj,
  Where,
} from '../../types';

const createIdWhereObj = (id: string) => {
  const idWhere: Where = {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: id,
      },
    },
  };

  return idWhere;
};

const getCategoriesSubList = async (app: CMSApp, db: MssqlData, id: string):
Promise<Error | string[]> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db) {
    const dbError = new Error('No Database given in the params');
    app.logger.error({ error: dbError });
    return dbError;
  }

  const idWhere = createIdWhereObj(id);

  const [viewCategoryError, viewCategoryResult] = await tryAsync(
    db.queryView(SPARX_SYSTEM_API_CATEGORY, [
      '"API Cateogry" as category',
    ], idWhere),
  );

  if (viewCategoryError || isError(viewCategoryResult)) {
    app.logger.error({ error: viewCategoryError || viewCategoryResult });
    return viewCategoryError || viewCategoryResult as Error;
  }

  return get(viewCategoryResult, '[0]', []).map((categoryObject: RawCategoryObj) => (categoryObject.category));
};

const getSoftwareProductsSubList = async (app: CMSApp, db: MssqlData, id: string):
Promise<Error | RawSoftwareObj[]> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db) {
    const dbError = new Error('No Database given in the params');
    app.logger.error({ error: dbError });
    return dbError;
  }

  const idWhere = createIdWhereObj(id);

  const [viewSoftwareError, viewSoftwareResult] = await tryAsync(
    db.queryView(SPARX_SYSTEM_SOFTWARE_FULL, [
      '"Software Name" as software_name',
      '"Sparx Software GUID" as softwareProductId',
      '"Technopedia Release ID" as technopedia_id',
      '"Vendor Name" as vendor_name',
      '"Category" as category',
      '"Used as API Gateway" as api_gateway_use',
      '"Used for AI Capabilities" as provides_ai_capability',
      '"Purchased Under Enterprise License Agreement" as ela_purchase',
      '"ELA Vendor ID" as ela_vendor_id',
      '"Software ELA Organization" as ela_organization',
      '"System Software Connection GUID" as systemSoftwareConnectionGuid',
      '"Software SoftwareCategory Connection GUID" as softwareCatagoryConnectionGUID',
      '"Software Vendor Connection GUID" as softwareVendorConnectionGuid',
    ], idWhere),
  );

  if (viewSoftwareError || isError(viewSoftwareResult)) {
    app.logger.error({ error: viewSoftwareError || viewSoftwareResult });
    return viewSoftwareError || viewSoftwareResult as Error;
  }

  return get(viewSoftwareResult, '[0]', []);
};

const getSystemSubList = async (app: CMSApp, db: MssqlData, id: string):
Promise<Error | RawSystemResult[]> => {
  if (!app) {
    return new Error('Unable to get the application from request');
  }

  if (!db) {
    const dbError = new Error('No Database given in the params');
    app.logger.error({ error: dbError });
    return dbError;
  }

  const idWhere = createIdWhereObj(id);
  const [viewSystemError, viewSystemResult] = await tryAsync(
    db.queryView(SPARX_SYSTEM, [
      '"API Developed" as apisDeveloped',
      '"API Description Published" as apiDescPublished',
      '"API Description Location" as apiDescPubLocation',
      '"API Data Area" as apiDataArea',
      '"API Accessibility" as apisAccessibility',
      '"Does the API use FHIR" as apiFHIRUse',
      '"Does the API use FHIR Other" as apiFHIRUseOther',
      '"System has API Gateway" as systemHasApiGateway',
      '"API Has Portal" as apiHasPortal',
      '"Uses AI Technology" as usesAiTech',
      '"AI Project Life Cycle Stage" as developmentStage',
      '"AI Solution Category Other" as aiSolnCatgOther',
    ], idWhere),
  );

  if (viewSystemError || isError(viewSystemResult)) {
    app.logger.error({ error: viewSystemError || viewSystemResult });
    return viewSystemError || viewSystemResult as Error;
  }

  return get(viewSystemResult, '[0]', []);
};

const getSoftwareProductsList = async (app: CMSApp, db: MssqlData, id: string) => {
  const categoryResults = await getCategoriesSubList(app, db, id);

  if (isError(categoryResults)) {
    return categoryResults;
  }
  const softwareProductResults = await getSoftwareProductsSubList(app, db, id);

  if (isError(softwareProductResults)) {
    return softwareProductResults;
  }

  const systemResults: RawSystemResult[] | Error = await getSystemSubList(app, db, id);

  if (isError(systemResults)) {
    return systemResults;
  }

  const fullList = systemResults.map((system: RawSystemResult) => {
    const systemHasApiGateway = get(system, 'businessPurposeOfAddress', '') as string;
    const formattedSystemHasApiGateway = strToBool(systemHasApiGateway);

    const formattedApiDataArea: string[] = categoryResults;

    return {
      ...system,
      systemHasApiGateway: formattedSystemHasApiGateway,
      apiDataArea: formattedApiDataArea,
      softwareProducts: softwareProductResults,
    };
  });

  return fullList[0];
};

export {
  getSoftwareProductsList,
  getSystemSubList,
  getSoftwareProductsSubList,
  getCategoriesSubList,
  createIdWhereObj,
};
