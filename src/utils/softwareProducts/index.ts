// Custom: App.
import MssqlData from 'src/data-sources/mssql';
import { splitOrNull, strToBoolOrNull } from 'src/utils/general';
import { Where } from 'src/types';
import { getColumnsFromEnum, mapEnumToInterface } from 'src/utils/db-helpers/mappers';
import { SparxSupportViews } from 'src/utils/constants/views';
import {
  ISoftwareProductResponse,
  ISoftwareRecord,
  SoftwareProductMap,
  SparxSystemApiCategoryMap,
  SparxSystemMap,
  TSoftwareProductRow,
  TSparxSystemApiCategoryRow,
  TSparxSystemColumns,
} from 'src/utils/softwareProducts/types';

const createIdWhereObj = (id: string): Where => ({
  where: {
    operation: {
      column: 'Sparx System GUID',
      operator: '=',
      value: id,
    },
  },
});

const getCategoriesById = async (
  db: MssqlData,
  id: string,
): Promise<string[]> => {
  const result = await db.queryViewTypedFixed<TSparxSystemApiCategoryRow[]>(
    SparxSupportViews.Sparx_System_API_Category,
    getColumnsFromEnum(SparxSystemApiCategoryMap),
    {
      where: createIdWhereObj(id),
    },
  );

  // noinspection SpellCheckingInspection
  return result.map((r) => r['API Cateogry']) as string[]; // Yep, misspelled.
};

const getSoftwareProductsSubList = async (
  db: MssqlData,
  id: string,
): Promise<ISoftwareRecord[]> => {
  const result = await db.queryViewTypedFixed<TSoftwareProductRow[]>(
    SparxSupportViews.Sparx_System_Software_Full,
    getColumnsFromEnum(SoftwareProductMap),
    {
      where: createIdWhereObj(id),
    },
  );

  return mapEnumToInterface<ISoftwareRecord>(SoftwareProductMap, result);
};

const getSystemSubList = async (
  db: MssqlData,
  id: string,
): Promise<ISoftwareProductResponse> => {
  const result = await db.queryViewTypedFixed<TSparxSystemColumns[]>(
    SparxSupportViews.Sparx_System,
    getColumnsFromEnum(SparxSystemMap),
    {
      where: createIdWhereObj(id),
    },
  );

  const mangled = result.map((r) => {
    if (typeof r['AI Solution Category'] === 'string') {
      const t = r['AI Solution Category'].trim();

      if (t.length) {
        return {
          ...r,
          'AI Solution Category': splitOrNull(t, '|'),
        };
      }
    }

    // Delete AI Solution Category if it's null, undefined, or empty string
    if (r['AI Solution Category'] === null || r['AI Solution Category'] === undefined || r['AI Solution Category'] === '') {
      // eslint-disable-next-line max-len
      // eslint-disable-next-line @typescript-eslint/naming-convention, @typescript-eslint/no-unused-vars
      const { 'AI Solution Category': _, ...rest } = r;
      return rest;
    }

    return r;
  });

  const mappedResult = mapEnumToInterface<ISoftwareProductResponse>(SparxSystemMap, mangled);

  return mappedResult[0];
};

const getSoftwareProductsList = async (
  db: MssqlData,
  id: string,
): Promise<ISoftwareProductResponse> => {
  const systemResults = await getSystemSubList(db, id);

  // No result found, return undefined (implicit type).
  if (!systemResults) {
    return systemResults;
  }

  systemResults.systemHasApiGateway = strToBoolOrNull(systemResults?.systemHasApiGateway);
  systemResults.apiDataArea = await getCategoriesById(db, id);
  systemResults.softwareProducts = await getSoftwareProductsSubList(db, id);

  if (systemResults.apiHasPortal && typeof systemResults.apiHasPortal === 'string') {
    systemResults.apiHasPortal = strToBoolOrNull(systemResults.apiHasPortal) ?? false;
  }

  if (!systemResults.apiDataArea?.length) {
    delete systemResults.apiDataArea;
  }
  if (!systemResults.softwareProducts?.length) {
    delete systemResults.softwareProducts;
  }

  systemResults.softwareProducts?.forEach((i) => {
    /* eslint-disable no-param-reassign */
    i.api_gateway_use = strToBoolOrNull(i.api_gateway_use) ?? false;
    i.provides_ai_capability = strToBoolOrNull(i.provides_ai_capability) ?? false;
    /* eslint-enable no-param-reassign */
  });

  return systemResults;
};

export {

  createIdWhereObj,
  getCategoriesById,
  getSoftwareProductsList,
  getSoftwareProductsSubList,
  getSystemSubList,
};
