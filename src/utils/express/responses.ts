// Modules.
import { Request, Response } from 'express';
import { DatabaseError } from 'sequelize';

// Custom.
import {
  CMSApp, ResourceConfig, ResourceConfigSub,
} from 'types';
import { isError } from 'lodash';
import { prependPathToResources } from '../resources';
import { getDb } from '../db-helpers';
import MssqlData from '../../data-sources/mssql';
import DatabaseConfigs from '../constants/databaseConfigs';
import Messages from '../constants/messages';

// Types.
interface EndpointError {
  package: string;
  service: string;
  action: string;
  error: Error;
}

/**
 * Basic interface for managing types coming out of `req.query` in express.
 *
 * Extends Record<string, string> so that it can be used in the integration harnesses.
 *
 * Implemented here so that endpoints can extend this interface where this comment lives.
 */
interface EndpointQueryParams extends Record<string, string> {
  [key: string]: string;
}

interface ResponseSuccess {
  result: 'success';
  message: string[];
}

interface ResponseError {
  message: string[];
}

const createResourceConfigHarness = (
  coreHandlers: ResourceConfig[],
  modules: Record<string, { default: () => ResourceConfigSub }>,
) : () => ResourceConfig[] => {
  // Store the route definitions for each service type in a key-value pair.
  const handlers: { [key: string]: ResourceConfig[] } = {};

  // For each service type, get the route configuration and store it.
  Object.keys(modules).forEach((key) => {
    const module = modules[key];
    const resourceSub: ResourceConfigSub = module.default();

    const prepend = prependPathToResources(resourceSub.path, resourceSub.resourceConfig);
    if (isError(prepend)) {
      throw new Error(`An error occurred updating the path for ${resourceSub.path}`);
    }
    handlers[resourceSub.path] = prepend;
  });

  return (): ResourceConfig[] => {
    const resources = coreHandlers;

    Object.keys(handlers).forEach((key) => {
      resources.push(...handlers[key]);
    });

    return resources;
  };
};

const endpointSetup = (req: Request, dbName: DatabaseConfigs): EndpointSetup => {
  const app = req?.systemApp;
  if (!app) {
    throw new Error(Messages.app_invalid);
  }

  const db = getDb<MssqlData>(app, dbName);
  if (isError(db)) {
    app.logger.error({ error: db });
    throw new Error(Messages.db_unavailable);
  }

  return {
    app,
    db,
  };
};

const sendError = (
  res: Response,
  code: number,
  response: ResponseError,
  app?: CMSApp,
  error?: unknown,
): void => {
  if (app) {
    if (error && error instanceof DatabaseError) {
      if (error.original instanceof AggregateError) {
        const aggregateErrors = error.original.errors.map((e) => e.message);

        app.logger.error({ error, errorList: aggregateErrors });
      }
    } else if (isError(error)) {
      app.logger.error({ error });
    }
  }

  res.status(code).send(response);
};

const sendSuccess = (res: Response, response: ResponseSuccess): void => {
  res.status(200).send(response);
};

const logAppError = (app: CMSApp | undefined, error: EndpointError): void => {
  if (!app) {
    // The logger is tied to app, but we don't have one, log something anyway.
    // eslint-disable-next-line no-console
    console.error('app is undefined!', error);
    return;
  }

  app.logger.error(error);
};

export {
  EndpointQueryParams,
  createResourceConfigHarness,
  endpointSetup,
  logAppError,
  sendError,
  sendSuccess,
};
