/**
 * Specific error used to test unhandled exceptions in unit tests.
 *
 * Needed because the error handling is good, but need to check
 * the worst case scenario.
 */
class UnitTestForcedExceptionError extends Error {
  constructor(message?: string) {
    super(message);

    this.name = 'UnitTestForcedExceptionError';
  }
}

export {
  // Expecting additional errors to be added over time, this prevents refactoring.
  // eslint-disable-next-line import/prefer-default-export
  UnitTestForcedExceptionError,
};
