// Modules.
import { Request } from 'express';

/**
 * Used to retrieve querystring parameters from an Express request.
 */
class QueryParams<T> {
  constructor(private readonly params: T) {}

  static fromQuery<T>(req: Request): QueryParams<T> {
    return this.from<T>(req, false);
  }

  static fromPath<T>(req: Request): QueryParams<T> {
    return this.from<T>(req, true);
  }

  private static from<T>(req: Request, path = false): QueryParams<T> {
    const queryObj = path ? req.params : req.query;

    const params = {} as T;
    Object.entries(queryObj).forEach(([key, value]) => {
      (params as Record<string, unknown>)[key] = value;
    });
    return new QueryParams(params);
  }

  // noinspection JSUnusedGlobalSymbols
  get<K extends keyof T>(key: K): T[K] {
    return this.params[key];
  }

  /**
   * validator is expected to throw an error if the params are invalid.
   */
  getAll(validator?: (params: T) => void): T {
    if (validator) {
      validator(this.params);
    }

    return this.params;
  }
}

export default QueryParams;
