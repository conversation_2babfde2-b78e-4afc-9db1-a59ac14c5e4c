// Modules.
import { Request } from 'express';

/**
 * Used to retrieve querystring parameters from an Express request.
 */
class QueryParams<T> {
  constructor(private readonly params: T) {}

  static fromRequest<T>(req: Request): QueryParams<T> {
    // Extract all properties from the query object.
    const queryObj = req.query as unknown as Record<string, unknown>;

    // Create an object with the same structure as T.
    const params = {} as T;

    Object.entries(queryObj).forEach(([key, value]) => {
      (params as Record<string, unknown>)[key] = value;
    });

    return new QueryParams(params);
  }

  // noinspection JSUnusedGlobalSymbols
  get<K extends keyof T>(key: K): T[K] {
    return this.params[key];
  }

  /**
   * validator is expected to throw an error if the params are invalid.
   */
  getAll(validator?: (params: T) => void): T {
    if (validator) {
      validator(this.params);
    }

    return this.params;
  }
}

export default QueryParams;
