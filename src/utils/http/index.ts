import axios, { AxiosRequestConfig } from 'axios';
import { get } from 'lodash';
import { CMSApp } from '../../types';

const getRequest = async (app: CMSApp, url: string, options: AxiosRequestConfig = {}) => (
  axios.get(url, options)
    .then((response) => response)
    .catch((error) => {
      app.logger.error(get(error, 'message', 'An error occurred when making a get request'));
      return error;
    })
);

const postRequest = async (
  app: CMSApp,
  url: string,
  data?: unknown,
  options: AxiosRequestConfig = {},
) => (
  axios.post(url, data, options)
    .then((response) => response)
    .catch((error) => {
      app.logger.error(get(error, 'message', 'An error occurred when making a post request'));
      return error;
    })
);

const putRequest = async (
  app: CMSApp,
  url: string,
  data?: unknown,
  options: AxiosRequestConfig = {},
) => (
  axios.put(url, data, options)
    .then((response) => response)
    .catch((error) => {
      app.logger.error(get(error, 'message', 'An error occurred when making a put request'));
      return error;
    })
);

const patchRequest = async (
  app: CMSApp,
  url: string,
  data?: unknown,
  options: AxiosRequestConfig = {},
) => (
  axios.patch(url, data, options)
    .then((response) => response)
    .catch((error) => {
      app.logger.error(get(error, 'message', 'An error occurred when making a patch request'));
      return error;
    })
);

const deleteRequest = async (app: CMSApp, url: string, options: AxiosRequestConfig = {}) => (
  axios.delete(url, options)
    .then((response) => response)
    .catch((error) => {
      app.logger.error(get(error, 'message', 'An error occurred when making a delete request'));
      return error;
    })
);

export {
  getRequest,
  postRequest,
  putRequest,
  patchRequest,
  deleteRequest,
};
