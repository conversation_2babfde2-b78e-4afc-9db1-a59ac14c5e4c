import {
  isError,
  isEmpty,
  attempt,
  isString,
  isArray,
  isPlainObject,
  get,
  isFunction,
} from 'lodash';
import Jo<PERSON>, { AnySchema, PartialSchemaMap, Root } from 'joi';
import PgData from '../../data-sources/pg';
import { getDb, INTAKE_SCHEMA } from '../db-helpers';
import { tryAsync } from '../general';
import {
  CMSApp,
  IntakeSchema,
  IntakeValues,
  IntakeValidationSchema,
} from '../../types';

const getSchema = async (app: CMSApp, name: string): Promise<IntakeSchema | Error> => {
  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    app.logger.error({ error: db });
    return db;
  }

  if (isEmpty(name)) {
    const err = new Error('The schema name is invalid');
    app.logger.error({ error: err });
    return err;
  }

  const findOnePromise = db.findOne(INTAKE_SCHEMA, { name }) as Promise<IntakeSchema | Error>;
  const [error, result] = await tryAsync(findOnePromise);
  if (error) {
    app.logger.error({ error });
    return error;
  }

  if (!result) {
    const resultEmpty = new Error('No result returned');
    app.logger.error({ error: resultEmpty });
    return resultEmpty;
  }

  if (isError(result)) {
    app.logger.error({ error: result });
    return result;
  }

  return result;
};

const createRule = (ruleString: string): AnySchema | Error => {
  const rules = ruleString.split(',').map((r) => r.trim());
  if (!Object.hasOwn(Joi, rules[0])) {
    return new Error('Invalid schema type');
  }

  // Bypass TS check due to requirement of how to abstantiate Joi and the
  // above check verifies the schema is valid before attempting to call it
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const baseSchema = Joi[rules[0]]();
  // Cut off the base schema entry
  rules.splice(0, 1);

  return rules.reduce((fullSchema, rule) => {
    const [methodName, rawParam] = rule.split(':') as [string, string?];
    const schemaFunc = get(fullSchema, methodName);
    if (!isFunction(schemaFunc)) {
      return fullSchema;
    }

    const schemaFn = schemaFunc as (param?: string | null) => AnySchema;
    const param = rawParam === 'null' ? null : rawParam;

    return schemaFn.call(fullSchema, param);
  }, baseSchema);
};

const parseSchema = (app: CMSApp, schema: string) => {
  if (isEmpty(schema)) {
    const err = new Error('Empty schema provided');
    app.logger.error({ error: err });
    return err;
  }

  const parsed = attempt(() => JSON.parse(schema));
  if (isError(parsed) || !isPlainObject(parsed)) {
    const err = new Error('An error occurred while setting up the schema validator');
    app.logger.error({ error: parsed });
    return err;
  }

  return parsed;
};

const processRules = async (
  app: CMSApp,
  root: Root,
  rules: IntakeValues,
): Promise<AnySchema | Error> => {
  if (isString(rules)) {
    const ruleParts = rules.split(':').map((r) => r.trim());
    if (ruleParts[0] === 'schema' && ruleParts.length === 2) {
      const schemaObj = await getSchema(app, ruleParts[1]);
      if (isError(schemaObj)) {
        return schemaObj;
      }

      const schema = parseSchema(app, get(schemaObj, 'schema', ''));
      if (isError(schema)) {
        return schema;
      }

      return processRules(app, root, schema);
    }

    const newRule = createRule(rules);
    return newRule;
  }

  if (isArray(rules)) {
    if (rules.length === 1) {
      const rule = rules[0];
      if (isString(rule)) {
        const newRule = createRule(rule);
        if (isError(newRule)) {
          return newRule;
        }

        return root.array().items(newRule);
      }

      if (isPlainObject(rule) || isArray(rule)) {
        const newRule = await processRules(app, root, rule);
        if (isError(newRule)) {
          return newRule;
        }

        return root.array().items(newRule);
      }

      return new Error('Invalid array rule configuration');
    }

    return new Error('Incorrect number of rules in the array configuration');
  }

  if (isPlainObject(rules)) {
    const schemaKeys = Object.keys(rules as IntakeValidationSchema);
    let loopError: Error | null = null;
    const schemaMap: PartialSchemaMap = {};
    const promises = schemaKeys.map((key) => (new Promise((resolve) => {
      const schema = (rules as IntakeValidationSchema)[key];
      processRules(app, root, schema).then((newRule) => {
        if (isError(newRule)) {
          loopError = newRule;
          resolve(newRule);
          return;
        }
        schemaMap[key] = newRule;
        resolve(true);
      });
    })));

    await Promise.all(promises);
    if (isError(loopError)) {
      return loopError;
    }

    return root.object(schemaMap);
  }

  return new Error('Cannot configure validator');
};

const validateWithSchema = async (
  app: CMSApp,
  schema: string,
  parsedBody: unknown,
): Promise<true | Error> => {
  const parsed = parseSchema(app, schema);
  if (isError(parsed)) {
    return parsed;
  }

  const rules = parsed as IntakeValidationSchema;
  const validator = await processRules(app, Joi, rules);
  if (isError(validator)) {
    return validator;
  }

  const { error } = validator.validate(parsedBody);
  if (error) {
    app.logger.error({ error });
    return new Error('The body is not valid');
  }

  return true;
};

export {
  getSchema,
  createRule,
  processRules,
  validateWithSchema,
};
