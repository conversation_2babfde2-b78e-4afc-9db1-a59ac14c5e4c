import {
  isBoolean,
  isEmpty,
  isError,
  isNaN,
  isNull,
  isNumber,
  isString,
} from 'lodash';
import { formatDate } from '../time';
import { RenameKeysProps } from '../../types';

const strToBool = (str: string | boolean): boolean => {
  if (isBoolean(str)) {
    return str;
  }

  if (isString(str) && (str.toLowerCase() === 'true' || str.toLowerCase() === 'yes' || str === '1')) {
    return true;
  }

  return false;
};

const strToBoolOrNull = (str: string | boolean | null): boolean | null => {
  if (isNull(str)) {
    return null;
  }

  return strToBool(str);
};

const strToNumber = (str: string): number => {
  if (!str) {
    return 0;
  }

  const number = parseInt(str, 10);
  if (isNaN(number)) {
    return 0;
  }

  return number;
};

const strToNumberOrNull = (str: string | null): number | null => {
  if (isNull(str)) {
    return null;
  }

  return strToNumber(str);
};

const boolToStr = (bool: boolean, isYesNo: boolean = false): string => {
  if (bool && isYesNo) {
    return 'Yes';
  }

  if (bool) {
    return 'true';
  }

  if (!bool && isYesNo) {
    return 'No';
  }

  return 'false';
};

const splitOrNull = (input: string | null, deliminator: string) => {
  if (!isString(input)) {
    return null;
  }

  return input.split(deliminator);
};

const tryAsync = <T>(promise: Promise<T>): Promise<TryAsyncResult<T>> => promise
  .then((data: T | Error) => {
    if (isError(data)) {
      return [data, undefined] as TryAsyncResult<T>;
    }

    return [null, data] as TryAsyncResult<T>;
  })
  .catch((err: Error) => [err, undefined] as TryAsyncResult<T>);

// Disabling no explicit any because the input/output of the function can be anything
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const tryCatch = <T extends (...args: any[]) => any>(
  func: T,
  ...args: Parameters<T>
): ReturnType<T> | Error => {
  try {
    return func(...args);
  } catch (error) {
    return error as Error;
  }
};

const unknownAsError = (v: unknown): Error => {
  if (v instanceof Error) {
    return v;
  }

  if (isString(v)) {
    return new Error(v);
  }

  if (isNumber(v) || isBoolean(v)) {
    return new Error(v.toString());
  }

  try {
    return new Error(JSON.stringify(v));
  } catch (e) {
    return new Error(`Unknown error: ${e}`);
  }
};

const renameKey = (
  obj: { [key: string]: unknown },
  oldKey: string,
  newKey: string,
  format?: string,
  emptyToNull?: boolean,
) => {
  if (oldKey === newKey) {
    return;
  }

  const oldPropDesc = Object.getOwnPropertyDescriptor(obj, oldKey);
  const oldProp = obj[oldKey];
  if (oldPropDesc) {
    let newValue = oldProp;
    if (format && !isNull(newValue) && !isEmpty(newValue)) {
      newValue = formatDate(newValue as string, format);
    } else if (isEmpty(newValue) && emptyToNull) {
      newValue = null;
    }
    Object.defineProperty(obj, newKey, {
      ...oldPropDesc,
      value: newValue,
    });
    // Disabling no param reassign to remove old key in place
    // eslint-disable-next-line no-param-reassign
    delete obj[oldKey];
  }
};

const renameKeys = (obj: { [key: string]: unknown }[], keys: RenameKeysProps[]) => {
  obj.forEach((item) => {
    keys.forEach((key) => {
      renameKey(item, key.oldKey, key.newKey, key.format, key.emptyToNull);
    });
  });

  return obj;
};

const iterableToArray = (iter: Iterable<string>) => [...iter];

export {
  boolToStr,
  iterableToArray,
  renameKey,
  renameKeys,
  splitOrNull,
  strToBool,
  strToBoolOrNull,
  strToNumber,
  strToNumberOrNull,
  tryAsync,
  tryCatch,
  unknownAsError,
};
