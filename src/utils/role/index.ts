import {
  get,
  isArray,
  isEmpty,
  isError,
  isNull,
  isString,
} from 'lodash';
import { XMLParser } from 'fast-xml-parser';
import MssqlData from 'data-sources/mssql';
import {
  RoleObject,
  CMSApp,
  XmlParamsObject,
  SparxUserData,
  UpdateTotalObject,
  Updatable,
  LDAPPersonMap,
  User,
  SparxResults,
  CreatePromiseResult,
} from 'types';
import { postQuery } from '../../subsystems/sparxea';
import {
  personIds,
  person,
} from '../../subsystems/ldap';
import {
  findSparxUser,
} from '../users';
import { createPostXMLPerson } from '../xml';
import { tryAsync } from '../general';

const convertRDFToGUID = (app: CMSApp, rdfString: string): string | Error => {
  if (!isString(rdfString)) {
    const error = new Error('The post query result did not yield in a string');
    app.logger.error({ error });
    return error;
  }

  let parsedBody;
  try {
    const parser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '',
    });

    parsedBody = parser.parse(rdfString);
  } catch (error) {
    app.logger.error({ error });
    return new Error('The string is not valid XML');
  }

  const resource = get(parsedBody, 'rdf:RDF.oslc_am:Resource')
    || get(parsedBody, 'rdf.oslc_am_Resource')
    || get(parsedBody, 'Resource');

  if (!resource) {
    const error = new Error('No parsable XML data');
    app.logger.error({ error, resource });
    return error;
  }

  const resourceObj = isArray(resource) ? get(resource, '[0]') : resource;
  const about: string | undefined = get(resourceObj, 'rdf:about') || get(resourceObj, 'about');
  if (!about) {
    const error = new Error('Unable to parse guid');
    app.logger.error({ error, about });
    return error;
  }

  // Regex to extract GUID including braces
  const match = about.match(/({[A-F0-9-]+})/i);
  if (!match) {
    const error = new Error('No valid GUID found in RDF string');
    app.logger.error({ error, rdfString });
    return error;
  }

  return match[0];
};

const createRelation = (
  fromid: string,
  property: string,
  toref: string,
) => ({ fromid, property, toref });

const roleValid = (role: RoleObject) => {
  const {
    objectId,
    roleTypeId,
    assigneeId,
    assigneeUserName,
  } = role;

  if (isEmpty(objectId)) {
    return false;
  }

  if (isEmpty(roleTypeId)) {
    return false;
  }

  if (isEmpty(assigneeId) && isEmpty(assigneeUserName)) {
    return false;
  }

  return true;
};

const createPromise = async (
  app: CMSApp,
  db: MssqlData,
  role: RoleObject,
): Promise<CreatePromiseResult> => {
  if (!app) {
    return new Error('No app provided');
  }

  if (!db) {
    return new Error('Database Unavailable');
  }

  if (!role) {
    return new Error('No role provided');
  }

  const {
    objectId,
    roleTypeId,
    assigneeId,
    assigneeUserName,
    assigneeFirstName,
    assigneeLastName,
    assigneePhone,
    assigneeEmail,
  } = role;

  if (isEmpty(objectId)) {
    const error = new Error('Must have objectId');
    app.logger.error({ error });
    return error;
  }

  if (isEmpty(roleTypeId)) {
    const error = new Error('Must have roleTypeId');
    app.logger.error({ error });
    return error;
  }

  if (!isEmpty(assigneeId)) {
    return [role];
  }

  if (isEmpty(assigneeUserName) || isNull(assigneeUserName)) {
    const error = new Error('No semi-required parameters provided');
    app.logger.error({ error });
    return error;
  }

  const sparxData: SparxUserData = {
    userName: assigneeUserName,
    firstName: assigneeFirstName || '',
    lastName: assigneeLastName || '',
    phone: assigneePhone || '',
    email: assigneeEmail || '',
    commonName: `${assigneeFirstName || ''} ${assigneeLastName || ''}`.trim(),
  };

  const [sparxError, sparxUser] = await tryAsync<SparxResults | Error>(
    findSparxUser(app, db, sparxData),
  );

  if (sparxError || isError(sparxUser)) {
    app.logger.error({ error: sparxError || sparxUser });
  }

  let rawResults: Array<LDAPPersonMap | User>;

  if (
    !sparxError
    && sparxUser
    && !isError(sparxUser)
    && isArray(sparxUser.users)
    && !isEmpty(sparxUser.users)
  ) {
    rawResults = sparxUser.users;
  } else {
    // fallback to personIds => person
    // personIds returns User[] | Error
    const [byIdError, byIdUser] = await tryAsync<Error | User[]>(
      personIds(app, sparxData.userName),
    );

    if (
      byIdError
      || isError(byIdUser)
      || !isArray(byIdUser)
      || isEmpty(byIdUser)
    ) {
      // check for other attributes before continuing
      const noOtherAttrs = isEmpty(sparxData.firstName)
        && isEmpty(sparxData.lastName)
        && isEmpty(sparxData.phone)
        && isEmpty(sparxData.email);

      if (noOtherAttrs) {
        app.logger.error({ error: byIdError || byIdUser });
        return new Error('No properties given to search the user');
      }

      // person returns User[] | Error
      const [personError, personUser] = await tryAsync<Error | User[]>(
        person(app, sparxData),
      );

      if (
        personError
        || isError(personUser)
        || !isArray(personUser)
        || isEmpty(personUser)
      ) {
        app.logger.error({ error: personError || personUser });
        return new Error('No user found with that search criteria');
      }

      rawResults = personUser;
    } else {
      rawResults = byIdUser;
    }
  }

  // Single‐user XML => POST flow (only when the object has 'cn')
  const userCn = get(rawResults, '[0].cn');
  if (
    rawResults.length === 1
    && isString(userCn)
  ) {
    const xmlParams: XmlParamsObject = {
      title: userCn,
      stereotype: 'Person',
      type: 'Actor',
    };

    const [xmlError, xmlBody] = await tryAsync<string | Error>(
      createPostXMLPerson(app, xmlParams),
    );

    if (xmlError || isError(xmlBody) || !isString(xmlBody)) {
      app.logger.error({ error: xmlError || xmlBody });
      return new Error('The xmlBody sent is undefined');
    }

    const [postError, postResponse] = await tryAsync<string>(
      postQuery(app, xmlBody),
    );

    if (postError || isError(postResponse) || !isString(postResponse)) {
      app.logger.error({ error: postError || postResponse });
      return new Error('Post query failed');
    }

    const guid = convertRDFToGUID(app, postResponse);
    if (isError(guid)) {
      app.logger.error({ error: guid });
      return new Error('Unable to get GUID');
    }

    return [{
      objectId,
      roleTypeId,
      assigneeId: guid,
    }];
  }

  // Multi‐user mapping: pick the right ID prop
  const mappedRoles: (Partial<RoleObject> | Error)[] = rawResults.map((item) => {
    let assigneeVal: string = '';
    const ids = get(item, 'id');
    const uid = get(item, 'uid');
    if (ids) {
      // LDAPPersonMap case
      // An LDAP user has 4 possible replies from LDAP.
      // string | Buffer<ArrayBufferLike> | Buffer<ArrayBufferLike>[] | string[] | undefined
      if (isString(ids)) {
        assigneeVal = ids;
      } else if (isArray(ids)) {
        assigneeVal = ids.map((id) => (isString(id) ? id : String(id))).join(',');
      } else {
        assigneeVal = String(ids);
      }
    } else if (uid) {
      // User case (has .uid)
      assigneeVal = uid;
    }

    if (isEmpty(assigneeVal)) {
      return new Error('Unable to get user ids');
    }

    return {
      objectId,
      roleTypeId,
      assigneeId: assigneeVal,
    };
  });

  const assigneeError = new Error();
  const validatedRoles = mappedRoles.map((item) => {
    if (!isEmpty(assigneeError.message)) {
      return null;
    }

    if (isError(item)) {
      assigneeError.message = item.message;
      return null;
    }

    return item;
  }).filter((n) => !isNull(n));

  if (!isEmpty(assigneeError.message)) {
    app.logger.error({ error: assigneeError });
    return new Error('Unable to get GUID(s)');
  }

  return validatedRoles;
};

const createUpdateObj = (
  app: CMSApp,
  iterationId: string,
  updateObject: Updatable,
): UpdateTotalObject | Error => {
  if (updateObject.assigneeId && !isEmpty(updateObject.assigneeId)) {
    const {
      objectId,
      assigneeId,
      roleTypeId,
    } = updateObject;

    const relationOne = createRelation(iterationId, 'object', objectId);
    const relationTwo = createRelation(iterationId, 'responsible', assigneeId);
    const relationThree = createRelation(iterationId, 'roletype', roleTypeId);

    return {
      Object: {
        ClassName: 'Role',
        Id: iterationId,
        Values: { name: '' },
      },
      Relations: { relationOne, relationTwo, relationThree },
    };
  }

  if (updateObject.assigneeUserName && !isEmpty(updateObject.assigneeUserName)) {
    const {
      objectId,
      assigneeUserName,
      roleTypeId,
    } = updateObject;

    const relationOne = createRelation(iterationId, 'object', objectId);
    const relationTwo = createRelation(iterationId, 'responsible', assigneeUserName);
    const relationThree = createRelation(iterationId, 'roletype', roleTypeId);

    return {
      Object: {
        ClassName: 'Role',
        Id: iterationId,
        Values: { name: '' },
      },
      Relations: { relationOne, relationTwo, relationThree },
    };
  }

  // we cast to partial User to get at .uid without dragging in the full Entry index signature
  const userUid = get(updateObject, 'uid', '');
  if (!isEmpty(userUid)) {
    const { objectId, roleTypeId } = updateObject;

    const relationOne = createRelation(iterationId, 'object', objectId);
    const relationTwo = createRelation(iterationId, 'responsible', userUid);
    const relationThree = createRelation(iterationId, 'roletype', roleTypeId);

    return {
      Object: {
        ClassName: 'Role',
        Id: iterationId,
        Values: { name: '' },
      },
      Relations: { relationOne, relationTwo, relationThree },
    };
  }

  const error = new Error('There was no assigneeId or username provided');
  app.logger.error({ error });
  return error;
};

export {
  createRelation,
  roleValid,
  createPromise,
  createUpdateObj,
  convertRDFToGUID,
};
