import { Sequelize, Model, ModelStatic } from 'sequelize';
import { get, isError } from 'lodash';
import { CMSApp, ModelDefinition } from '../../types';

const initModels = async (
  app: CMSApp,
  db: Sequelize,
  models: ModelDefinition[],
) => {
  let definedModels: ModelStatic<Model>[] = [];
  let errorModels;

  await Promise.all(models.map(async (model) => {
    const name = get(model, 'name');
    const define = get(model, 'define');
    const options = get(model, 'options', {});

    if (!name || !define) {
      return new Error(`Model ${name || 'Unknown'} is not defined properly`);
    }

    return db.define(name, define, options);
  }))
    .then((values) => {
      if (isError(values)) {
        errorModels = values;
        return;
      }
      definedModels = values as ModelStatic<Model>[];
    })
    .catch((errors) => {
      errorModels = errors;
    });

  if (errorModels) {
    app.logger.error({ msg: errorModels });
    return new Error('Unable to define models.');
  }

  return definedModels;
};

const initViews = async (app: CMSApp, db: Sequelize, views: ModelDefinition[]) => {
  const definedViews = initModels(app, db, views);

  return definedViews;
};

export {
  initModels,
  initViews,
};
