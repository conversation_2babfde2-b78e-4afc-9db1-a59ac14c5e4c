import {
  DestroyOptions,
  FindOptions,
  UpdateOptions,
  UpsertOptions,
} from 'sequelize';
import { get } from 'lodash';
import { DataSourceType, DataOptions, CMSApp } from '../types';

abstract class DataSource implements DataSourceType {
  protected abstractMethodError: string;
  name: string = '';
  app: CMSApp;

  constructor(options: DataOptions) {
    const props = Object.getOwnPropertyNames(DataSource.prototype) as (keyof DataSource)[];
    props.forEach((prop) => {
      if (this[prop as keyof this] === DataSource.prototype[prop]) {
        throw new TypeError(`Please implement abstract method: ${prop}`);
      }
    });
    this.abstractMethodError = 'Do not call abstract method from child.';

    this.app = get(options, 'app');
  }

  abstract isDbReady(): boolean;

  abstract initDb(): Promise<void | Error>;

  abstract deleteOne(
    model: string,
    params: Record<string, number | string>,
    options: DestroyOptions
  ): Promise<'failed' | 'success'>;

  abstract findOne(
    model: string,
    params: Record<string, unknown>,
    options: FindOptions | object
  ): Promise<unknown>;

  abstract findAll(
    model: string,
    options: FindOptions | object
  ): Promise<unknown[]>;

  abstract findAllByParameter(
    model: string,
    params: Record<string, unknown>,
    options: FindOptions | object
  ): Promise<unknown[]>;

  abstract insertMany(
    model: string,
    objects: Record<string, unknown>[],
    options: Record<string, unknown>,
  ): Promise<number[] | 'failed'>;

  abstract insertOne(
    model: string,
    newObj: object,
    options: object
  ): Promise<number | 'failed'>;

  abstract updateOne(
    model: string,
    updatedObj: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpdateOptions | object,
  ): Promise<'success' | 'failed'>;

  abstract updateMany(
    model: string,
    updatedObjs: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpdateOptions | object,
  ): Promise<'success' | 'failed'>;

  abstract upsert(
    model: string,
    object: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpsertOptions | object,
  ): Promise<'success' | 'failed' | number>;

  abstract closeConnection(): Promise<void | boolean>;
}

export default DataSource;
