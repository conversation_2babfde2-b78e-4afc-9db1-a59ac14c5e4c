[{"name": "EASIIntakeV07", "schema": "{\"intakeId\":\"string,required,allow:null\",\"archivedAt\":\"string,required,allow:null\",\"businessNeed\":\"string,required,allow:null\",\"businessOwner\":\"string,required,allow:null\",\"businessOwnerComponent\":\"string,required,allow:null\",\"component\":\"string,required,allow:null\",\"contractEndDate\":\"string,required,allow:null\",\"contractStartDate\":\"string,required,allow:null\",\"contractNumber\":\"string,required,allow:null\",\"contractor\":\"string,required,allow:null\",\"costIncrease\":\"string,required,allow:null\",\"decidedAt\":\"string,required,allow:null\",\"eaSupportRequest\":\"boolean,required,strict\",\"existingContract\":\"string,required,allow:null\",\"existingFunding\":\"boolean,required,strict\",\"fundingSources\":{\"fundingSourceId\":\"string,required,allow:null\",\"fundingNumber\":\"string,required,allow:null\",\"fundingSource\":\"string,required,allow:null\"},\"grbDate\":\"string,required,allow:null\",\"grtDate\":\"string,required,allow:null\",\"issoName\":\"string,required,allow:null\",\"lifecycleExpiresAt\":\"string,required,allow:null\",\"processStatus\":\"string,required,allow:null\",\"productManager\":\"string,required,allow:null\",\"productManagerComponent\":\"string,required,allow:null\",\"projectName\":\"string,required,allow:null\",\"requestType\":\"string,required,allow:null\",\"requester\":\"string,required,allow:null\",\"solution\":\"string,required,allow:null\",\"status\":\"string,required,allow:null\",\"submittedAt\":\"string,required,allow:null\",\"userEUA\":\"string,required,allow:null\",\"hasUiChanges\":\"boolean,required,strict\"}", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"name": "EASIIntakeV08", "schema": "{\"intakeId\": \"string,required,allow:null\",\"archivedAt\": \"string,required,allow:null\",\"businessNeed\": \"string,required,allow:null\",\"businessOwner\": \"string,required,allow:null\",\"businessOwnerComponent\": \"string,required,allow:null\",\"component\": \"string,required,allow:null\",\"contractEndDate\": \"string,required,allow:null\",\"contractStartDate\": \"string,required,allow:null\",\"contractNumber\": \"string,required,allow:null\",\"contractor\": \"string,required,allow:null\",\"costIncrease\": \"string,required,allow:null\",\"decidedAt\": \"string,required,allow:null\",\"eaSupportRequest\": \"boolean,required,strict\",\"existingContract\": \"string,required,allow:null\",\"existingFunding\": \"boolean,required,strict\",\"fundingSources\": {\"fundingSourceId\": \"string,required,allow:null\",\"fundingNumber\": \"string,required,allow:null\",\"fundingSource\": \"string,required,allow:null\"},\"grbDate\": \"string,required,allow:null\",\"grtDate\": \"string,required,allow:null\",\"issoName\": \"string,required,allow:null\",\"lifecycleExpiresAt\": \"string,required,allow:null\",\"processStatus\": \"string,required,allow:null\",\"productManager\": \"string,required,allow:null\",\"productManagerComponent\": \"string,required,allow:null\",\"projectName\": \"string,required,allow:null\",\"requestType\": \"string,required,allow:null\",\"requester\": \"string,required,allow:null\",\"solution\": \"string,required,allow:null\",\"status\": \"string,required,allow:null\",\"submittedAt\": \"string,required,allow:null\",\"userEUA\": \"string,required,allow:null\",\"hasUiChanges\": \"boolean,required,strict\",\"usingSoftware\": \"string,required,allow:null\",\"acquisitionMethods\": \"string,required,allow:null\",\"usesAiTech\": \"boolean,required,strict\",\"currentAnnualSpending\": \"string,required,allow:null\",\"currentAnnualSpendingITPortion\": \"string,required,allow:null\",\"plannedYearOneSpending\": \"string,required,allow:null\",\"plannedYearOneSpendingITPortion\": \"string,required,allow:null\",\"projectAcronym\": \"string,required,allow:null\",\"scheduledProductionDate\": \"string,required,allow:null\"}", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"name": "BusinessCase05", "schema": "{\"archivedAt\": \"string,required,allow:null\",\"businessCaseId\": \"string,required,allow:null\",\"businessNeed\": \"string,required,allow:null\",\"businessOwner\": \"string,required,allow:null\",\"cmsBenefit\": \"string,required,allow:null\",\"currentSolutionSummary\": \"string,required,allow:null\",\"intakeId\": \"string,required,allow:null\",\"priorityAlignment\": \"string,required,allow:null\",\"projectName\": \"string,required,allow:null\",\"requester\": \"string,required,allow:null\",\"requesterPhoneNumber\": \"string,required,allow:null\",\"status\": \"string,required,allow:null\",\"successIndicators\": \"string,required,allow:null\",\"userEUA\": \"string,required,allow:null\",\"collaborationNeeded\": \"string,required,allow:null\",\"responseToGRTFeedback\": \"string,required,allow:null\",\"businessSolutions\": \"schema:BusinessCaseSolutionV05\"}", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"name": "BusinessCaseSolutionV05", "schema": "{\"acquisitionApproach\": \"string,required,allow:null\",\"cons\": \"string,required,allow:null\",\"costSavings\": \"string,required,allow:null\",\"hasUI\": \"string,required,allow:null\",\"hostingCloudServiceType\": \"string,required,allow:null\",\"hostingLocation\": \"string,required,allow:null\",\"hostingType\": \"string,required,allow:null\",\"pros\": \"string,required,allow:null\",\"securityIsApproved\": \"boolean,required,strict\",\"securityIsBeingReviewed\": \"string,required,allow:null\",\"solutionType\": \"string,required,allow:null\",\"summary\": \"string,required,allow:null\",\"title\": \"string,required,allow:null\",\"targetContractAward\": \"string,required,allow:null\",\"targetCompletionDate\": \"string,required,allow:null\",\"zeroTrustAlignment\": \"string,required,allow:null\",\"hostingCloudStrategy\": \"string,required,allow:null\",\"workforceTrainingReqs\": \"string,required,allow:null\",\"lifecycleCostLines\": \"schema:LifecycleCostlineV05\"}", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"name": "LifecycleCostlineV05", "schema": "{\"cost\": \"string,required,allow:null\",\"id\": \"string,required,allow:null\",\"phase\": \"string,required,allow:null\",\"solution\": \"string,required,allow:null\",\"year\": \"string,required,allow:null\"}", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}]