[{"globalName": "alfabet.api.user", "globalValue": "cedar_alfabet_api_01", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.api.url", "globalValue": "http://ip-10-235-62-86.ec2.internal/ALFABET/api", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.user", "globalValue": "SVC-ISPG-CEDAR_D", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.instance", "globalValue": "CFACTS3", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.rest.url", "globalValue": "https://cfactsdev.cms.gov", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.report.guid.ato", "globalValue": "6A40B41A-F2FA-428D-9CA9-B3F1813EDEDD", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.report.page.size.ato", "globalValue": "50", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.indicator.cntTtlNonPrivUsrPop", "globalValue": "190-169-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.indicator.cntTtlPrivUsrPop", "globalValue": "190-170-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.indicator.cntTtlUsrPop", "globalValue": "190-171-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.appGroup.atoAppGroup", "globalValue": "157-825-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.currency.usd", "globalValue": "653-2-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "technopedia.api.url", "globalValue": "http://api.technopedia.com:48000/api/v1/", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.evaluation.cms", "globalValue": "15-81-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "ldap.api.principal", "globalValue": "uid=cedar_dev2,ou=system accounts,dc=cms,dc=hhs,dc=gov", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "ldap.api.url", "globalValue": "ldaps://ldap-aws-app-d.cms.gov:11636", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.refstr.indicator.numFedSupFte", "globalValue": "190-117-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.refstr.indicator.numContSupFte", "globalValue": "190-118-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.refstr.indicator.numDirSysUsr", "globalValue": "190-119-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.refstr.evaluation.cms", "globalValue": "15-81-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.projectArch.id", "globalValue": "590", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.contractDeliverable.id", "globalValue": "22", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.application.id", "globalValue": "326", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.deployment.id", "globalValue": "351", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.survey.numOfPages", "globalValue": "9", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cedar.api.budget.maxResults", "globalValue": "5000", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cedar.api.budget.year", "globalValue": "2022", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "nt.util.notifications.local.publish", "globalValue": "TRUE", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.indicator.fips199OverallImpactRating", "globalValue": "190-181-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.dl", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "devSmtpFrom", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.distro", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "notifications.default.from", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.api.report.date.filter.ato", "globalValue": "10000", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "deploy.dummy.variable", "globalValue": "DEV", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.evaluation.fips", "globalValue": "15-93-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.sorn.hhs.query", "globalValue": "?conditions%5Bagencies%5D%5B%5D=health-and-human-services-department&conditions%5Bterm%5D=SORN+", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.sorn.base.url", "globalValue": "https://www.federalregister.gov/documents/search", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.sorn.cms.query", "globalValue": "?conditions%5Bagencies%5D%5B%5D=centers-for-medicare-medicaid-services&conditions%5Bterm%5D=", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.uri.id", "globalValue": "218", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.hostname", "globalValue": "systemcensus.cedardev.cms.gov", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notes.notification.enabled", "globalValue": "TRUE", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notes.notification.includeHistory", "globalValue": "TRUE", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notes.notification.exchange.reviewers.emails", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notification.default.from", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notification.default.cc", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notes.notification.exchange.enabled", "globalValue": "TRUE", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.supportContacts.id", "globalValue": "814", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.roletype.qa", "globalValue": "238-55-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.refstr.roletype.da", "globalValue": "238-56-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.notes.notification.default.to", "globalValue": "<EMAIL>,<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.reviewer.distro", "globalValue": "<EMAIL>", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cedar.core.domain.models", "globalValue": "CMS ARM;CMS BRM;CMS DRM;FEA ARM;FEA BRM;FEA DRM", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cedar.core.domain.model.levels", "globalValue": "CMS ARM;Domain;Area;Category|CMS BRM;Area;SubFunction|CMS DRM;Area;Category;Subcategory|FEA ARM;Domain;Area;Category|FEA BRM;MissionSector;BusinessFunction;Services|FEA DRM;Domain;Area;Category", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.relations.id", "globalValue": "139", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "census.fiscal.year", "globalValue": "25", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "cfacts.ato.system.version", "globalValue": "22", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "testGlobalVariable", "globalValue": "new value", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "ldap.api.person.find.limit", "globalValue": "20", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.dev", "globalValue": "143-45-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.opsAndMaint", "globalValue": "143-46-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.helpDeskCallCenter", "globalValue": "143-47-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.planningSupport", "globalValue": "143-49-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.softwareLicenses", "globalValue": "143-48-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.infrastructure", "globalValue": "143-50-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.serviceTools", "globalValue": "143-51-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.other", "globalValue": "143-52-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "easi.refstr.costType.unknown", "globalValue": "143-53-0", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.easiFundingSource.id", "globalValue": "867", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.person.external.source", "globalValue": "CMS_LDAP_DEV", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "alfabet.class.peripheral.id", "globalValue": "152", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "sparx.api.url", "globalValue": "https://sparxea.cedardev.cms.gov/", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "sparx.api.path", "globalValue": "CMS_Dev_Model/oslc/am", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}, {"globalName": "sparx.api.uid", "globalValue": "api-admin", "createdBy": "TST1", "createdDate": "2025-03-19 10:36:00-04", "updatedBy": "TST1", "updatedDate": "2025-03-19 10:36:00-04"}]