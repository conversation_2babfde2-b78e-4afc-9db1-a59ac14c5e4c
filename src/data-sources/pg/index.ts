import {
  get,
  isEmpty,
  isError,
  isNumber,
  isNaN,
} from 'lodash';
import {
  FindOptions,
  DestroyOptions,
  UpdateOptions,
  Model,
  Options,
  Sequelize,
  UpsertOptions,
} from 'sequelize';
import { PgDataOptions, CMSApp } from '../../types';
import DataSource from '../data-source';
import { getSecret } from '../../subsystems/secrets';
import { tryAsync } from '../../utils/general';
import { initModels } from '../models';
import models from './models';

class PgData extends DataSource {
  app: CMSApp;
  db: Sequelize | null = null;
  name: string;
  secretString: string;
  databaseName: string = '';
  notImplementedError: string = 'Method not implemented.';

  constructor(options: PgDataOptions) {
    super(options);
    this.app = get(options, 'app');
    this.name = get(options, 'name', '');
    this.secretString = get(options, 'secretString', '');

    if (!this.app || !this.name || !this.secretString) {
      throw new TypeError('Invalid Options Provided');
    }
  }

  createDatabaseIfNotExists = async (
    client: Sequelize,
    dbName: string,
  ): Promise<boolean | Error> => {
    const queryInterface = client.getQueryInterface();
    const [createError] = await tryAsync(queryInterface.createDatabase(`${dbName}`));
    if (isError(createError)) {
      const message = get(createError, 'message', '');
      if (message.indexOf('already exists') === -1) {
        this.app.logger.error({ msg: 'createDB error', message, createError });
        client.close();
        return createError;
      }
    }

    client.close();
    return true;
  };

  initDb = async (): Promise<void | Error> => {
    const secrets = await getSecret(this.app, this.secretString);
    if (isError(secrets)) {
      const secretsErrorMsg = `Error getting ${this.name} secrets`;
      this.app.logger.error({ msg: secretsErrorMsg, secrets });
      return new Error(secretsErrorMsg);
    }

    const dbUser = get(secrets, `${this.name.toUpperCase()}_DB_USER`);
    const dbPass = get(secrets, `${this.name.toUpperCase()}_DB_PASS`);
    const databaseHost = get(secrets, `${this.name.toUpperCase()}_HOST`);
    this.databaseName = get(secrets, `${this.name.toUpperCase()}_DB`, 'postgres');
    const rawPort = parseInt(get(secrets, `${this.name.toUpperCase()}_PORT`, '3000'), 10);
    if (isNaN(rawPort)) {
      const portError = new Error('Port is misconfigured');
      this.app.logger.error(portError);
      return portError;
    }
    const databasePort = rawPort;

    if (!databaseHost || !databasePort || !dbUser || !dbPass) {
      const misconfiguredError = new Error(`Misconfigured database credentials: ${this.name}`);
      this.app.logger.error(misconfiguredError);
      return misconfiguredError;
    }

    const options: Options = {
      pool: {
        max: 10,
        idle: 10000,
      },
      logging: false,
      define: {
        timestamps: false,
      },
      dialect: 'postgres',
      host: databaseHost,
      port: databasePort,
    };

    const createClient = new Sequelize('postgres', dbUser, dbPass, options);
    if (isError(createClient)) {
      const clientErrorMsg = 'Invalid Connection to the database';
      this.app.logger.error({ msg: clientErrorMsg, createClient });
      this.db = null;
      return new Error(clientErrorMsg);
    }

    const createDbStatus = await this.createDatabaseIfNotExists(createClient, this.databaseName);
    if (isError(createDbStatus)) {
      this.db = null;
      return createDbStatus;
    }

    const connectedDb = new Sequelize(this.databaseName, dbUser, dbPass, options);
    const [critAuthError, dbAuth] = await tryAsync(connectedDb.authenticate());

    if (critAuthError || isError(dbAuth)) {
      const connectionErrorMsg = `Error connecting to ${this.databaseName} database`;
      this.app.logger.error({ msg: connectionErrorMsg, error: critAuthError || dbAuth });
      if (this.db) {
        await this.db.close();
        this.db = null;
      }

      return new Error(connectionErrorMsg);
    }

    this.db = connectedDb;
    this.app.logger.info({ msg: `Connected to ${this.databaseName} database` });
    const [critInitError, processedModels] = await tryAsync(initModels(this.app, this.db, models));
    if (critInitError || isError(processedModels)) {
      const initErrorMsg = `Unable to init models for ${this.name}`;
      this.app.logger.error({ msg: initErrorMsg, error: critInitError || processedModels });
      await this.db.close();
      this.db = null;
      return new Error(initErrorMsg);
    }

    const [critSyncError, sync] = await tryAsync(this.db.sync());
    if (critSyncError || isError(sync)) {
      const syncErrorMsg = 'There was an error syncing models to the database';
      this.app.logger.error({ msg: syncErrorMsg, error: critSyncError || sync });
      await this.db.close();
      this.db = null;
      return new Error(syncErrorMsg);
    }

    return undefined;
  };

  checkDbAndModel = (model: string) => {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    const calledModel = get(this.db, `models.${model}`, null);
    if (!calledModel) {
      throw new Error('The provided model could not be found in the initialized models');
    }

    return calledModel;
  };

  closeConnection = async () => {
    if (this.db) {
      const [critError, error] = await tryAsync(this.db.close());
      if (critError || error) {
        this.db = null;
        return false;
      }

      return true;
    }

    return false;
  };

  deleteOne = async <TModel extends string,
  TParams extends Record<string, string | number>,
  TOptions extends DestroyOptions>(
    model: TModel,
    parameter: TParams,
    options: TOptions = {} as TOptions,
  ): Promise<'success' | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.destroy({
      ...options,
      where: parameter,
    });

    const resultProc = (result: number): 'success' | 'failed' => {
      if (result !== 1) {
        return 'failed';
      }
      return 'success';
    };

    return this.processResult(query, resultProc) as Promise<'success' | 'failed'>;
  };

  drop = async (model: string) => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.drop();
    const resultProc = (result: Error | string | void) => result;
    return this.processResult(query, resultProc);
  };

  findAll = async (
    model: string,
    options: FindOptions | object = {},
  ): Promise<unknown[]> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findAll(options);

    const resultProc = (results: Model[]): unknown[] => {
      if (isEmpty(results)) {
        return [];
      }

      return results.map((item) => get(item, 'dataValues', {}));
    };

    return this.processResult(query, resultProc) as Promise<unknown[]>;
  };

  findAllByParameter(
    model: string,
    parameters: Record<string, unknown>,
    options: FindOptions | object = {},
  ): Promise<unknown[]> {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findAll({
      ...options,
      where: parameters,
    });

    const resultProc = (results: Model[]): unknown[] => {
      if (isEmpty(results)) {
        return [];
      }

      return results.map((item) => get(item, 'dataValues', {}));
    };

    return this.processResult(query, resultProc) as Promise<unknown[]>;
  }

  findOne = async (
    model: string,
    parameter: Record<string, unknown>,
    options: FindOptions = {},
  ): Promise<unknown> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findOne({
      ...options,
      where: parameter,
    });

    const resultProc = (result: Model | null): unknown => {
      if (!result) return null;

      const dataValues = get(result, 'dataValues', null);
      if (dataValues && get(dataValues, 'id', null)) {
        return dataValues;
      }

      return result;
    };

    return this.processResult(query, resultProc);
  };

  insertMany = async (
    model: string,
    objects: Record<string, unknown>[],
    options: object = {},
  ): Promise<number[] | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    if (isEmpty(objects)) {
      return [];
    }

    const query = calledModel.bulkCreate(objects, options);

    const resultProc = (dbResponse: Model[]): number[] | 'failed' => {
      const ids = dbResponse.map((result) => get(result, 'dataValues.id', null)).filter(isNumber);

      if (isEmpty(ids)) {
        this.app.logger.error({ error: 'PG Insert Many failed to insert any items' });
        return 'failed';
      }

      return ids;
    };

    return this.processResult(query, resultProc) as Promise<number[] | 'failed'>;
  };

  insertOne = async (
    model: string,
    newObj: Record<string, unknown>,
    options: object = {},
  ): Promise<number | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.create(newObj, options);

    const resultProc = (result: Model): number | 'failed' => {
      const id = get(result, 'dataValues.id', null);
      if (!id) {
        this.app.logger.error({ error: 'PG Insert One failed to insert the item' });
        return 'failed';
      }
      return id;
    };

    return this.processResult(query, resultProc) as Promise<number | 'failed'>;
  };

  isDbReady(): boolean {
    if (!this.db) {
      return false;
    }

    return true;
  }

  processResult = async <Q>(query: Promise<Q>, resultProc: (result: Q) => unknown) => {
    const [dbError, result] = await tryAsync(query);
    if (dbError || isError(result)) {
      this.app.logger.error(dbError || result);
      return dbError || result;
    }
    return resultProc(result as Q);
  };

  seedDb = async (
    model: string,
    data: Record<string, unknown>[],
    options: object = {},
  ) => {
    const [dbError, result] = await tryAsync(this.insertMany(model, data, options));
    if (dbError || isError(result) || result === 'failed') {
      this.app.logger.error({ error: dbError || result });
      return dbError || result;
    }
    return result;
  };

  truncate = async (
    model: string,
  ) => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.truncate({ restartIdentity: true });
    const resultProc = (result: Error | string | void) => result;
    return this.processResult(query, resultProc);
  };

  updateOne = async (
    model: string,
    updatedObj: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpdateOptions | object = {},
  ): Promise<'success' | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.update(updatedObj, {
      ...options,
      where: filter,
    });

    const resultProc = (result: [number]): 'success' | 'failed' => (
      result[0] === 1 ? 'success' : 'failed'
    );

    return this.processResult(query, resultProc) as Promise<'success' | 'failed'>;
  };

  updateMany = async (
    model: string,
    updatedObjs: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpdateOptions | object = {},
  ): Promise<'success' | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.update(updatedObjs, {
      ...options,
      where: filter,
    });

    const resultProc = (result: [number]): 'success' | 'failed' => (
      result[0] === 1 ? 'success' : 'failed'
    );

    return this.processResult(query, resultProc) as Promise<'success' | 'failed'>;
  };

  upsert = async (
    model: string,
    object: Record<string, unknown>,
    filter: Record<string, unknown>,
    options: UpsertOptions | object = {},
  ): Promise<'success' | 'failed' | number> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.upsert(object, {
      ...options,
      conflictWhere: filter,
    });

    const resultProc = ([instance, created]: [Model, boolean | null]): 'success' | 'failed' | number => {
      const id = get(instance, 'dataValues.id', null) as number | null;
      if (id) {
        return id;
      }

      if (created) {
        return 'success';
      }

      return 'failed';
    };

    return this.processResult(query, resultProc) as Promise<'success' | 'failed' | number>;
  };
}

export default PgData;
