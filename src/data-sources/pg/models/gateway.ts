import { INTEGER, STRING, DATE } from 'sequelize';
import { ModelDefinition } from '../../../types';

const gateway: ModelDefinition = {
  name: 'gateway',
  define: {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    key: {
      type: STRING,
      allowNull: false,
    },
    createdBy: {
      type: STRING,
      allowNull: false,
    },
    createdDate: {
      type: DATE,
      allowNull: false,
    },
  },
};

export default gateway;
