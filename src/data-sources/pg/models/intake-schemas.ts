import { INTEGER, STRING, DATE } from 'sequelize';
import { ModelDefinition } from '../../../types';

const intakeSchemas: ModelDefinition = {
  name: 'intakeSchemas',
  define: {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: STRING,
      allowNull: false,
    },
    schema: {
      type: STRING(65534),
      allowNull: false,
    },
    createdBy: {
      type: STRING,
      allowNull: false,
    },
    createdDate: {
      type: DATE,
      allowNull: false,
    },
    updatedBy: {
      type: STRING,
      allowNull: false,
    },
    updatedDate: {
      type: DATE,
      allowNull: false,
    },
  },
};

export default intakeSchemas;
