import { STRING, JSON, DATE } from 'sequelize';
import { ModelDefinition } from '../../../types';

const sessions: ModelDefinition = {
  name: 'sessions',
  define: {
    sid: {
      type: STRING,
      unique: true,
      allowNull: false,
      primaryKey: true,
    },
    sess: {
      type: JSON,
      allowNull: false,
    },
    expire: {
      type: DATE,
      allowNull: false,
    },
  },
};

export default sessions;
