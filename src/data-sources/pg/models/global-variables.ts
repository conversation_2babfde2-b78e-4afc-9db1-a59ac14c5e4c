import { STRING, INTEGER, DATE } from 'sequelize';
import { ModelDefinition } from '../../../types';

const globalVariables: ModelDefinition = {
  name: 'globalVariables',
  define: {
    id: {
      type: INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    globalName: {
      type: STRING,
      allowNull: false,
    },
    globalValue: {
      type: STRING,
      allowNull: false,
    },
    createdBy: {
      type: STRING,
      allowNull: false,
    },
    createdDate: {
      type: DATE,
      allowNull: false,
    },
    updatedBy: {
      type: STRING,
      allowNull: false,
    },
    updatedDate: {
      type: DATE,
      allowNull: false,
    },
  },
};

export default globalVariables;
