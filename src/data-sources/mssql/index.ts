// Temporarily disabling this rule till all required functions are found
/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  get,
  isEmpty,
  isError,
  isNaN,
  isNumber,
  isString,
  isNull,
  isUndefined,
} from 'lodash';
import {
  FindOptions,
  Model,
  Options,
  Sequelize,
} from 'sequelize';
import {
  CMSApp,
  MssqlDataOptions,
  StoredProcedureData,
  StoredProcedureParam,
  StoredProcedureQuery,
  Where,
} from '../../types';
import DataSource from '../data-source';
import { getSecret } from '../../subsystems/secrets';
import { processWhere } from '../../utils/db-helpers';
import { tryAsync } from '../../utils/general';
import Parameterize, {
  ParameterizeOrderByConfig,
  ParameterizeLiterals,
} from '../../utils/db-helpers/parameterize';
import Messages from '../../utils/constants/messages';

export interface IWmInsertResult {
  insertedIds: number[];
  affectedRows: number;
}

type WMInsertResult = { [key: string]: number };

export interface IQueryViewTypedFixedOptions {
  where?: Where;
  orderBy?: ParameterizeOrderByConfig;
  distinct?: boolean;
  limit?: number;
}

class MssqlData extends DataSource {
  app: CMSApp;
  db: Sequelize | null = null;
  name: string;
  secretString: string;
  dbName: string = '';
  databaseName: string = '';
  notImplementedError: string = 'Method not implemented.';
  msSqlTypes: string[] = [
    'tinyint',
    'smallint',
    'int',
    'bigint',
    'bit',
    'money',
    'smallmoney',
    'float',
    'real',
    'date',
    'time',
    'datetime',
    'datetimeoffset',
    'datetime',
    'smalldatetime',
    'text',
    'ntext',
    'json',
    'rowversion',
    'sql_variant',
    'uniqueidentifier',
    'xml',
  ];
  msSqlTypesWithParams: string[] = [
    'decimal',
    'numeric',
    'char',
    'varchar',
    'nchar',
    'nvarchar',
    'binary',
    'varbinary',
    'vector',
  ];

  constructor(options: MssqlDataOptions) {
    super(options);
    this.app = get(options, 'app');
    this.name = get(options, 'name', '');
    this.secretString = get(options, 'secretString', '');
    this.dbName = get(options, 'dbName');

    if (!this.app || !this.name) {
      throw new TypeError('Invalid Options Provided');
    }
  }

  isDbReady(): boolean {
    if (!this.db) {
      return false;
    }

    return true;
  }

  checkDbAndModel = (model: string) => {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    const calledModel = get(this.db, `models.${model}`, null);
    if (!calledModel) {
      throw new Error('The provided model could not be found in the initialized models');
    }

    return calledModel;
  };

  processResult = async <Q>(query: Promise<Q>, resultProc: (result: Q) => unknown) => {
    const [dbError, result] = await tryAsync(query);
    if (dbError || isError(result)) {
      this.app.logger.error(dbError || result);
      return dbError || result;
    }
    return resultProc(result as Q);
  };

  initDb = async (): Promise<void | Error> => {
    const secrets = await getSecret(this.app, this.secretString);
    if (isError(secrets)) {
      const secretsErrorMsg = `Error getting ${this.name} secrets`;
      this.app.logger.error({ msg: secretsErrorMsg, secrets });
      return new Error(secretsErrorMsg);
    }

    this.databaseName = get(secrets, this.dbName);
    const dbUser = get(secrets, 'db_user');
    const dbPass = get(secrets, 'db_pass');
    const databaseHost = get(secrets, 'db_host');
    const rawPort = parseInt(get(secrets, 'db_port', ''), 10);
    if (isNaN(rawPort)) {
      const portError = new Error('Port is misconfigured');
      this.app.logger.error(portError);
      return portError;
    }
    const databasePort = rawPort;

    if (!this.databaseName || !databaseHost || !databasePort || !dbUser || !dbPass) {
      const misconfiguredError = new Error(`Misconfigured database credentials: ${this.name}`);
      this.app.logger.error(misconfiguredError);
      return misconfiguredError;
    }

    const options: Options = {
      pool: {
        max: 10,
        idle: 10000,
      },
      logging: false,
      // MSSQL specific logging flag
      // Comment out logging above and uncomment logQueryParameters to see queries made by MSSQL
      // logQueryParameters: true,
      define: {
        timestamps: false,
      },
      database: this.databaseName,
      dialect: 'mssql',
      host: databaseHost,
      port: databasePort,
    };

    const connectedDb = new Sequelize(this.databaseName, dbUser, dbPass, options);
    const [critAuthError, dbAuth] = await tryAsync(connectedDb.authenticate());

    if (critAuthError || isError(dbAuth)) {
      const authError = new Error(`Error connecting to ${this.name} database`);
      this.app.logger.error(authError);
      if (this.db) {
        await this.db.close();
        this.db = null;
      }
      return authError;
    }

    this.db = connectedDb;
    this.app.logger.info(`Connected to ${this.name} database`);
    return dbAuth;
  };

  /**
   * Temporary wrapper that doesn't return a (Object, Number) tuple.
   */
  public queryViewTypedFixed = async<T>(
    view: string,
    columns?: string[],
    options?: IQueryViewTypedFixedOptions,
  ): Promise<T> => {
    const result = await this.queryViewTyped<[T, number]>(
      view,
      columns,
      options?.where,
      options?.orderBy,
      options?.distinct,
      options?.limit,
    );

    return result[0];
  };

  // noinspection OverlyComplexFunctionJS
  queryViewTyped = async<T>(
    view: string,
    columns?: string[],
    where?: Where,
    orderBy?: ParameterizeOrderByConfig,
    distinct?: boolean,
    limit?: number,
  ): Promise<T> => {
    if (!this.db) {
      throw new Error(Messages.db_connection_invalid);
    }

    if (limit && !isNumber(limit)) {
      throw new Error(`queryViewTyped: value ${limit} provided for limit must be a number`);
    }

    const queryColumns: string[] = columns?.length ? columns : ['*'];

    let whereQuery = '';
    let params = {};
    if (where) {
      const whereResult = processWhere(where);
      if (isError(whereResult)) {
        throw whereResult;
      }

      const {
        query: whereQueryResult,
        params: whereParams,
      } = whereResult;

      whereQuery = whereQueryResult;
      params = {
        ...whereParams,
      };
    }

    let orderByClause: string = '';
    if (orderBy) {
      const result = Parameterize.orderBy(orderBy);
      orderByClause = result.query;

      params = {
        ...params,
        ...result.params,
      };
    }

    const distinctClause = distinct ? 'DISTINCT ' : '';
    const limitClause = limit ? `TOP ${limit} ` : '';

    const sql = `
      SELECT ${limitClause}
        ${distinctClause}${queryColumns.join(', ')}
      FROM
        ${view}
      ${whereQuery}
      ${orderByClause}
    `;
    const result = await this.db.query(sql, { replacements: params });

    return result as T;
  };

  queryView = async (
    view: string,
    columns?: string | string[],
    where?: Where,
    orderBy?: ParameterizeOrderByConfig,
  ) => {
    if (!this.db) {
      throw new Error('Database connection invalid');
    }

    let queryColumns: string[] = ['*'];
    if (columns) {
      queryColumns = isString(columns) ? [columns] : columns;
    }

    let whereQuery = '';
    let params = {};
    if (where) {
      const whereResult = processWhere(where);
      if (isError(whereResult)) {
        throw whereResult;
      }

      const {
        query: whereQueryResult,
        params: whereParams,
      } = whereResult;

      whereQuery = whereQueryResult;
      params = {
        ...whereParams,
      };
    }

    let orderByClause: string = '';
    if (orderBy) {
      const result = Parameterize.orderBy(orderBy);
      orderByClause = result.query;

      params = {
        ...params,
        ...result.params,
      };
    }

    const sql = `
      SELECT
        ${queryColumns.join(', ')}
      FROM
        ${view}
      ${whereQuery}
      ${orderByClause}
    `;
    const query = this.db.query(sql, { replacements: params });
    const resultProc = (results: unknown[]) => results;

    return this.processResult(query, resultProc);
  };

  processSPParams(params: StoredProcedureParam[], throwError = false): string | Error {
    const declarations: string[] = [];
    const paramErrors = new Error();
    params.every((item) => {
      const name = get(item, 'name');
      if (!name) {
        paramErrors.message = 'No name provided for one of the stored procedure parameters';
        return false;
      }

      const type = get(item, 'type');
      if (!type) {
        paramErrors.message = `No type provided for stored procedure parameter ${name}`;
        return false;
      }

      if (!this.msSqlTypes.includes(type) && !this.msSqlTypesWithParams.includes(type)) {
        paramErrors.message = `The provided type ${type} for stored procedure parameter ${name} is invalid`;
        return false;
      }

      if (this.msSqlTypesWithParams.includes(type)) {
        const param = get(item, 'param');
        const param2 = get(item, 'param2');
        if (!param) {
          paramErrors.message = `The provided type ${type} for stored procedure parameter ${name} requires a parameter`;
          return false;
        }

        if (!param2 && (isNumber(param) || param === 'max')) {
          declarations.push(`@${name} ${type}(${param})`);
          return true;
        }

        const twoParamTypes = ['decimal', 'numeric'];
        if (twoParamTypes.includes(type) && isNumber(param) && isNumber(param2)) {
          declarations.push(`@${name} ${type}(${param}, ${param2})`);
          return true;
        }

        paramErrors.message = `The provided type ${type} for stored procedure parameter ${name} requires a parameter of type number or the string max`;
        return false;
      }

      declarations.push(`@${name} ${type}`);
      return true;
    });

    if (paramErrors.message !== '') {
      if (throwError) {
        throw paramErrors;
      }
      return paramErrors;
    }

    if (isEmpty(declarations)) {
      return '';
    }

    return `, ${declarations.join(', ')}`;
  }

  static processSPData(data: StoredProcedureData[], throwError = false): string | Error {
    const query: string[] = [];
    const dataErrors = new Error();

    data.every((item) => {
      const name = get(item, 'name');
      if (!name) {
        dataErrors.message = 'No name provided for one of the stored procedure data';
        return false;
      }

      // FIXME: Add injection testing to the value
      const value = get(item, 'value');
      if (isUndefined(value)) {
        dataErrors.message = `No value provided for stored procedure data ${name}`;
        return false;
      }

      if (isNull(value)) {
        query.push(`@${name} = NULL`);
        return true;
      }

      const isOutput = get(item, 'isOutput', false);
      if (isOutput) {
        query.push(`@${name} = @${value} OUTPUT`);
        return true;
      }

      query.push(`@${name} = N'${value}'`);
      return true;
    });

    if (dataErrors.message !== '') {
      if (throwError) {
        throw dataErrors;
      }
      return dataErrors;
    }

    if (isEmpty(query)) {
      return '';
    }

    return ` ${query.join(', ')}`;
  }

  static processSPQueries(queries: StoredProcedureQuery[], throwError = false): string | Error {
    const query: string[] = [];
    const queryErrors = new Error();
    queries.every((item) => {
      const key = get(item, 'resultKey');
      if (!key) {
        queryErrors.message = 'No result key provided for query';
        return false;
      }

      const name = get(item, 'paramName');
      if (!name) {
        queryErrors.message = `No parameter name provided for the result key ${key}`;
        return false;
      }

      const wrapAsParam = get(item, 'wrapAsParam', false);
      if (wrapAsParam) {
        query.push(`'${key}' = N'@${name}'`);
        return true;
      }

      query.push(`'${key}' = @${name}`);
      return true;
    });

    if (queryErrors.message !== '') {
      if (throwError) {
        throw queryErrors;
      }
      return queryErrors;
    }

    if (isEmpty(query)) {
      return '';
    }

    return `, ${query.join(', ')}`;
  }

  public async queryStoredProceduresTyped<T>(
    storedProcedure: string,
    params?: StoredProcedureParam[],
    data?: StoredProcedureData[],
    resultQueries?: StoredProcedureQuery[],
  ): Promise<T> {
    if (!this.db) {
      throw new Error('Database connection invalid');
    }

    const query = `
      DECLARE @return_value int${this.processSPParams(params ?? [], true)}
      EXECUTE @return_value = ${storedProcedure}${data ? MssqlData.processSPData(data, true) : ''}
      SELECT 'queryStatus' = @return_value${MssqlData.processSPQueries(resultQueries ?? [], true)}
    `;

    const result = await this.db.query(query);

    return result as T;
  }

  queryStoredProcedures = async (
    storedProcedure: string,
    params?: StoredProcedureParam[],
    data?: StoredProcedureData[],
    resultQueries?: StoredProcedureQuery[],
  ) => {
    if (!this.db) {
      throw new Error('Database connection invalid');
    }

    let spDeclare = 'DECLARE @return_value int';

    if (params) {
      const processedParams = this.processSPParams(params);
      if (isError(processedParams)) {
        return processedParams;
      }

      spDeclare = `${spDeclare}${processedParams}`;
    }

    let spData: string = '';
    if (data) {
      const processedData = MssqlData.processSPData(data);
      if (isError(processedData)) {
        return processedData;
      }

      spData = processedData;
    }

    let spResults: string = 'SELECT \'queryStatus\' = @return_value';
    if (resultQueries) {
      const processedQueries = MssqlData.processSPQueries(resultQueries);
      if (isError(processedQueries)) {
        return processedQueries;
      }

      spResults = `${spResults}${processedQueries}`;
    }

    const query = this.db.query(`${spDeclare} EXECUTE @return_value = ${storedProcedure}${spData} ${spResults}`);
    const resultProc = (results: unknown[]) => results;

    return this.processResult(query, resultProc);
  };

  // model: string, params: Record<string, number | string>, options: DestroyOptions
  deleteOne(): Promise<'failed' | 'success'> {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  findOne(
    model: string,
    parameter: Record<string, unknown>,
    options: FindOptions | object,
  ): Promise<unknown> {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findOne({
      ...options,
      where: parameter,
    });

    const resultProc = (result: Model | null): unknown => {
      if (!result) return null;

      const dataValues = get(result, 'dataValues', null);
      if (dataValues && get(dataValues, 'id', null)) {
        return dataValues;
      }

      return result;
    };

    return this.processResult(query, resultProc);
  }

  findAll(model: string, options: FindOptions | object): Promise<unknown[]> {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findAll(options);

    const resultProc = (results: Model[]): unknown[] => {
      if (isEmpty(results)) {
        return [];
      }

      return results.map((item) => get(item, 'dataValues', {}));
    };

    return this.processResult(query, resultProc) as Promise<unknown[]>;
  }

  findAllByParameter(
    model: string,
    parameters: Record<string,
    unknown>,
    options: FindOptions | object,
  ): Promise<unknown[]> {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.findAll({
      ...options,
      where: parameters,
    });

    const resultProc = (results: Model[]): unknown[] => {
      if (isEmpty(results)) {
        return [];
      }

      return results.map((item) => get(item, 'dataValues', {}));
    };

    return this.processResult(query, resultProc) as Promise<unknown[]>;
  }

  insertMany = async (
    model: string,
    objects: Record<string, unknown>[],
    options: object = {},
  ): Promise<number[] | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    if (isEmpty(objects)) {
      return [];
    }

    const query = calledModel.bulkCreate(objects, options);

    const resultProc = (dbResponse: Model[]): number[] | 'failed' => {
      const ids = dbResponse.map((result) => get(result, 'dataValues.id', null)).filter(isNumber);

      if (isEmpty(ids)) {
        this.app.logger.error({ error: 'MSSQL Insert Many failed to insert any items' });
        return 'failed';
      }

      return ids;
    };

    return this.processResult(query, resultProc) as Promise<number[] | 'failed'>;
  };

  insertOne = async (
    model: string,
    newObj: Record<string, unknown>,
    options: object = {},
  ): Promise<number | 'failed'> => {
    const calledModel = this.checkDbAndModel(model);
    if (isError(calledModel)) {
      throw calledModel as Error;
    }

    const query = calledModel.create(newObj, options);

    const resultProc = (result: Model): number | 'failed' => {
      const id = get(result, 'dataValues.id', null);
      if (!id) {
        this.app.logger.error({ error: 'MSSQL Insert One failed to insert the item' });
        return 'failed';
      }
      return id;
    };

    return this.processResult(query, resultProc) as Promise<number | 'failed'>;
  };

  // model: string, updatedObj: Record<string, unknown>,
  // filter: Record<string, unknown>, options: UpdateOptions | object
  updateOne(): Promise<'success' | 'failed'> {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  // model: string, updatedObjs: Record<string, unknown>,
  // filter: Record<string, unknown>, options: UpdateOptions | object
  updateMany(): Promise<'success' | 'failed'> {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  // model: string, object: Record<string, unknown>,
  // filter: Record<string, unknown>, options: UpsertOptions | object
  upsert(): Promise<'success' | 'failed' | number> {
    const isDbReady = this.isDbReady();
    if (!isDbReady) {
      throw new Error('Database connection invalid');
    }

    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  async wmDelete(
    table: string,
    where: Where,
    options: object = {},
  ): Promise<number> {
    if (!this.db) {
      throw new Error('wmDelete: database connection invalid');
    }

    const whereResult = processWhere(where);
    if (isError(whereResult)) {
      this.app.logger.error({ error: whereResult });
      throw whereResult;
    }

    const sql = `DELETE FROM ${table}${whereResult.query}`;

    // Note: RAW is needed for metadata to return the number of affected rows.
    const [, metadata] = await this.db.query(sql, {
      replacements: whereResult.params,
      type: 'RAW',
      ...options,
    });

    if (!isNumber(metadata)) {
      throw new Error('wmDelete: failed to delete any items, metadata is not a number');
    }

    if (metadata === 0) {
      throw new Error('wmDelete: failed to delete any items, affected rows is zero');
    }

    return metadata;
  }

  async wmInsert(
    table: string,
    columns: string[],
    values: unknown[][],
    insertedIdColumn: string,
    literals: ParameterizeLiterals = {},
  ): Promise<IWmInsertResult> {
    if (!this.db) {
      throw new Error('Database connection invalid');
    }

    // Input validation.
    if (isEmpty(columns)) {
      throw new Error('No columns provided');
    }
    if (isEmpty(values)) {
      throw new Error('No values provided');
    }
    if (columns.length !== values[0].length) {
      throw new Error('Number of columns must match number of values in each record');
    }

    const [clause, replacements] = Parameterize.insert(columns, values, literals);
    // noinspection SqlResolve
    const sql = `
        INSERT INTO ${table} (${columns.join(', ')})
        OUTPUT INSERTED.${insertedIdColumn} AS id
        VALUES ${clause}
    `;

    const [result, metadata] = await this.db.query(sql, {
      replacements,
    });

    // Check for errors.
    if (isEmpty(result)) {
      throw new Error('wmInsert: inserted IDs is empty');
    }
    if (!metadata || metadata === 0) {
      throw new Error('wmInsert: failed to insert any items, metadata is empty');
    }

    // Extract the inserted IDs.
    const insertedIds: number[] = (result as WMInsertResult[]).map((item) => item.id);

    return { insertedIds, affectedRows: metadata as number };
  }

  async wmUpdate(
    table: string,
    columns: string[],
    values: unknown[],
    where: Where,
    parameterReplacements: Record<string, string> = {},
    options: object = {},
  ): Promise<number[] | Error> {
    if (!this.db) {
      throw new Error('Database connection invalid');
    }

    // Input validation
    if (isEmpty(columns)) {
      throw new Error('No columns provided');
    }
    if (isEmpty(values)) {
      throw new Error('No values provided');
    }
    if (columns.length !== values.length) {
      throw new Error('Number of columns must match number of values');
    }

    // Process WHERE clause
    const whereResult = processWhere(where);
    if (isError(whereResult)) {
      this.app.logger.error({ error: whereResult });
      return whereResult;
    }

    // Construct the SET clause with proper MSSQL parameter placeholders
    const setClause = columns.map((name, index) => {
      if (parameterReplacements[name]) {
        return `${name} = ${parameterReplacements[name]}`;
      }
      return `${name} = :p${index + 1}`;
    }).join(', ');

    const sql = `UPDATE ${table} SET ${setClause}${whereResult.query}`;

    // Create parameter object for values
    const flatValues = values.reduce<Record<string, unknown>>((acc, value, index) => {
      acc[`p${index + 1}`] = value;
      return acc;
    }, {});

    const query = this.db.query(sql, {
      replacements: {
        ...flatValues as Record<string, string | number>,
        ...whereResult.params,
      },
      type: 'UPDATE',
      ...options,
    });

    const resultProc = (results: unknown[]): number[] | Error => {
      if (isEmpty(results)) {
        this.app.logger.error({ error: 'MSSQL Update failed to update any items' });
        return new Error('wmUpdate, resultsProc: results is empty');
      }

      // Return the number of affected rows
      if (results.length !== 2) {
        return new Error(`wmUpdate, resultsProc: results has invalid length (${results.length} vs 2)`);
      }

      return [results[1] as number];
    };

    return this.processResult(query, resultProc) as Promise<number[] | Error>;
  }

  closeConnection = async (): Promise<void | boolean> => {
    if (this.db) {
      const [critError, error] = await tryAsync(this.db.close());
      if (critError || error) {
        this.db = null;
        return false;
      }

      return true;
    }

    return false;
  };
}

export default MssqlData;
