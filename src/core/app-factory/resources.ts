import { Express, Router } from 'express';
import {
  get,
  set,
  isEmpty,
  isString,
  isFunction,
  isError,
} from 'lodash';
import { ValidationError } from 'joi';
import {
  CMSApp,
  ResourceConfig,
  GetResourceConfig,
  InitResources,
  RegisterResources,
} from '../../types';
import { prependPathToResources, validateResourceConfig } from '../../utils/resources';

const mountResource = (app: CMSApp, resourceConfig: ResourceConfig): Error | boolean => {
  const method = get(resourceConfig, 'method');
  if (!method) {
    return new Error('Resource configuration is missing the method');
  }

  const path = get(resourceConfig, 'path');
  if (!path) {
    return new Error('Resource configuration is missing the path');
  }

  const resource = get(resourceConfig, 'resource');
  if (!resource) {
    return new Error('Resource configuration is missing the resource');
  }

  const isPublic = get(resourceConfig, 'public', false);
  if (isPublic) {
    app.resources.publicRoutes.push({
      path,
      method,
    });
  }

  app.resources.router[method](path, resource);

  return true;
};

const initResources: InitResources = (app: CMSApp) => {
  if (isEmpty(app.resources.registry)) {
    throw new Error('No resources found to initialize');
  }

  const mountErrors: Error[] = [];
  app.resources.registry.forEach((item) => {
    const mountResult = mountResource(app, item);
    if (isError(mountResult)) {
      app.logger.error(`An error occurred mounting ${get(item, 'path', 'unknown')}: ${mountResult.message}`);
      mountErrors.push(mountResult);
    }

    app.logger.info(`Mounting ${get(item, 'path', '')} to method ${get(item, 'method', '')}`);
  });

  if (!isEmpty(mountErrors)) {
    app.logger.error({ error: mountErrors });
    throw new Error('One or more resources failed to mount');
  }

  app.logger.info('Resource initialization complete');
  app.use('/', app.resources.router);
};

const registerResources: RegisterResources = async (
  app: CMSApp,
  path: string,
  resourceRouter: string | GetResourceConfig,
): Promise<Error | boolean> => {
  if (isEmpty(path)) {
    return new Error('The path cannot be empty');
  }

  // validate resourceRouter
  let getResourceConfig: GetResourceConfig;
  if (isString(resourceRouter)) {
    try {
      const { default: module } = await import(resourceRouter);
      getResourceConfig = module;
    } catch (importError) {
      return new Error('An error occurred while importing the resource router');
    }
  } else {
    getResourceConfig = resourceRouter;
  }

  if (!getResourceConfig) {
    return new Error('Unable to load resource module');
  }

  if (!isFunction(getResourceConfig)) {
    return new Error('The resource module does not have the getResourceConfig function');
  }

  const resourceConfig: ResourceConfig[] = getResourceConfig();
  const registryErrors: ValidationError[] = [];

  // Append path to each resource
  const resources = prependPathToResources(path, resourceConfig);
  if (isError(resources)) {
    return resources;
  }

  resources.forEach((resource) => {
    // validate resource
    const { error: registryError } = validateResourceConfig(resource);
    if (registryError) {
      registryErrors.push(registryError);
      return;
    }

    // Check for duplicate and replace
    let dupId: number = -1;
    const duplicate = app.resources.registry.find((item, idx) => {
      if (item.path === resource.path && item.method === resource.method) {
        dupId = idx;
        return true;
      }

      return false;
    });

    if (duplicate && dupId >= 0) {
      app.resources.registry.splice(dupId, 1);
    }

    app.resources.registry.push(resource);
  });

  if (!isEmpty(registryErrors)) {
    app.logger.error({ error: registryErrors });
    return new Error('One or more resources failed to validate their config');
  }

  return true;
};

const addResourceRegistry = (app: Express) => {
  const apiRouter = Router();
  set(app, 'resources.router', apiRouter);
  set(app, 'resources.initResources', initResources);
  set(app, 'resources.register', registerResources);
  set(app, 'resources.registry', []);
  set(app, 'resources.publicRoutes', []);
};

export {
  mountResource,
  initResources,
  registerResources,
  addResourceRegistry,
};
