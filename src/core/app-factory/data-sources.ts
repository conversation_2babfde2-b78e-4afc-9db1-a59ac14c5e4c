import { isError, set, get } from 'lodash';
import { Express } from 'express';
import { CMSApp, DataOptions, DataSourceType } from '../../types';
import { tryAsync } from '../../utils/general';

const registerDataSource = async <D extends DataSourceType, DO extends DataOptions>(
  app: CMSApp,
  dataSourceName: string,
  UserDataSource: new (options: DO) => D,
  dataSourceOptions?: Partial<DO>,
): Promise<void> => {
  const secretString = get(app, `config.systems.${dataSourceName}.dbSecretString`, '');
  // check if DataSource is a child of DataSource
  const dsOptions = {
    ...dataSourceOptions,
    app,
    name: dataSourceName,
    secretString,
  } as DO;
  const dataSource = new UserDataSource(dsOptions);
  set(app, `dataSources[${dataSourceName}]`, dataSource);
  // FIXME: Add child logger here for the new data source
  const [error, initResult] = await tryAsync(app.dataSources[dataSourceName].initDb());

  if (error) {
    app.logger.error({ error });
    throw error;
  }

  if (isError(initResult)) {
    app.logger.error({ error: initResult });
    throw initResult;
  }
};

const registerAppDataSources = (app: Express): void => {
  set(app, 'dataSources', {
    register: registerDataSource,
  });
};

export {
  registerDataSource,
  registerAppDataSources,
};
