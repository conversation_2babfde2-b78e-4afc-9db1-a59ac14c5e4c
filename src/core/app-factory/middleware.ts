import cors from 'cors';
import { readFileSync } from 'fs';
import {
  stat,
  unlink,
  open,
} from 'fs/promises';
import path from 'path';
import express, {
  Request,
  Response,
  NextFunction,
} from 'express';
import { get, isFunction } from 'lodash';
import swaggerUiExpress from 'swagger-ui-express';
import swaggerAutogen from 'swagger-autogen';
import { tryAsync } from '../../utils/general';
import { CMSApp, DataSourceType } from '../../types';

const addAppToReq = (app: CMSApp) => {
  const addAppToRequest = (req: Request, res: Response, next: NextFunction) => {
    req.systemApp = app as CMSApp;
    res.systemApp = app as CMSApp;
    next();
  };
  app.use(addAppToRequest);
};

const cleanupDataSources = (app: CMSApp) => (
  _req: Request,
  res: Response,
  next: NextFunction,
) => {
  res.on('finish', async () => {
    const dsEntries = Object.entries(app.dataSources) as [string, DataSourceType][];

    dsEntries.forEach(async ([key, ds]) => {
      if (key === 'register') return; // skip the helper itself

      if (isFunction(ds.closeConnection)) {
        try {
          await ds.closeConnection();
          app.logger.debug({ dataSource: key }, 'Closed data source connection');
        } catch (err) {
          app.logger.error({ dataSource: key, error: err }, 'Error closing data source connection');
        }
      }
    });
  });

  next();
};

const addApiDocs = async (app: CMSApp) => {
  const exampleId = '{550e8400-e29b-41d4-a716-446655440000}';
  const DOC = {
    openapi: '3.0.0',
    info: {
      title: 'WebMethods Replacement API',
      description: 'The CMS Enterprise Data and Analytics Repository (CEDAR) Core API',
      version: '1.0.0',
    },
    servers: [{
      url: 'http://localhost:3000',
      description: 'Local server',
    }],
    security: [{
      GatewayKey: [],
      JWTKey: [],
    }],
    components: {
      securitySchemes: {
        JWTKey: {
          type: 'apiKey',
          in: 'header',
          name: 'x-jwt-key',
        },
        GatewayKey: {
          type: 'apiKey',
          in: 'header',
          name: 'x-Gateway-APIKey',
        },
      },
      '@schemas': {
        ErrorResponse: {
          type: 'object',
          required: ['error', 'message'],
          properties: {
            error: {
              type: 'string',
              example: 'Unable to retrieve system application',
            },
            message: {
              type: 'string',
              example: 'Internal Service Error',
            },
          },
        },
        Unauthorized: {
          type: 'object',
          required: ['error'],
          properties: {
            error: {
              type: 'string',
              example: 'Unauthorized access',
            },
          },
        },
        NotFound: {
          type: 'object',
          required: ['error'],
          properties: {
            error: {
              type: 'string',
              example: 'Not Found',
            },
          },
        },
        Conflict: {
          type: 'object',
          required: ['error'],
          properties: {
            error: {
              type: 'string',
              example: 'Conflict',
            },
          },
        },
        BadRequest: {
          type: 'object',
          required: ['message'],
          properties: {
            message: {
              type: 'string',
              example: 'Bad request - missing or invalid parameter',
            },
          },
        },
        Role: {
          type: 'object',
          required: ['application', 'objectId', 'roleTypeId'],
          properties: {
            application: {
              type: 'string',
              '@enum': ['alfabet', 'all'],
              example: 'alfabet',
              description: 'Application where the role assignment exists',
            },
            assigneeDesc: {
              type: 'string',
              example: 'description',
            },
            assigneeEmail: {
              type: 'string',
              example: '<EMAIL>',
            },
            assigneeFirstName: {
              type: 'string',
              example: 'John',
            },
            assigneeId: {
              type: 'string',
              example: exampleId,
              description: 'ID of the role assignee, if a person',
            },
            assigneeIsDeleted: {
              type: 'string',
              example: 'false',
              description: 'Indicated the person assigned the role is marked for deletion',
            },
            assigneeLastName: {
              type: 'string',
              example: 'Doe',
            },
            assigneeOrgId: {
              type: 'string',
              example: exampleId,
              description: 'ID of the role assignee, if an organization',
            },
            assigneeOrgName: {
              type: 'string',
              example: 'CMS',
            },
            assigneePhone: {
              type: 'string',
              example: '************',
            },
            assigneeType: {
              type: 'string',
              '@enum': ['organization', 'person'],
              example: 'person',
            },
            assigneeUserName: {
              type: 'string',
              example: 'jdoe',
              description: 'Username of the role assignee, if a person',
            },
            objectId: {
              type: 'string',
              example: exampleId,
              description: 'ID of the object the role is assigned to',
            },
            objectType: {
              type: 'string',
              example: 'project',
              description: 'The type of object the role is assigned to',
            },
            roleId: {
              type: 'string',
              example: exampleId,
              description: 'ID of the role assignment',
            },
            roleTypeDesc: {
              type: 'string',
              example: 'Description',
              description: 'Description of the role type',
            },
            roleTypeId: {
              type: 'string',
              example: exampleId,
              description: 'ID of the role type',
            },
            roleTypeName: {
              type: 'string',
              example: 'Analyst',
              description: 'Name of the role type',
            },
          },
        },
        RoleType: {
          type: 'object',
          required: ['application', 'objectId', 'roleTypeId'],
          properties: {
            application: {
              type: 'string',
              '@enum': ['alfabet', 'all'],
              example: 'alfabet',
              description: 'Application where the role assignment exists',
            },
            name: {
              type: 'string',
              '@enum': [
                'AI Contact',
                'API Contact',
                'Budget Analyst',
                'Business Owner',
                'Business Question Contact',
                'Contracting Officer\'s Representative (COR)',
                'DA Reviewer',
                'Data Center Contact',
                'ISSO Government Task Lead (GTL)',
                'Project Lead',
                'QA Reviewer',
                'System Maintainer',
                'Subject Matter Expert (SME)',
                'Support Staff',
                'Survey Point of Contact',
                'Technical System Issues Contact',
              ],
              description: {
                type: 'string',
              },
              id: {
                type: 'string',
              },
            },
          },
        },
      },
    },
  };

  const OUTPUT_FILE = path.resolve(process.cwd(), 'swaggerDocs.json');
  const [accessError, accessResult] = await tryAsync(stat(OUTPUT_FILE));
  if (accessError && get(accessError, 'code') !== 'ENOENT') {
    app.logger.error({ error: accessError });
    process.exit(1);
  }

  if (accessResult) {
    await unlink(OUTPUT_FILE);
  }

  const [openError, openResult] = await tryAsync(open(OUTPUT_FILE, 'w'));
  if (openError || !openResult) {
    app.logger.error({ error: openError || 'Opening file failed' });
    process.exit(1);
  }

  await openResult.close();

  // This will fail if any files are empty.
  const ENDPOINTS: string[] = [
    path.resolve(process.cwd(), 'src/resources/**/*.ts'),
  ];

  const [genError, genResult] = await tryAsync(
    swaggerAutogen({ openapi: DOC.openapi })(OUTPUT_FILE, ENDPOINTS, DOC),
  );
  if (genError) {
    app.logger.error({ error: genError });
    process.exit(1);
  }

  if (!genResult) {
    app.logger.error({ error: 'Documentation auto generation failed without error' });
    process.exit(1);
  }

  if (!genResult.success) {
    // check genResult.data to see if an Error is here
    app.logger.error({ error: 'Documentation auto generation failed' });
    process.exit(1);
  }
  app.logger.info(`✅ swaggerDocs.json generated at ${OUTPUT_FILE}`);

  const swaggerDocument = JSON.parse(readFileSync(OUTPUT_FILE, 'utf-8'));
  return swaggerDocument;
};

const initMiddleware = async (app: CMSApp, createSwagger: boolean = true) => {
  app.use(cors());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  addAppToReq(app);
  // Generate & serve Swagger docs
  if (createSwagger) {
    const swaggerDocument = await addApiDocs(app);
    app.use('/api-docs', swaggerUiExpress.serve, swaggerUiExpress.setup(swaggerDocument));
  }
};

export default initMiddleware;
export {
  addAppToReq,
  cleanupDataSources,
};
