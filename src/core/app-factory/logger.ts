import { stdout, stderr } from 'process';
import Logger, {
  createLogger,
  stdSerializers,
  DEBUG,
  LoggerOptions,
} from 'bunyan';
import {
  get,
  set,
  clone,
  defaults,
  lowerCase,
} from 'lodash';
import { AppConfig, CMSApp, SessionObject } from '../../types';

const loggers: { [key: string]: Logger } = {};

// FIXME: Temp user serializer until the user object is finalized
const userSerializer = () => '[user]';

const sessionSerializer = (session: SessionObject) => {
  const clonedSession = clone(session);
  if (clonedSession.session) {
    clonedSession.session = sessionSerializer(clonedSession.session);
  }

  return clonedSession;
};

const bodySerializer = (body: SessionObject) => sessionSerializer(body);

const addStandardSerializers = (logger: Logger) => {
  logger.addSerializers(defaults({
    // Prevent the logger from logging the logger
    logger: () => '[logger]',
    // Handle error and err keys
    error: stdSerializers.err,
  }, stdSerializers));

  if (logger.level() >= DEBUG) {
    logger.addSerializers({
      user: userSerializer,
      session: sessionSerializer,
      body: bodySerializer,
    });
  }
};

const createBunyanLogger = (loggerConfig: LoggerOptions) => {
  if (loggerConfig.streams) {
    get(loggerConfig, 'streams', []).forEach((item) => {
      if (lowerCase(get(item, 'name', '')) === 'stdout') {
        // Allowing param reassignment to convert system config to bunyan config
        // eslint-disable-next-line no-param-reassign
        item.stream = stdout;
      } else if (lowerCase(get(item, 'name', '')) === 'stderr') {
        // Allowing param reassignment to convert system config to bunyan config
        // eslint-disable-next-line no-param-reassign
        item.stream = stderr;
      }
    });
  }

  const newLogger = createLogger(loggerConfig);
  addStandardSerializers(newLogger);
  loggers[get(loggerConfig, 'name', 'none')] = newLogger;
  return newLogger;
};

const loggingService = (config: AppConfig) => {
  get(config, 'logging.loggers', []).forEach((item) => createBunyanLogger(item));

  return {
    get: (name: string) => get(loggers, name),
    create: (loggerConfig: LoggerOptions) => createBunyanLogger(loggerConfig),
    getNames: () => Object.keys(loggers),
  };
};

const addLoggingToApp = (app: CMSApp) => {
  const config: AppConfig | undefined = get(app, 'config');
  if (!config) {
    throw new Error('No API config found in the application');
  }

  const service = loggingService(config);
  set(app, 'loggingService', service);

  const serverLogger = app.loggingService.get(get(config, 'logging.logNames.server', 'server'));
  set(app, 'logger', serverLogger);
};

export {
  userSerializer,
  sessionSerializer,
  bodySerializer,
  addStandardSerializers,
  createBunyanLogger,
  loggingService,
  addLoggingToApp,
};
