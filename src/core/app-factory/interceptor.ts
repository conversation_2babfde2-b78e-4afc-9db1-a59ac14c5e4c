import { Express } from 'express';
import notFoundHandler from '../../interceptors/not-found-handler';
import checkIfAuthorized from '../../interceptors/authentication';
import { addSessionTokensToReq } from '../../subsystems/authentication/jwt';

const initInterceptors = (app: Express) => {
  // Session Token Secrets
  app.use(addSessionTokensToReq);
  // 404 Handling
  app.use(notFoundHandler);
  // Authorization Handling
  app.use(checkIfAuthorized);
};

export {
  // eslint-disable-next-line import/prefer-default-export
  initInterceptors,
};
