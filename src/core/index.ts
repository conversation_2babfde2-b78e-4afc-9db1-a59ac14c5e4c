import { get, isEmpty } from 'lodash';
import initConfig from './app-factory/config';
import { registerAppDataSources } from './app-factory/data-sources';
import { initInterceptors } from './app-factory/interceptor';
import { addLoggingToApp } from './app-factory/logger';
import initMiddleware from './app-factory/middleware';
import { initOuterceptors } from './app-factory/outerceptor';
import { addResourceRegistry } from './app-factory/resources';
import { initSecrets } from '../subsystems/secrets';
import { initAuthSecrets } from '../subsystems/authentication/jwt';
import {
  CMSApp,
  DSSetup,
  SessionSetup,
  PgDataOptions,
  MssqlDataOptions,
} from '../types';
import PgData from '../data-sources/pg';
import MssqlData from '../data-sources/mssql';

const initApp = async (
  app: CMSApp,
  dsSetup: DSSetup[],
  sessionSetup: SessionSetup,
  createSwagger: boolean = true,
): Promise<CMSApp> => {
  initConfig(app);
  addLoggingToApp(app);
  registerAppDataSources(app);
  addResourceRegistry(app);
  const secretStrings: string[] = get(app, 'config.secretStrings', []) as string[];
  await initSecrets(app, secretStrings);
  initAuthSecrets();

  if (isEmpty(dsSetup)) {
    throw new Error('There must be at least one database configured');
  }

  // dsSetup.forEach(async (item) => {
  //   // await item.registerDS(app);
  //   // await app.dataSources.register<MssqlData, MssqlDataOptions>(app, 'sparxea', MssqlData, {
  //   //   dbName: 'sparx_support_db',
  //   // });
  //   const {
  //     dsType,
  //     dsName,
  //     options = {},
  //   } = item;
  //   await app.dataSources.register<dsType>(app, dsName, dsType, options);
  // });

  // Data Source
  // init PG data source
  await app.dataSources.register<PgData, PgDataOptions>(app, 'core', PgData);

  // init MSSQL data source
  await app.dataSources.register<MssqlData, MssqlDataOptions>(app, 'sparxea', MssqlData, {
    dbName: 'sparx_support_db',
  });

  // init MSSQL data source
  await app.dataSources.register<MssqlData, MssqlDataOptions>(app, 'cedarSupport', MssqlData, {
    dbName: 'cedar_support_db',
  });

  const {
    enableSession,
    dsName: sessionDsName,
  } = sessionSetup;

  await enableSession(app, sessionDsName);

  await initMiddleware(app, createSwagger);
  initInterceptors(app);
  initOuterceptors(app);

  return app as CMSApp;
};

export default initApp;
export {
  initOuterceptors,
};
