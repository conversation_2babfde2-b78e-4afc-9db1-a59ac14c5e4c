import sparxById from './by-id';
import sparxCreate from './create';
import sparxRemove from './remove';
import { GetResourceConfig } from '../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-sparx-by-id',
  path: '/:id',
  method: 'get',
  resource: sparxById,
}, {
  name: 'delete-sparx',
  path: '/:id',
  method: 'delete',
  resource: sparxRemove,
}, {
  name: 'post-sparx',
  path: '/',
  method: 'post',
  resource: sparxCreate,
}];

export default getResourceConfig;
