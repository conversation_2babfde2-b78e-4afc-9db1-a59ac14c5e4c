import { Request, Response } from 'express';
import { get, isEmpty, isError } from 'lodash';
import Joi from 'joi';
import { postQuery, getToken, getPackageParentId } from '../../subsystems/sparxea';

const sparxCreate = async (req: Request, res: Response) => {
  const body = get(req, 'body', {});
  if (isEmpty(body)) {
    return res.status(400).send({ message: 'No body provided' });
  }

  const validateBodyField = (field: string, fieldName: string) => {
    const querySchema = Joi.string()
      .not()
      .empty()
      .messages({
        'string.empty': `${fieldName} is required and can not be empty`,
      });
    const { error } = querySchema.validate(field);
    return error;
  };

  const title = get(body, 'title', '');
  const titleError = validateBodyField(title, 'title');
  if (titleError) {
    return res.status(400).send({ message: titleError.message });
  }

  const packageName = get(body, 'packageName', '');
  const packageNameError = validateBodyField(packageName, 'packageName');
  if (packageNameError) {
    return res.status(400).send({ message: packageNameError.message });
  }

  const artifactNames: string[] = ['Intake', 'EASi Funding Source', 'Buisness Case', 'Business Case Solution', 'SORN', 'System URL', 'EASi BCS Cost Line'];
  const actorNames: string[] = ['Contact', 'Person'];

  let type: string = 'Class';
  if (artifactNames.includes(packageName)) {
    type = 'Artifact';
  }

  if (actorNames.includes(packageName)) {
    type = 'Actor';
  }

  const parentId = getPackageParentId(packageName);
  if (!parentId) {
    return res.status(403).send({ message: 'Unable to get find the parent package ID' });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const token = await getToken(app);
  if (isError(token)) {
    app.logger.error({ error: token });
    return res.status(500).send({ message: 'Unable to get token to make the request' });
  }

  const queryObject = `<?xml version="1.0"?><rdf:RDF xmlns:oslc_am="http://open-services.net/ns/am#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"><oslc_am:Resource><dcterms:title>${title}</dcterms:title><dcterms:type>${type}</dcterms:type><ss:resourcetype>Element</ss:resourcetype><ss:parentresourceidentifier>pk_${parentId}</ss:parentresourceidentifier><ss:useridentifier>${token}</ss:useridentifier><ss:stereotype><ss:stereotypename><ss:name></ss:name></ss:stereotypename></ss:stereotype></oslc_am:Resource></rdf:RDF>`;

  const createdObject = await postQuery(app, queryObject);
  if (isError(createdObject)) {
    app.logger.error({ error: createdObject });
    return res.status(500).send({ message: 'The object failed to create' });
  }

  return res.status(200).set('Content-Type', 'application/xml').send(createdObject);
};

export default sparxCreate;
