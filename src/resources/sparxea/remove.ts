import { Request, Response } from 'express';
import { get, isError } from 'lodash';
import <PERSON><PERSON> from 'joi';
import { deleteQuery } from '../../subsystems/sparxea';

const sparxRemove = async (req: Request, res: Response) => {
  const id = get(req, 'params.id', '');

  if (!id) {
    return res.status(400).send({ message: 'No object id provided' });
  }

  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id);
  if (error) {
    return res.status(400).send({ message: 'Invalid object id' });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const objectById = await deleteQuery(app, `el_{${id}}`);
  if (isError(objectById)) {
    app.logger.error({ error: objectById });
    return res.status(500).send({ message: 'Failed' });
  }

  return res.status(200).set('Content-Type', 'application/xml').send(objectById);
};

export default sparxRemove;
