import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_SYSTEM_DATAEXCHANGE,
} from '../../../utils/db-helpers';
import {
  tryAsync,
  strToBoolOrNull,
} from '../../../utils/general';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/exchange/{id}'
#swagger.method = 'get'
#swagger.tags = ['Exchange']
#swagger.description = 'Exchange by id'
#swagger.parameters['id'] = {
  in: 'path',
  required: true,
  description: 'ID of exchange to retrieve',
  type: 'string',
  example: '550e8400-e29b-41d4-a716-446655440000'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Successfully retrieved exchange by id',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          apiOwnership: {
            type: 'string',
          },
          businessPurposeOfAddress: {
            type: 'array',
            items: {
              type: 'string',
              example: 'Customer Service'
            }
          },
          'connectionFrequency': {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          containsBankingData: {
            type: 'boolean'
          },
          containsBeneficiaryAddress: {
            type: 'boolean'
          },
          containsHealthDisparityData: {
            type: 'boolean'
          },
          containsPhi: {
            type: 'boolean'
          },
          containsPii: {
            type: 'boolean'
          },
          dataExchangeAgreement: {
            type: 'string'
          },
          dataFormat: {
            type: 'string'
          },
          dataFormatOther: {
            type: 'string'
          },
          exchangeCUIDescription: {
            type: 'string'
          },
          exchangeCUIType: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          exchangeConnectionAuthenticated: {
            type: 'boolean'
          },
          exchangeContainsCUI: {
            type: 'boolean'
          },
          exchangeDescription: {
            type: 'string',
            example: 'Reference data on vendors acting on behalf of insurance issuers'
          },
          exchangeEndDate: {
            type: 'string',
            format: 'date'
          },
          exchangeId: {
            type: 'string',
            example: '139-1178-0'
          },
          exchangeName: {
            type: 'string',
            example: 'Acumen Web Portals 1.0 >> Drug Data Processing System 1.0'
          },
          exchangeNetworkProtocol: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          exchangeNetworkProtocolOther: {
            type: 'string'
          },
          exchangeRetiredDate: {
            type: 'string',
            format: 'date'
          },
          exchangeStartDate: {
            type: 'string',
            format: 'date'
          },
          exchangeState: {
            type: 'string',
            example: 'Active'
          },
          exchangeVersion: {
            type: 'string',
            example: '1'
          },
          fromOwnerId: {
            type: 'string',
            example: '326-762-0'
          },
          fromOwnerName: {
            type: 'string',
            example: 'Account Management'
          },
          fromOwnerType: {
            type: 'string',
            '@enum': [
              'application',
              'organization'
            ]
          },
          isAddressEditable: {
            type: 'boolean'
          },
          isBeneficiaryMailingFile: {
            type: 'boolean'
          },
          numOfRecords: {
            type: 'string',
            example: '100,000 – 1 Million'
          },
          sharedViaApi: {
            type: 'boolean'
          },
          toOwnerId: {
            type: 'string',
            example: '326-762-0'
          },
          toOwnerName: {
            type: 'string',
            example: 'Account Management'
          },
          toOwnerType: {
            type: 'string',
            '@enum': [
              'application',
              'organization'
            ]
          },
          typeOfData: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                },
                name: {
                  type: 'string',
                }
              }
            }
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const exchangeById = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'params.id', '');
  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));

  if (error) {
    app.logger.error({ error });
    return res.status(400).send({ error: 'Invalid object id' });
  }

  const where = {
    where: {
      operation: {
        column: 'Connection GUID',
        operator: '=',
        value: id,
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert string to string array after call
    '"Exchange Frequency" as connectionFrequency',
    // Convert string to string array after call
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert Yes/No to boolean after call
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert string to string array after call
    '"Type of Data" as typeOfData',
    // Convert Yes/No to boolean after call
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert string to string array after call
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve exchange by system ID' });
  }

  const exchangeResults = get(viewResult, '[0]', []).map((item: object) => {
    const connectionFrequency = get(item, 'connectionFrequency', null) as string | null;
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null) as string | null;
    const typeOfData = get(item, 'typeOfData', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null) as string | null;
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (connectionFrequency) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    let formattedTypeOfData: string[] = [];
    if (typeOfData) {
      formattedTypeOfData = typeOfData.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return res.status(200).send(exchangeResults[0]);
};

export default exchangeById;
