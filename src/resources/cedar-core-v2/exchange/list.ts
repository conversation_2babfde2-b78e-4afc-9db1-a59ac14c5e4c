import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';
import { getDataExchangeListUtil } from '../../../utils/dataExchange';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/exchange'
#swagger.method = 'get'
#swagger.tags = ['Exchange']
#swagger.description = 'Exchange Find List'
#swagger.parameters['systemId'] = {
  in: 'query',
  required: true,
  description: 'ID of the system that the data exchange is associated with',
  type: 'string',
  example: '550e8400-e29b-41d4-a716-446655440000'
}
#swagger.parameters['direction'] = {
  in: 'query',
  required: true,
  description: 'The direction of the data exchange, either sender, receiver, or both',
  schema: {
    type: 'string',
    example: 'sender',
    '@enum': [
      'sender',
      'receiver',
      'both'
    ]
  }
}
#swagger.parameters['version'] = {
  in: 'query',
  required: true,
  description: 'Version of the data exchanges to be retrieved',
  type: 'string',
  example: '2.0'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Successfully retrieved exchange list',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          Exchanges: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                apiOwnership: {
                  type: 'string',
                },
                businessPurposeOfAddress: {
                  type: 'array',
                  items: {
                    type: 'string',
                    example: 'Customer Service'
                  }
                },
                'connectionFrequency': {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                containsBankingData: {
                  type: 'boolean'
                },
                containsBeneficiaryAddress: {
                  type: 'boolean'
                },
                containsHealthDisparityData: {
                  type: 'boolean'
                },
                containsPhi: {
                  type: 'boolean'
                },
                containsPii: {
                  type: 'boolean'
                },
                dataExchangeAgreement: {
                  type: 'string'
                },
                dataFormat: {
                  type: 'string'
                },
                dataFormatOther: {
                  type: 'string'
                },
                exchangeCUIDescription: {
                  type: 'string'
                },
                exchangeCUIType: {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                exchangeConnectionAuthenticated: {
                  type: 'boolean'
                },
                exchangeContainsCUI: {
                  type: 'boolean'
                },
                exchangeDescription: {
                  type: 'string',
                  example: 'Reference data on vendors acting on behalf of insurance issuers'
                },
                exchangeEndDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeId: {
                  type: 'string',
                  example: '139-1178-0'
                },
                exchangeName: {
                  type: 'string',
                  example: 'Acumen Web Portals 1.0 >> Drug Data Processing System 1.0'
                },
                exchangeNetworkProtocol: {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                exchangeNetworkProtocolOther: {
                  type: 'string'
                },
                exchangeRetiredDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeStartDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeState: {
                  type: 'string',
                  example: 'Active'
                },
                exchangeVersion: {
                  type: 'string',
                  example: '1'
                },
                fromOwnerId: {
                  type: 'string',
                  example: '326-762-0'
                },
                fromOwnerName: {
                  type: 'string',
                  example: 'Account Management'
                },
                fromOwnerType: {
                  type: 'string',
                  '@enum': [
                    'application',
                    'organization'
                  ]
                },
                isAddressEditable: {
                  type: 'boolean'
                },
                isBeneficiaryMailingFile: {
                  type: 'boolean'
                },
                numOfRecords: {
                  type: 'string',
                  example: '100,000 – 1 Million'
                },
                sharedViaApi: {
                  type: 'boolean'
                },
                toOwnerId: {
                  type: 'string',
                  example: '326-762-0'
                },
                toOwnerName: {
                  type: 'string',
                  example: 'Account Management'
                },
                toOwnerType: {
                  type: 'string',
                  '@enum': [
                    'application',
                    'organization'
                  ]
                },
                typeOfData: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                      },
                      name: {
                        type: 'string',
                      }
                    }
                  }
                }
              }
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        },
        required: ['Exchanges', 'count']
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const exchangeList = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();

  const allowedDirections = [
    'both',
    'receiver',
    'sender',
  ];

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId');
  if (!isString(systemId) || isEmpty(systemId)) {
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\' and \'direction\'' });
  }

  const direction = get(req, 'query.direction');
  if (!isString(direction) || isEmpty(direction)) {
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\' and \'direction\'' });
  }

  // @ts-expect-error Version is not used, putting this in here so it's always a string
  const version = get(req, 'query.version'); // eslint-disable-line @typescript-eslint/no-unused-vars

  if (!allowedDirections.includes(direction)) {
    return res.status(400).send({ error: 'The provided direction is not valid' });
  }

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return res.status(400).send({ error: 'The system ID is not valid' });
  };

  const exchangeResults = await getDataExchangeListUtil(app, db, systemId, direction);

  if (isError(exchangeResults)) {
    app.logger.error({ error: exchangeResults });
    return res.status(500).send({ message: 'Unable to retrieve exchange by system ID' });
  }

  return res.status(200).send(exchangeResults);
};

export default exchangeList;
