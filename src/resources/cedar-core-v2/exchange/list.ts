import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
  unset,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_SYSTEM_DATAEXCHANGE,
} from '../../../utils/db-helpers';
import {
  strToBoolOrNull,
  tryAsync,
} from '../../../utils/general';
import {
  OrObject,
  Where,
  ExchangeListResult,
} from '../../../types';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/exchange'
#swagger.method = 'get'
#swagger.tags = ['Exchange']
#swagger.description = 'Exchange Find List'
#swagger.parameters['systemId'] = {
  in: 'query',
  required: true,
  description: 'ID of the system that the data exchange is associated with',
  type: 'string',
  example: '550e8400-e29b-41d4-a716-446655440000'
}
#swagger.parameters['direction'] = {
  in: 'query',
  required: true,
  description: 'The direction of the data exchange, either sender, receiver, or both',
  schema: {
    type: 'string',
    example: 'sender',
    '@enum': [
      'sender',
      'receiver',
      'both'
    ]
  }
}
#swagger.parameters['version'] = {
  in: 'query',
  required: true,
  description: 'Version of the data exchanges to be retrieved',
  type: 'string',
  example: '2.0'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Successfully retrieved exchange list',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          Exchanges: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                apiOwnership: {
                  type: 'string',
                },
                businessPurposeOfAddress: {
                  type: 'array',
                  items: {
                    type: 'string',
                    example: 'Customer Service'
                  }
                },
                'connectionFrequency': {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                containsBankingData: {
                  type: 'boolean'
                },
                containsBeneficiaryAddress: {
                  type: 'boolean'
                },
                containsHealthDisparityData: {
                  type: 'boolean'
                },
                containsPhi: {
                  type: 'boolean'
                },
                containsPii: {
                  type: 'boolean'
                },
                dataExchangeAgreement: {
                  type: 'string'
                },
                dataFormat: {
                  type: 'string'
                },
                dataFormatOther: {
                  type: 'string'
                },
                exchangeCUIDescription: {
                  type: 'string'
                },
                exchangeCUIType: {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                exchangeConnectionAuthenticated: {
                  type: 'boolean'
                },
                exchangeContainsCUI: {
                  type: 'boolean'
                },
                exchangeDescription: {
                  type: 'string',
                  example: 'Reference data on vendors acting on behalf of insurance issuers'
                },
                exchangeEndDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeId: {
                  type: 'string',
                  example: '139-1178-0'
                },
                exchangeName: {
                  type: 'string',
                  example: 'Acumen Web Portals 1.0 >> Drug Data Processing System 1.0'
                },
                exchangeNetworkProtocol: {
                  type: 'array',
                  items: {
                    type: 'string'
                  }
                },
                exchangeNetworkProtocolOther: {
                  type: 'string'
                },
                exchangeRetiredDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeStartDate: {
                  type: 'string',
                  format: 'date'
                },
                exchangeState: {
                  type: 'string',
                  example: 'Active'
                },
                exchangeVersion: {
                  type: 'string',
                  example: '1'
                },
                fromOwnerId: {
                  type: 'string',
                  example: '326-762-0'
                },
                fromOwnerName: {
                  type: 'string',
                  example: 'Account Management'
                },
                fromOwnerType: {
                  type: 'string',
                  '@enum': [
                    'application',
                    'organization'
                  ]
                },
                isAddressEditable: {
                  type: 'boolean'
                },
                isBeneficiaryMailingFile: {
                  type: 'boolean'
                },
                numOfRecords: {
                  type: 'string',
                  example: '100,000 – 1 Million'
                },
                sharedViaApi: {
                  type: 'boolean'
                },
                toOwnerId: {
                  type: 'string',
                  example: '326-762-0'
                },
                toOwnerName: {
                  type: 'string',
                  example: 'Account Management'
                },
                toOwnerType: {
                  type: 'string',
                  '@enum': [
                    'application',
                    'organization'
                  ]
                },
                typeOfData: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: {
                        type: 'string',
                      },
                      name: {
                        type: 'string',
                      }
                    }
                  }
                }
              }
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        },
        required: ['Exchanges', 'count']
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const exchangeList = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();

  const allowedDirections = [
    'both',
    'receiver',
    'sender',
  ];

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId');
  if (!isString(systemId) || isEmpty(systemId)) {
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\' and \'direction\'' });
  }

  const direction = get(req, 'query.direction');
  if (!isString(direction) || isEmpty(direction)) {
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\' and \'direction\'' });
  }

  const version = get(req, 'query.version');
  // eslint-disable-next-line no-console
  console.log({ version });

  if (!allowedDirections.includes(direction)) {
    return res.status(400).send({ error: 'The provided direction is not valid.' });
  }

  const paramOrs: OrObject[] | null = [];

  const byIdWhere: Where = {
    where: {},
    or: [],
  };

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    return res.status(400).send({ error: 'The system ID is not valid' });
  }

  if (direction === 'sender') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'receiver') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }
  if (direction === 'both') {
    byIdWhere.where = {
      operation: {
        column: 'Sparx Sendor GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
    paramOrs.push({
      operation: {
        column: 'Sparx Receiver GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    });
    byIdWhere.or = paramOrs;
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_DATAEXCHANGE, [
    '"Connection GUID" as exchangeId',
    '"Connection Name" as exchangeName',
    '"Exchange Description" as exchangeDescription',
    '"Exchange Version" as exchangeVersion',
    '"Object State" as exchangeState',
    '"Exchange Start Date" as exchangeStartDate',
    '"Exchange End Date" as exchangeEndDate',
    '"Retire Date" as exchangeRetiredDate',
    '"Sparx Sendor GUID" as fromOwnerId',
    '"Sender Name" as fromOwnerName',
    '"Sender Type" as fromOwnerType',
    '"Sparx Receiver GUID" as toOwnerId',
    '"Receiver Name" as toOwnerName',
    '"Receiver Type" as toOwnerType',
    // Convert to string array
    '"Exchange Frequency" as connectionFrequency',
    // Convert to boolean
    '"IE Agreement" as dataExchangeAgreement',
    // Convert to boolean
    '"Exchange includes Beneficiary Address Data" as containsBeneficiaryAddress',
    // Convert to string array
    '"Beneficiary Address Purpose" as businessPurposeOfAddress',
    // Convert to boolean
    '"Address Data Editable" as isAddressEditable',
    // Convert to boolean
    '"Exchange Contains PII" as containsPii',
    // Convert to boolean
    '"Exchange Contains PHI" as containsPhi',
    // Convert to boolean
    '"Contains Health Disparity Data" as containsHealthDisparityData',
    // Convert to boolean
    '"Exchange Includes Banking Data" as containsBankingData',
    // Convert to boolean
    '"Data Shared via API" as sharedViaApi',
    '"API Ownership" as apiOwnership',
    // Convert to array of object
    '"Type of Data" as typeOfDataName',
    '"Type of Data ID" as typeOfDataId',
    '"Number of Records Exchanged" as numOfRecords',
    '"Exchange Format" as dataFormat',
    '"Exchange Format Other" dataFormatOther',
    // Convert to boolean
    '"Exchange Contains CUI" as exchangeContainsCUI',
    '"Exchange CUI Description" as exchangeCUIDescription',
    // Convert to string array
    '"Exchange CUI Type" as exchangeCUIType',
    // Convert to boolean
    '"Exchange Connection Authenticated" as exchangeConnectionAuthenticated',
    // Convert to string array
    '"Exchange Network Protocol" as exchangeNetworkProtocol',
    '"Exchange Network Protocol Other" as exchangeNetworkProtocolOther',
  ], byIdWhere));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ message: 'Unable to retrieve exchange by system ID' });
  }

  const exchangeResults = get(viewResult, '[0]', []).map((item: ExchangeListResult) => {
    const connectionFrequency = get(item, 'connectionFrequency', '') as string;
    const containsBeneficiaryAddress = get(item, 'containsBeneficiaryAddress', null);
    const businessPurposeOfAddress = get(item, 'businessPurposeOfAddress', null) as string | null;
    const isAddressEditable = get(item, 'isAddressEditable', null);
    const containsPii = get(item, 'containsPii', null);
    const containsPhi = get(item, 'containsPhi', null);
    const containsHealthDisparityData = get(item, 'containsHealthDisparityData', null);
    const containsBankingData = get(item, 'containsBankingData', null);
    const sharedViaApi = get(item, 'sharedViaApi', null);
    const typeOfDataName = get(item, 'typeOfDataName', null) as string | null;
    const typeOfDataId = get(item, 'typeOfDataId', null) as string | null;
    const exchangeContainsCUI = get(item, 'exchangeContainsCUI', null);
    const exchangeCUIType = get(item, 'exchangeCUIType', null) as string | null;
    const exchangeConnectionAuthenticated = get(item, 'exchangeConnectionAuthenticated', null);
    const exchangeNetworkProtocol = get(item, 'exchangeNetworkProtocol', null) as string | null;

    let formattedConnectionFrequency: string[] = [];
    if (!isEmpty(connectionFrequency)) {
      formattedConnectionFrequency = connectionFrequency.split('|');
    }

    let formattedBusinessPurposeOfAddress: string[] = [];
    if (businessPurposeOfAddress) {
      formattedBusinessPurposeOfAddress = businessPurposeOfAddress.split('|');
    }

    const formattedTypeOfData: object[] = [];
    if ((!isEmpty(typeOfDataName)) && (!isEmpty(typeOfDataId))) {
      formattedTypeOfData.push({
        id: typeOfDataId,
        name: typeOfDataName,
      });
    }

    let formattedExchangeCUIType: string[] = [];
    if (exchangeCUIType) {
      formattedExchangeCUIType = exchangeCUIType.split('|');
    }

    let formattedExchangeNetworkProtocol: string[] = [];
    if (exchangeNetworkProtocol) {
      formattedExchangeNetworkProtocol = exchangeNetworkProtocol.split('|');
    }

    // deleting these as they're converted
    unset(item, 'typeOfDataId');
    unset(item, 'typeOfDataName');

    return {
      ...item,
      connectionFrequency: formattedConnectionFrequency,
      containsBeneficiaryAddress: strToBoolOrNull(containsBeneficiaryAddress),
      businessPurposeOfAddress: formattedBusinessPurposeOfAddress,
      isAddressEditable: strToBoolOrNull(isAddressEditable),
      containsPii: strToBoolOrNull(containsPii),
      containsPhi: strToBoolOrNull(containsPhi),
      containsHealthDisparityData: strToBoolOrNull(containsHealthDisparityData),
      containsBankingData: strToBoolOrNull(containsBankingData),
      sharedViaApi: strToBoolOrNull(sharedViaApi),
      typeOfData: formattedTypeOfData,
      exchangeContainsCUI: strToBoolOrNull(exchangeContainsCUI),
      exchangeCUIType: formattedExchangeCUIType,
      exchangeConnectionAuthenticated: strToBoolOrNull(exchangeConnectionAuthenticated),
      exchangeNetworkProtocol: formattedExchangeNetworkProtocol,
    };
  });

  return res.status(200).send({
    count: exchangeResults.length,
    Exchanges: exchangeResults,
  });
};

export default exchangeList;
