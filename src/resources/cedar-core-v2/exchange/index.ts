// /exchange/{id}
// /exchange/list
import exchangeById from './by-id';
import exchangeList from './list';
import { GetResourceConfig } from '../../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-exchange-list',
  path: '/',
  method: 'get',
  resource: exchangeList,
}, {
  name: 'get-exchange-by-id',
  path: '/:id',
  method: 'get',
  resource: exchangeById,
}];

export default getResourceConfig;
