import { SwaggerDocumentation } from '../../../../types/swagger';

const organizationSchema = {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      example: '{11111111-**************-************}',
    },
    name: {
      type: 'string',
      example: 'Office of Management and Budget (OMB)',
    },
    acronym: {
      type: 'string',
      example: 'OMB',
      nullable: true,
    },
    description: {
      type: 'string',
      example: 'The primary office of the President responsible for developing the President\'s annual budget and for overseeing the implementation of that budget.',
      nullable: true,
    },
    component: {
      type: 'string',
      example: 'Department/Operating Division',
      nullable: true,
    },
    fullPath: {
      type: 'string',
      example: 'Department of Health & Human Services > Centers for Medicare & Medicaid Services > Office of Management and Budget (OMB)',
      nullable: true,
    },
    level: {
      type: 'integer',
      example: 3,
      nullable: true,
    },
    parentId: {
      type: 'string',
      example: '{66666666-7777-8888-9999-000000000000}',
      nullable: true,
    },
    // children property is added below for recursive definition
  },
};

// Add the recursive children property after the main schema is defined
organizationSchema.properties.children = {
  type: 'array',
  items: organizationSchema,
  description: 'List of child organizations.',
};

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/census-core-v2/organization',
  method: 'get',
  tags: ['Census Core'],
  description: 'Retrieve a list of organizations based on various criteria. If no criteria are provided, an empty list will be returned.',
  parameters: [
    {
      name: 'id',
      in: 'query',
      description: 'The GUID of a specific organization to find. If provided, returns the organization and its children.',
      required: false,
      schema: {
        type: 'string',
        example: '{11111111-**************-************}',
      },
    },
    {
      name: 'name',
      in: 'query',
      description: 'The exact name of a specific organization to find. If provided, returns the organization and its children.',
      required: false,
      schema: {
        type: 'string',
        example: 'Office of Management and Budget (OMB)',
      },
    },
    {
      name: 'acronym',
      in: 'query',
      description: 'The acronym of a specific organization to find. If provided, returns the organization and its children.',
      required: false,
      schema: {
        type: 'string',
        example: 'OMB',
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successful response with a list of organizations.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Successfully retrieved organizations'],
              },
              count: {
                type: 'integer',
                example: 1,
              },
              organizations: {
                type: 'array',
                items: organizationSchema,
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request - Missing or invalid parameters',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized - Missing or invalid authentication credentials',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;