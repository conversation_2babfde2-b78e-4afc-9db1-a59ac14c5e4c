import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isArray,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_ORGANIZATION,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  Where,
} from '../../../types';

// interface OrgData {
//   id: string;
//   name: string;
//   acronym: string;
//   description: string;
//   component: string;
//   fullPath: string;
//   parentId: string;
//   Organizations: Organization[];
//   // level: number;
// }

export interface Organization {
  id: string;
  name: string;
  acronym: string;
  description: string;
  component: string;
  fullPath: string;
  level?: number;
  parentId?: string;
  Organizations?: Organization[];
}

class Org {
  public id: string;
  public name: string;
  public acronym: string;
  public description: string;
  public component: string;
  public fullPath: string;
  public level?: number;
  public parentId?: string;
  private Organizations: Org[] = [];

  constructor(data?: Organization) {
    if (data) {
      this.id = data.id;
      this.name = data.name;
      this.acronym = data.acronym;
      this.description = data.description ?? '';
      this.component = data.component;
      this.fullPath = data.fullPath;
      this.level = data.level;
      this.parentId = data.parentId;
    } else {
      // Default values for placeholder orgs
      this.id = '';
      this.name = '';
      this.acronym = '';
      this.description = '';
      this.component = '';
      this.fullPath = '';
    }
  }

  setData(data: Organization): void {
    this.id = data.id;
    this.name = data.name;
    this.acronym = data.acronym;
    this.description = data.description ?? '';
    this.component = data.component;
    this.fullPath = data.fullPath;
    this.level = data.level;
    this.parentId = data.parentId;
  }

  addChild(child: Org): void {
    this.Organizations.push(child);
  }

  getChildren(): Org[] {
    return this.Organizations;
  }

  // Convert to Organization interface for serialization
  toOrganization(): Organization {
    const result: Organization = {
      id: this.id,
      name: this.name,
      acronym: this.acronym,
      description: this.description,
      component: this.component,
      fullPath: this.fullPath,
      // level: this.level,
      parentId: this.parentId,
      Organizations: [],
    };

    // Only include organizations if there are any
    if (!isEmpty(this.Organizations)) {
      result.Organizations = this.Organizations.map((child) => child.toOrganization());
    } else {
      delete result.Organizations;
    }
    return result;
  }
}

// Main conversion function
const buildOrganizationTree = (orgList: Organization[]): Org | null => {
  const records = new Map<string, Org>();
  let topOrg: Org | null = null;

  orgList.forEach((orgData) => {
    const { id, parentId } = orgData;

    // Attempt to retrieve org from hash map
    let thisOrg = records.get(id);

    // If org is not in hash map, create new org and add to hash map
    if (thisOrg == null) {
      thisOrg = new Org(orgData);
      records.set(id, thisOrg);
    } else {
      thisOrg.setData(orgData);
    }

    // If parent id exists
    if (parentId != null) {
      // Get parent from hash map
      let parent = records.get(parentId);

      // If parent is not in hash map
      if (parent == null) {
        // Create empty parent and add to hash map, gets hydrated later above
        parent = new Org();
        records.set(parentId, parent);
      }
      // Add children
      parent.addChild(thisOrg);
    } else {
      topOrg = thisOrg;
    }
  });

  return topOrg;
};

const buildOrganizationSubtree = (orgList: Organization[], rootOrgId: string): Org | null => {
  const records = new Map<string, Org>();
  const childrenMap = new Map<string, string[]>(); // parentId -> childIds[]
  let targetOrg: Org | null = null;

  // Build children mapping and create org objects
  orgList.forEach((orgData) => {
    const { id, parentId } = orgData;

    // Create org object
    let thisOrg = records.get(id);
    if (thisOrg == null) {
      thisOrg = new Org(orgData);
      records.set(id, thisOrg);
    } else {
      // if (isEmpty(orgData.Organizations)) {
      //   delete orgData.Organizations;
      // }
      thisOrg.setData(orgData);
    }

    // Mark target org
    if (id === rootOrgId) {
      targetOrg = thisOrg;
    }

    // Build children mapping
    if (parentId) {
      if (!childrenMap.has(parentId)) {
        childrenMap.set(parentId, []);
      }
      childrenMap.get(parentId)!.push(id);
    }
  });

  if (!targetOrg) {
    return null;
  }

  // Recursively build subtree starting from target org
  const buildSubtreeRecursive = (orgId: string): void => {
    const org = records.get(orgId);
    if (!org) return;

    const childIds = childrenMap.get(orgId) || [];
    childIds.forEach((childId) => {
      const childOrg = records.get(childId);
      if (childOrg) {
        org.addChild(childOrg);
        buildSubtreeRecursive(childId); // Recursively build children
      }
    });
  };

  buildSubtreeRecursive(rootOrgId);
  return targetOrg;
};

// Helper function to convert Org to a serializable format
function convertOrg(org: Org | null): Organization | null {
  if (!org) return null;
  return org.toOrganization();
}

export function processOrganizationList(orgList: Organization[]): Organization | null {
  const topOrg = buildOrganizationTree(orgList);
  return convertOrg(topOrg);
}

export function processFilteredOrganizationList(
  orgList: Organization[],
  id: string | null,
  _name: string | null,
  _acronym: string | null,
): Organization | null {
  const topOrg = buildOrganizationSubtree(orgList, id || '');
  return convertOrg(topOrg);
}

const organizationList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = req.query.id ?? null;
  const name = req.query.name ?? null;
  const acronym = req.query.acronym ?? null;

  const whereObj: Where = {
    where: {
      operation: {
        column: 'Org State',
        operator: '=',
        value: 'Active',
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_ORGANIZATION, [
    '"Sparx Organization GUID" as id',
    '"Organization Name" as name',
    '"Acronym" as acronym',
    '"Comments" as description',
    '"Organization Component" as component',
    '"Full Path with Org Name" as fullPath',
    '"Parent Organization GUID" as parentId',
    // '"Organization Name Level" as level',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve organizations' });
  }

  const queryViewResult = viewResult as Array<number | Array<Organization>>;

  const organizationResult = isArray(queryViewResult[0]) ? queryViewResult[0] : [];
  if ((typeof organizationResult === 'number') || !isArray(organizationResult)) {
    app.logger.error({ error: new Error('The returned data was not in the expected format') });
    return res.status(500).send({ error: 'Unable to retrieve organizations' });
  }
  const orgTree = processOrganizationList(organizationResult);

  if (id || name || acronym) {
    const rootOrg = organizationResult.find((org: Organization) => (
      org.id === id || org.name === name || org.acronym === acronym
    ));

    if (!rootOrg) {
      return res.status(404).send({ error: 'Organization not found' });
    }

    const filteredOrgTree = buildOrganizationSubtree(organizationResult, rootOrg.id);
    return res.status(200).send({
      Organizations: [filteredOrgTree],
    });
  }

  return res.status(200).send({
    count: organizationResult.length,
    Organizations: orgTree,
  });
};

export default organizationList;
