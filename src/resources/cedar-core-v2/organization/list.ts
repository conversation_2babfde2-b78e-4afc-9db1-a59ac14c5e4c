import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_ORGANIZATION,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  OrObject,
  Where,
  ThreatListResult,
} from '../../../types';

const organizationList = async (
  req: Request,
  res: Response,
) => {
  const convertCDLToArray = (string: string) => string.split(',');

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const whereObj: Where = {
    where: {
      operation: {
        column: 'Org State',
        operator: '=',
        value: 'Active',
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_ORGANIZATION, [
    '"Acronym" as acronym',
    '"Comments" as description',
    '"Full Path with Org Name" as fullPath',
    '"Organization Component" as component',
    '"Organization Name Level" as level',
    '"Parent Organization GUID" as parentId',
    '"Sparx Organization GUID" as id',
    '"Organization Name" as name',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve organizations' });
  }

  console.log({
    viewResult,
  })
  const organizationResult = isEmpty(viewResult[0]) ? [] : viewResult[0];

  return res.status(200).send({
    count: organizationResult.length,
    Organizations: organizationResult,
  });
};

export default organizationList;
