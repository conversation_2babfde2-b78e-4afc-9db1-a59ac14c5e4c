import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isArray,
  isEmpty,
  isNull,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_ORGANIZATION,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  OrObject,
  Where,
} from '../../../types';

// interface OrgData {
//   id: string;
//   name: string;
//   acronym: string;
//   description: string;
//   component: string;
//   fullPath: string;
//   parentId: string;
//   Organizations: Organization[];
//   // level: number;
// }

export interface Organization {
  id: string;
  name: string;
  acronym: string;
  description: string;
  component: string;
  fullPath: string;
  level?: number;
  parentId?: string;
  children: Organization[];
}

class Org {
  private id: string;
  private name: string;
  private acronym: string;
  private description: string;
  private component: string;
  private fullPath: string;
  private level?: number;
  private parentId?: string;
  private children: Organization[];
  private Organizations: Org[] = [];

  constructor({
    id:,
    name:,
    acronym:,
    description:,
    components:,
    fullPath:,
    level:,
    parentId:,
    children,
  }) {
    if (id) {
      this.id = id;
    }
    if (name) {
      this.name = name;
    }
    if (acronym) {
      this.acronym = acronym;
    }
    if (description) {
      this.description = description;
    }
    if (components) {
      this.component = components;
    }
    if (fullPath) {
      this.fullPath = fullPath;
    }
    if (level) {
      this.level = level;
    }
    if (parentId) {
      this.parentId = parentId;
    }
    if (children) {
      this.children = children;
    }
  }

  setData(data: Organization): void {
    this.data = data;
  }

  addChild(child: Org): void {
    this.Organizations.push(child);
  }

  getData(): Organization | null {
    return this.data;
  }

  getChildren(): Org[] {
    return this.Organizations;
  }
}

// Main conversion function
function buildOrganizationTree(orgList: Organization[]): Org | null {
  const records = new Map<string, Org>();
  let topOrg: Org | null = null;

  for (const orgData of orgList) {
    const id = orgData.id;
    const parentId = orgData.parentId;

    // Attempt to retrieve org from hash map
    let thisOrg = records.get(id);

    // If org is not in hash map, create new org and add to hash map
    if (thisOrg == null) {
      thisOrg = new Org(orgData);
      records.set(id, thisOrg);
    } else {
      thisOrg.setData(orgData);
    }

    // If parent id exists
    if (parentId != null) {
      // Get parent from hash map
      let parent = records.get(parentId);

      // If parent is not in hash map
      if (parent == null) {
        // Create empty parent and add to hash map, gets hydrated later above
        parent = new Org();
        records.set(parentId, parent);
      }
      // Add children
      parent.addChild(thisOrg);
    } else {
      topOrg = thisOrg;
    }
  }

  return topOrg;
}

// Alternative more efficient single-pass version
function buildOrganizationSubtree(orgList: Organization[], rootOrgId: string): Org | null {
  const records = new Map<string, Org>();
  const childrenMap = new Map<string, string[]>(); // parentId -> childIds[]
  let targetOrg: Org | null = null;

  // Build children mapping and create org objects
  for (const orgData of orgList) {
    const id = orgData.id;
    const parentId = orgData.parentId;

    // Create org object
    let thisOrg = records.get(id);
    if (thisOrg == null) {
      thisOrg = new Org(orgData);
      records.set(id, thisOrg);
    } else {
      thisOrg.setData(orgData);
    }

    // Mark target org
    if (id === rootOrgId) {
      targetOrg = thisOrg;
    }

    // Build children mapping
    if (parentId) {
      if (!childrenMap.has(parentId)) {
        childrenMap.set(parentId, []);
      }
      childrenMap.get(parentId)!.push(id);
    }
  }

  if (!targetOrg) {
    return null;
  }

  // Recursively build subtree starting from target org
  function buildSubtreeRecursive(orgId: string): void {
    const org = records.get(orgId);
    if (!org) return;

    const childIds = childrenMap.get(orgId) || [];
    for (const childId of childIds) {
      const childOrg = records.get(childId);
      if (childOrg) {
        org.addChild(childOrg);
        buildSubtreeRecursive(childId); // Recursively build children
      }
    }
  }

  buildSubtreeRecursive(rootOrgId);
  return targetOrg;
}

// Helper function to convert Org to a serializable format
function convertOrg(org: Org | null): any {
  if (!org) return null;

  const data = org.getData();
  const Organizations = org.getChildren();

  return {
    ...data,
    Organizations: Organizations.length > 0
      ? Organizations.map((child) => convertOrg(child)) : undefined,
  };
}

export function processOrganizationList(orgList: Organization[]): any {
  const topOrg = buildOrganizationTree(orgList);
  return convertOrg(topOrg);
}

export function processFilteredOrganizationList(
  orgList: Organization[],
  id: string | null,
  name: string | null,
  acronym: string | null,
): any {
  const topOrg = (orgList, id, name, acronym);
  return convertOrg(topOrg);
}

const organizationList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = req.query.id ?? null;
  const name = req.query.name ?? null;
  const acronym = req.query.acronym ?? null;

  const whereObj: Where = {
    where: {
      operation: {
        column: 'Org State',
        operator: '=',
        value: 'Active',
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_ORGANIZATION, [
    '"Sparx Organization GUID" as id',
    '"Organization Name" as name',
    '"Acronym" as acronym',
    '"Comments" as description',
    '"Organization Component" as component',
    '"Full Path with Org Name" as fullPath',
    '"Parent Organization GUID" as parentId',
    // '"Organization Name Level" as level',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve organizations' });
  }

  const queryViewResult = viewResult as Array<number | Array<OrgData>>;

  const organizationResult = isArray(queryViewResult[0]) ? queryViewResult[0] : [];
  // const organizationResult: Organization[] = isEmpty(viewResult[0]) ? [] : viewResult[0];
  if ((typeof organizationResult === 'number') || !isArray(organizationResult)) {
    app.logger.error({ error: new Error('The returned data was not in the expected format') });
    return res.status(500).send({ error: 'Unable to retrieve organizations' });
  }
  const orgTree = processOrganizationList(organizationResult);

  if (id || name || acronym) {
    const rootOrg = organizationResult.find((org: Organization) => org.id === id
      || org.name === name
      || org.acronym === acronym
    );
    const filteredOrgTree = buildOrganizationSubtree(organizationResult, rootOrg.id);
    console.log({
      filteredOrgTree,
    })
    // const filteredOrgTree = processFilteredOrganizationList(organizationResult, id, name, acronym);
    return res.status(200).send([{
      Organizations: filteredOrgTree,
    }]);
  }

  return res.status(200).send({
    count: organizationResult.length,
    Organizations: orgTree,
  });
};

export default organizationList;
