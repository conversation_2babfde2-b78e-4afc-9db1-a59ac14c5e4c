import { isError } from 'lodash';
import authorityToOperate from './authorityToOperate';
import exchange from './exchange';
import role from './role';
import system from './system';
import budget from './budget';
import costType from './costType';
import contract from './contract';
import budgetSystemCost from './budgetSystemCost';
import softwareProduct from './softwareProducts';
import threat from './threat';
import url from './url';
import deployment from './deployment';

import { GetResourceConfig } from '../../types';
import { prependPathToResources } from '../../utils/resources';

const updatedAuthorityToOperate = prependPathToResources('/authorityToOperate', authorityToOperate());
if (isError(updatedAuthorityToOperate)) {
  throw new Error('An error occurred updating the path for authority to operate');
}

const updatedExchange = prependPathToResources('/exchange', exchange());
if (isError(updatedExchange)) {
  throw new Error('An error occurred updating the path for exchange');
}

const updatedRole = prependPathToResources('/role', role());
if (isError(updatedRole)) {
  throw new Error('An error occurred updating the path for role');
}

const updatedSystemSummary = prependPathToResources('/system', system());
if (isError(updatedSystemSummary)) {
  throw new Error('An error occurred updating the path for system');
}

const updatedUrl = prependPathToResources('/url', url());
if (isError(updatedUrl)) {
  throw new Error('An error occurred updating the path for url');
}

const updatedBudget = prependPathToResources('/budget', budget());
if (isError(updatedBudget)) {
  throw new Error('An error occured udpating the path for budget');
}

const updatedcostType = prependPathToResources('/costType', costType());
if (isError(updatedcostType)) {
  throw new Error('An error occured udpating the path for costType');
}

const updatedThreat = prependPathToResources('/threat', threat());
if (isError(updatedThreat)) {
  throw new Error('An error occured updating the path for threat');
}

const updatedContract = prependPathToResources('/contract', contract());
if (isError(updatedContract)) {
  throw new Error('An error occurred updating the path for contract');
}

const updatedBudgetSystemCost = prependPathToResources('/budgetSystemCost', budgetSystemCost());
if (isError(updatedBudgetSystemCost)) {
  throw new Error('An error occurred updating the path for budget system cost');
}

const updatedSoftwareProducts = prependPathToResources('/softwareProducts', softwareProduct());
if (isError(updatedSoftwareProducts)) {
  throw new Error('An error occurred updating the path for software products');
}

const updatedDeployment = prependPathToResources('/deployment', deployment());
if (isError(updatedDeployment)) {
  throw new Error('An error occurred updating the path for deployment');
}

const getResourceConfig: GetResourceConfig = () => [
  ...updatedAuthorityToOperate,
  ...updatedExchange,
  ...updatedRole,
  ...updatedSystemSummary,
  ...updatedBudget,
  ...updatedcostType,
  ...updatedContract,
  ...updatedBudgetSystemCost,
  ...updatedThreat,
  ...updatedUrl,
  ...updatedSoftwareProducts,
  ...updatedDeployment,
];

export default getResourceConfig;
