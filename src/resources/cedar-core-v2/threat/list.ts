import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_ATO_THREAT,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  OrObject,
  Where,
  ThreatListResult,
} from '../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/threat'
#swagger.method = 'get'
#swagger.tags = ['Threat']
#swagger.description = 'Threat List'
#swagger.parameters['ids'] = {
  in: 'path',
  required: true,
  description: 'A comma delimited list of object IDs, for example, an ATO ID.',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000},{550e8400-e29b-41d4-a716-446655440000}'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'A list of threats (like a Plan of Actions and Milestones POA&M), for a specific object (like an ATO) using a unique ID.',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          Threats: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                alternativeId: {
                  type: 'string',
                  description: 'ID assigned by a source system. For example, POA&M ID from CFACTS'
                },
                controlFamily: {
                  type: 'string'
                },
                daysOpen: {
                  type: 'integer',
                  format: 'int32'
                },
                id: {
                  type: 'string',
                  description: 'ID assigned by CEDAR'
                },
                parentId: {
                  type: 'string',
                  description: 'ID of the object the threat is assigned to'
                },
                type: {
                  type: 'string'
                },
                weaknessRiskLevel: {
                  type: 'string'
                }
              }
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const threatList = async (
  req: Request,
  res: Response,
) => {
  const convertCDLToArray = (string: string) => string.split(',');

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const ids = get(req, 'query.ids');
  if (!isString(ids) || isEmpty(ids)) {
    return res.status(400).send({ error: 'Please provide required parameter \'ids\'' });
  }

  const idsArr = convertCDLToArray(ids);

  const whereObj: Where = {
    where: {
      operation: {
        column: 'Sparx ATO GUID',
        operator: '=',
        value: idsArr[0],
      },
    },
  };

  if (idsArr.length > 1) {
    whereObj.or = [];
    for (let i = 1; i < idsArr.length; i += 1) {
      const orObj: OrObject = {
        operation: {
          column: 'Sparx ATO GUID',
          operator: '=',
          value: idsArr[i],
        },
      };
      whereObj.or.push(orObj);
    }
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_ATO_THREAT, [
    '"Sparx Threat GUID" as id',
    '"Sparx ATO GUID" as parentId',
    '"Threat" as alternativeId',
    '"Control Family" as controlFamily',
    '"Days Open" as daysOpen',
    '"Risk Level" as weaknessRiskLevel',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve threats by ids' });
  }

  const threatsResults = get(viewResult, '[0]', []).map((item: ThreatListResult) => {
    const updatedDaysOpen = parseInt(item.daysOpen as string, 10);
    return {
      ...item,
      daysOpen: updatedDaysOpen,
      type: 'POA&M',
    };
  });

  if (threatsResults.length === 0) {
    return res.status(200).send({
      count: 0,
    });
  }

  return res.status(200).send({
    count: threatsResults.length,
    Threats: threatsResults,
  });
};

export default threatList;
