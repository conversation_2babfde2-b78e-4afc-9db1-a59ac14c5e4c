// /role/{id}
import { isError } from 'lodash';
import roleById from './by-id';
import roleTypeFind from './type';
import roleAdd from './add';
import { GetResourceConfig } from '../../../types';
import { prependPathToResources } from '../../../utils/resources';

const updatedRoleTypeFind = prependPathToResources('/type', roleTypeFind());
if (isError(updatedRoleTypeFind)) {
  throw new Error('An error occurred updating the path for authority to operate');
}

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-role-by-id',
  path: '/',
  method: 'get',
  resource: roleById,
}, {
  name: 'add-role',
  path: '/',
  method: 'post',
  resource: roleAdd,
},
...updatedRoleTypeFind,
];

export default getResourceConfig;
