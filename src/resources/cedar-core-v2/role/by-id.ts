import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_API_GET_ROLE,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  Where,
} from '../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/role'
#swagger.method = 'get'
#swagger.tags = ['Role']
#swagger.description = 'Finds a list of role assignments based on an object's ID. If objectId and roleTypeId are both provided, a list of role assignments for only those specific role type IDs are returned. If roleId is provided, then objectId and roleTypeId should not be provided and a specific role assignment is returned.'
#swagger.summary = 'Finds a list of role assignments based on an object's ID and/or role type.'
#swagger.produces = ['application/json']

#swagger.parameters['application'] = {
  in: 'query',
  required: true,
  schema: {
    type: 'string',
    description: 'Application where the object or role exists.',
    '@enum': ['alfabet', 'all'],
    example: 'alfabet'
  }
}

#swagger.parameters['roleId'] = {
  in: 'query',
  required: false,
  description: 'ID of a specific role assignment',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.parameters['objectId'] = {
  in: 'query',
  required: false,
  description: 'ID of the object that roles are assigned to',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.parameters['roleTypeId'] = {
  in: 'query',
  required: false,
  description: 'ID of a specific role type to be returned. Used with objectId to limit results',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.responses[200] = {
  description: 'Successfully fetched roles',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          Roles: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/Role'
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[404] = {
  description: 'Not Found',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/NotFound'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const roleById = async (
  req: Request,
  res: Response,
): Promise<Response> => {
  const application = get(req, 'query.application', '');
  const objectId = get(req, 'query.objectId', '') as string;
  const roleId = get(req, 'query.roleId', '') as string;
  const roleTypeId = get(req, 'query.roleTypeId', '') as string;

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  if (isEmpty(application)) {
    return res.status(400).send({ message: 'Please specify a valid application.' });
  }

  if (isEmpty(objectId) && isEmpty(roleId)) {
    return res.status(400).send({ message: 'Either role ID or object ID must be provided.' });
  }

  if (!isEmpty(objectId) && !isEmpty(roleId)) {
    return res.status(400).send({ message: 'If role ID is provided then object ID should not be provided.' });
  }

  if (!isEmpty(roleId) && !isEmpty(roleTypeId)) {
    return res.status(400).send({ message: 'If role ID is provided then role type ID should not be provided.' });
  }

  const handleDb = async (where: Where) => {
    const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_API_GET_ROLE, [
      '"Sparx System GUID" as objectId',
      '"Role ID" as roleId',
      '"Sparx Role Type GUID" as roleTypeId',
      '"Role Name" as roleTypeName',
      '"Role Description" as roleTypeDesc',
      '"Sparx Asignee GUID" as assigneeId',
      '"Asignee User Name" as assigneeUserName',
      '"Assignee Is Deleted" as assigneeIsDeleted',
      '"Asignee First Name" as assigneeFirstName',
      '"Asignee Last Name" as assigneeLastName',
      '"Asignee Email" as assigneeEmail',
      '"Assignee Phone" as assigneePhone',
      '"Assignee Org ID" as assigneeOrdId',
      '"Assignee Organization Name" as assigneeOrgName',
      '"Assignee Description" as assigneeDesc',
      '"Assignee Type" as assigneeType',
    ], where));

    if (viewError || isError(viewResult)) {
      app.logger.error({ error: viewError || viewResult });
      return res.status(500).send({ message: 'Unable to retrieve role by ID' });
    }

    const rolesResults = get(viewResult, '[0]', []).map((item: object) => ({ application, ...item }));

    return res.status(200).send({
      count: rolesResults.length,
      Roles: rolesResults,
    });
  };

  if (!isEmpty(roleTypeId)) {
    const where: Where = {
      where: {
        operation: {
          column: 'Sparx Role Type GUID',
          operator: '=',
          value: roleTypeId,
        },
      },
    };

    if (!isEmpty(objectId)) {
      where.and = [{
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: objectId,
        },
      }];
    }

    return handleDb(where);
  }

  const where: Where = {
    where: {
      operation: {
        column: '',
        operator: '=',
        value: '',
      },
    },
  };

  if (!isEmpty(objectId)) {
    where.operation = {
      column: 'Sparx System GUID',
      operator: '=',
      value: objectId,
    };
  }

  if (!isEmpty(roleId)) {
    where.operation = {
      column: 'Role ID',
      operator: '=',
      value: roleId,
    };
  }

  return handleDb(where);
};

export default roleById;
