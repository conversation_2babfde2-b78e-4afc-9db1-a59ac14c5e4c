import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../../data-sources/mssql';
import {
  getDb,
  DBO_SPARX_ROLE,
} from '../../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../../utils/general';
import {
  Where,
  RoleTypeInitialResult,
} from '../../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/role/type/{application}'
#swagger.method = 'get'
#swagger.tags = ['Role']
#swagger.summary = 'Finds a list of role types available within an application. This interface takes in application.'
#swagger.description = 'Finds a list of role types available within an application. This interface takes in application. The list of current roles are: AI Contact API Contact Budget Analyst Business Owner Business Question Contact Contracting Officer's Representative (COR) DA Reviewer Data Center Contact ISSO Government Task Lead (GTL) Project Lead QA Reviewer System Maintainer Subject Matter Expert (SME) Support Staff Survey Point of Contact Technical System Issues Contact'
#swagger.produces = ['application/json']

#swagger.parameters['application'] = {
  in: 'path',
  required: true,
  schema: {
    type: 'string',
    description: 'Application identifier to retrieve role types for',
    '@enum': ['alfabet', 'all'],
    example: 'alfabet'
  }
}

#swagger.responses[200] = {
  description: 'Successfully fetched role types',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          RoleTypes: {
            type: 'array',
            items: {
              $ref: '#/components/schemas/RoleType'
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: { $ref: '#/components/schemas/BadRequest' }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: { $ref: '#/components/schemas/Unauthorized' }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: { $ref: '#/components/schemas/ErrorResponse' }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const roleTypeFind = async (
  req: Request,
  res: Response,
) => {
  const application = get(req, 'params.application', '');
  const acceptedApplications = ['all', 'alfabet'];
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  if (isEmpty(application)) {
    return res.status(400).send({ message: 'Please specify a valid application' });
  }

  if (!acceptedApplications.includes((application as string))) {
    return res.status(400).send({ message: 'Please specify a valid application' });
  }

  const where: Where = {
    where: {
      operation: {
        column: 'Census Role',
        operator: '=',
        value: 'TRUE',
      },
    },
  };

  const [viewError, viewResult] = await tryAsync(db.queryView(DBO_SPARX_ROLE, [
    '"Sparx Role GUID" as id',
    '"Role Name" as name',
    '"Role Description" as description',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ message: 'Unable to retrieve role type' });
  }

  const rolesResults = get(viewResult, '[0]', []).map((item: RoleTypeInitialResult) => ({
    id: item.id,
    application,
    name: item.name,
    description: item.description,
  }));

  return res.status(200).send({
    count: rolesResults.length,
    RoleTypes: rolesResults,
  });
};

export default roleTypeFind;
