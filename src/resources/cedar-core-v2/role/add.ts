import {
  Response,
  Request,
} from 'express';
import {
  attempt,
  get,
  isError,
  isEmpty,
  uniq,
  isNull,
  set,
} from 'lodash';
import {
  roleValid,
  createPromise,
  createUpdateObj,
} from '../../../utils/role';
import {
  tryAsync,
} from '../../../utils/general';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SP_INSERT_OBJECTROLE,
} from '../../../utils/db-helpers';
import {
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
  RoleAddResultRaw,
  RoleObject,
  RoleParsedObject,
  CreatePromiseResult,
} from '../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/role'
#swagger.method = 'post'
#swagger.summary = 'Add role assignments to a CEDAR application. This interface takes in objectId, roleTypeId AND either assigneeId, assigneeUserName or assigneeOrgId are required.'
#swagger.tags = ['Role']
#swagger.description = 'Add role assignments to a CEDAR application. This interface takes in objectId, roleTypeId AND either assigneeId, assigneeUserName or assigneeOrgId are required.'
#swagger.produces = ['application/json']

#swagger.requestBody = {
  required: true,
  description: 'Role assignment information to be added to a CEDAR application.',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: ['application', 'Roles'],
        properties: {
          application: {
            type: 'string',
            description: 'Application where the role assignments will be added',
            '@enum': ['alfabet', 'all'],
            example: 'alfabet'
          },
          Roles: {
            type: 'array',
            items: {
                $ref: '#/components/schemas/Role'
            }
          }
        }
      }
    }
  }
}

#swagger.responses[200] = {
  description: 'Successful request',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          result: {
            type: 'string',
            example: 'success'
          },
          message: {
            type: 'array',
            items: {
              type: 'string',
              example: '{550e8400-e29b-41d4-a716-446655440000}'
            }
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const roleAdd = async (
  req: Request,
  res: Response,
): Promise<Response> => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];
  const queryData: StoredProcedureData[] = [{
    name: 'jsonOutput',
    value: 'result',
    isOutput: true,
  }];
  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  const application: string = get(req, 'body.application', '');
  const acceptedApplications = ['all', 'alfabet'];

  if (!application || !acceptedApplications.includes(application)) {
    app.logger.error({ error: new Error('Invalid application given') });
    return res.status(400).send({ message: 'Please specify a valid application.' });
  }

  const roleObjArr = get(req, 'body.Roles', []) as RoleObject[];
  const validArr = roleObjArr.filter((role) => roleValid(role));

  const promises: Array<Promise<CreatePromiseResult>> = validArr.map(
    (role) => createPromise(app, db, role),
  );

  // this will always return an array of completed promises or an error
  const results: RoleObject[][] | Error = await Promise.all(promises).catch((error) => error);

  if (isError(results)) {
    app.logger.error({ error: results });
    return res.status(500).send({ error: 'No roles were able to be seeded' });
  }

  if (results.some((result) => isEmpty(result))) {
    const error = new Error('No roles were able to be seeded');
    app.logger.error({ error });
    return res.status(500).send({ error: error.message });
  }

  if (!results.every((item) => !isError(item))) {
    const errors = results.filter(isError);
    app.logger.error({ error: errors });
    return res.status(500).send({ error: 'There was an error creating the roles' });
  }

  // Making a new array to check for error so we dont mutate the original
  const innerResults = results.flatMap((result) => result);
  if (innerResults.some((innerRes) => isError(innerRes))) {
    const errors = innerResults.filter(isError);
    app.logger.error({ error: errors });
    return res.status(500).send({ error: 'There was an error creating the roles' });
  }

  const flatResults: RoleObject[] = results.flat();

  const updates = flatResults.map(
    (role, index) => createUpdateObj(app, String(index + 1), role),
  );

  const firstUpdateError = new Error();
  const validatedUpdates = updates.map((item) => {
    if (!isEmpty(firstUpdateError.message)) {
      return null;
    }

    if (isError(item)) {
      set(firstUpdateError, 'message', item.message);
      return null;
    }

    return item;
  }).filter((n) => !isNull(n));

  if (!isEmpty(firstUpdateError.message)) {
    app.logger.error({ error: firstUpdateError });
    return res.status(500).send({ error: 'An error occurred while creating one or more of the roles' });
  }

  const finalObjects = validatedUpdates.map((user) => user.Object);
  const finalRelations = validatedUpdates.flatMap((user) => [
    user.Relations.relationOne,
    user.Relations.relationTwo,
    user.Relations.relationThree,
  ]);

  const roleQueryData = {
    CurrentProfile: 'API User',
    Objects: finalObjects,
    Relations: finalRelations,
  };

  const jsonInput = JSON.stringify(roleQueryData);

  queryData.push({
    name: 'jsonInput',
    value: jsonInput,
  });

  const [roleAddError, roleAddResults] = await tryAsync(
    db.queryStoredProcedures(SP_INSERT_OBJECTROLE, queryParams, queryData, queryResults),
  );

  if (roleAddError || isError(roleAddResults)) {
    app.logger.error({ error: roleAddError || roleAddResults });
    return res.status(500).send({ error: 'Unable to get results from stored procedure' });
  }

  if (isEmpty(roleAddResults)) {
    app.logger.error({ error: 'No results received from stored procedure' });
    return res.status(500).send({ error: 'Unable to get results from stored procedure' });
  }

  // check the returned status
  // Casting as RoleAddResultRaw[][] due to queryStoredProcedures typing of Promise<unknown>
  const rows = roleAddResults as RoleAddResultRaw[][];
  const firstRow = get(rows, '[0][0]', {});
  const status = get(firstRow, 'queryStatus', -1);

  if (status === -1) {
    app.logger.error({ error: firstRow });
    return res.status(500).send({ error: 'Status of the query was invalid' });
  }

  if (status === 1) {
    return res.status(200).send({});
  }

  const rowResult = get(firstRow, 'result', '');
  if (!rowResult) {
    const error = new Error('The query stored procedures did not yield a result');
    app.logger.error({ error });
    return res.status(500).send({ error: error.message });
  }

  const parsed: RoleParsedObject = attempt(() => JSON.parse(rowResult
    .replace('{"GUID": ', '[')
    .replace('},"Count":', '], "Count": ')
    .replace(/"GUID": /g, '')));

  if (isError(parsed)) {
    const error = new Error('Invalid Sparx results');
    app.logger.error({ error });
    return res.status(500).send({ error: error.message });
  }

  const guids = uniq(parsed.NewObjects);
  return res.status(200).send({
    result: 'success',
    message: guids,
  });
};

export default roleAdd;
