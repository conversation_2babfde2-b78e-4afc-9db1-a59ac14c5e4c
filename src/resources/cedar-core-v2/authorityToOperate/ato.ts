import { Response, Request } from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import { getDb, SPARX_ATO_FULL_TBL } from '../../../utils/db-helpers';
import {
  tryAsync,
  strToBoolOrNull,
  strToBool,
  boolToStr,
} from '../../../utils/general';
import { isValidFormat } from '../../../utils/time';
import { OrObject, Where } from '../../../types';

const ato = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();
  const systemId = get(req, 'query.systemId');
  if (systemId && !isString(systemId)) {
    return res.status(400).send({ error: 'The systemId is a string' });
  }

  const byIdWhere = {
    where: {},
  };
  if (systemId) {
    const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
    if (systemIdVal.error) {
      return res.status(400).send({ error: 'The systemId is not valid' });
    }

    byIdWhere.where = {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: `{${systemIdVal.value}}`,
      },
    };
  }

  const paramOrs: OrObject[] = [];
  const uuid = get(req, 'query.uuid');
  if (uuid) {
    const uuidVal = baseSchema.uuid().validate(uuid);
    if (uuidVal.error) {
      return res.status(400).send({ error: 'The uuid is not valid' });
    }

    paramOrs.push({
      operation: {
        column: 'CMS_UUID',
        operator: '=',
        value: uuidVal.value,
      },
    });
  }

  const fismaSystemAcronym = get(req, 'query.fismaSystemAcronym');
  if (fismaSystemAcronym) {
    const fismaVal = baseSchema.validate(fismaSystemAcronym);
    if (fismaVal.error) {
      return res.status(400).send({ error: 'The fismaSystemAcronym is not valid' });
    }

    paramOrs.push({
      operation: {
        column: 'Acronym',
        operator: '=',
        value: fismaVal.value,
      },
    });
  }

  const tlcPhase = get(req, 'query.tlcPhase');
  if (tlcPhase) {
    const tlcVal = baseSchema.validate(tlcPhase);
    if (tlcVal.error) {
      return res.status(400).send({ error: 'The tlcPhase is not valid' });
    }

    paramOrs.push({
      operation: {
        column: 'TLC Phase',
        operator: '=',
        value: tlcVal.value,
      },
    });
  }

  const hasPiiQuery = get(req, 'query.containsPersonallyIdentifiableInformation');
  if (hasPiiQuery) {
    const piiVal = baseSchema.valid('true', 'True', 'TRUE', 'false', 'False', 'FALSE').validate(hasPiiQuery);
    if (piiVal.error) {
      return res.status(400).send({ error: 'The containsPersonallyIdentifiableInformation is not valid' });
    }

    const hasPiiBool = strToBool(piiVal.value);
    const hassPii = boolToStr(hasPiiBool, true);
    paramOrs.push({
      operation: {
        column: 'Collect Maintain Share PII',
        operator: '=',
        value: hassPii,
      },
    });
  }

  const isPhiQuery = get(req, 'query.isProtectedHealthInformation');
  if (isPhiQuery) {
    const phiVal = baseSchema.valid('true', 'True', 'TRUE', 'false', 'False', 'FALSE').validate(isPhiQuery);
    if (phiVal.error) {
      return res.status(400).send({ error: 'The isProtectedHealthInformation is not valid' });
    }

    const isPhiBool = strToBool(phiVal.value);
    const isPhi = boolToStr(isPhiBool, true);
    paramOrs.push({
      operation: {
        column: 'Has PHI',
        operator: '=',
        value: isPhi,
      },
    });
  }

  const dispositionDateAfter = get(req, 'query.dispositionDateAfter');
  if (dispositionDateAfter && !isString(dispositionDateAfter)) {
    return res.status(400).send({ error: 'The dispositionDateAfter is not a string' });
  }

  if (dispositionDateAfter) {
    const dateVal = isValidFormat(dispositionDateAfter, 'YYYY-MM-DD');
    if (!dateVal) {
      return res.status(400).send({ error: 'The dispositionDateAfter is not a valid format' });
    }

    paramOrs.push({
      operation: {
        column: 'Effective Date',
        operator: '=',
        value: dispositionDateAfter,
      },
    });
  }

  const dispositionDateBefore = get(req, 'query.dispositionDateBefore');
  if (dispositionDateBefore && !isString(dispositionDateBefore)) {
    return res.status(400).send({ error: 'The dispositionDateBefore is not a string' });
  }

  if (dispositionDateBefore) {
    const dateVal = isValidFormat(dispositionDateBefore, 'YYYY-MM-DD');
    if (!dateVal) {
      return res.status(400).send({ error: 'The dispositionDateBefore is not a valid format' });
    }

    paramOrs.push({
      operation: {
        column: 'Expiration Date',
        operator: '=',
        value: dispositionDateBefore,
      },
    });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ message: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ message: 'Database Unavailable' });
  }

  let where: Where | undefined;
  if (!isEmpty(byIdWhere.where)) {
    where = byIdWhere;
  }

  if (!systemId && !isEmpty(paramOrs)) {
    where = {
      where: paramOrs[0],
    };

    if (paramOrs.length > 1) {
      const whereOr: OrObject[] = [];
      paramOrs.slice(1).forEach((item) => {
        whereOr.push(item);
      });
      where.or = whereOr;
    }
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_ATO_FULL_TBL, [
    '"id" as cedarId',
    '"CMS_UUID" as uuid',
    '"ATO Name" as fismaSystemName',
    '"Acronym" as fismaSystemAcronym',
    '"Actual Disposition Date" as actualDispositionDate',
    // Convert String to Integer with DB
    'CAST("Count of Open POAMs" AS INTEGER) as countOfOpenPoams',
    // Convert String to Integer with DB
    'CAST("NonPrivileged User Population" AS INTEGER) as countOfTotalNonPrivilegedUserPopulation',
    // Convert String to Integer with DB
    'CAST("Privileged User Population" AS INTEGER) as countOfTotalPrivilegedUserPopulation',
    // Convert Yes/No to boolean after call
    '"Collect Maintain Share PII" as containsPersonallyIdentifiableInformation',
    '"Effective Date" as dateAuthorizationMemoSigned',
    '"Expiration Date" as dateAuthorizationMemoExpires',
    '"EAuthentication Level" as eAuthenticationLevel',
    '"FIPS 199 Overall Impact Rating" as fips199OverallImpactRating',
    // Convert Yes/No to boolean after call
    '"Accessed by Non-Org Users" as isAccessedByNonOrganizationalUsers',
    // Convert Yes/No to boolean after call
    '"PII is Limited to Username and Password" as isPiiLimitedToUserNameAndPass',
    // Convert Yes/No to boolean after call
    '"Has PHI" as isProtectedHealthInformation',
    '"Last Act Date" as lastActScaDate',
    '"Last Assessment Date" as lastAssessmentDate',
    '"Last Contingency Plan Completion Date" as lastContingencyPlanCompletionDate',
    '"Last Pentest Date" as lastPenTestDate',
    '"PIA Completion Date" as piaCompletionDate',
    '"PrimaryCyberRiskAdvisor" as primaryCyberRiskAdvisor',
    '"Privacy POC" as privacySubjectMatterExpert',
    // Convert String to Integer with DB, convert to single place decimal after call
    'CAST("Recovery Point Objective" AS DECIMAL(10, 2)) as recoveryPointObjective',
    // Convert String to Integer with DB, convert to single place decimal after call
    'CAST("Recovery Time Objective" AS INTEGER) as recoveryTimeObjective',
    '"System of Records Notice" as systemOfRecordsNotice',
    '"TLC Phase" as tlcPhase',
    '"XLC Phase" as xlcPhase',
  ], where));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ message: 'Unable to retrieve authority to operate data' });
  }

  const atoResults = get(viewResult, '[0]', []).map((item: object) => {
    const itemHasPii = get(item, 'containsPersonallyIdentifiableInformation', null);
    const isAccessedByNonOrganizationalUsers = get(item, 'isAccessedByNonOrganizationalUsers', null);
    const isPiiLimitedToUserNameAndPass = get(item, 'isPiiLimitedToUserNameAndPass', null);
    const isProtectedHealthInformation = get(item, 'isProtectedHealthInformation', null);
    const systemOfRecordsNotice = get(item, 'systemOfRecordsNotice', null) as string | null;
    const recoveryPointObjective = get(item, 'recoveryPointObjective', null) as number | null;
    const recoveryTimeObjective = get(item, 'recoveryTimeObjective', null) as number | null;

    let formattedSORN: string[] = [];
    if (systemOfRecordsNotice) {
      formattedSORN = systemOfRecordsNotice.split('|');
    }

    let formattedRPO = recoveryPointObjective;
    if (formattedRPO && `${formattedRPO}`.indexOf('.') === -1) {
      formattedRPO = Number(formattedRPO.toFixed(1));
    }

    let formattedRTO = recoveryTimeObjective;
    if (formattedRTO && `${formattedRTO}`.indexOf('.') === -1) {
      formattedRTO = Number(formattedRTO.toFixed(1));
    }

    return {
      ...item,
      containsPersonallyIdentifiableInformation: strToBoolOrNull(itemHasPii),
      isAccessedByNonOrganizationalUsers: strToBoolOrNull(isAccessedByNonOrganizationalUsers),
      isPiiLimitedToUserNameAndPass: strToBoolOrNull(isPiiLimitedToUserNameAndPass),
      isProtectedHealthInformation: strToBoolOrNull(isProtectedHealthInformation),
      systemOfRecordsNotice: formattedSORN,
      recoveryPointObjective: formattedRPO,
      recoveryTimeObjective: formattedRTO,
    };
  });

  return res.status(200).send({
    AuthorityToOperateList: atoResults,
    count: atoResults.length,
  });
};

export default ato;
