import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  SPARX_SYSTEM_ANNUAL_COST,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';
import {
  Where,
  BudgetSystemCostInitialResult,
} from '../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/budgetSystemCostFind'
#swagger.method = 'get'
#swagger.tags = ['budgetSystemCost']
#swagger.description = ''
#swagger.consumes: ['application/json']
#swagger.produces: ['application/json']
#swagger.parameters['systemId'] = {
  in: 'query',
  required: false,
  description: 'ID of the system that the budget is associated with. Adding this parameter will instruct the interface to only return the system matching the systemId.',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'OK',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
        'count',
        ],
        properties: {
          BudgetActualCost: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                ActualSystemCost: {
                  type: 'string',
                },
                FiscalYear: {
                  type: 'string',
                },
                systemId: {
                  type: 'string',
                }
              }
            }
          },
          count: {
            type: 'integer',
            format: 'int32'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const budgetSystemCostFind = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId', '') as string;

  const handleDb = async (where?: Where) => {
    const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_SYSTEM_ANNUAL_COST, [
      '"Year" as FiscalYear',
      '"Cost" as ActualSystemCost',
      '"Sparx System GUID" as systemId',
    ], where));

    if (viewError || isError(viewResult)) {
      app.logger.error({ error: viewError || viewResult });
      return res.status(500).send({ message: 'Unable to retrieve budget system cost' });
    }

    const costsResults = get(viewResult, '[0]', []).map((item: BudgetSystemCostInitialResult) => {
      const updatedActualSystemCost = `${item.ActualSystemCost}.0`;
      const updatedFiscalYear = `${item.FiscalYear}`;
      return ({
        FiscalYear: updatedFiscalYear,
        ActualSystemCost: updatedActualSystemCost,
        systemId: item.systemId,
      });
    });

    return res.status(200).send({
      count: costsResults.length,
      BudgetActualCost: costsResults,
    });
  };

  if (!isEmpty(systemId)) {
    const where: Where = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: systemId,
        },
      },
    };

    return handleDb(where);
  }

  return handleDb();
};

export default budgetSystemCostFind;
