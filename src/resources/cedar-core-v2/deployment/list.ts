import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';

import {
  deploymentListUtil,
} from '../../../utils/deployments';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/deployment'
#swagger.method = 'get'
#swagger.tags = ['deployment']
#swagger.description = 'Retrieve a list of deployments based on query criteria (systemId, state, status and deploymentType).'
#swagger.operationId = 'deploymentFindList'
#swagger.consumes = ['application/json']
#swagger.produces = ['application/json']

#swagger.parameters['systemId'] = {
  in: 'query',
  description: 'ID of the system that the deployment is associated with.',
  required: true,
  type: 'string',
}

#swagger.parameters['state'] = {
  in: 'query',
  description: 'Deployment state.',
  required: false,
  type: 'string',
  '@enum': [
    'active',
    'planned',
    'retired',
  ],
  example: 'active'
}

#swagger.parameters['status'] = {
  in: 'query',
  description: 'Deployment status.',
  required: false,
  type: 'string',
  '@enum': [
    'active',
    'planned',
    'retired',
  ],
}

#swagger.parameters['deploymentType'] = {
  in: 'query',
  description: 'Deployment type.',
  required: false,
  type: 'string',
  '@enum': [
    'COOP DR',
    'Development',
    'Implementation',
    'Integration',
    'Production',
    'Testing',
    'Training',
    'Validation',
    'Other'
  ],
}

#swagger.responses[200] = {
  description: 'OK',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'count',
        ],
        properties: {
          Deployments: {
            type: 'array',
            items: {
              type: 'object',
              required: [
                'id',
                'name',
                'systemId',
              ],
              properties: {
                id: {
                  type: 'string',
                  example: '351-1-0'
                },
                name: {
                  type: 'string',
                  example: 'Accountable Care Organization Management System v.1.0 (COOP DR)'
                },
                description: {
                  type: 'string'
                },
                deploymentType: {
                  type: 'string',
                  '@enum': [
                    'COOP DR',
                    'Development',
                    'Implementation',
                    'Integration',
                    'Production',
                    'Testing',
                    'Training',
                    'Validation',
                    'Other'
                  ]
                },
                systemId: {
                  type: 'string',
                  example: '326-1-0'
                },
                systemName: {
                  type: 'string',
                  example: 'Health Insurance and Oversight System'
                },
                systemVersion: {
                  type: 'string',
                  example: '1'
                },
                status: {
                  type: 'string',
                  '@enum': [
                    'approved',
                    'draft'
                  ]
                },
                state: {
                  type: 'string',
                  '@enum': [
                    'active',
                    'planned',
                    'retired'
                  ]
                },
                startDate: {
                  type: 'string',
                  format: 'date'
                },
                endDate: {
                  type: 'string',
                  format: 'date'
                },
                deploymentElementId: {
                  type: 'string',
                  example: '69-1-0'
                },
                contractorName: {
                  type: 'string',
                  example: 'Acumen'
                },
                hasProductionData: {
                  type: 'string',
                  example: 'yes'
                },
                isHotSite: {
                  type: 'string',
                  example: 'yes'
                },
                replicatedSystemElements: {
                  type: 'array',
                  items: {
                    type: 'string',
                    example: 'System Server Software'
                  }
                },
                wanType: {
                  type: 'string',
                  example: 'Internet - Contractor'
                },
                wanTypeOther: {
                  type: 'string',
                  example: 'Internet - Contractor'
                },
                movingToCloud: {
                  type: 'string',
                  example: 'Yes'
                },
                movingToCloudDate: {
                  type: 'string',
                  format: 'date',
                  example: '2021-10-01'
                },
                usersRequiringMFA: {
                  type: 'string',
                  example: 'End-users,Developers,System Administrators,Other Special Users'
                },
                otherSpecialUsers: {
                  type: 'string',
                  example: 'end users'
                },
                networkEncryption: {
                  type: 'string',
                  example: 'We do not encrypt network traffic leaving our system,We encrypt network traffic leaving our system to other systems on the CMS-internal network'
                },
                awsEnclave: {
                  type: 'string',
                  example: 'AWS East'
                },
                awsEnclaveOther: {
                  type: 'string',
                  example: 'AWS Pacific'
                },
                DataCenter: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: '55-1-0'
                    },
                    name: {
                      type: 'string',
                      example: 'CMS Baltimore Data Center - EDC4'
                    },
                    version: {
                      type: 'string',
                      example: '1'
                    },
                    description: {
                      type: 'string'
                    },
                    status: {
                      type: 'string',
                      '@enum': [
                        'approved',
                        'draft'
                      ]
                    },
                    state: {
                      type: 'string',
                      '@enum': [
                        'active',
                        'planned',
                        'retired'
                      ]
                    },
                    startDate: {
                      type: 'string',
                      format: 'date'
                    },
                    endDate: {
                      type: 'string',
                      format: 'date'
                    },
                    address1: {
                      type: 'string',
                      example: '123 main street'
                    },
                    address2: {
                      type: 'string',
                      example: 'suite 100'
                    },
                    city: {
                      type: 'string',
                      example: 'New York'
                    },
                    addressState: {
                      type: 'string',
                      example: 'NY'
                    },
                    zip: {
                      type: 'string',
                      example: '10002'
                    }
                  }
                }
              }
            }
          },
          count: {
            type: 'integer',
          }
        },
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const deploymentList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const deploymentResult = await deploymentListUtil(
    app,
    db,
    get(req, 'query', {}) as Record<'string', string>,
  );

  if (isError(deploymentResult)) {
    app.logger.error({ error: deploymentResult });
    return res.status(500).send({ error: 'There was an issue fetching the deployments/data centers' });
  }

  return res.status(200).send(deploymentResult);
};

export default deploymentList;
