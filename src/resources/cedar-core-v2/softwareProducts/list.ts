import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';
import { getSoftwareProductsList } from '../../../utils/softwareProducts';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/softwareProducts'
#swagger.method = 'get'
#swagger.tags = ['softwareProducts']
#swagger.description = 'softwareProductsList'
#swagger.parameters['id'] = {
  in: 'path',
  required: true,
  description: 'Application ID',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Returns relevant properties and a list of software products',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          aiSolnCatg: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          aiSolnCatgOther: {
              type: 'string'
          },
          apiDataArea: {
              type: 'array',
              items: {
                  type: 'string'
              }
          },
          apiDescPubLocation: {
              type: 'string'
          },
          apiDescPublished: {
              type: 'string'
          },
          apiFHIRUse: {
              type: 'string'
          },
          apiFHIRUseOther: {
              type: 'string'
          },
          apiHasPortal: {
              type: 'boolean'
          },
          apisAccessibility: {
              type: 'string'
          },
          apisDeveloped: {
              type: 'string'
          },
          developmentStage: {
              type: 'string'
          },
          softwareProducts: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                api_gateway_use: {
                  type: 'boolean'
                },
                ela_purchase: {
                  type: 'string'
                },
                ela_vendor_id: {
                  type: 'string'
                },
                provides_ai_capability: {
                  type: 'boolean'
                },
                refstr: {
                  type: 'string'
                },
                softwareCatagoryConnectionGuid: {
                  type: 'string'
                },
                softwareVendorConnectionGuid: {
                  type: 'string'
                },
                software_cost: {
                  type: 'string'
                },
                software_ela_organization: {
                  type: 'string'
                },
                software_name: {
                  type: 'string'
                },
                systemSoftwareConnectionGuid: {
                  type: 'string'
                },
                technopedia_category: {
                  type: 'string'
                },
                technopedia_id: {
                  type: 'string'
                },
                vendor_name: {
                  type: 'string'
                }
              }
            }
          },
          systemHasApiGateway: {
              type: 'boolean'
          },
          usesAiTech: {
              type: 'string'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const softwareProductsList = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'query.id');

  if (!isString(id) || isEmpty(id)) {
    const error = new Error('Please provide required parameters \'id\'');
    app.logger.error({ error });
    return res.status(400).send({ error: 'Please provide required parameters \'id\'' });
  }

  const idVal = baseSchema.uuid().validate(id.slice(1).slice(0, -1));
  if (idVal.error) {
    app.logger.error({ error: idVal.error });
    return res.status(400).send({ error: 'The system ID is not valid' });
  }

  const softwareProductsListCall = await getSoftwareProductsList(app, db, id);

  if (isError(softwareProductsListCall)) {
    app.logger.error({ error: softwareProductsListCall });
    return res.status(500).send({ error: softwareProductsListCall.message });
  }
  return res.status(200).send(softwareProductsListCall);
};

export default softwareProductsList;
