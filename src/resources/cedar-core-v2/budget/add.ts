import { Request, Response } from 'express';
import {
  get, isArray, isEmpty, isError, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';
import MssqlData from '../../../data-sources/mssql';
import { getDb } from '../../../utils/db-helpers';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { sendError, sendSuccess, logAppError } from '../../../utils/express/responses';
import {
  CMSApp,
} from '../../../types';
import {
  BudgetRequest,
  executeSparxBudgetOperation,
} from '../../../utils/budget';

// Define local types as required, as per guidelines.
export interface BudgetAddRequestPayload {
  Budgets: BudgetRequest[];
}

const budgetAdd = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  let app: CMSApp | undefined;

  try {
    app = get(req, 'systemApp');
    if (!app) {
      sendError(res, 500, { message: [Messages.app_invalid] });
      return;
    }

    const db = getDb<MssqlData>(app, DatabaseConfigs.sparxea);
    if (isError(db)) {
      app.logger.error({ error: db });
      sendError(res, 500, { message: [Messages.db_unavailable] });
      return;
    }

    const payload: BudgetAddRequestPayload = get(req, 'body');
    const budgetsToAdd = get(payload, 'Budgets');

    // Input validation: Budgets array must be present and not empty
    if (!budgetsToAdd || !isArray(budgetsToAdd) || isEmpty(budgetsToAdd)) {
      sendError(res, 400, { message: ['Please provide a list of budgets.'] });
      return;
    }

    // Validate all budgets first before processing
    const invalidBudget = budgetsToAdd.find(
      (budget) => !budget.projectId || !isString(budget.projectId),
    );
    if (invalidBudget) {
      sendError(res, 400, { message: ['Missing or invalid `projectId` for a budget.'] });
      return;
    }

    // Execute the SparX budget operation using the utility function
    const result = await executeSparxBudgetOperation(app, db, budgetsToAdd);

    if (isError(result)) {
      const errorMessage = result.message;

      // Check if this is a stored procedure non-zero status error
      if (errorMessage.includes('Stored procedure returned non-zero status')) {
        // The utility already logged the error, just send the response
        sendError(res, 500, { message: ['Failed to add budget(s).'] });
        return;
      }

      // All other errors from the utility function are treated as stored procedure execution errors
      // because the utility function handles the database operations
      sendError(res, 500, { message: [Messages.db_query_stored_procedure_error] });
      return;
    }

    sendSuccess(res, {
      result: 'success',
      message: [`Successfully added ${result.count} budget(s).`],
    });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'budget',
        action: 'add',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: ['Internal server error'] });
  }
});

export default budgetAdd;
