import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/budget/list',
  method: 'delete',
  tags: ['System Census'],
  description: 'Delete a list of budget items based on their ID(s).',
  parameters: [
    {
      name: 'id',
      in: 'query',
      description: 'An array of one or more budget IDs to delete. Can be provided as repeated parameters (id=ID1&id=ID2) or with bracket notation (id[]=ID1&id[]=ID2).',
      required: true,
      schema: {
        type: 'array',
        items: {
          type: 'string',
          example: '11111111-2222-3333-4444-555555555555',
        },
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Budget items deleted successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Deleted 1 budget item(s)'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
