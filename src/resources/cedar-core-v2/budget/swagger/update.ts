import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/budget',
  method: 'put',
  tags: ['System Census'],
  description: 'Update one or more existing budgets for a system.',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['Budgets'],
          properties: {
            Budgets: {
              type: 'array',
              description: 'List of budgets to update for a system.',
              items: {
                type: 'object',
                required: ['projectId', 'fundingId'],
                properties: {
                  FiscalYear: {
                    type: 'string',
                    description: 'Fiscal year of the budget.',
                    example: '2024',
                    nullable: true,
                  },
                  FundingSource: {
                    type: 'string',
                    description: 'Source of the budget funding.',
                    example: 'Internal',
                    nullable: true,
                  },
                  id: {
                    type: 'string',
                    description: 'OFM budget internal ID in system of record (optional).',
                    example: '{11111111-2222-3333-4444-555555555555}',
                    nullable: true,
                  },
                  Name: {
                    type: 'string',
                    description: 'Name of the budget.',
                    example: 'Project Alpha Budget',
                    nullable: true,
                  },
                  projectId: {
                    type: 'string',
                    description: 'OFM budget project ID in system of record.',
                    example: '{66666666-7777-8888-9999-000000000000}',
                  },
                  systemId: {
                    type: 'string',
                    description: 'System which this budget funds (optional).',
                    example: '{11111111-2222-3333-4444-555555555555}',
                    nullable: true,
                  },
                  projectTitle: {
                    type: 'string',
                    description: 'Title of this project (optional).',
                    example: 'Alpha System Modernization',
                    nullable: true,
                  },
                  fundingId: {
                    type: 'string',
                    description: 'Cross-reference ID for relationship between budget project and application in system of record.',
                    example: '{12345678-ABCD-EFGH-1234-567890ABCDEF}',
                  },
                  funding: {
                    type: 'string',
                    description: 'Description of the allocation of this budget to the system in question (optional).',
                    example: '1000000',
                    nullable: true,
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Budgets updated successfully.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Successfully updated 1 budget(s).'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request - Missing or invalid parameters.',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized - Missing or invalid authentication credentials.',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error.',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
