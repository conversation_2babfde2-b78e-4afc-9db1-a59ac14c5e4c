import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/CEDAR Core API/2.0.0/budget', // Note: Webmethods API group is census-core-v2, but service path seems to align with 'CEDAR Core API' from examples
  method: 'post',
  tags: ['CEDAR Core'],
  description: 'Add new budget(s) to the Alfabet system. This interface takes an array of one or more budgets.',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['Budgets'],
          properties: {
            Budgets: {
              type: 'array',
              description: 'List of budgets to be added to CEDAR.',
              items: {
                type: 'object',
                required: ['projectId'],
                properties: {
                  FiscalYear: {
                    type: 'string',
                    description: 'The fiscal year for the budget.',
                    example: '2024',
                    nullable: true,
                  },
                  FundingSource: {
                    type: 'string',
                    description: 'The source of funding for the budget.',
                    example: 'Federal Funds',
                    nullable: true,
                  },
                  id: {
                    type: 'string',
                    description: 'OFM budget internal ID in system of record (optional on add).',
                    example: '{11111111-2222-3333-4444-555555555555}',
                    nullable: true,
                  },
                  Name: {
                    type: 'string',
                    description: 'Name of the budget item (optional).',
                    example: 'Project Alpha Budget',
                    nullable: true,
                  },
                  projectId: {
                    type: 'string',
                    description: 'OFM budget project ID in system of record (required).',
                    example: '{22222222-3333-4444-5555-666666666666}',
                  },
                  systemId: {
                    type: 'string',
                    description: 'ID of the system which this budget funds (optional).',
                    example: '{33333333-4444-5555-6666-777777777777}',
                    nullable: true,
                  },
                  projectTitle: {
                    type: 'string',
                    description: 'Title of this project (optional).',
                    example: 'Annual Software License Renewal',
                    nullable: true,
                  },
                  fundingId: {
                    type: 'string',
                    description: 'Cross-reference ID for relationship between budget project and application (optional).',
                    example: '{44444444-5555-6666-7777-888888888888}',
                    nullable: true,
                  },
                  funding: {
                    type: 'string',
                    description: 'Description of the allocation of this budget to the system in question (optional).',
                    example: 'Allocated for FY24 operations',
                    nullable: true,
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully added budget(s).',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Successfully added 1 budget(s).'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
