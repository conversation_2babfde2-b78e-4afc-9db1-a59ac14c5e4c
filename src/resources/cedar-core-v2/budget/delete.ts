import { Request, Response } from 'express';
import {
  isArray, isEmpty, isString, isError,
} from 'lodash';
import asyncHandler from 'express-async-handler';
import { unknownAsError } from '../../../utils/general';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import {
  endpointSetup, logAppError, sendError, sendSuccess,
} from '../../../utils/express/responses';
import Messages from '../../../utils/constants/messages';
import QueryParams from '../../../utils/express/queryParams';
import { deleteQuery } from '../../../subsystems/sparxea';
import { BudgetDeleteMessages, mapDeleteResponse } from '../../../utils/budget';

/**
 * Query parameters interface for budget delete operation
 */
export interface BudgetDeleteQueryParams {
  id: string[];
}

/**
 * Validates query string parameters for budget delete operation
 * @param p - Query parameters containing budget IDs to validate
 * @throws {Error} When IDs are missing, empty, or not strings
 */
const qsValidate = (p: BudgetDeleteQueryParams): void => {
  let check: string[] = [];

  if (isString(p.id) && !isEmpty(p.id)) {
    check = [p.id];
  } else if (isArray(p.id) && !isEmpty(p.id)) {
    check = p.id;
  } else {
    throw new Error(BudgetDeleteMessages.error_ids_missing_or_empty);
  }

  check.forEach((id) => {
    if (!isString(id) || isEmpty(id)) {
      throw new Error(BudgetDeleteMessages.error_ids_not_string);
    }
  });
};

/**
 * Handles batch deletion of budget entities following WebMethods budgetDeleteList service logic.
 *
 * This service processes a list of budget IDs and attempts to delete each one via the SparX API.
 * It follows the WebMethods flow pattern by:
 * - Validating input parameters
 * - Iterating through each ID and attempting deletion
 * - Tracking success/failure counts
 * - Applying WebMethods response mapping logic:
 *   - HTTP 400 if no objects were deleted (not found)
 *   - HTTP 400 if partial deletion occurred
 *   - HTTP 200 if all objects were successfully deleted
 *   - HTTP 500 for internal server errors
 *
 * @param req - Express request object containing query parameters with budget IDs
 * @param res - Express response object for sending the API response
 * @returns Promise<void> - Async handler that sends appropriate HTTP response
 *
 * @example
 * DELETE /budget?id=123&id=456
 * // Attempts to delete budgets with IDs 123 and 456
 */
const budgetDeleteList = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const { app } = endpointSetup(req, DatabaseConfigs.sparxea);

  try {
    const params = QueryParams.fromQuery<BudgetDeleteQueryParams>(req).getAll();

    try {
      qsValidate(params);
    } catch (e) {
      sendError(res, 400, { message: [unknownAsError(e).message] });
      return;
    }

    const idsToDelete = isArray(params.id) ? params.id : [params.id];
    const expectedCount = idsToDelete.length;

    let succeededCount = 0;

    // Process each deletion sequentially to match WebMethods flow pattern
    await Promise.all(idsToDelete.map(async (id) => {
      try {
        const sparxId = `cn_${id}`;
        const result = await deleteQuery(app, sparxId);

        if (isError(result)) {
          app.logger.error({
            message: `Failed to delete budget item with ID ${id}`,
            error: result,
          });
        } else {
          succeededCount += 1;
        }
      } catch (error) {
        app.logger.error({
          message: `Failed to delete budget item with ID ${id}`,
          error: unknownAsError(error),
        });
      }
    }));

    // Use WebMethods-style response mapping
    const deleteResponse = mapDeleteResponse(expectedCount, succeededCount);

    if (deleteResponse.status === 200) {
      sendSuccess(res, { result: 'success', message: deleteResponse.message });
    } else {
      sendError(res, deleteResponse.status, { message: deleteResponse.message });
    }
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'budget',
        action: 'deleteList',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: [Messages.internal_server_error] });
  }
});

export default budgetDeleteList;
