import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import { budgetFindUtil } from '../../../utils/budget';
import {
  getDb,
} from '../../../utils/db-helpers';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/budget'
#swagger.method = 'get'
#swagger.tags = ['Budget']
#swagger.description = 'Retrieve a list of budgets based on query criteria listed in the parameters section. Passing a SystemId will cause the interface to return the budget(s) for just that system. Setting onlyIds to true will only return the id's, whereas if not set, the response will also include projectId, systemId, fundingId and funding. This interface has a limit of 5000 records'
#swagger.summary = 'Retrieve a list of budgets based on query criteria listed in the parameters section. Passing a SystemId will cause the interface to return the budget(s) for just that system. Setting onlyIds to true will only return the id's, whereas if not set, the response will also include projectId, systemId, fundingId and funding. This interface has a limit of 5000 records'
#swagger.operationId = 'budgetFind'
#swagger.consumes = [
  'application/json',
]
#swagger.produces = [
  'application/json',
]
#swagger.parameters['systemId'] = {
  in: 'query',
  required: false,
  description: 'ID of the system that the budget is associated with. Adding this parameter will only return the system matching the systemId',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.parameters['projectTitle'] = {
  in: 'query',
  required: false,
  description: 'ID of the system that the budget is associated with. Adding this parameter will instruct the interface to only return the system matching the systemId.',
  type: 'string',
  example: 'Project'
}

#swagger.parameters['projectId'] = {
  in: 'query',
  required: false,
  description: 'Project Id string to search. Adding this parameter will instruct the interface to only return the system matching the projectId.',
  type: 'string',
}

#swagger.parameters['onlyIds'] = {
  in: 'query',
  required: false,
  description: 'Whether the call will return simply the IDs. If not set, the response will also include projectId, systemId, fundingId and funding.',
  type: 'boolean',
  example: 'true'
}

#swagger.parameters['idsOnly'] = {
  in: 'query',
  required: false,
  description: 'Whether the call will return simply the IDs.  If not set, the response will also include projectId, systemId, fundingId and funding.',
  type: 'boolean',
  example: 'true'
}

#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'OK',
  content: {
    'application/json': {
      schema: {
        required: [
          'projectId',
        ],
        type: 'object',
        required: [
          'count',
        ],
        properties: {
          Budgets: {
            type: 'array',
            required: [
              'projectId',
            ],
            items: {
              type: 'object',
              properties: {
                FiscalYear: {
                  type: 'string'
                },
                FundingSource: {
                  type: 'string'
                },
                Name: {
                  type: 'string'
                },
                funding: {
                  type: 'string',
                  description: 'Description of the allocation of this budget to the system in question',
                  example: 'Most of this funding is directly and only for this system (over 80%)'
                },
                fundingId: {
                  type: 'string',
                  description: 'Cross-reference ID for relationship between budget project and application in system of record',
                  example: '123-45-678'
                },
                id: {
                  type: 'string',
                  description: 'OFM budget internal ID in system of record',
                  example: '589-329-0'
                },
                projectId: {
                  type: 'string',
                  description: 'OFM budget project ID in system of record',
                  example: '000010'
                },
                projectTitle: {
                  type: 'string',
                  description: 'Title of this project',
                  example: 'CMS Accountable Care Organizations'
                },
                systemId: {
                  type: 'string',
                  description: 'System which this budget funds',
                  example: '123-45-678,'
                }
              }
            }
          },
          count: {
            type: 'integer',
          },
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const budgetFind = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const budgetResult = await budgetFindUtil(
    app,
    db,
    get(req, 'query', {}) as Record<string, string>,
  );

  if (isError(budgetResult)) {
    app.logger.error({ error: budgetResult });
    return res.status(500).send({ error: 'There was an issue fetching the budgets' });
  }

  return res.status(200).send(budgetResult);
};

export default budgetFind;
