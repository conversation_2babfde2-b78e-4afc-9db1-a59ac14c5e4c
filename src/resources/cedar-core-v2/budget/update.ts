import { Request, Response } from 'express';
import {
  get, isArray, isEmpty, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';

import { unknownAsError } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import {
  endpointSetup, logAppError, sendError, sendSuccess,
} from '../../../utils/express/responses';
import { UnitTestForcedExceptionError } from '../../../utils/express/errors';

/**
 * Represents a budget record for update operations.
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Budget document type.
 */
export interface Budget {
  /** Fiscal year of the budget */
  FiscalYear?: string;
  /** Source of the budget funding */
  FundingSource?: string;
  /** OFM budget internal ID in system of record */
  id?: string;
  /** Name of the budget */
  Name?: string;
  /** OFM budget project ID in system of record (required) */
  projectId: string;
  /** System which this budget funds */
  systemId?: string;
  /** Title of this project */
  projectTitle?: string;
  /**
   * Cross-reference ID for relationship between budget project and application - critical for
   * updates (required for processing)
   */
  fundingId: string;
  /** Description of the allocation of this budget to the system */
  funding?: string;
}

/**
 * Request payload for budget update operations.
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BudgetUpdateRequest
 * document type.
 */
export interface BudgetUpdateRequestPayload {
  /** Array of budgets to update */
  Budgets: Budget[];
}

/**
 * Internal request structure for the Alfabet system update operation.
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest
 * document type.
 */
export interface UpdateRequest {
  /** API client profile - hardcoded to "API User" */
  CurrentProfile: string;
  /** API culture setting */
  APICulture?: string;
  /** Array of objects to update in the Alfabet system */
  Objects: UpdateObject[];
  /** Array of relations to update (not used in this flow) */
  Relations?: UpdateRelation[];
}

/**
 * Represents an object being updated in the Alfabet system.
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object document type.
 */
export interface UpdateObject {
  /** Reference string identifying the object - mapped from Budget.fundingId */
  RefStr?: string;
  /** Class name of the object being updated - hardcoded to "ProjectArch" */
  ClassName: string;
  /** Iteration ID for the update operation */
  Id: string;
  /** Specific attributes being updated for the object */
  Values: ProjectArchValues;
  /** Generic attributes (not used in this flow) */
  GenericAttributes?: Record<string, unknown>[];
}

/**
 * Represents a relation update in the Alfabet system (not used in this flow).
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations document type.
 */
export interface UpdateRelation {
  /** Source reference for the relation */
  FromRef?: string;
  /** Source ID for the relation */
  FromId?: string;
  /** Property name for the relation */
  Property?: string;
  /** Target reference for the relation */
  ToRef?: string;
  /** Target ID for the relation */
  ToId?: string;
}

/**
 * ProjectArch values structure for Alfabet system updates.
 * Based on the WebMethods cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ProjectArch document type.
 */
export interface ProjectArchValues {
  /** Change category */
  changecategory?: string;
  /** Change comments */
  changecomments?: string;
  /** CMS funding information - mapped from Budget.funding */
  cms_funding?: string;
  /** Last updated date for REST operations */
  cms_rest_last_updated_date?: string;
  /** User who performed the REST update */
  cms_rest_updated_user?: string;
  /** General comments */
  comments?: string;
  /** Object reference */
  object?: string;
  /** Project reference */
  project?: string;
  /** SAG implementation ID */
  sag_imp_id?: string;
  /** Sample record for use cases */
  samplerecordforusecases?: string;
  /** Type classification */
  type?: string;
}

/**
 * Error messages for budget update operations.
 * Matches the WebMethods service error handling behavior.
 */
export enum BudgetUpdateMessages {
  error_budgets_missing = 'Please provide a list of budgets.',
  error_funding_id_missing = 'System Budget Connection GUID is Null',
  error_unexpected_sp_status = 'Unexpected stored procedure status',
}

/**
 * Handles budget update requests for the CEDAR system.
 * Updates existing budget information in the Alfabet data model via SQL Server stored procedure.
 *
 * @param req - Express request object containing the budget update payload
 * @param res - Express response object for sending the result
 * @returns Promise<void>
 *
 * @example
 * ```typescript
 * // Request body example:
 * {
 *   "Budgets": [
 *     {
 *       "projectId": "{*************-8888-9999-000000000000}",
 *       "fundingId": "{12345678-ABCD-EFGH-1234-567890ABCDEF}",
 *       "funding": "1000000",
 *       "FiscalYear": "2024",
 *       "Name": "Project Alpha Budget"
 *     }
 *   ]
 * }
 * ```
 */
const handler = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const { app, db } = endpointSetup(req, DatabaseConfigs.sparxea);

  try {
    const payload: BudgetUpdateRequestPayload = req.body;
    const budgetsToUpdate = payload.Budgets;

    if (!budgetsToUpdate || !isArray(budgetsToUpdate) || isEmpty(budgetsToUpdate)) {
      sendError(res, 400, { message: [BudgetUpdateMessages.error_budgets_missing] });
      return;
    }

    const updateRequest: UpdateRequest = {
      CurrentProfile: 'API User',
      Objects: [],
    };

    // Map each budget to an UpdateObject following the webmethods flow logic
    for (let i = 0; i < budgetsToUpdate.length; i += 1) {
      const budget = budgetsToUpdate[i];

      if (!budget.fundingId || !isString(budget.fundingId)) {
        sendError(res, 400, { message: [BudgetUpdateMessages.error_funding_id_missing] });
        return;
      }

      // Populate ProjectArchValues from Budget properties
      const projectArchValues: ProjectArchValues = {
        cms_funding: budget.funding, // Maps Budget.funding to ProjectArch.cms_funding
        // Other ProjectArch fields are not explicitly mapped in the provided flow XML,
        // so they are omitted or can be added as empty strings/nulls if required by the SP.
        // Based on the WM flow, only cms_funding is set on ProjectArch.
      };

      const updateObject: UpdateObject = {
        RefStr: budget.fundingId, // Map Budget.fundingId to Object.RefStr
        ClassName: 'ProjectArch', // Hardcoded in WM flow
        Id: String(i + 1), // Iteration number used as ID in WM loop
        Values: projectArchValues,
      };

      updateRequest.Objects.push(updateObject);
    }

    // Convert the UpdateRequest object to a JSON string for the stored procedure
    const jsonInput = JSON.stringify(updateRequest);

    let spResult: { queryStatus: number }[];

    try {
      // Execute the stored procedure
      spResult = await db.queryStoredProceduresTyped<[{ queryStatus: number }]>(
        'SP_Update_SystemBudget_json',
        [
          { name: 'jsonInput', type: 'nvarchar', param: 'max' },
        ],
        [
          { name: 'jsonInput', value: jsonInput },
        ],
        [], // No specific output results mapped beyond the return_value
      );
    } catch (error) {
      // Unit testing.
      if (error instanceof UnitTestForcedExceptionError) {
        // noinspection ExceptionCaughtLocallyJS
        throw error;
      }

      app.logger.error({ error: unknownAsError(error) });
      sendError(res, 500, {
        message: [Messages.db_query_stored_procedure_error],
      });
      return;
    }

    // Check if the stored procedure result is valid
    if (!spResult || !isArray(spResult) || isEmpty(spResult)) {
      sendError(res, 500, {
        message: [Messages.db_query_result_missing],
      });
      return;
    }

    const queryStatus = get(spResult, '[0].queryStatus');

    // The webMethods flow checks for @RETURN_VALUE === 0 for success
    if (queryStatus !== 0) {
      sendError(res, 500, {
        message: [BudgetUpdateMessages.error_unexpected_sp_status],
      });
      return;
    }

    sendSuccess(res, { result: 'success', message: [`Successfully updated ${budgetsToUpdate.length} budget(s).`] });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'budget',
        action: 'update',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: [Messages.internal_server_error] });
  }
});

export default handler;
