import { isError } from 'lodash';
import detail from './detail';
import summary from './summary';
import { GetResourceConfig } from '../../../types';
import { prependPathToResources } from '../../../utils/resources';

const updatedDetail = prependPathToResources('/detail', detail());
if (isError(updatedDetail)) {
  throw new Error('An error occurred updating the path for detail');
}

const updatedSummary = prependPathToResources('/summary', summary());
if (isError(updatedSummary)) {
  throw new Error('An error occurred updating the path for summary');
}

const getResourceConfig: GetResourceConfig = () => [
  ...updatedDetail,
  ...updatedSummary,
];

export default getResourceConfig;
