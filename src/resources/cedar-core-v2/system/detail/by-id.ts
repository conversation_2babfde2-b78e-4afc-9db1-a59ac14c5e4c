// Disabling max length due to swagger documentation
/* eslint-disable max-len */
import { Response, Request } from 'express';
import {
  get,
  isEmpty,
  isError,
  isNull,
  isString,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../../data-sources/mssql';
import { getDb, SPARX_EASI_SYSTEM } from '../../../../utils/db-helpers';
import {
  strToBoolOrNull,
  tryAsync,
  strToNumberOrNull,
  splitOrNull,
} from '../../../../utils/general';
import { formatDate } from '../../../../utils/time';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/system/detail/{id}'
#swagger.method = 'get'
#swagger.tags = ['System']
#swagger.description = 'System Detail by id'
#swagger.parameters['id'] = {
  in: 'path',
  required: true,
  description: 'ID of the system (UUID string)',
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-************}'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Good response',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          BusinessOwnerInformation: {
            type: 'object',
            properties: {
              '508UserInterface': {
                type: 'string',
                '@enum': [
                  'Yes, UI accessible by external users.',
                  'Yes, UI accessible by internal Federal Employees.',
                  'Yes, UI accessible by both internal Federal Employees and external users.',
                  'No, this system doesn\'t have a UI.'
                ]
              },
              beneficiaryAddressPurpose: {
                type: 'array',
                items: {
                  type: 'string',
                  '@enum': [
                    'Mailing',
                    'Payment Calculation',
                    'Coordination of Benefits',
                    'Subsidy Calculation',
                    'Premium Calculation',
                    'Risk Adjustment',
                    'Coordination of Care',
                    'Eligibility and Enrollment',
                    'Customer Service',
                    'Education and Outreach',
                    'Innovation',
                    'Research and Demonstrations',
                    'Healthcare Quality Improvement',
                    'Program Oversight',
                    'Actuarial Services',
                    'Regulatory and Policy Development',
                    'Audit Support',
                    'Patient Care Reporting',
                    'Beneficiary Data Access'
                  ]
                }
              },
              beneficiaryAddressPurposeOther: {
                type: 'string',
                example: 'Customer Service'
              },
              beneficiaryAddressSource: {
                type: 'array',
                items: {
                  type: 'string',
                  example: 'Medicaid Address (State)'
                }
              },
              beneficiaryAddressSourceOther: {
                type: 'string',
                example: 'Medicaid Address (State)'
              },
              beneficiaryInformation: {
                type: 'array',
                items: {
                  type: 'string',
                  '@enum': [
                    'Beneficiary Address',
                    'Beneficiary email',
                    'Beneficiary Mobile Number',
                    'None of the Above'
                  ]
                }
              },
              costPerYear: {
                type: 'string',
                example: '13759255.66'
              },
              editBeneficiaryInformation: {
                type: 'boolean'
              },
              isCmsOwned: {
                type: 'boolean',
                example: true
              },
              numberOfContractorFte: {
                type: 'string',
                example: '100'
              },
              numberOfFederalFte: {
                type: 'string',
                example: '25'
              },
              numberOfSupportedUsersPerMonth: {
                type: 'string',
                example: '314'
              },
              storesBankingData: {
                type: 'boolean',
                example: false
              },
              storesBeneficiaryAddress: {
                type: 'boolean',
                example: true
              }
            }
          },
          DataCenterHosting: {
            type: 'object',
            properties: {
              movingToCloud: {
                type: 'string',
                example: 'Yes',
                '@enum': ['Yes','No','Plans']
              },
              movingToCloudDate: {
                type: 'string',
                format: 'date',
                example: '2021-10-13T00:00:00.000Z'
              }
            }
          },
          SoftwareProductDetails: {
            type: 'object',
            properties: {
              aiPlan: {
                type: 'string',
                '@enum': [
                  'Yes - This system has developed AI capabilities.',
                  'Yes - This system uses a SaaS AI tool like Remesh...',
                  'No – And this system currently has no plans to utilize AI capabilities',
                  'No – But there currently are plans to use AI capabilities in the next two years'
                ]
              },
              apiDataArea: {
                type: 'array',
                items: {
                  type: 'string',
                  '@enum': [
                    'Beneficiary and Consumer',
                    'Health Insurance Program',
                    'Healthcare Payment',
                    'Healthcare Quality',
                    'Healthcare Service',
                    'Organization',
                    'Provider',
                    'Supporting Resource'
                  ]
                }
              },
              apiFHIRUse: {
                type: 'string',
                '@enum': ['No','FHIR','HL7','Other Standard']
              },
              apiFHIRUseOther: {
                type: 'string'
              },
              apisAccessibility: {
                type: 'string',
                '@enum': ['Both','External Access','Internal Access']
              },
              apisDeveloped: {
                type: 'string',
                '@enum': ['No','Yes','API In development but not yet launched.']
              },
              systemAiType: {
                type: 'array',
                items: { type: 'string' }
              },
              systemAiTypeOther: {
                type: 'string'
              },
              systemHasApiGateway: {
                type: 'boolean'
              }
            }
          },
          SystemMaintainerInformation: {
            type: 'object',
            properties: {
              adHocAgileDeploymentFrequency: {
                type: 'string',
                '@enum': [
                  'Annually','Semi-Annually','Quarterly','Monthly','Every Two Weeks',
                  'Weekly','Twice a Week','Daily','Hourly','Ad Hoc/As Needed',
                  'Not Applicable','Other'
                ]
              },
              agileUsed: {
                type: 'boolean',
                example: true
              },
              authoritativeDatasource: {
                type: 'string'
              },
              businessArtifactsOnDemand: {
                type: 'boolean',
                example: true
              },
              dataAtRestEncryptionKeyManagement: {
                type: 'string',
                '@enum': [
                  'We do not encrypt data at rest.',
                  'We perform ad hoc management of encryption keys.',
                  'We have a process for managing encryption keys.',
                  'We have a process for managing encryption keys and it is automated.'
                ]
              },
              deploymentFrequency: {
                type: 'string',
                example: 'Monthly',
                '@enum': [
                  'Annually','Semi-Annually','Quarterly','Monthly','Every Two Weeks',
                  'Weekly','Twice a Week','Daily','Hourly','Ad Hoc/As Needed',
                  'Not Applicable','Other'
                ]
              },
              devCompletionPercent: {
                type: 'string',
                example: '10-14%'
              },
              devWorkDescription: {
                type: 'string',
                example: 'The type of development work underway...'
              },
              ecapParticipation: {
                type: 'boolean',
                example: true
              },
              frontendAccessType: {
                type: 'string',
                example: 'IPv4 and IPv6',
                '@enum': ['IPv4 Only','IPv4 and IPv6','IPv6 Only']
              },
              hardCodedIpAddress: {
                type: 'boolean',
                example: true
              },
              ip6EnabledAssetPercent: {
                type: 'string',
                example: 'Between 20% and 49%',
                '@enum': [
                  'Less than 20%','Between 20% and 49%','Between 50% and 79%','80% or above'
                ]
              },
              ip6TransitionPlan: {
                type: 'string',
                example: 'Yes, transition to IPv6',
                '@enum': ['Yes','transition to IPv6','No','decommission/replace before 2026']
              },
              ipEnabledAssetCount: {
                type: 'integer',
                format: 'int32',
                example: 1
              },
              legalHoldCaseName: {
                type: 'string'
              },
              locallyStoredUserInformation: {
                type: 'boolean'
              },
              majorRefreshDate: {
                type: 'string',
                format: 'date'
              },
              multifactorAuthenticationMethod: {
                type: 'array',
                example: '',
                items: {
                  type: 'string',
                  '@enum': [
                    'One Time Password sent via Email',
                    'One Time Password sent via SMS',
                    'One Time Password or Push from an authenticator app e.g. Google Authenticator DUO',
                    'One time Password from a hardware token e.g. RSA SecurID',
                    'FIDO U2F (e.g. YubiKey as a second factor)',
                    'PIV/certificate',
                    'FIDO2/WebAuthn (passwordless authentication includes Windows Hello)',
                    'None',
                    'Other'
                  ]
                }
              },
              multifactorAuthenticationMethodOther: {
                type: 'string'
              },
              netAccessibility: {
                type: 'string',
                example: 'Accessible to a CMS-internal network only',
                '@enum': [
                  'Accessible to the Public Internet (non-restricted access)',
                  'Accessible to a CMS-internal network only',
                  'Accessible to both public internet and to CMS-internal network'
                ]
              },
              networkTrafficEncryptionKeyManagement: {
                type: 'string',
                '@enum': [
                  'We do not encrypt any network traffic.',
                  'We perform ad hoc management of encryption keys.',
                  'We have a process for managing encryption keys.',
                  'We have a process for managing encryption keys and it is automated.'
                ]
              },
              noMajorRefresh: { type: 'boolean' },
              noPersistentRecordsFlag: { type: 'boolean' },
              noPlannedMajorRefresh: { type: 'boolean' },
              omDocumentationOnDemand: {
                type: 'boolean',
                example: true
              },
              plansToRetireReplace: {
                type: 'string',
                example: 'Yes - Retire and Replace',
                '@enum': ['No','Yes - Retire and Replace','Yes - Retire but NOT Replace']
              },
              quarterToRetireReplace: {
                type: 'string',
                example: '3'
              },
              recordsManagementBucket: {
                type: 'array',
                items: {
                  type: 'string',
                  example: 'Administrative Management',
                  '@enum': [
                    'Leadership and Operations (Hint=1)',
                    'Policy and Regulations (Hint=2)',
                    'Senior Leadership Records (Administrator) (Hint=2)',
                    'Formal Reports and Studies (Hint=2)',
                    'Committee Files (non FACA) (Hint=2)',
                    'Other Leadership and Operations Records (Hint=2)',
                    'Administrative Management (Hint=1)',
                    'Routine Administrative Records (Hint=2)',
                    'Other Administrative Management Records (Hint=2)',
                    'Financial Records (Hint=1)',
                    'Claims Records (Hint=2)',
                    'Financial Reporting Records (Hint=2)',
                    'Non-perm HCPCS codes (Hint=2)',
                    'Other Financial Records (Hint=2)',
                    'Enrollment Records (Hint=1)',
                    'Beneficiary Records (Hint=1)',
                    'Provider and Health Plan Records (Hint=1)',
                    'Provider Applications and Certifications (Hint=2)',
                    'Records Related to Health Plans (Hint=2)',
                    'Program Review and Audit Records (Hint=2)',
                    'Hearings Files (Hint=2)',
                    'Administrative Records (Hint=2)',
                    'Other Provider and Health Plan Records (Hint=2)',
                    'Research and Program Analysis (Hint=1)',
                    'Public Use Statistical and Summary Files (Hint=2)',
                    'Supporting Records (Hint=2)',
                    'Other Research and Program Analysis',
                    'Public Outreach and Engagement (Hint=1)',
                    'Significant Public Outreach and Engagement Records (Hint=2)',
                    'Photographs and Videos (Hint=2)',
                    'Other Public Outreach and Engagement Records (Hint=2)',
                    'Compliance and Integrity (Hint=1)',
                    'Plans and Agreements (Hint=2)',
                    'Administrative Records (Hint=2)',
                    'Review / Audit Records (Hint=2)',
                    'Reports (Hint=2)',
                    'Legal Records (Hint=2)',
                    'Clinical Laboratory Improvement Amendments (CLIA) records (Hint=2)',
                    'Other Compliance and Integrity Records (Hint=2)'
                  ]
                }
              },
              recordsManagementDisposalLocation: { type: 'string' },
              recordsManagementDisposalPlan: { type: 'string' },
              recordsUnderLegalHold: { type: 'boolean' },
              sourceCodeOnDemand: {
                type: 'boolean',
                example: true
              },
              systemCustomization: {
                type: 'string',
                example: 'Less Than 20% Customization',
                '@enum': [
                  'COTS - Less than 20% custom coding',
                  'GOTS – less than 20% custom coding',
                  'Mixed – Uses COTS or GOTS and has more than 20% custom coding',
                  'Custom developed'
                ]
              },
              systemDataLocation: {
                type: 'array',
                items: {
                  type: 'string',
                  '@enum': [
                    'Integrated Data Repository (IDR)',
                    'Chronic Condition Warehouse (CCW)',
                    'This system',
                    'Another CMS system - Describe in Notes'
                  ]
                }
              },
              systemDataLocationNotes: { type: 'string' },
              systemDesignOnDemand: {
                type: 'boolean',
                example: true
              },
              systemProductionDate: {
                type: 'string',
                format: 'date'
              },
              systemRequirementsOnDemand: {
                type: 'boolean',
                example: true
              },
              testPlanOnDemand: {
                type: 'boolean',
                example: true
              },
              testReportsOnDemand: {
                type: 'boolean',
                example: true
              },
              testScriptsOnDemand: {
                type: 'boolean',
                example: true
              },
              yearToRetireReplace: {
                type: 'string',
                example: '2023'
              }
            }
          },
          acronym: {
            type: 'string',
            example: 'CMSS'
          },
          atoEffectiveDate: {
            type: 'string',
            format: 'date',
            example: '2021-10-13T00:00:00.000Z'
          },
          atoExpirationDate: {
            type: 'string',
            format: 'date',
            example: '2021-10-13T00:00:00.000Z'
          },
          belongsTo: {
            type: 'string',
            example: '326-10-0'
          },
          businessOwnerOrg: {
            type: 'string',
            example: 'Center for Medicare Management'
          },
          businessOwnerOrgComp: {
            type: 'string',
            example: 'CM-(FFS)'
          },
          description: {
            type: 'string',
            example: 'This is a CMS System description'
          },
          ictObjectId: {
            type: 'string',
            example: '326-3-0'
          },
          id: {
            type: 'string',
            example: '326-2-0'
          },
          name: {
            type: 'string',
            example: 'CMS System'
          },
          nextVersionId: {
            type: 'string',
            example: '326-1-0'
          },
          previousVersionId: {
            type: 'string',
            example: '326-3-0'
          },
          state: {
            type: 'string',
            example: 'Active'
          },
          status: {
            type: 'string',
            example: 'Approved'
          },
          systemMaintainerOrg: {
            type: 'string',
            example: 'OIT'
          },
          systemMaintainerOrgComp: {
            type: 'string',
            example: 'Enterprise Architecture and Data Group'
          },
          uuid: {
            type: 'string',
            example: '12FFF52E-195B-4E48-9A38-669A8BD71234'
          },
          version: {
            type: 'string',
            example: '1.0'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
const detailById = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ message: 'Unable to get the application from request' });
  }

  const id = get(req, 'params.id', '');
  if (!id) {
    const logError = new Error('No id was provided');
    app.logger.error({ error: logError });
    return res.status(400).send({ message: 'No object id provided' });
  }

  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));
  if (error) {
    const logError = new Error('Invalid object id provided');
    app.logger.error({ error: logError });
    return res.status(400).send({ error: logError.message });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ message: 'Database Unavailable' });
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_EASI_SYSTEM, [
    // '"System ID"',
    // '"Sparx System ID"',
    '"Sparx System GUID" as id',
    '"Sparx System GUID" as ictObjectId',
    '"System Name" as name',
    '"Acronym" as acronym',
    '"Object State" as state',
    '"CMS UUID" as uuid',
    // '"ID"',
    '"nextVersionID" as nextVersionId',
    '"previousVersionID" as previousVersionId',
    // '"ICT Object ID"',
    '"Description" as description',
    '"Version" as version',
    '"status" as status',
    '"belongsTo" as belongsTo',
    '"Is CMS Owned" as isCmsOwned',
    '"Beneficiary Address" as storesBeneficiaryAddress',
    '"Beneficiary Address Source" as beneficiaryAddressSource',
    '"Beneficiary Address Source Other" as beneficiaryAddressSourceOther',
    '"Benefitiary Address Purpose" as beneficiaryAddressPurpose',
    '"Other Benefitiary Address Purpose" as beneficiaryAddressPurposeOther',
    '"Banking Data" as storesBankingData',
    '"Cost Per Year" as costPerYear',
    '"Number of Federal FTE" as numberOfFederalFte',
    '"Number of Contractor FTE" as numberOfContractorFte',
    '"Number of Supported Users Per Month" as numberOfSupportedUsersPerMonth',
    '"Cloud Migration Plan" as movingToCloud',
    '"Cloud Migrated Date" as movingToCloudDate',
    '"System Customization" as systemCustomization',
    '"Front End Access Type" frontendAccessType',
    '"CMS System Access" as netAccessibility',
    '"IP Enabled Asset Count" as ipEnabledAssetCount',
    '"Percent IPV6" as ip6EnabledAssetPercent',
    '"Hard Coded IP Address" as hardCodedIpAddress',
    '"E-Cap Participation" as ecapParticipation',
    '"Start Date" as systemProductionDate',
    '"Development Complete Percent" as devCompletionPercent',
    '"Development Work Still Underway" as devWorkDescription',
    '"Agile Methodology Use" as agileUsed',
    '"Deployment Frequency" as deploymentFrequency',
    '"Next Major Tech Refresh Date" as majorRefreshDate',
    '"Retire or Replace" as plansToRetireReplace',
    '"Retire or Replace Date" as yearToRetireReplace',
    '"Planned Retirement Quarter" as quarterToRetireReplace',
    '"Business Artifacts on Demand" as businessArtifactsOnDemand',
    '"Test Script on Demand" as testScriptsOnDemand',
    '"Test Reports on Demand" as testReportsOnDemand',
    '"Test Plan on Demand" as testPlanOnDemand',
    '"Source Code on Demand" as sourceCodeOnDemand',
    '"Design on Demand" as systemDesignOnDemand',
    '"Ops and Maintenance Plans on Demand" as omDocumentationOnDemand',
    '"Requirements on Demand" as systemRequirementsOnDemand',
    '"Business Artifact Location" as recordsManagementBucket',
    // '"Test Script Location"',
    // '"Test Reports Location"',
    // '"Test Plan Location"',
    // '"Source Code Location"',
    // '"System Design Location"',
    // '"Ops and Maintenance Plan Location"',
    // '"Requirements Location"',
    // '"Records Management Format"',
    // '"Records Management Format Other"',
    // '"Records Management Metadata"',
    // '"Records Management Approved Schedule"',
    // '"Records Management Disposal"',
    '"API Developed" as apisDeveloped',
    '"API Data Area" as apiDataArea',
    '"API Accessibility" as apisAccessibility',
    '"Does the API use FHIR" as apiFHIRUse',
    '"Does the API use FHIR Other" as apiFHIRUseOther',
    '"System has API Gateway" as systemHasApiGateway',
    '"Artificial Intelligence Plan" as aiPlan',
    '"Artificial Intelligence" as systemAiType',
    '"Artificial Intelligence - OTHER Description" as systemAiTypeOther',
    '"businessOwnerOrg" as businessOwnerOrg',
    '"Business Owner Organization Component" as businessOwnerOrgComp',
    '"System Maintainer Organization" as systemMaintainerOrg',
    '"System Maintainer Organization Component" as systemMaintainerOrgComp',
    '"IPV6 Transition Plan" as ip6TransitionPlan',
    // '"Development Work Still Underway"',
    '"Edit Beneficiary Information" as editBeneficiaryInformation',
    '"System has UI" as \'508UserInterface\'',
    // '"Hosted on Cloud"',
    '"System Data Location" as systemDataLocation',
    '"System Data Location Notes" as systemDataLocationNotes',
    '"Deployment AdHoc Frequency" as adHocAgileDeploymentFrequency',
    '"Locally Stored User Info" as locallyStoredUserInformation',
    '"MFA Method" as multifactorAuthenticationMethod',
    '"MFA Other" as multifactorAuthenticationMethodOther',
    '"Network Traffic Encryption Management" as networkTrafficEncryptionKeyManagement',
    '"Data At Rest Encryption Management" as dataAtRestEncryptionKeyManagement',
    '"No Persistent Records Flag" as noPersistentRecordsFlag',
    '"Records Management Disposal Location" as recordsManagementDisposalLocation',
    '"Records Management Disposal Plan" as recordsManagementDisposalPlan',
    '"Records Under Legal Hold" as recordsUnderLegalHold',
    '"Legal Hold Case Name" as legalHoldCaseName',
    '"No Major Refresh Flag" as noMajorRefresh',
    '"No Planned Major Refresh Flag" as noPlannedMajorRefresh',
    '"Beneficiary Information" as beneficiaryInformation',
    '"System Data Authoritative Source" as authoritativeDatasource',
    '"ATO Effective Date" as atoEffectiveDate',
    '"ATO Expiration Date" as atoExpirationDate',
    '"System Ownership" as systemOwnership',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: id,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve system detail by ID' });
  }

  const resultStatus = get(viewResult, '[1]');
  if (resultStatus !== 1) {
    return res.status(500).send({ error: 'An error occurred processing the system detail' });
  }

  const formatSystemDate = (key: string) => {
    const rawDate: string | null = get(viewResult, `[0][0].${key}`, null);
    if (isNull(rawDate)) {
      return null;
    }

    if (isString(rawDate) && !isEmpty(rawDate)) {
      const formattedDate = formatDate(rawDate, 'YYYY-MM-DD');
      if (isError(formattedDate)) {
        app.logger.warn({ warn: `The ${key} is not empty and not a valid date` });
        return null;
      }
      return formattedDate;
    }

    app.logger.warn({ warn: `The ${key} is not a string, is empty, and not is null` });
    return null;
  };

  const returnObj = {
    id: get(viewResult, '[0][0].id', null),
    nextVersionId: get(viewResult, '[0][0].nextVersionId', null),
    previousVersionId: get(viewResult, '[0][0].previousVersionId', null),
    ictObjectId: get(viewResult, '[0][0].ictObjectId', null),
    uuid: get(viewResult, '[0][0].uuid', null),
    name: get(viewResult, '[0][0].name', null),
    description: get(viewResult, '[0][0].description', null),
    acronym: get(viewResult, '[0][0].acronym', null),
    state: get(viewResult, '[0][0].state', null),
    status: get(viewResult, '[0][0].status', null),
    belongsTo: get(viewResult, '[0][0].belongsTo', null),
    businessOwnerOrg: get(viewResult, '[0][0].businessOwnerOrg', null),
    businessOwnerOrgComp: get(viewResult, '[0][0].businessOwnerOrgComp', null),
    systemMaintainerOrg: get(viewResult, '[0][0].systemMaintainerOrg', null),
    systemMaintainerOrgComp: get(viewResult, '[0][0].systemMaintainerOrgComp', null),
    atoEffectiveDate: formatSystemDate('atoEffectiveDate'),
    atoExpirationDate: formatSystemDate('atoExpirationDate'),
    BusinessOwnerInformation: {
      isCmsOwned: strToBoolOrNull(get(viewResult, '[0][0].isCmsOwned', null)),
      storesBeneficiaryAddress: strToBoolOrNull(get(viewResult, '[0][0].storesBeneficiaryAddress', null)),
      beneficiaryAddressPurpose: splitOrNull(get(viewResult, '[0][0].beneficiaryAddressPurpose', null), '|'),
      beneficiaryAddressPurposeOther: get(viewResult, '[0][0].beneficiaryAddressPurposeOther', null),
      beneficiaryAddressSource: splitOrNull(get(viewResult, '[0][0].beneficiaryAddressSource', null), '|'),
      beneficiaryAddressSourceOther: get(viewResult, '[0][0].beneficiaryAddressSourceOther', null),
      storesBankingData: strToBoolOrNull(get(viewResult, '[0][0].storesBankingData', null)),
      costPerYear: get(viewResult, '[0][0].costPerYear', null),
      numberOfFederalFte: get(viewResult, '[0][0].numberOfFederalFte', null),
      numberOfContractorFte: get(viewResult, '[0][0].numberOfContractorFte', null),
      numberOfSupportedUsersPerMonth: get(viewResult, '[0][0].numberOfSupportedUsersPerMonth', null),
      beneficiaryInformation: splitOrNull(get(viewResult, '[0][0].beneficiaryInformation', null), '|'),
      editBeneficiaryInformation: strToBoolOrNull(get(viewResult, '[0][0].editBeneficiaryInformation', null)),
      '508UserInterface': get(viewResult, '[0][0].508UserInterface', null),
      systemOwnership: get(viewResult, '[0][0].systemOwnership', null),
    },
    DataCenterHosting: {
      movingToCloud: get(viewResult, '[0][0].movingToCloud', null),
      movingToCloudDate: formatSystemDate('movingToCloudDate'),
    },
    SystemMaintainerInformation: {
      systemCustomization: get(viewResult, '[0][0].systemCustomization', null),
      frontendAccessType: get(viewResult, '[0][0].frontendAccessType', null),
      netAccessibility: get(viewResult, '[0][0].netAccessibility', null),
      ipEnabledAssetCount: strToNumberOrNull(get(viewResult, '[0][0].ipEnabledAssetCount', null)),
      ip6EnabledAssetPercent: get(viewResult, '[0][0].ip6EnabledAssetPercent', null),
      ip6TransitionPlan: get(viewResult, '[0][0].ip6TransitionPlan', null),
      hardCodedIpAddress: strToBoolOrNull(get(viewResult, '[0][0].hardCodedIpAddress', null)),
      ecapParticipation: strToBoolOrNull(get(viewResult, '[0][0].ecapParticipation', null)),
      systemProductionDate: formatSystemDate('systemProductionDate'),
      devCompletionPercent: get(viewResult, '[0][0].devCompletionPercent', null),
      devWorkDescription: get(viewResult, '[0][0].devWorkDescription', null),
      agileUsed: strToBoolOrNull(get(viewResult, '[0][0].agileUsed', null)),
      deploymentFrequency: get(viewResult, '[0][0].deploymentFrequency', null),
      majorRefreshDate: formatSystemDate('.majorRefreshDate'),
      plansToRetireReplace: get(viewResult, '[0][0].plansToRetireReplace', null),
      yearToRetireReplace: get(viewResult, '[0][0].yearToRetireReplace', null),
      quarterToRetireReplace: get(viewResult, '[0][0].quarterToRetireReplace', null),
      businessArtifactsOnDemand: strToBoolOrNull(get(viewResult, '[0][0].businessArtifactsOnDemand', null)),
      systemRequirementsOnDemand: strToBoolOrNull(get(viewResult, '[0][0].systemRequirementsOnDemand', null)),
      systemDesignOnDemand: strToBoolOrNull(get(viewResult, '[0][0].systemDesignOnDemand', null)),
      sourceCodeOnDemand: strToBoolOrNull(get(viewResult, '[0][0].sourceCodeOnDemand', null)),
      testPlanOnDemand: strToBoolOrNull(get(viewResult, '[0][0].testPlanOnDemand', null)),
      testScriptsOnDemand: strToBoolOrNull(get(viewResult, '[0][0].testScriptsOnDemand', null)),
      testReportsOnDemand: strToBoolOrNull(get(viewResult, '[0][0].testReportsOnDemand', null)),
      omDocumentationOnDemand: strToBoolOrNull(get(viewResult, '[0][0].omDocumentationOnDemand', null)),
      recordsManagementBucket: splitOrNull(get(viewResult, '[0][0].recordsManagementBucket', null), '|'),
      adHocAgileDeploymentFrequency: get(viewResult, '[0][0].adHocAgileDeploymentFrequency', null),
      dataAtRestEncryptionKeyManagement: get(viewResult, '[0][0].dataAtRestEncryptionKeyManagement', null),
      legalHoldCaseName: get(viewResult, '[0][0].legalHoldCaseName', null),
      locallyStoredUserInformation: strToBoolOrNull(get(viewResult, '[0][0].locallyStoredUserInformation', null)),
      multifactorAuthenticationMethod: splitOrNull(get(viewResult, '[0][0].multifactorAuthenticationMethod', null), '/'),
      multifactorAuthenticationMethodOther: get(viewResult, '[0][0].multifactorAuthenticationMethodOther', null),
      networkTrafficEncryptionKeyManagement: get(viewResult, '[0][0].networkTrafficEncryptionKeyManagement', null),
      noMajorRefresh: strToBoolOrNull(get(viewResult, '[0][0].noMajorRefresh', null)),
      noPersistentRecordsFlag: strToBoolOrNull(get(viewResult, '[0][0].noPersistentRecordsFlag', null)),
      noPlannedMajorRefresh: strToBoolOrNull(get(viewResult, '[0][0].noPlannedMajorRefresh', null)),
      recordsManagementDisposalLocation: get(viewResult, '[0][0].recordsManagementDisposalLocation', null),
      recordsManagementDisposalPlan: get(viewResult, '[0][0].recordsManagementDisposalPlan', null),
      recordsUnderLegalHold: strToBoolOrNull(get(viewResult, '[0][0].recordsUnderLegalHold', null)),
      systemDataLocation: splitOrNull(get(viewResult, '[0][0].systemDataLocation', null), '|'),
      systemDataLocationNotes: get(viewResult, '[0][0].systemDataLocationNotes', null),
    },
    SoftwareProductDetails: {
      apisDeveloped: get(viewResult, '[0][0].apisDeveloped', null),
      apisAccessibility: get(viewResult, '[0][0].apisAccessibility', null),
      apiFHIRUse: get(viewResult, '[0][0].apiFHIRUse', null),
      apiFHIRUseOther: get(viewResult, '[0][0].apiFHIRUseOther', null),
      apiDataArea: splitOrNull(get(viewResult, '[0][0].apiDataArea', null), '|'),
      systemHasApiGateway: strToBoolOrNull(get(viewResult, '[0][0].systemHasApiGateway', null)),
      aiPlan: get(viewResult, '[0][0].aiPlan', null),
      systemAiType: get(viewResult, '[0][0].systemAiType', null),
      systemAiTypeOther: get(viewResult, '[0][0].systemAiTypeOther', null),
    },
  };

  return res.status(200).send(returnObj);
};

export default detailById;
