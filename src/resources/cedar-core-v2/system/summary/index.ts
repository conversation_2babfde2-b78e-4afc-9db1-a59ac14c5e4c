// /system/summary
// /system/summary/{id}
import systemSummaryById from './by-id';
import systemSummaryList from './list';
import { GetResourceConfig } from '../../../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-system-summary-list',
  path: '/',
  method: 'get',
  resource: systemSummaryList,
}, {
  name: 'get-system-summary-by-id',
  path: '/:id',
  method: 'get',
  resource: systemSummaryById,
}];

export default getResourceConfig;
