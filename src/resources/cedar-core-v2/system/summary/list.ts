import { Response, Request } from 'express';
import {
  attempt,
  get,
  isError,
  map,
  pick,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../../data-sources/mssql';
import { StoredProcedureParam, StoredProcedureData, StoredProcedureQuery } from '../../../../types';
import { getDb, SP_GET_SYSTEM_LIST, SP_GET_SYSTEM_LIST_ROLE } from '../../../../utils/db-helpers';
import { tryAsync, strToBool, renameKeys } from '../../../../utils/general';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/system/summary'
#swagger.method = 'get'
#swagger.tags = ['System']
#swagger.description = 'System Summary list'
#swagger.produces = ['application/json']

#swagger.parameters['state'] = {
  in: 'query',
  description: 'Filter by state',
  required: true,
  type: 'string',
  example: 'Active'
}
#swagger.parameters['status'] = {
  in: 'query',
  description: 'Filter by status',
  required: true,
  type: 'string',
  enum: ['New', 'Used'],
  example: 'Used'
}
#swagger.parameters['version'] = {
  in: 'query',
  description: 'API version',
  required: true,
  type: 'string',
  example: '2.0.0'
}
#swagger.parameters['includeInSurvey'] = {
  in: 'query',
  description: 'Include in survey flag (true/false)',
  required: true,
  type: 'string',
  example: 'false'
}
#swagger.parameters['idsOnly'] = {
  in: 'query',
  description: 'Return only ID + name pairs (true/false)',
  required: true,
  type: 'string',
  example: 'true'
}
#swagger.parameters['belongsTo'] = {
  in: 'query',
  description: 'Belongs to filter',
  required: true,
  type: 'string',
  example: 'OrgA'
}
#swagger.parameters['userName'] = {
  in: 'query',
  description: 'User name filter',
  required: true,
  type: 'string',
  example: 'jdoe'
}
#swagger.parameters['roleType'] = {
  in: 'query',
  description: 'Role type filter',
  required: true,
  type: 'string',
  example: 'Administrator'
}

#swagger.responses[200] = {
  description: 'Successfully retrieved system summary list',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          SystemSummary: {
            type: 'array',
            items: {
              type: 'object'
            },
            example: [
              { id: '550e8400-e29b-41d4-a716-446655440000', name: 'System A' }
            ]
          },
          count: {
            type: 'integer',
            description: 'Number of items returned',
            example: 1
          }
        },
        required: ['SystemSummary', 'count']
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const systemSummaryList = async (
  req: Request,
  res: Response,
) => {
  const validateParams = (param: string | null) => {
    const paramSchema = Joi.string().allow(null, '');
    const {
      error,
      value,
    } = paramSchema.validate(param);

    if (error) {
      return error;
    }

    return value;
  };

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ message: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ message: 'Database Unavailable' });
  }

  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];
  const queryData: StoredProcedureData[] = [{
    name: 'Outputjson',
    value: 'result',
    isOutput: true,
  }];
  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  // Both calls
  // state
  const stateParam = get(req, 'query.state', null) as string | null;
  const state = validateParams(stateParam);
  if (isError(state)) {
    return res.status(400).send({ message: state.message });
  }
  queryData.push({
    name: 'objectState',
    value: state,
  });

  // status
  const statusParam = get(req, 'query.status', null) as string | null;
  const status = validateParams(statusParam);
  if (isError(status)) {
    return res.status(400).send({ message: status.message });
  }
  queryData.push({
    name: 'status',
    value: status,
  });

  // version
  const versionParam = get(req, 'query.version', null) as string | null;
  const version = validateParams(versionParam);
  if (isError(version)) {
    return res.status(400).send({ message: version.message });
  }
  queryData.push({
    name: 'version',
    value: version,
  });

  // includeInSurvey
  const includeInSurveyParam = get(req, 'query.includeInSurvey', null) as string | null;
  const includeInSurvey = validateParams(includeInSurveyParam);
  if (isError(includeInSurvey)) {
    return res.status(400).send({ message: includeInSurvey.message });
  }
  queryData.push({
    name: 'includeInSurvey',
    value: includeInSurvey,
  });

  // idsOnly - Used to process results
  const idsOnlyParam = get(req, 'query.idsOnly', null) as string | null;
  const idsOnlyParamVal = validateParams(idsOnlyParam);
  if (isError(idsOnlyParamVal)) {
    return res.status(400).send({ message: idsOnlyParamVal.message });
  }
  const idsOnly = strToBool(idsOnlyParamVal);

  // belongsTo
  const belongsToParam = get(req, 'query.belongsTo', null) as string | null;
  const belongsTo = validateParams(belongsToParam);
  if (isError(belongsTo)) {
    return res.status(400).send({ message: belongsTo.message });
  }
  queryData.push({
    name: 'belongsTo',
    value: belongsTo,
  });

  let spQuery = SP_GET_SYSTEM_LIST;

  // By Role Call
  // userName
  const userNameParam = get(req, 'query.userName', null) as string | null;
  const userName = validateParams(userNameParam);
  if (isError(userName)) {
    return res.status(400).send({ message: userName.message });
  }
  if (userName) {
    spQuery = SP_GET_SYSTEM_LIST_ROLE;
    queryData.push({
      name: 'userName',
      value: userName,
    });

    // roleType
    const roleTypeParam = get(req, 'query.roleType', null) as string | null;
    const roleType = validateParams(roleTypeParam);
    if (isError(roleType)) {
      return res.status(400).send({ message: roleType.message });
    }
    queryData.push({
      name: 'roleType',
      value: roleType,
    });
  }

  // FIXME: status must be null to get results, pending ticket with DBA
  const [systemListError, systemListResults] = await tryAsync(
    db.queryStoredProcedures(spQuery, queryParams, queryData, queryResults),
  );

  if (systemListError || isError(systemListResults)) {
    app.logger.error({ error: systemListError || systemListResults });
    return res.status(500).send({ message: 'Unable to retrieve the system list' });
  }

  const systemListQueryStatus: number = get(systemListResults, '[0][0].queryStatus', -1);
  if (systemListQueryStatus === -1) {
    return res.status(500).send({ message: 'Status of the query was invalid' });
  }

  if (systemListQueryStatus === 1) {
    return res.status(200).send({});
  }

  const systemListResult = get(systemListResults, '[0][0].result');
  if (!systemListResult) {
    return res.status(500).send({ message: 'Unable to get get result from query' });
  }

  const parsedSystemListResult = attempt(JSON.parse.bind(null, systemListResult));
  if (isError(parsedSystemListResult)) {
    return res.status(500).send({ message: 'Unable to parse database response' });
  }

  const systems = get(parsedSystemListResult, 'ResultSet', []);
  if (idsOnly) {
    const idSystems = map(systems, (value) => pick(value, ['id', 'name']));
    return res.status(200).send({
      SystemSummary: idSystems,
      count: idSystems.length,
    });
  }

  const mappedSystems = renameKeys(systems, [{
    oldKey: 'objectState',
    newKey: 'state',
  }, {
    oldKey: 'businessOwnerComp',
    newKey: 'businessOwnerOrgComp',
  }, {
    oldKey: 'systemMaintainerComp',
    newKey: 'systemMaintainerOrgComp',
  }, {
    oldKey: 'ATO Effective Date',
    newKey: 'atoEffectiveDate',
    format: 'YYYY-MM-DD',
    emptyToNull: true,
  }, {
    oldKey: 'ATO Expiration Date',
    newKey: 'atoExpirationDate',
    format: 'YYYY-MM-DD',
    emptyToNull: true,
  }]);

  return res.status(200).send({
    SystemSummary: mappedSystems,
    count: mappedSystems.length,
  });
};

export default systemSummaryList;
