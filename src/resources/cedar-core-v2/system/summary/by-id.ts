import { Response, Request } from 'express';
import { get, isError } from 'lodash';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../../data-sources/mssql';
import { getDb, SPARX_EASI_SYSTEM } from '../../../../utils/db-helpers';
import { tryAsync } from '../../../../utils/general';

/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/system/summary/{id}'
#swagger.method = 'get'
#swagger.tags = ['System']
#swagger.description = 'System Summary by id'
#swagger.parameters['id'] = {
  in: 'path',
  required: true,
  description: 'ID of the system (UUID string)',
  type: 'string',
  example: '550e8400-e29b-41d4-a716-************'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Successfully retrieved system summary',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          SystemSummary: {
            type: 'object',
            description: 'The system summary record'
          },
          count: {
            type: 'integer',
            description: 'Number of summary items returned',
            example: 1
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
const systemSummaryById = async (
  req: Request,
  res: Response,
) => {
  const id = get(req, 'params.id', '');

  if (!id) {
    return res.status(400).send({ message: 'No object id provided' });
  }

  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));
  if (error) {
    return res.status(400).send({ message: 'Invalid object id' });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ message: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    return res.status(500).send({ message: 'Database Unavailable' });
  }

  const [viewError, viewResult] = await tryAsync(db.queryView(SPARX_EASI_SYSTEM, [
    '"Sparx System GUID" as id',
    '"nextVersionID" as nextVersionId',
    '"previousVersionID" as previousVersionId',
    '"Acronym" as acronym',
  ], {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: id,
      },
    },
  }));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ message: 'Unable to retrieve system summary by ID' });
  }

  const systemSummary = get(viewResult, '[0]', []);
  return res.status(200).send({
    SystemSummary: systemSummary,
    count: systemSummary.length,
  });
};

export default systemSummaryById;
