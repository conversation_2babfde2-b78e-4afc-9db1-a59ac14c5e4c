import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import contractListUtil from '../../../utils/contract';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';

const contractList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const result = await contractListUtil(
    app,
    db,
    get(req, 'query.systemId', '') as string,
    get(req, 'query', {}) as Record<string, string>,
  );

  if (isError(result)) {
    app.logger.error({ error: result });
    return res.status(500).send({ error: 'There was an issue fetching the contracts' });
  }

  return res.status(200).send(result);
};

export default contractList;
