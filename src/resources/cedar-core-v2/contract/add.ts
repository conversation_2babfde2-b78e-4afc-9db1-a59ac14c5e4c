import { Request, Response } from 'express';
import {
  isArray,
  isEmpty,
  isError,
} from 'lodash';
import asyncHandler from 'express-async-handler';
import MssqlData from '../../../data-sources/mssql';
import { getDb } from '../../../utils/db-helpers';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { sendError, sendSuccess, logAppError } from '../../../utils/express/responses';
import {
  CMSApp,
} from '../../../types';
import { contractAddUtil, ContractRequest } from '../../../utils/contract/index';

export interface ContractAddRequest {
  Contracts: ContractRequest[];
}

const contractAdd = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  let app: CMSApp | undefined;

  try {
    app = req?.systemApp;
    if (!app) {
      sendError(res, 500, { message: [Messages.app_invalid] });
      return;
    }

    const db = getDb<MssqlData>(app, DatabaseConfigs.sparxea);
    if (isError(db)) {
      app.logger.error({ error: db });
      sendError(res, 500, { message: [Messages.db_unavailable] });
      return;
    }

    const payload: ContractAddRequest = req?.body;
    const contractsToAdd = payload?.Contracts;

    if (isEmpty(contractsToAdd) || !isArray(contractsToAdd)) {
      sendError(res, 400, { message: ['Please provide a list of contracts.'] });
      return;
    }

    // Use the new utility function for contract add
    const result = await contractAddUtil(app, db, contractsToAdd);

    if (isError(result)) {
      const errorMessage = result.message;
      if (errorMessage.includes('Stored procedure returned non-zero status')) {
        sendError(res, 500, { message: ['Failed to add contract(s).'] });
        return;
      }
      sendError(res, 500, { message: [Messages.db_query_stored_procedure_error] });
      return;
    }

    sendSuccess(res, {
      result: 'success',
      message: result.guid ?? '',
    });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'contract',
        action: 'add',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: ['Internal server error'] });
  }
});

export default contractAdd;
