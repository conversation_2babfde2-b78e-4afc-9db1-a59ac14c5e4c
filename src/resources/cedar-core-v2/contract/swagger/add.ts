import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/contract',
  method: 'post',
  tags: ['System Census'],
  description: 'Add new contract deliverables to the system.',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          required: ['Contracts'],
          properties: {
            Contracts: {
              type: 'array',
              description: 'List of contract objects to be added as deliverables.',
              items: {
                type: 'object',
                required: ['id', 'systemId'],
                properties: {
                  id: {
                    type: 'string',
                    description: 'The ID of the existing contract in Sparx that this deliverable relates to.',
                    example: '{11111111-2222-3333-4444-555555555555}',
                  },
                  ContractNumber: {
                    type: 'string',
                    description: 'The contract number.',
                    example: 'ABC-123',
                    nullable: true,
                  },
                  IsDeliveryOrg: {
                    type: 'string',
                    description: 'Indicates if it is a delivery organization. Expected values: "Yes" or "No".',
                    example: 'Yes',
                    nullable: true,
                  },
                  OrderNumber: {
                    type: 'string',
                    description: 'The order number associated with the contract.',
                    example: 'ORD-001',
                    nullable: true,
                  },
                  ProductServiceDescription: {
                    type: 'string',
                    description: 'Description of the product or service provided.',
                    example: 'Software development services',
                    nullable: true,
                  },
                  ProjectTitle: {
                    type: 'string',
                    description: 'Title of the project associated with the contract.',
                    example: 'Census Modernization Project',
                    nullable: true,
                  },
                  ServiceProvided: {
                    type: 'string',
                    description: 'Detailed description of the service provided.',
                    example: 'Development of Census Data API',
                    nullable: true,
                  },
                  parentAwardId: {
                    type: 'string',
                    description: 'The ID of the parent award/contract, if applicable.',
                    example: '{66666666-7777-8888-9999-aaaaaaaaaaaa}',
                    nullable: true,
                  },
                  contractADO: {
                    type: 'string',
                    description: 'Indicates if it is an ADO Parent Contract. Expected values: "Yes" or "No".',
                    example: 'No',
                    nullable: true,
                  },
                  awardId: {
                    type: 'string',
                    description: 'The award ID or main contract identifier.',
                    example: 'AWARD-XYZ',
                    nullable: true,
                  },
                  description: {
                    type: 'string',
                    description: 'General description of the contract.',
                    example: 'Annual maintenance contract for Census systems.',
                    nullable: true,
                  },
                  systemId: {
                    type: 'string',
                    description: 'The ID of the system (architecture element) that this contract deliverable is associated with.',
                    example: '{22222222-3333-4444-5555-666666666666}',
                  },
                  POPStartDate: {
                    type: 'string',
                    format: 'date',
                    description: 'Period of Performance (POP) start date.',
                    example: '2023-01-01',
                    nullable: true,
                  },
                  POPEndDate: {
                    type: 'string',
                    format: 'date',
                    description: 'Period of Performance (POP) end date.',
                    example: '2024-12-31',
                    nullable: true,
                  },
                  contractName: {
                    type: 'string',
                    description: 'The name of the contract.',
                    example: 'Census API Development Contract',
                    nullable: true,
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Contract deliverables added successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['{GUID_OF_NEW_DELIVERABLE_1}', '{GUID_OF_NEW_DELIVERABLE_2}'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
