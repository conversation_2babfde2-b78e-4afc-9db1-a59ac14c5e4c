// Modules.
import { Request, Response } from 'express';
import {
  isArray, isError, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { unknownAsError, tryAsync } from '../../../utils/general';
import { endpointSetup, logAppError, sendError } from '../../../utils/express/responses';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import Messages from '../../../utils/constants/messages';

// Define local types as required
export enum CostTypeApplication {
  All = 'all',
  Alfabet = 'alfabet',
}

export interface CostTypeFindQueryParams {
  application: CostTypeApplication;
  name: string;
}

export interface DbCostType extends Record<string, unknown> {
  'Sparx CostPool GUID': string;
  'CostPool Name': string;
}

export type DbCostTypeQueryResult = [DbCostType[], number];

export interface CostType extends Record<string, unknown> {
  id: string;
  name: string;
}


export interface CostTypeFindResponse {
  id?: string;
  name?: string;
  CostTypes: CostType[];
}

export enum CostTypeFindMessages {
  // error_application_invalid = 'Please specify a valid application',
  error_name_missing = 'Please specify a valid cost type name',
}

// export const convertDbCostTypeToCostType = (dbCostType: DbCostType): CostType => ({
//   id: dbCostType['Sparx CostPool GUID'],
//   name: dbCostType['CostPool Name'],
// });

const qsValidate = (p: CostTypeFindQueryParams): void => {
  if (!p.application || !(Object.values(CostTypeApplication) as string[]).includes(p.application)) {
    throw new Error(CostTypeFindMessages.error_application_invalid);
  }

  if (p.name !== undefined && !isString(p.name)) {
    throw new Error(CostTypeFindMessages.error_name_missing);
  }
};

const handler = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.sparxea);

    try {
      // neither name nor application are actually required or used at all
      // Designer says that if they're not there to throw an error, so just
      // following that logic - but really neither param does anything
      const name = req.query.name ?? '';
      if (name !== undefined && !isString(name)) {
        sendError(res, 400, { message: [CostTypeFindMessages.error_name_missing] });
        return;
      }

      const application = req.query.application ?? '';
      if (application !== undefined && !isString(application)) {
        sendError(res, 400, { message: [CostTypeFindMessages.error_application_missing] });
        return;
      }

      const [error, result] = await tryAsync(
        db.queryView(
          'Sparx_CostPool',
          [
            '"Sparx CostPool GUID" as id',
            '"CostPool Name" as name',
          ],
        ),
      );

      if (error || isError(result)) {
        app.logger.error({ error: error ?? result });
        sendError(res, 500, { message: [Messages.db_query_view_error] });
        return;
      }

      if (!isArray(result) || !isArray(result[0])) {
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }

      const topLevelHighlightName = 'Internal Labor';

      const dbCostTypes: CostType[] = result[0];
      let highlightedCostType = dbCostTypes.find((costType) => costType.name === topLevelHighlightName);

      if (!highlightedCostType) {
        highlightedCostType = {
          name: '',
          id: '',
        };
      }

      const filteredCostType = dbCostTypes.filter((costType) => costType.name !== topLevelHighlightName);

      const response: CostTypeFindResponse = {
        name: highlightedCostType.name,
        id: highlightedCostType.id,
        CostTypes: filteredCostType,
      };

      res.status(200).send(response);
    } catch (error) {
      const e = unknownAsError(error);
      logAppError(app, {
        package: 'census-core-v2',
        service: 'costType',
        action: 'find-list',
        error: e,
      });
      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export default handler;
