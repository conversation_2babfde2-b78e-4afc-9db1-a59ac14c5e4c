import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/CEDAR Core API/2.0.0/costType/list',
  method: 'get',
  tags: ['Cedar Core'],
  description: 'Retrieve a list of cost types based on application and optionally name.',
  parameters: [
    {
      name: 'application',
      in: 'query',
      description: 'Application where the object or role exists.',
      required: true,
      schema: {
        type: 'string',
        enum: ['alfabet', 'all'],
        example: 'alfabet',
      },
    },
    {
      name: 'name',
      in: 'query',
      description: 'The name of a specific group of cost types to filter by.',
      required: false,
      schema: {
        type: 'string',
        example: 'IT',
        nullable: true,
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully fetched cost types',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: 'ID of the first cost type in the list (if available)',
                example: '{11111111-2222-3333-4444-555555555555}',
                nullable: true,
              },
              name: {
                type: 'string',
                description: 'Name of the first cost type in the list (if available)',
                example: 'IT',
                nullable: true,
              },
              CostTypes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: {
                      type: 'string',
                      example: '{11111111-2222-3333-4444-555555555555}',
                    },
                    name: {
                      type: 'string',
                      example: 'IT',
                    },
                  },
                },
              },
              count: {
                type: 'integer',
                format: 'int32',
                example: 1,
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;