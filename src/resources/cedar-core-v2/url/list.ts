import {
  Response,
  Request,
} from 'express';
import {
  get,
  isEmpty,
  isError,
} from 'lodash';
import <PERSON><PERSON> from 'joi';
import { cedarUrlsUtil } from '../../../utils/urls';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/url/{id}'
#swagger.method = 'get'
#swagger.tags = ['Url']
#swagger.description = 'URL List'
#swagger.produces = ['application/json']

#swagger.parameters['id'] = {
  in: 'path',
  description: 'ID of object the URLs are associated with.',
  required: true,
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.responses[200] = {
  description: 'Successfully retrieved a list of URLs associated with an object in CEDAR',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          UrlList: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                address: {
                  type: 'string',
                  description: 'A valid and full URL'
                },
                isApiEndpoint: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate whether URL is an API Endpoint'
                },
                isBehindWebApplicationFirewall: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate whether the application is behind a Web Application Firewall (WAF)'
                },
                isVersionCodeRepository: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate if this URL provides access to a versioned code repository?'
                },
                urlHostingEnv: {
                  type: 'string',
                  description: 'The hosting environment associated with a specific URL',
                  example: 'Production'
                },
                urlId: {
                  type: 'string',
                  description: 'Unique key that uniquely identified the URL in database',
                  example: '218-10-0'
                }
              }
            }
          },
          count: {
            type: 'integer',
            description: 'Number of items returned',
            example: 1
          }
        },
        required: ['UrlList', 'count']
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const urlList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ message: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ message: 'Database Unavailable' });
  }

  const id = get(req, 'params.id', '');

  const querySchema = Joi.string().uuid();
  const { error } = querySchema.validate(id.slice(1).slice(0, -1));
  if (error) {
    app.logger.error({ error });
    return res.status(400).send({ message: 'Invalid object id' });
  }

  const urlResult = await cedarUrlsUtil(app, db, id);

  if (isError(urlResult)) {
    app.logger.error({ error: urlResult });
    return res.status(500).send({ error: 'There was an issue fetching the urls' });
  }

  if (isEmpty(urlResult)) {
    return res.status(200).send({
      count: 0,
    });
  }

  return res.status(200).send({
    count: urlResult.length,
    UrlList: urlResult,
  });
};

export default urlList;
