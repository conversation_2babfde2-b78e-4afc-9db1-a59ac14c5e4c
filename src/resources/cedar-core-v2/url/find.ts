// Modules.
import { Request, Response } from 'express';
import async<PERSON>and<PERSON> from 'express-async-handler';
import { get, isError } from 'lodash';

// Custom: App.
import { cedarUrlsUtil, UrlFindCedarQueryParams } from 'src/utils/urls';
import MssqlData from 'src/data-sources/mssql';
import { getDb } from 'src/utils/db-helpers';
import QueryParams from 'src/utils/express/queryParams';

/* eslint-disable max-len */
/* NOSONAR
#swagger.start
#swagger.path = '/gateway/CEDAR Core API/2.0.0/url/{id}'
#swagger.method = 'get'
#swagger.tags = ['Url']
#swagger.description = 'URL List'
#swagger.produces = ['application/json']

#swagger.parameters['id'] = {
  in: 'path',
  description: 'ID of object the URLs are associated with.',
  required: true,
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.responses[200] = {
  description: 'Successfully retrieved a list of URLs associated with an object in CEDAR',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          UrlList: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                address: {
                  type: 'string',
                  description: 'A valid and full URL'
                },
                isApiEndpoint: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate whether URL is an API Endpoint'
                },
                isBehindWebApplicationFirewall: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate whether the application is behind a Web Application Firewall (WAF)'
                },
                isVersionCodeRepository: {
                  type: 'boolean',
                  description: 'A boolean flag to indicate if this URL provides access to a versioned code repository?'
                },
                urlHostingEnv: {
                  type: 'string',
                  description: 'The hosting environment associated with a specific URL',
                  example: 'Production'
                },
                urlId: {
                  type: 'string',
                  description: 'Unique key that uniquely identified the URL in database',
                  example: '218-10-0'
                }
              }
            }
          },
          count: {
            type: 'integer',
            description: 'Number of items returned',
            example: 1
          }
        },
        required: ['UrlList', 'count']
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

/**
 * Note: Webmethods accepts any value and just returns an empty list.
 */
const urlList = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const app = get(req, 'systemApp');
  if (!app) {
    res.status(500).send({ message: 'Unable to get the application from request' });
    return;
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    res.status(500).send({ message: 'Database Unavailable' });
    return;
  }

  // Use the new parameter handling pattern
  const { id } = QueryParams.fromPath<UrlFindCedarQueryParams>(req).getAll();

  const urlResult = await cedarUrlsUtil(app, db, id);

  if (isError(urlResult)) {
    app.logger.error({ error: urlResult });
    res.status(500).send({ error: 'There was an issue fetching the urls' });
    return;
  }

  if (!urlResult.length) {
    res.status(200).send({
      count: 0,
    });
    return;
  }

  res.status(200).send({
    count: urlResult?.length,
    UrlList: urlResult,
  });
});

export default urlList;
