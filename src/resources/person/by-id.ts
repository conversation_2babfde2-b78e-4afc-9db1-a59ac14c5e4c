import { Request, Response } from 'express';
import { get, isError } from 'lodash';
import <PERSON><PERSON> from 'joi';
import { personIds } from '../../subsystems/ldap';

const personById = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const id = get(req, 'params.id', '');

  if (!id) {
    return res.status(400).send({ message: 'No person id provided' });
  }

  const querySchema = Joi.string().alphanum().min(4).max(4);
  const { error } = querySchema.validate(id);
  if (error) {
    return res.status(400).send({ message: 'Invalid person id' });
  }

  const user = await personIds(app, id);
  if (isError(user)) {
    return res.status(500).send({ message: 'Failed' });
  }

  return res.status(200).send(user);
};

export default personById;
