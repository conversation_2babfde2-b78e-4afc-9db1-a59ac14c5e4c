import { Request, Response } from 'express';
import <PERSON><PERSON> from 'joi';
import { get, isError } from 'lodash';
import { bind<PERSON>erson } from '../../subsystems/ldap';
import { getJwt, setJwt } from '../../subsystems/authentication/jwt';

/*
#swagger.start
#swagger.path = '/gateway/LDAP/1.0/authenticate'
#swagger.method = 'post'
#swagger.tags = ['Auth']
#swagger.description = 'Auth endpoint'
#swagger.security = []
#swagger.requestBody = {
  required: true,
  content: {
    "application/json": {
      schema: {
        type: "object",
        properties: {
          username: { type: "string", example: "user" },
          password: { type: "string", example: "1234" }
        },
        required: ["username", "password"]
      }
    }
  }
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Authentication successful',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          message: { type: 'string', example: 'Success' }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request – validation or bind failure',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          message: {
            type: 'string',
            example: 'Account validation error: "username" is required'
          }
        }
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
const authenticate = async (req: Request, res: Response) => {
  // get credentials
  const loginValues: { username: string, password: string } = {
    username: get(req, 'body.username'),
    password: get(req, 'body.password'),
  };

  // Add schema
  const loginSchema = Joi.object({
    username: Joi.string()
      .regex(/^[A-Za-z0-9]{4}$/)
      .required()
      .messages({
        'string.pattern.base': '{{#label}} is an invalid value',
      }),
    password: Joi.string()
      .min(8)
      .max(8)
      .required()
      .messages({
        'string.min': '{{#label}} is a minimum of 8 characters',
        'string.max': '{{#label}} is a maximum of 8 characters',
        'string.pattern.base': '{{#label}} is an invalid value',
      }),
  }).messages({
    'any.required': '{{#label}} is a required field',
    'string.empty': '{{#label}} is required and can not be empty',
  });

  // validate Joi schema
  const { error } = loginSchema.validate(loginValues);
  if (error) {
    return res.status(400).send({ message: `Account validation error: ${error.message}` });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const bind = await bindPerson(app, loginValues);
  if (isError(bind)) {
    app.logger.error({ error: bind });
    return res.status(400).send({ message: 'Failed' });
  }

  const jwt = getJwt(req, loginValues.username);
  if (isError(jwt)) {
    app.logger.error({ error: jwt });
    return res.status(500).send({ message: 'Failed' });
  }

  setJwt(res, jwt);

  return res.status(200).send({ message: 'Success' });
};

export default authenticate;
