import { Request, Response } from 'express';
import { GetResourceConfig } from '../../types';

// #swagger.start
/*
#swagger.path = '/'
#swagger.method = 'get'
#swagger.description = 'Root endpoint'
#swagger.produces = ['application/json']
#swagger.responses[200] = { message: 'Hello from the server!' }
*/
// #swagger.end

// @todo-response Webmethods returns an HTML page here.
const root = (_: Request, res: Response) => {
  res.status(200).json({ message: 'Hello from the server!' });
};

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-root',
  path: '/',
  method: 'get',
  resource: root,
  public: true,
}];

export default getResourceConfig;
export {
  root,
};
