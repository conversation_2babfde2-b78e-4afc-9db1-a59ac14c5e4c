import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';
import { budgetFindUtil } from '../../../utils/budget';
import contractListUtil from '../../../utils/contract';

const budgetAndContractsPageFind = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId');

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\'');
    app.logger.error({ error });
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\'' });
  }

  const systemIdVal = baseSchema.uuid().validate(systemId);
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return res.status(400).send({ error: 'The system ID is not valid' });
  }

  // budget flow
  const budgetResponseResult = await budgetFindUtil(
    app,
    db,
    { systemId: systemIdVal.value },
  );

  if (isError(budgetResponseResult)) {
    app.logger.error({ error: budgetResponseResult });
    return res.status(500).send({ error: 'There was an issue fetching the budgets' });
  }

  if (
    !Object.hasOwn(budgetResponseResult, 'Budgets')
    || budgetResponseResult.count === 0
  ) {
    const error = new Error('No budgets found');
    app.logger.error({ error });
    return res.status(500).send({ error: error.message });
  }

  // contract flow
  // assigning query manually because this page is only looking using systemId
  const contractsResponseResult = await contractListUtil(
    app,
    db,
    systemIdVal.value,
    { systemId: systemIdVal.value },
  );

  if (isError(contractsResponseResult)) {
    app.logger.error({ error: contractsResponseResult });
    return res.status(500).send({ error: 'There was an issue fetching the contracts' });
  }

  return res.status(200).send({
    systemId: systemIdVal.value,
    Budgets: budgetResponseResult.Budgets,
    Contracts: contractsResponseResult.Contracts,
  });
};

export default budgetAndContractsPageFind;
