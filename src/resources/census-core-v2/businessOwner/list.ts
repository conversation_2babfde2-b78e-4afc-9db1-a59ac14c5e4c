// generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import { get, isError, isString, isEmpty } from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import { getDb, SPARX_SYSTEM_BUSINESS_OWNER_CENSUS } from '../../../utils/db-helpers';
import { tryAsync, strToBoolOrNull, splitOrNull, strToNumberOrNull } from '../../../utils/general';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { Where } from '../../../types';

// Local types as required
export interface DbBusinessOwner {
  'Sparx System ID': number | null;
  'Sparx System GUID': string | null;
  'System Name': string | null;
  Description: string | null;
  Acronym: string | null;
  'Object State': string | null;
  'CMS UUID': string | null;
  'CMS Owned': string | null;
  'Contractor Owned': string | null;
  'System Cost Per Year': string | null;
  'Number of Contractor Support FTEs': string | null;
  'Number of Direct System Users': string | null;
  'Number of Federal Support FTEs': string | null;
  'Beneficiary Information': string | null;
  'Edit Beneficiary Information': string | null;
  'System has UI': string | null;
  'Health Disparity Data': string | null;
  'System Ownership': string | null;
  'System UI Accessibility': string | null;
}

export interface BusinessOwner {
  id: string;
  pageName: string;
  description: string | null;
  systemName: string | null;
  acronym: string | null;
  objectState: string | null;
  cmsUuid: string | null;
  cmsOwned: string | null;
  contractorOwned: string | null;
  systemOwnership: string | null;
  costPerYear: number | null;
  numberOfFederalFte: number | null;
  numberOfContractorFte: number | null;
  numberOfSupportedUsersPerMonth: number | null;
  beneficiaryInformation: string[] | null;
  editBeneficiaryInformation: boolean | null;
  systemHasUi: string | null;
  storesHealthDisparityData: boolean | null;
  systemUiAccessibility: string[] | null;
}

export const convertDbBusinessOwnerToBusinessOwner = (
  dbOwner: DbBusinessOwner,
): BusinessOwner => ({
  id: dbOwner['Sparx System GUID'] || '',
  pageName: 'BusinessOwnerBasicInformation',
  description: dbOwner.Description,
  systemName: dbOwner['System Name'],
  acronym: dbOwner.Acronym,
  objectState: dbOwner['Object State'],
  cmsUuid: dbOwner['CMS UUID'],
  cmsOwned: dbOwner['CMS Owned'],
  contractorOwned: dbOwner['Contractor Owned'],
  systemOwnership: dbOwner['System Ownership'],
  costPerYear: strToNumberOrNull(dbOwner['System Cost Per Year']),
  numberOfFederalFte: strToNumberOrNull(dbOwner['Number of Federal Support FTEs']),
  numberOfContractorFte: strToNumberOrNull(dbOwner['Number of Contractor Support FTEs']),
  numberOfSupportedUsersPerMonth: strToNumberOrNull(dbOwner['Number of Direct System Users']),
  beneficiaryInformation: splitOrNull(dbOwner['Beneficiary Information'], '|'),
  editBeneficiaryInformation: strToBoolOrNull(dbOwner['Edit Beneficiary Information']),
  systemHasUi: dbOwner['System has UI'],
  storesHealthDisparityData: strToBoolOrNull(dbOwner['Health Disparity Data']),
  systemUiAccessibility: splitOrNull(dbOwner['System UI Accessibility'], '|'),
});

const businessOwnerFindList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, DatabaseConfigs.cedarSupport);
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'query.id');
  if (!id || !isString(id)) {
    const error = new Error('ID must be provided');
    app.logger.error({ error });
    return res.status(400).send({ error: 'ID must be provided' });
  }

  const columnsToSelect = [
    '"Sparx System ID"',
    '"Sparx System GUID"',
    '"System Name"',
    '"Description"',
    '"Acronym"',
    '"Object State"',
    '"CMS UUID"',
    '"CMS Owned"',
    '"Contractor Owned"',
    '"System Cost Per Year"',
    '"Number of Contractor Support FTEs"',
    '"Number of Direct System Users"',
    '"Number of Federal Support FTEs"',
    '"Beneficiary Information"',
    '"Edit Beneficiary Information"',
    '"System has UI"',
    '"Health Disparity Data"',
    '"System Ownership"',
    '"System UI Accessibility"',
  ];

  const whereClause: Where = {
    where: {
      operation: {
        column: 'Sparx System GUID',
        operator: '=',
        value: id,
      },
    },
  };

  const [queryError, queryResult] = await tryAsync(
    db.queryView(
      SPARX_SYSTEM_BUSINESS_OWNER_CENSUS,
      columnsToSelect,
      whereClause,
    ),
  );

  if (queryError || isError(queryResult)) {
    app.logger.error({ error: queryError || queryResult });
    return res.status(500).send({ error: 'Error querying database' });
  }

  if (!queryResult || !Array.isArray(queryResult)) {
    app.logger.error({ error: new Error('Unable to get result from query') });
    return res.status(500).send({ error: 'Unable to get result from query' });
  }

  const businessOwnersRaw: DbBusinessOwner[] = get(queryResult, '[0]') as DbBusinessOwner[];

  if (isEmpty(businessOwnersRaw)) {
    app.logger.error({ error: new Error('No records found with that ID') });
    return res.status(400).send({ error: 'No records found with that ID' });
  }

  if (businessOwnersRaw.length > 1) {
    app.logger.error({ error: new Error('More than one system found with that ID') });
    return res.status(400).send({ error: 'More than one system found with that ID' });
  }

  const businessOwner: BusinessOwner = convertDbBusinessOwnerToBusinessOwner(businessOwnersRaw[0]);

  return res.status(200).send(businessOwner);
};

export default businessOwnerFindList;
