// generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/page/businessOwner',
  method: 'get',
  tags: ['page'],
  description: 'Get business owner basic information for a system by system ID.',
  parameters: [{
    name: 'id',
    in: 'query',
    description: 'The GUID of the system for which to retrieve business owner information.',
    required: true,
    schema: {
      type: 'string',
      example: '{11111111-**************-************}',
    },
  }],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Business owner information retrieved successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: 'The GUID of the system.',
                example: '{11111111-**************-************}',
              },
              pageName: {
                type: 'string',
                description: 'The page name for the business owner information.',
                example: 'BusinessOwnerBasicInformation',
              },
              description: {
                type: 'string',
                description: 'Description of the system.',
                example: 'This is a test system for business owner details.',
                nullable: true,
              },
              systemName: {
                type: 'string',
                description: 'Name of the system.',
                example: 'Test Business System',
                nullable: true,
              },
              acronym: {
                type: 'string',
                description: 'Acronym for the system.',
                example: 'TBS',
                nullable: true,
              },
              objectState: {
                type: 'string',
                description: 'State of the object in the system (e.g., "Active", "Retired").',
                example: 'Active',
                nullable: true,
              },
              cmsUuid: {
                type: 'string',
                description: 'CMS Universal Unique Identifier for the system.',
                example: '{11111111-**************-************}',
                nullable: true,
              },
              cmsOwned: {
                type: 'string',
                description: 'Indicates if the system is CMS owned (e.g., "Yes", "No").',
                example: 'Yes',
                nullable: true,
              },
              contractorOwned: {
                type: 'string',
                description: 'Indicates if the system is contractor owned (e.g., "Yes", "No").',
                example: 'No',
                nullable: true,
              },
              systemOwnership: {
                type: 'string',
                description: 'Overall ownership of the system (e.g., "Federal", "Contractor").',
                example: 'Federal',
                nullable: true,
              },
              costPerYear: {
                type: 'number',
                format: 'float',
                description: 'Annual cost of the system (e.g., "1000000.50").',
                example: 1000000.50,
                nullable: true,
              },
              numberOfFederalFte: {
                type: 'number',
                format: 'integer',
                description: 'Number of Federal FTEs supporting the system (e.g., "5").',
                example: 5,
                nullable: true,
              },
              numberOfContractorFte: {
                type: 'number',
                format: 'integer',
                description: 'Number of Contractor FTEs supporting the system (e.g., "10").',
                example: 10,
                nullable: true,
              },
              numberOfSupportedUsersPerMonth: {
                type: 'number',
                format: 'integer',
                description: 'Number of direct system users per month (e.g., "1000").',
                example: 1000,
                nullable: true,
              },
              beneficiaryInformation: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Types of beneficiary information stored (e.g., "Name|Address").',
                example: ['Name', 'Address'],
                nullable: true,
              },
              editBeneficiaryInformation: {
                type: 'boolean',
                description: 'Indicates if beneficiary information can be edited (e.g., "true", "false").',
                example: true,
                nullable: true,
              },
              systemHasUi: {
                type: 'string',
                description: 'Indicates if the system has a user interface (e.g., "Yes", "No").',
                example: 'Yes',
                nullable: true,
              },
              storesHealthDisparityData: {
                type: 'boolean',
                description: 'Indicates if the system stores health disparity data (e.g., "true", "false").',
                example: false,
                nullable: true,
              },
              systemUiAccessibility: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Accessibility features of the system UI (e.g., "WCAG 2.0|Section 508").',
                example: ['WCAG 2.0', 'Section 508'],
                nullable: true,
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request (e.g., missing ID, no records found, or multiple records found)',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
