// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/systems',
  method: 'get',
  tags: ['System Census'],
  description: 'Retrieves a list of systems with high-level information and points of contact as part of the System Census.',
  parameters: [
    {
      name: 'version',
      in: 'query',
      description: 'System versions to filter by.',
      required: false,
      schema: {
        type: 'string',
        example: 'v1.0',
      },
    },
    {
      name: 'includeInSurvey',
      in: 'query',
      description: 'Include only systems eligible for the system census. Boolean value (e.g., "true", "false", "1", "0", "yes", "no").',
      required: false,
      schema: {
        type: 'string',
        example: 'true',
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully retrieved list of systems.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              pageName: {
                type: 'string',
                example: 'SystemsList',
              },
              count: {
                type: 'integer',
                format: 'int32',
                example: 10,
              },
              SystemsList: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'string', example: '{11111111-2222-3333-4444-555555555555}' },
                    nextVersionId: { type: 'string', example: '{22222222-3333-4444-5555-666666666666}', nullable: true },
                    previousVersionId: { type: 'string', example: '{00000000-1111-2222-3333-444444444444}', nullable: true },
                    name: { type: 'string', example: 'Example System A' },
                    description: { type: 'string', example: 'Description for Example System A', nullable: true },
                    version: { type: 'string', example: '1.0', nullable: true },
                    acronym: { type: 'string', example: 'ESA', nullable: true },
                    state: { type: 'string', example: 'Active', nullable: true },
                    status: { type: 'string', example: 'Operational', nullable: true },
                    businessOwnerOrg: { type: 'string', example: 'CMS/OIT/FO/ABC', nullable: true },
                    businessOwnerOrgComp: { type: 'string', example: 'Office of Information Technology', nullable: true },
                    systemMaintainerOrg: { type: 'string', example: 'CMS/OIT/DO/XYZ', nullable: true },
                    systemMaintainerOrgComp: { type: 'string', example: 'Division of Operations', nullable: true },
                    qaReviewerAssignmentId: { type: 'string', example: '{77777777-8888-9999-AAAA-BBBBBBBBBBBB}', nullable: true },
                    qaReviewerFirstName: { type: 'string', example: 'Jane', nullable: true },
                    qaReviewerLastName: { type: 'string', example: 'Doe', nullable: true },
                    qaReviewerUserName: { type: 'string', example: 'JDOE', nullable: true },
                    daReviewerAssignmentId: { type: 'string', example: '{CCCCCCCC-DDDD-EEEE-FFFF-111111111111}', nullable: true },
                    daReviewerFirstName: { type: 'string', example: 'John', nullable: true },
                    daReviewerLastName: { type: 'string', example: 'Smith', nullable: true },
                    daReviewerUserName: { type: 'string', example: 'JSMITH', nullable: true },
                    censusStatus: { type: 'string', example: 'Complete', nullable: true },
                    percentComplete: { type: 'integer', example: 100, nullable: true },
                  },
                },
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
