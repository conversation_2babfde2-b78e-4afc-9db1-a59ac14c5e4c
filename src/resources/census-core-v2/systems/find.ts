// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import { isError, isString } from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { tryAsync } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import {
  endpointSetup, logAppError, sendError,
} from '../../../utils/express/responses';
import { StoredProcedureParam, StoredProcedureData, StoredProcedureQuery } from '../../../types';

// Types.
export interface SystemsListItem {
  id: string;
  nextVersionId: string;
  previousVersionId: string;
  ictObjectId: string;
  uuid: string;
  name: string;
  description: string;
  version: string;
  acronym: string;
  objectState: string;
  status: string;
  belongsTo: string;
  businessOwnerOrg: string;
  businessOwnerOrgComp: string;
  systemMaintainerOrg: string;
  systemMaintainerOrgComp: string;
  qaReviewerAssignmentId: string;
  qaReviewerFirstName: string;
  qaReviewerLastName: string;
  qaReviewerUserName: string;
  daReviewerAssignmentId: string;
  daReviewerFirstName: string;
  daReviewerLastName: string;
  daReviewerUserName: string;
  censusStatus?: string;
  percentComplete?: string;
}

export interface SystemsListResponse {
  pageName: string;
  count: number;
  SystemsList: SystemsListItem[];
}

export type SpResult = [ { queryStatus: number, result: string }[], number ];

// Constants.
export const DB_PROC_NAME = 'SP_Get_SystemCensusList';

/**
 * Endpoint handler.
 *
 * Note:
 *   OG supports querystring parameters:
 *     version: string
 *     includedInSurvey: bool
 *
 *  Neither of these parameters is used in the query, confirmed with Don. --hrivera
 */
const handler = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const { app, db } = endpointSetup(req, DatabaseConfigs.sparxSupport);

  try {
    const spParams: StoredProcedureParam[] = [{
      name: 'result',
      type: 'nvarchar',
      param: 'max',
    }];

    const spData: StoredProcedureData[] = [{
      name: 'OutputJson',
      value: 'result',
      isOutput: true,
    }];

    const spQuery: StoredProcedureQuery[] = [{
      resultKey: 'result',
      paramName: 'result',
    }];

    const [error, result] = await tryAsync<SpResult>(
      db.queryStoredProceduresTyped<SpResult>(DB_PROC_NAME, spParams, spData, spQuery),
    );

    if (error || isError(result)) {
      app.logger.error({ error: error || result });
      sendError(res, 500, { message: [Messages.db_query_stored_procedure_error] });
      return;
    }

    const text = result?.[0]?.[0]?.result;
    if (!isString(text)) {
      sendError(res, 500, { message: [Messages.db_query_result_missing] });
      return;
    }

    let json: { ResultSet: SystemsListItem[] };
    try {
      json = JSON.parse(text);
    } catch (parseError) {
      app.logger.error({ error: parseError });
      sendError(res, 500, { message: [Messages.sp_json_parse_error] });
      return;
    }

    const response: SystemsListResponse = {
      pageName: 'SystemsList',
      count: json.ResultSet.length,
      SystemsList: json.ResultSet,
    };

    res.status(200).send(response);
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'systems',
        action: 'find',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: [Messages.internal_server_error] });
  }
});

export default handler;
