// Define local types as required
import { EndpointQueryParams } from '../../../../utils/express/responses';

// noinspection SpellCheckingInspection
const DbSystemMaintainer = {
  'System ID': '',
  'Sparx System ID': '',
  'Sparx System GUID': '',
  'System Name': '',
  'System Customization': null as string | null,
  'Front End Access Type': null as string | null,
  'CMS System Access': null as string | null,
  'IP Enabled Asset Count': null as string | null,
  'Percent IPV6': null as string | null,
  'Long Term IPV6 Plan': null as string | null,
  'Start Date': null as string | null,
  'Development Work Still Underway': null as string | null,
  'Agile Methodology Use': null as string | null,
  'Deployment Frequency': null as string | null,
  // @strange Column misspelled in database.
  'Deplolyment AdHoc Frequency': null as string | null,
  'Last Major Tech Refresh Date': null as string | null,
  'No Major Refresh Flag': null as string | null,
  'Next Major Tech Refresh Date': null as string | null,
  'No Planned Major Refresh Flag': null as string | null,
  'Retire or Replace': null as string | null,
  'Retire or Replace Date': null as string | null,
  'Planned Retirement Quarter': null as string | null,
  'Business Artifacts on Demand': null as string | null,
  'Business Artifact Location': null as string | null,
  'Requirements on Demand': null as string | null,
  'Requirements Location': null as string | null,
  'Design on Demand': null as string | null,
  'System Design Location': null as string | null,
  'Source Code on Demand': null as string | null,
  'Souce Code Location': null as string | null,
  'Test Plan on Demand': null as string | null,
  'Test Plan Location': null as string | null,
  'Test Script on Demand': null as string | null,
  'Test Script Location': null as string | null,
  'Test Reports on Demand': null as string | null,
  'Test Report Location': null as string | null,
  'Ops and Maintenance Plans on Demand': null as string | null,
  'Ops and Maintenance Plan Location': null as string | null,
  'No Persistent Records Flag': null as string | null,
  // This gets populated by the second query.
  // 'Records Management Bucket': string | null;
  'Records Management Record Type Identification': null as string | null,
  'Metadata Glossary': null as string | null,
  'System Data Authoritative Source': null as string | null,
  'System Data Location': null as string | null,
  'System Data Location Notes': null as string | null,
  'Centralized Data Catalog': null as string | null,
  'EDL Plan': null as string | null,
  'Identity Management Solution': null as string | null,
  'Identity Management Solution Other': null as string | null,
  'Locally Stored User Info': null as string | null,
  'MFA Method': null as string | null,
  'MFA Other': null as string | null,
  'Network Traffic Encryption Management': null as string | null,
  'Data At Rest Encryption Management': null as string | null,
  'Records Under Legal Hold': null as string | null,
  'Legal Hold Case Name': null as string | null,
  'Records Management Approved Schedule': null as string | null,
  'Records Management Disposal Plan': null as string | null,
  'Records Management Disposal Location': null as string | null,
  'Records Management Format': null as string | null,
  'CMS Owned': null as string | null,
  'Hard Coded IP Address': null as string | null,
  Version: null as string | null,
} as const;

type DbSystemMaintainer = typeof DbSystemMaintainer;

const DbSystemRecordsManagementBucket = {
  'Sparx System GUID': '',
  'Records Management Bucket': '',
} as const;

type DbSystemRecordsManagementBucket = typeof DbSystemRecordsManagementBucket;

interface SystemMaintainerResponse {
  id: string;
  version?: string | null;
  pageName: 'SystemMaintainerBasicInfo';
  name?: string | null;
  systemCustomization?: string | null;
  frontendAccessType?: string | null;
  netAccessibility?: string | null;
  ipEnabledAssetCount?: number | null;
  ip6EnabledAssetPercent?: string | null;
  ip6TransitionPlan?: string | null;
  systemProductionDate?: string | null;
  devWorkDescription?: string | null;
  agileUsed?: boolean | null;
  deploymentFrequency?: string | null;
  adHocAgileDeploymentFrequency?: string | null;
  majorRefreshDate?: string | null;
  noMajorRefresh?: boolean | null;
  nextMajorRefreshDate?: string | null;
  noPlannedMajorRefresh?: boolean | null;
  plansToRetireReplace?: string | null;
  yearToRetireReplace?: string | null;
  quarterToRetireReplace?: string | null;
  businessArtifactsOnDemand?: boolean | null;
  businessArtifactsLocation?: string | null;
  systemRequirementsOnDemand?: boolean | null;
  systemRequirementsLocation?: string | null;
  systemDesignOnDemand?: boolean | null;
  systemDesignLocation?: string | null;
  sourceCodeOnDemand?: boolean | null;
  // noinspection SpellCheckingInspection
  sourceCodeLoction?: string | null;
  testPlanOnDemand?: boolean | null;
  testPlanLocation?: string | null;
  testScriptsOnDemand?: boolean | null;
  testScriptsLocation?: string | null;
  testReportsOnDemand?: boolean | null;
  testReportsLocation?: string | null;
  omDocumentationOnDemand?: boolean | null;
  omDocumentationLocation?: string | null;
  noPersistentRecordsFlag?: boolean | null;
  recordsManagementBucket?: string[] | null;
  recordsManagementRecordTypeId?: boolean | null;
  hasMetadataGlossary?: boolean | null;
  authoritativeDatasource?: string | null;
  systemDataLocation?: string[] | null;
  systemDataLocationNotes?: string | null;
  storeInCentralDataCatalog?: boolean | null;
  haveEnterpriseDataLakePlan?: string | null;
  identityManagementSolution?: string[] | null;
  identityManagementSolutionOther?: string | null;
  locallyStoredUserInformation?: boolean | null;
  multifactorAuthenticationMethod?: string[] | null;
  multifactorAuthenticationMethodOther?: string | null;
  networkTrafficEncryptionKeyManagement?: string | null;
  dataAtRestEncryptionKeyManagement?: string | null;
  recordsUnderLegalHold?: boolean | null;
  legalHoldCaseName?: string | null;
  isRecordManagementScheduleApproved?: boolean | null;
  recordsManagementDisposalPlan?: boolean | null;
  recordsManagementDisposalLocation?: string | null;
  recordManagementFormat?: string[] | null;
  recordManagementFormatOther?: string | null;
  // noinspection SpellCheckingInspection
  anotherCMSsystem?: boolean | null;
  hardCodedIpAddress?: boolean | null;
}

interface SystemMaintainerQueryParams extends EndpointQueryParams {
  id: string;
}

enum SystemMaintainerMessages {
  invalid_parameter_system_id = 'The systemId parameter is required and must be a string',
}

export {
  DbSystemMaintainer,
  DbSystemRecordsManagementBucket,
  SystemMaintainerMessages,
  SystemMaintainerQueryParams,
  SystemMaintainerResponse,
};
