// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/systemMaintainer',
  method: 'get',
  tags: ['System Census'],
  description: 'Retrieve system maintainer information for a given system ID.',
  parameters: [
    {
      name: 'id',
      in: 'query',
      description: 'The unique identifier (GUID) of the system.',
      required: true,
      schema: {
        type: 'string',
        example: '{11111111-**************-************}',
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully retrieved system maintainer information.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              id: {
                type: 'string',
                description: 'The unique identifier (GUID) of the system.',
                example: '{11111111-**************-************}',
              },
              version: {
                type: 'string',
                description: 'Version of the system.',
                nullable: true,
                example: '1.0',
              },
              pageName: {
                type: 'string',
                description: 'The name of the page, always "SystemMaintainerBasicInfo".',
                example: 'SystemMaintainerBasicInfo',
              },
              name: {
                type: 'string',
                description: 'The name of the system.',
                nullable: true,
                example: 'Example System',
              },
              systemCustomization: {
                type: 'string',
                description: 'Description of system customization.',
                nullable: true,
                example: 'Highly customized for specific business needs.',
              },
              frontendAccessType: {
                type: 'string',
                description: 'Type of frontend access.',
                nullable: true,
                example: 'Web-based',
              },
              netAccessibility: {
                type: 'string',
                description: 'Network accessibility of the system.',
                nullable: true,
                example: 'Internal Network Only',
              },
              ipEnabledAssetCount: {
                type: 'integer',
                format: 'int64',
                description: 'Count of IP enabled assets.',
                nullable: true,
                example: 150,
              },
              ip6EnabledAssetPercent: {
                type: 'string',
                description: 'Percentage of IPV6 enabled assets.',
                nullable: true,
                example: '75%',
              },
              ip6TransitionPlan: {
                type: 'string',
                description: 'Long-term IPV6 transition plan.',
                nullable: true,
                example: 'Full transition by 2025',
              },
              systemProductionDate: {
                type: 'string',
                description: 'Date when the system went into production.',
                nullable: true,
                example: '2020-01-01',
              },
              devWorkDescription: {
                type: 'string',
                description: 'Description of ongoing development work.',
                nullable: true,
                example: 'Ongoing feature enhancements and bug fixes.',
              },
              agileUsed: {
                type: 'boolean',
                description: 'Indicates if Agile methodology is used for development.',
                nullable: true,
                example: true,
              },
              deploymentFrequency: {
                type: 'string',
                description: 'Frequency of system deployments.',
                nullable: true,
                example: 'Monthly',
              },
              adHocAgileDeploymentFrequency: {
                type: 'string',
                description: 'Ad-hoc agile deployment frequency.',
                nullable: true,
                example: 'As needed',
              },
              majorRefreshDate: {
                type: 'string',
                description: 'Date of the last major technology refresh.',
                nullable: true,
                example: '2023-06-15',
              },
              noMajorRefresh: {
                type: 'boolean',
                description: 'Indicates if there has been no major refresh.',
                nullable: true,
                example: false,
              },
              nextMajorRefreshDate: {
                type: 'string',
                description: 'Date of the next planned major technology refresh.',
                nullable: true,
                example: '2025-01-01',
              },
              noPlannedMajorRefresh: {
                type: 'boolean',
                description: 'Indicates if there is no planned major refresh.',
                nullable: true,
                example: false,
              },
              plansToRetireReplace: {
                type: 'string',
                description: 'Plans to retire or replace the system.',
                nullable: true,
                example: 'No plans currently',
              },
              yearToRetireReplace: {
                type: 'string',
                description: 'Year planned for retirement or replacement.',
                nullable: true,
                example: '2028',
              },
              quarterToRetireReplace: {
                type: 'string',
                description: 'Quarter planned for retirement or replacement.',
                nullable: true,
                example: 'Q4',
              },
              businessArtifactsOnDemand: {
                type: 'boolean',
                description: 'Availability of business artifacts on demand.',
                nullable: true,
                example: true,
              },
              businessArtifactsLocation: {
                type: 'string',
                description: 'Location of business artifacts.',
                nullable: true,
                example: 'SharePoint',
              },
              systemRequirementsOnDemand: {
                type: 'boolean',
                description: 'Availability of system requirements on demand.',
                nullable: true,
                example: true,
              },
              systemRequirementsLocation: {
                type: 'string',
                description: 'Location of system requirements.',
                nullable: true,
                example: 'Jira',
              },
              systemDesignOnDemand: {
                type: 'boolean',
                description: 'Availability of system design on demand.',
                nullable: true,
                example: true,
              },
              systemDesignLocation: {
                type: 'string',
                description: 'Location of system design documents.',
                nullable: true,
                example: 'Confluence',
              },
              sourceCodeOnDemand: {
                type: 'boolean',
                description: 'Availability of source code on demand.',
                nullable: true,
                example: true,
              },
              sourceCodeLoction: {
                type: 'string',
                description: 'Location of source code.',
                nullable: true,
                example: 'GitLab',
              },
              testPlanOnDemand: {
                type: 'boolean',
                description: 'Availability of test plans on demand.',
                nullable: true,
                example: true,
              },
              testPlanLocation: {
                type: 'string',
                description: 'Location of test plans.',
                nullable: true,
                example: 'TestRail',
              },
              testScriptsOnDemand: {
                type: 'boolean',
                description: 'Availability of test scripts on demand.',
                nullable: true,
                example: true,
              },
              testScriptsLocation: {
                type: 'string',
                description: 'Location of test scripts.',
                nullable: true,
                example: 'GitLab',
              },
              testReportsOnDemand: {
                type: 'boolean',
                description: 'Availability of test reports on demand.',
                nullable: true,
                example: true,
              },
              testReportsLocation: {
                type: 'string',
                description: 'Location of test reports.',
                nullable: true,
                example: 'Confluence',
              },
              omDocumentationOnDemand: {
                type: 'boolean',
                description: 'Availability of O&M documentation on demand.',
                nullable: true,
                example: true,
              },
              omDocumentationLocation: {
                type: 'string',
                description: 'Location of O&M documentation.',
                nullable: true,
                example: 'ServiceNow CMDB',
              },
              noPersistentRecordsFlag: {
                type: 'boolean',
                description: 'Indicates if there are no persistent records.',
                nullable: true,
                example: false,
              },
              recordsManagementBucket: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'List of records management buckets.',
                nullable: true,
                example: ['Bucket A', 'Bucket B'],
              },
              recordsManagementRecordTypeId: {
                type: 'boolean',
                description: 'Indicates if records management record type is identified.',
                nullable: true,
                example: true,
              },
              hasMetadataGlossary: {
                type: 'boolean',
                description: 'Indicates if the system has a metadata glossary.',
                nullable: true,
                example: true,
              },
              authoritativeDatasource: {
                type: 'string',
                description: 'Authoritative data source for the system.',
                nullable: true,
                example: 'Enterprise Data Lake',
              },
              systemDataLocation: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Locations where system data is stored.',
                nullable: true,
                example: ['AWS S3', 'Azure Blob'],
              },
              systemDataLocationNotes: {
                type: 'string',
                description: 'Notes regarding system data locations.',
                nullable: true,
                example: 'Data replicated across multiple regions.',
              },
              storeInCentralDataCatalog: {
                type: 'boolean',
                description: 'Indicates if data is stored in a central data catalog.',
                nullable: true,
                example: true,
              },
              haveEnterpriseDataLakePlan: {
                type: 'string',
                description: 'Status of Enterprise Data Lake plan.',
                nullable: true,
                example: 'In Progress',
              },
              identityManagementSolution: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Identity management solutions used.',
                nullable: true,
                example: ['LDAP', 'Okta'],
              },
              identityManagementSolutionOther: {
                type: 'string',
                description: 'Other identity management solutions.',
                nullable: true,
                example: 'Custom SAML integration',
              },
              locallyStoredUserInformation: {
                type: 'boolean',
                description: 'Indicates if user information is stored locally.',
                nullable: true,
                example: false,
              },
              multifactorAuthenticationMethod: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Methods of multifactor authentication used.',
                nullable: true,
                example: ['TOTP', 'Smart Card'],
              },
              multifactorAuthenticationMethodOther: {
                type: 'string',
                description: 'Other multifactor authentication methods.',
                nullable: true,
                example: 'Biometric scan',
              },
              networkTrafficEncryptionKeyManagement: {
                type: 'string',
                description: 'Network traffic encryption key management details.',
                nullable: true,
                example: 'AWS KMS',
              },
              dataAtRestEncryptionKeyManagement: {
                type: 'string',
                description: 'Data at rest encryption key management details.',
                nullable: true,
                example: 'Azure Key Vault',
              },
              recordsUnderLegalHold: {
                type: 'boolean',
                description: 'Indicates if records are under legal hold.',
                nullable: true,
                example: false,
              },
              legalHoldCaseName: {
                type: 'string',
                description: 'Name of the legal hold case.',
                nullable: true,
                example: 'Case_XYZ',
              },
              isRecordManagementScheduleApproved: {
                type: 'boolean',
                description: 'Indicates if the record management schedule is approved.',
                nullable: true,
                example: true,
              },
              recordsManagementDisposalPlan: {
                type: 'boolean',
                description: 'Indicates if a records management disposal plan exists.',
                nullable: true,
                example: true,
              },
              recordsManagementDisposalLocation: {
                type: 'string',
                description: 'Location for records management disposal.',
                nullable: true,
                example: 'Offsite archive',
              },
              recordsManagementFormat: {
                type: 'array',
                items: {
                  type: 'string',
                },
                description: 'Formats of records management.',
                nullable: true,
                example: ['PDF', 'XML'],
              },
              recordManagementFormatOther: {
                type: 'string',
                description: 'Other records management formats.',
                nullable: true,
                example: 'Proprietary format',
              },
              anotherCMSsystem: {
                type: 'boolean',
                description: 'Indicates if it is another CMS system.',
                nullable: true,
                example: true,
              },
              hardCodedIpAddress: {
                type: 'boolean',
                description: 'Indicates if the system uses hardcoded IP addresses.',
                nullable: true,
                example: false,
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    404: {
      description: 'Not found',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/NotFound',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
