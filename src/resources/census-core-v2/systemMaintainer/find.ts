// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import { isString } from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { splitOrNull, strToBoolOrNull } from '../../../utils/general';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { endpointSetup, logAppError, sendError } from '../../../utils/express/responses';
import QueryParams from '../../../utils/express/queryParams';
import MssqlData from '../../../data-sources/mssql';
import { SparxSupportViews } from '../../../utils/constants/views';
import { formatDateOrNull } from '../../../utils/time';
import Messages from '../../../utils/constants/messages';
import {
  DbSystemMaintainer,
  DbSystemRecordsManagementBucket,
  SystemMaintainerMessages,
  SystemMaintainerQueryParams,
  SystemMaintainerResponse,
} from './types/find';
import { UnitTestForcedExceptionError } from '../../../utils/express/errors';

const checkForUnexpectedKeys = (
  obj: Record<string, unknown>,
  expected: Record<string, unknown>,
  name: string,
): void => {
  const expectedKeys = Object.keys(expected);

  Object.keys(obj).forEach((key) => {
    if (!expectedKeys.includes(key)) {
      throw new Error(`Key "${key}" is not a valid key for ${name}`);
    }
  });
};

const mapDbToResponse = (
  dbRecord: DbSystemMaintainer,
  dbRecordsManagementBucket: DbSystemRecordsManagementBucket,
): SystemMaintainerResponse => {
  checkForUnexpectedKeys(
    dbRecord,
    DbSystemMaintainer,
    'DbSystemMaintainer',
  );
  checkForUnexpectedKeys(
    dbRecordsManagementBucket,
    DbSystemRecordsManagementBucket,
    'DbSystemRecordsManagementBucket',
  );

  // noinspection SpellCheckingInspection
  const response: SystemMaintainerResponse = {
    id: dbRecord['Sparx System GUID'],
    version: dbRecord?.Version?.toString() ?? null,
    pageName: 'SystemMaintainerBasicInfo',
    name: dbRecord['System Name'],
    systemCustomization: dbRecord['System Customization'],
    frontendAccessType: dbRecord['Front End Access Type'],
    netAccessibility: dbRecord['CMS System Access'],
    ipEnabledAssetCount: dbRecord?.['IP Enabled Asset Count']
      ? parseInt(dbRecord['IP Enabled Asset Count'], 10)
      : null,
    ip6EnabledAssetPercent: dbRecord['Percent IPV6'],
    ip6TransitionPlan: dbRecord['Long Term IPV6 Plan'],
    systemProductionDate: dbRecord['Start Date']
      ? formatDateOrNull(dbRecord['Start Date'], 'YYYY-MM-DD') ?? 'invalid date'
      : null,
    devWorkDescription: dbRecord['Development Work Still Underway'],
    agileUsed: strToBoolOrNull(dbRecord['Agile Methodology Use']),
    deploymentFrequency: dbRecord['Deployment Frequency'],
    adHocAgileDeploymentFrequency: dbRecord['Deplolyment AdHoc Frequency'],
    majorRefreshDate: dbRecord['Last Major Tech Refresh Date']
      ? formatDateOrNull(dbRecord['Last Major Tech Refresh Date'], 'YYYY-MM-DD') ?? 'invalid date'
      : null,
    noMajorRefresh: strToBoolOrNull(dbRecord['No Major Refresh Flag']),
    nextMajorRefreshDate: dbRecord['Next Major Tech Refresh Date']
      ? formatDateOrNull(dbRecord['Next Major Tech Refresh Date'], 'YYYY-MM-DD') ?? 'invalid date'
      : null,
    noPlannedMajorRefresh: strToBoolOrNull(dbRecord['No Planned Major Refresh Flag']),
    plansToRetireReplace: dbRecord['Retire or Replace'],
    yearToRetireReplace: dbRecord['Retire or Replace Date'],
    quarterToRetireReplace: dbRecord['Planned Retirement Quarter'],
    businessArtifactsOnDemand: strToBoolOrNull(dbRecord['Business Artifacts on Demand']),
    businessArtifactsLocation: dbRecord['Business Artifact Location'],
    systemRequirementsOnDemand: strToBoolOrNull(dbRecord['Requirements on Demand']),
    systemRequirementsLocation: dbRecord['Requirements Location'],
    systemDesignOnDemand: strToBoolOrNull(dbRecord['Design on Demand']),
    systemDesignLocation: dbRecord['System Design Location'],
    sourceCodeOnDemand: strToBoolOrNull(dbRecord['Source Code on Demand']),
    sourceCodeLoction: dbRecord['Souce Code Location'],
    testPlanOnDemand: strToBoolOrNull(dbRecord['Test Plan on Demand']),
    testPlanLocation: dbRecord['Test Plan Location'],
    testScriptsOnDemand: strToBoolOrNull(dbRecord['Test Script on Demand']),
    testScriptsLocation: dbRecord['Test Script Location'],
    testReportsOnDemand: strToBoolOrNull(dbRecord['Test Reports on Demand']),
    testReportsLocation: dbRecord['Test Report Location'],
    omDocumentationOnDemand: strToBoolOrNull(dbRecord['Ops and Maintenance Plans on Demand']),
    omDocumentationLocation: dbRecord['Ops and Maintenance Plan Location'],
    noPersistentRecordsFlag: strToBoolOrNull(dbRecord['No Persistent Records Flag']),
    recordsManagementBucket: splitOrNull(dbRecordsManagementBucket['Records Management Bucket'], '|'),
    recordsManagementRecordTypeId: strToBoolOrNull(dbRecord['Records Management Record Type Identification']),
    hasMetadataGlossary: strToBoolOrNull(dbRecord['Metadata Glossary']),
    authoritativeDatasource: dbRecord['System Data Authoritative Source'],
    systemDataLocation: splitOrNull(dbRecord['System Data Location'], '|'),
    systemDataLocationNotes: dbRecord['System Data Location Notes'],
    storeInCentralDataCatalog: strToBoolOrNull(dbRecord['Centralized Data Catalog']),
    haveEnterpriseDataLakePlan: dbRecord['EDL Plan'],
    identityManagementSolution: splitOrNull(dbRecord['Identity Management Solution'], '|'),
    identityManagementSolutionOther: dbRecord['Identity Management Solution Other'],
    locallyStoredUserInformation: strToBoolOrNull(dbRecord['Locally Stored User Info']),
    multifactorAuthenticationMethod: splitOrNull(dbRecord['MFA Method'] ? dbRecord['MFA Method'].replace(/\n/g, ' ') : null, '|'),
    multifactorAuthenticationMethodOther: dbRecord['MFA Other'],
    networkTrafficEncryptionKeyManagement: dbRecord['Network Traffic Encryption Management'],
    dataAtRestEncryptionKeyManagement: dbRecord['Data At Rest Encryption Management'],
    recordsUnderLegalHold: strToBoolOrNull(dbRecord['Records Under Legal Hold']),
    legalHoldCaseName: dbRecord['Legal Hold Case Name'],
    isRecordManagementScheduleApproved: strToBoolOrNull(dbRecord['Records Management Approved Schedule']),
    recordsManagementDisposalPlan: strToBoolOrNull(dbRecord['Records Management Disposal Plan']),
    recordsManagementDisposalLocation: dbRecord['Records Management Disposal Location'],
    recordManagementFormat: splitOrNull(
      dbRecord['Records Management Format']
        ? dbRecord['Records Management Format'].replace(/\n/g, ' ')
        : null,
      '|',
    ),
    anotherCMSsystem: strToBoolOrNull(dbRecord['CMS Owned']),
    hardCodedIpAddress: strToBoolOrNull(dbRecord['Hard Coded IP Address']),
  };

  // These exist in the flow, but not in the original response.
  const exclude = [
    'recordManagementFormat',
    'recordsManagementDisposalLocation',
    'recordsManagementDisposalPlan',
    'hardCodedIpAddress',
  ];

  Object.keys(response).forEach((key) => {
    if (exclude.includes(key)) {
      delete response[key as keyof typeof response];
    }
  });

  return response;
};

const getMaintainer = async (
  db: MssqlData,
  id: string,
): Promise<DbSystemMaintainer[]> => {
  const columns: string[] = Object.keys(DbSystemMaintainer).map(
    (key) => `[${key}]`,
  );

  const result = await db.queryViewTyped<[DbSystemMaintainer[], number]>(
    SparxSupportViews.Sparx_System,
    columns,
    {
      where: {
        operation: { column: 'Sparx System GUID', operator: '=', value: id },
      },
    },
  );

  return result?.[0];
};

const getRecordsManagementBucket = async (
  db: MssqlData,
  id: string,
): Promise<DbSystemRecordsManagementBucket[]> => {
  const columns: string[] = Object.keys(DbSystemRecordsManagementBucket).map(
    (key) => `[${key}]`,
  );

  const result = await db.queryViewTyped<[DbSystemRecordsManagementBucket[], number]>(
    SparxSupportViews.Sparx_System_RecordsManagementBucket_Census,
    columns,
    {
      where: {
        operation: { column: 'Sparx System GUID', operator: '=', value: id },
      },
    },
  );

  return result?.[0];
};

/**
 * Note:
 *   Likely unused querystring parameters:
 *
 *     `<param name="anotherCMSsystem;"></param>`
 *     `<param name="version;"></param>`
 *
 *   This is in the opening stanza of the XML:
 *
 *     `<MAPDELETE FIELD="/anotherCMSsystem;3.1;0"></MAPDELETE>`
 *     `<MAPDELETE FIELD="/version;1;0"></MAPDELETE>`
 */
const handler = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.sparxSupport);

    try {
      const {
        id,
      } = QueryParams.fromRequest<SystemMaintainerQueryParams>(req).getAll();

      if (!isString(id)) {
        sendError(res, 400, { message: [SystemMaintainerMessages.invalid_parameter_system_id] });
        return;
      }

      let recordMaintainer: DbSystemMaintainer[];
      let recordRecordsManagementBucket: DbSystemRecordsManagementBucket[];

      try {
        recordMaintainer = await getMaintainer(db, id);
        recordRecordsManagementBucket = await getRecordsManagementBucket(db, id);
      } catch (e) {
        // Unit testing.
        if (e instanceof UnitTestForcedExceptionError) {
          // noinspection ExceptionCaughtLocallyJS
          throw e;
        }

        app.logger.error({ error: e });
        sendError(res, 500, { message: [Messages.db_query_view_error] }, app, e);
        return;
      }

      if (!recordMaintainer || !recordRecordsManagementBucket) {
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }

      if (recordMaintainer.length === 0 || recordRecordsManagementBucket.length === 0) {
        sendError(res, 400, { message: ['System maintainer information not found'] });
        return;
      }

      if (recordMaintainer.length !== recordRecordsManagementBucket.length) {
        sendError(res, 400, { message: ['Maintainer and records management bucket record counts do not match'] });
        return;
      }

      const response = mapDbToResponse(recordMaintainer[0], recordRecordsManagementBucket[0]);

      res.status(200).send(response);
    } catch (error) {
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'systemMaintainer',
          action: 'find',
          error: error as Error,
        });
      }

      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export {
  checkForUnexpectedKeys,
};

export default handler;
