// Generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
  isUndefined,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import { getDb, SYSTEM_SURVEY_REVIEWERS } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';
import {
  Where,
  ReviewerFindResponse,
  Reviewer,
  DbReviewer,
} from '../../../types';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/System Census Core API/2.0.0/admin/reviewer'
#swagger.method = 'get',
#swagger.tags = ['admin'],
#swagger.description = 'Reviewers are CMS employees responsible for verifying the accuracy and completeness of the System Census Survey data each year. There are two types of reviewers, QA and DA. The list of potential reviewers is returned by this endpoint and can be filtered by \'type\'',
#swagger.parameters['type'] = {
  in: 'query',
  description: 'The type of reviewers to be returned. Either \'QA\', \'DA\', or null/empty. Null/empty will return all reviewers.',
  required: false,
  schema: {
    type: 'string',
    '@enum': ['QA', 'DA'],
    nullable: true,
  },
  example: 'QA',
}

#swagger.produces = ['application/json'],
#swagger.responses[200] = {
  description: 'Reviewers list retrieved successfully',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          count: {
            type: 'integer',
            description: 'The number of reviewers returned.',
            example: 2,
          },
          Reviewers: {
            type: 'array',
            description: 'List of reviewers.',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'Unique identifier for the reviewer.',
                  example: '11111111-2222-3333-4444-555555555555',
                },
                userName: {
                  type: 'string',
                  description: 'The username of the reviewer.',
                  example: 'T3ST',
                },
                fullName: {
                  type: 'string',
                  description: 'The full name of the reviewer.',
                  example: 'Test User',
                },
                type: {
                  type: 'string',
                  description: 'The type of reviewer (e.g., QA, DA).',
                  example: 'QA',
                }
              }
            }
          }
        }
      }
    }
  }
}

#swagger.responses[400]: {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest',
      }
    }
  }
}

#swagger.responses[401]: {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized',
      }
    }
  }
}

#swagger.responses[500]: {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse',
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

export const convertDbReviewerToReviewer = (reviewer: DbReviewer): Reviewer => ({
  id: reviewer.SYSTEM_SURVEY_REVIEWER_ID,
  userName: reviewer.REVIEWER_USERNAME,
  fullName: reviewer.REVIEWER_FULLNAME,
  type: reviewer.REVIEWER_TYPE,
});

const reviewerTypes = ['QA', 'DA'];

const adminReviewerFind = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const initQueryType = get(req, 'query.type', '');

  let type;
  if (initQueryType === '' || !isString(initQueryType)) {
    type = undefined;
  } else {
    type = initQueryType.toUpperCase();
  }

  if (isString(type) && !reviewerTypes.includes(type)) {
    app.logger.error({ error: new Error('\'type\' must be QA or DA') });
    return res.status(400).send({ error: '\'type\' must be QA or DA' });
  }

  let whereClause: Where | undefined;
  if (!isEmpty(type) && !isUndefined(type)) {
    whereClause = {
      where: {
        operation: {
          column: 'REVIEWER_TYPE',
          operator: '=',
          value: type,
        },
      },
    };
  }

  const [queryError, queryResult] = await tryAsync(db.queryView(
    SYSTEM_SURVEY_REVIEWERS,
    [
      'SYSTEM_SURVEY_REVIEWER_ID',
      'REVIEWER_USERNAME',
      'REVIEWER_FULLNAME',
      'REVIEWER_TYPE',
    ],
    isEmpty(whereClause) || isUndefined(whereClause) ? undefined : whereClause,
  ));

  if (queryError || isError(queryResult)) {
    app.logger.error({ error: queryError || queryResult });
    return res.status(500).send({ error: 'Error querying database' });
  }

  if (!queryResult || !Array.isArray(queryResult)) {
    app.logger.error({ error: 'Unable to get result from query' });
    return res.status(500).send({ error: 'Unable to get result from query' });
  }

  const reviewersRaw: DbReviewer[] = queryResult[0] as DbReviewer[];
  const reviewers: Reviewer[] = reviewersRaw.map(convertDbReviewerToReviewer);

  return res.status(200).send({
    count: reviewers.length,
    Reviewers: reviewers,
  } as ReviewerFindResponse);
};

export default adminReviewerFind;
