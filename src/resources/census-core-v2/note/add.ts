// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import {
  get, isArray, isEmpty, isError, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { tryAsync, unknownAsError } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { queryPersons } from '../../../subsystems/ldap';
import { CMSApp, User } from '../../../types';
import { ParameterizedFunctions } from '../../../utils/db-helpers/parameterize';
import {
  endpointSetup, logAppError, SendError, sendSuccess,
} from '../../../utils/express/responses';
import { UnitTestForcedExceptionError } from '../../../utils/express/errors';
import { IWmInsertResult } from '../../../data-sources/mssql';
import { CedarSupportTables } from '../../../utils/constants/tables';

// Environment variables for global configurations,
// Note: email notifications are not enabled in OG production.
const CENSUS_NOTES_NOTIFICATION_ENABLED = process.env.CENSUS_NOTES_NOTIFICATION_ENABLED === 'true';
const CENSUS_NOTES_NOTIFICATION_INCLUDE_HISTORY = process.env.CENSUS_NOTES_NOTIFICATION_INCLUDE_HISTORY === 'true';
const CENSUS_NOTIFICATION_DEFAULT_FROM = process.env.CENSUS_NOTIFICATION_DEFAULT_FROM ?? '<EMAIL>';

// Define local types as required
export interface NoteRequest {
  noteId?: string; // Should not be present on add
  systemId: string;
  pageName: string;
  userId: string;
  userFirst?: string;
  userLast?: string;
  userRole?: string;
  note: string;
  createdOn?: string; // Populated by DB 'getutcdate()'
}

export interface NoteAddRequestPayload {
  Notes: NoteRequest[];
  EmailFlags?: {
    notifyReviewer?: boolean;
    notifyRespondent?: boolean;
    includeHistory?: boolean;
  };
}

export interface DbNoteForInsert {
  'System_ID': string;
  'PAGE_NAME': string;
  'NOTES_USER': string;
  'NOTES_USER_FIRST_NAME'?: string;
  'NOTES_USER_LAST_NAME'?: string;
  'NOTES': string;
  'NOTES_USER_ROLE'?: string;
}

export enum NoteAddMessages {
  error_ldap_lookup_failed = 'LDAP lookup failed',
  error_note_content_missing = 'Missing or invalid `note` content for a note',
  error_note_id_present = 'Cannot add Note when `noteId` is already present',
  error_notes_missing_in_body = 'Please provide required input \'Notes\'',
  error_page_name_missing = 'Missing or invalid `pageName` for a note',
  error_system_id_missing = 'Missing or invalid `systemId` for a note',
  error_user_id_missing = 'Missing or invalid `userId` for a note',
}

// Helper function to generate page URL
// noinspection OverlyComplexFunctionJS
const generatePageUrl = (refstr: string, pageName: string): string => {
  let pageUrlSegment = pageName.toLowerCase();
  switch (pageName) {
    case 'BudgetAndContracts':
      pageUrlSegment = 'budgetandcontracts';
      break;
    case 'BusinessOwnerBasicInformation':
      pageUrlSegment = 'businessownerbasicinfo';
      break;
    case 'ContactsAndRoles':
      pageUrlSegment = 'roles';
      break;
    case 'DataCenterHostingEnvironments':
      pageUrlSegment = 'dataCenterHostingEnvironments';
      break;
    case 'DataExchanges':
      pageUrlSegment = 'dataExchange';
      break;
    case 'SoftwareProducts':
      pageUrlSegment = 'softwareProducts';
      break;
    case 'SystemComponents':
      pageUrlSegment = 'systemcomponents';
      break;
    case 'SystemLifecycleAndPlannedReleases':
      pageUrlSegment = 'systemPlannedReleases';
      break;
    case 'SystemMaintainerBasicInfo':
      pageUrlSegment = 'systemMaintainerBasicInfo';
      break;
    case 'Urls':
      pageUrlSegment = 'urls';
      break;
    default:
      // Fallback or error for unmapped pages
      break;
  }
  const hostname = process.env.CENSUS_HOSTNAME ?? 'mock-census-app.example.com';
  return `https://${hostname}/${pageUrlSegment}/${refstr}`;
};

interface SendNoteNotificationParams {
  systemId: string;
  userId: string;
  pageName: string;
  noteContent: string;
  notifyRespondent: boolean;
  notifyReviewer: boolean;
  includeHistory: boolean;
}

// Placeholder for notification functionality
const sendNoteNotification = (
  app: CMSApp,
  params: SendNoteNotificationParams,
): void => {
  // Simulate fetching system summary and roles
  const systemName = 'Mock System Name';
  const pageUrl = generatePageUrl(params.systemId, params.pageName);

  // noinspection GrazieInspection
  let message = `<!DOCTYPE HTML>
<HTML lang="en">
<BODY>
  <DIV style="font-family:Calibri, sans-serif; font-size:16px;">System Census Notification - a note was added to the following system:</DIV>
  <BR>
  <TABLE>
    <TR>
      <TD><B>System ID:</B></TD>
      <TD>${params.systemId}</TD>
    </TR>
    <TR>
      <TD><B>System Name:</B></TD>
      <TD>${systemName}</TD>
    </TR>
    <TR>
      <TD><B>Page:</B></TD>
      <TD>${params.pageName}</TD>
    </TR>
    <TR>
      <TD><B>Author ID:</B></TD>
      <TD>${params.userId}</TD>
    </TR>
    <TR>
      <TD><B>Note:</B></TD>
      <TD>${params.noteContent}</TD>
    </TR>
    <TR>
      <TD><B>Page URL:</B></TD>
      <TD>${pageUrl}</TD>
    </TR>
  </TABLE>`;

  if (params.includeHistory) {
    // Simulate noteFindList call
    const mockNoteHistory = [
      {
        createdOn: '2024-01-01 10:00:00', userFirst: 'Jane', userLast: 'Doe', note: 'Previous note 1',
      },
      {
        createdOn: '2024-01-02 11:00:00', userFirst: 'John', userLast: 'Smith', note: 'Previous note 2',
      },
    ];
    let historyTable = '\n\nNote History:\n'
      + '<TABLE style="border: 1px solid #ddd; width: 100%;">\n'
      + '\t<TR>\n'
      + '\t\t<TH style="width:15%;">Date</TH>\n'
      + '\t\t<TH style="width:15%;">User</TH>\n'
      + '\t\t<TH style="width:70%;">Note</TH>\n'
      + '\t</TR>';
    mockNoteHistory.forEach((historyNote) => {
      const createdDate = new Date(historyNote.createdOn).toISOString().slice(0, 19).replace('T', ' ');
      historyTable += `\n\t<TR>\n\t\t<TD>${createdDate}</TD>\n\t\t<TD>${historyNote.userFirst} ${historyNote.userLast}</TD>\n\t\t<TD>${historyNote.note}</TD>\n\t</TR>`;
    });
    historyTable += '\n</TABLE>';
    message += historyTable;
  }
  message += '\n</BODY>\n</HTML>';

  const recipientEmails: string[] = [];
  if (params.notifyRespondent) {
    // Simulate LDAP call for respondent email
    recipientEmails.push('<EMAIL>');
  }
  if (params.notifyReviewer) {
    // Simulate role find by object id and role type id for reviewer emails
    recipientEmails.push('<EMAIL>', '<EMAIL>');
  }

  const distinctToEmails = Array.from(new Set(recipientEmails));

  if (distinctToEmails.length > 0) {
    app.logger.info({
      message: `Sending note notification for System ID: ${params.systemId}, Page: ${params.pageName}`,
      to: distinctToEmails.join(', '),
      subject: `Census Notification - ${systemName} - ${params.pageName}`,
      body: message,
      targets: {
        smtp: {
          to: distinctToEmails,
          cc: [CENSUS_NOTIFICATION_DEFAULT_FROM],
          from: CENSUS_NOTIFICATION_DEFAULT_FROM,
          notifySupport: false,
        },
      },
    });
  } else {
    app.logger.info(`No recipients for note notification for System ID: ${params.systemId}, Page: ${params.pageName}`);
  }
};

const sendNoteNotificationDataExchange = (
  app: CMSApp,
  params: SendNoteNotificationParams,
): void => {
  // This is a specific notification for 'DataExchanges' page
  if (params.pageName !== 'DataExchanges') {
    return;
  }

  // Simulate fetching system summary (acronym) and generating URL
  const acronym = 'MockACR';
  const url = generatePageUrl(params.systemId, params.pageName);

  const subject = `System Census – QA Note Added to Data Exchanges page - ${acronym}`;
  const message = `Hello, \n\nThe Data Exchanges page of the ${acronym} system has had a QA Note entered. \n\nPlease review at: \n\n${url}\n\nThank You, \n\nThe System Census Application\n`;

  // Simulate finding data exchange reviewers/recipients
  const recipientEmails = ['<EMAIL>'];

  if (recipientEmails.length > 0) {
    app.logger.info({
      message: `Sending data exchange note notification for System ID: ${params.systemId}, Page: ${params.pageName}`,
      to: recipientEmails.join(', '),
      subject,
      body: message,
      targets: {
        smtp: {
          to: recipientEmails,
          cc: [CENSUS_NOTIFICATION_DEFAULT_FROM],
          from: CENSUS_NOTIFICATION_DEFAULT_FROM,
          notifySupport: false,
        },
      },
    });
  } else {
    app.logger.info(`No recipients for data exchange note notification for System ID: ${params.systemId}, Page: ${params.pageName}`);
  }
};

const handler = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const { app, db } = endpointSetup(req, DatabaseConfigs.cedarSupport);
  const sendError = new SendError(app, res);

  try {
    const payload: NoteAddRequestPayload = req.body;
    const notesToAdd = payload.Notes;

    // Input validation: Notes array must be present and not empty
    if (!notesToAdd || !isArray(notesToAdd) || isEmpty(notesToAdd)) {
      sendError.generic(400, { message: [NoteAddMessages.error_notes_missing_in_body] });
      return;
    }

    const notifyReviewer = payload.EmailFlags?.notifyReviewer ?? false;
    const notifyRespondent = payload.EmailFlags?.notifyRespondent ?? false;
    const includeHistory = payload.EmailFlags?.includeHistory
      ?? CENSUS_NOTES_NOTIFICATION_INCLUDE_HISTORY;

    // @todo-bug Potential bug with the returns inside the map here. --hrivera
    const notesForDb: DbNoteForInsert[] = notesToAdd
      .map((note): DbNoteForInsert | null => {
        if (note.noteId) {
          sendError.generic(400, { message: [NoteAddMessages.error_note_id_present] });
          return null;
        }

        if (!note.systemId || !isString(note.systemId)) {
          sendError.generic(400, { message: [NoteAddMessages.error_system_id_missing] });
          return null;
        }

        if (!note.pageName || !isString(note.pageName)) {
          sendError.generic(400, { message: [NoteAddMessages.error_page_name_missing] });
          return null;
        }

        if (!note.note || !isString(note.note)) {
          sendError.generic(400, { message: [NoteAddMessages.error_note_content_missing] }); // eslint-disable-line max-len
          return null;
        }

        if (!note.userId || !isString(note.userId)) {
          sendError.generic(400, { message: [NoteAddMessages.error_user_id_missing] });
          return null;
        }

        return {
          System_ID: note.systemId,
          PAGE_NAME: note.pageName,
          NOTES_USER: note.userId,
          NOTES_USER_FIRST_NAME: note.userFirst,
          NOTES_USER_LAST_NAME: note.userLast,
          NOTES: note.note,
          NOTES_USER_ROLE: note.userRole,
        };
      }).filter((note): note is DbNoteForInsert => note !== null);

    if (isEmpty(notesForDb)) {
      return;
    }

    // Process notes that need LDAP lookup in parallel
    const notesWithLdapLookup = notesForDb.filter(
      (note) => !note.NOTES_USER_FIRST_NAME || !note.NOTES_USER_LAST_NAME,
    );

    if (notesWithLdapLookup.length > 0) {
      const ldapPromises = notesWithLdapLookup.map(async (note) => {
        const filter = `(uid=${note.NOTES_USER})`;
        const attributes = ['givenName', 'sn'];
        const [error, result] = await tryAsync(queryPersons(app, filter, attributes));

        if (error || isError(result)) {
          throw new Error(`Error querying for ${note.NOTES_USER} in LDAP`);
        }
        if (!isArray(result) || result.length === 0) {
          throw new Error(`No user found for ${note.NOTES_USER}`);
        }
        if (result.length > 1) {
          throw new Error(`Multiple users found for ${note.NOTES_USER}`);
        }

        const user: User = get(result, '[0]');
        Object.assign(note, {
          NOTES_USER_FIRST_NAME: user.givenName,
          NOTES_USER_LAST_NAME: user.sn,
        });
      });

      try {
        await Promise.all(ldapPromises);
      } catch (error) {
        app.logger.error({ error: unknownAsError(error) });
        sendError.generic(400, { message: [NoteAddMessages.error_ldap_lookup_failed] });
        return;
      }
    }

    let result: IWmInsertResult;

    try {
      result = await db.wmInsert(
        CedarSupportTables.SYSTEM_SURVEY_PAGE_NOTES,
        [
          'System_ID',
          'PAGE_NAME',
          'NOTES_USER',
          'NOTES_CREATED_DATE',
          'NOTES',
          'NOTES_USER_ROLE',
          'NOTES_USER_FIRST_NAME',
          'NOTES_USER_LAST_NAME',
        ],
        notesForDb.map((note) => [
          note.System_ID,
          note.PAGE_NAME,
          note.NOTES_USER,
          'GETUTCDATE()',
          note.NOTES,
          note.NOTES_USER_ROLE,
          note.NOTES_USER_FIRST_NAME,
          note.NOTES_USER_LAST_NAME,
        ]),
        'SYSTEM_SURVEY_PAGE_NOTES_ID',
        {
          NOTES_CREATED_DATE: ParameterizedFunctions.GETUTCDATE,
        },
      );
    } catch (error) {
      // Unit testing.
      if (error instanceof UnitTestForcedExceptionError) {
        // noinspection ExceptionCaughtLocallyJS
        throw error;
      }

      sendError.database(500, { message: [Messages.db_insert_error] }, error);
      return;
    }

    if (!result) {
      sendError.generic(500, { message: [Messages.db_query_result_missing] });
      return;
    }

    res.header('INSERTED_IDS', JSON.stringify(result.insertedIds, null));
    res.header('AFFECTED_ROWS', JSON.stringify(result.affectedRows, null));

    // Invoke notification functionality if enabled
    if (CENSUS_NOTES_NOTIFICATION_ENABLED && notesForDb.length > 0) {
      const firstNote = notesForDb[0];
      sendNoteNotification(
        app,
        {
          systemId: firstNote.System_ID,
          userId: firstNote.NOTES_USER,
          pageName: firstNote.PAGE_NAME,
          noteContent: firstNote.NOTES,
          notifyRespondent,
          notifyReviewer,
          includeHistory,
        },
      );

      sendNoteNotificationDataExchange(
        app,
        {
          systemId: firstNote.System_ID,
          userId: firstNote.NOTES_USER,
          pageName: firstNote.PAGE_NAME,
          noteContent: firstNote.NOTES,
          notifyRespondent,
          notifyReviewer,
          includeHistory,
        },
      );
    }

    sendSuccess(res, { result: 'success', message: [`Inserted ${result.affectedRows} note(s)`] });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'note',
        action: 'add-list',
        error: error as Error,
      });
    }
    sendError.generic(500, { message: [Messages.internal_server_error] });
  }
});

export default handler;
