import { GetResourceConfigSub } from '../../../types';
import addHandler from './add';
import deleteHandler from './delete';
import findHandler from './find';

const getResourceConfig: GetResourceConfigSub = () => ({
  path: '/note',
  resourceConfig: [
    {
      name: 'note-add',
      path: '/',
      method: 'post',
      resource: addHandler,
    },
    {
      name: 'note-delete',
      path: '/',
      method: 'delete',
      resource: deleteHandler,
    },
    {
      name: 'note-find',
      path: '/',
      method: 'get',
      resource: findHandler,
    },
  ],
});

export default getResourceConfig;
