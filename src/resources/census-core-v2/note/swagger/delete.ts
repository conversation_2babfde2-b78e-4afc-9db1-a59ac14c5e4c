// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/note/list',
  method: 'delete',
  tags: ['System Census'],
  description: 'Delete a list of notes based on their ID(s).',
  parameters: [
    {
      name: 'id',
      in: 'query',
      description: 'An array of one or more note IDs to delete. Can be provided as repeated parameters (id=123&id=456) or with bracket notation (id[]=123&id[]=456).',
      required: true,
      schema: {
        type: 'array',
        items: {
          type: 'string',
          example: '11111111-2222-3333-4444-555555555555',
        },
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Notes deleted successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Deleted 1 note(s)'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
