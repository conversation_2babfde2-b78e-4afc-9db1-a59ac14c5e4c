// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/note/list',
  method: 'get',
  tags: ['System Census'],
  description: 'Retrieve a list of notes based on a system ID and page name.',
  parameters: [
    {
      name: 'id',
      in: 'query',
      description: 'A system\'s ID',
      required: true,
      schema: {
        type: 'string',
        example: '{11111111-2222-3333-4444-555555555555}',
      },
    },
    {
      name: 'pageName',
      in: 'query',
      description: 'The system census page name',
      required: true,
      schema: {
        type: 'string',
        example: 'Urls',
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successful response',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              count: {
                type: 'integer',
                example: 1,
              },
              Notes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    noteId: {
                      type: 'string',
                      example: '3998',
                    },
                    systemId: {
                      type: 'string',
                      example: '{11111111-2222-3333-4444-555555555555}',
                    },
                    pageName: {
                      type: 'string',
                      example: 'Urls',
                    },
                    userId: {
                      type: 'string',
                      example: 'T3ST',
                    },
                    userFirst: {
                      type: 'string',
                      example: 'Test',
                    },
                    userLast: {
                      type: 'string',
                      example: 'User',
                    },
                    userRole: {
                      type: 'string',
                      example: 'Reviewer',
                    },
                    note: {
                      type: 'string',
                      example: 'This is a test note.',
                    },
                    createdOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-20T12:00:00Z',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request - Missing or invalid parameters',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized - Missing or invalid authentication credentials',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
