// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/note',
  method: 'post',
  tags: ['System Census'],
  description: 'Add a list of notes for a system',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            Notes: {
              type: 'array',
              description: 'List of notes to add to a system',
              items: {
                type: 'object',
                required: ['systemId', 'pageName', 'userId', 'note'],
                properties: {
                  noteId: {
                    type: 'string',
                    description: 'Note ID (should not be provided for add operations)',
                    example: '1234',
                    nullable: true,
                  },
                  systemId: {
                    type: 'string',
                    description: 'The ID of the system the note is for',
                    example: '{11111111-2222-3333-4444-555555555555}',
                  },
                  pageName: {
                    type: 'string',
                    description: 'The name of the page the note is associated with',
                    example: 'Urls',
                  },
                  userId: {
                    type: 'string',
                    description: 'The ID of the user adding the note',
                    example: 'T3ST',
                  },
                  userFirst: {
                    type: 'string',
                    description: 'First name of the user (optional)',
                    example: 'John',
                    nullable: true,
                  },
                  userLast: {
                    type: 'string',
                    description: 'Last name of the user (optional)',
                    example: 'Doe',
                    nullable: true,
                  },
                  userRole: {
                    type: 'string',
                    description: 'Role of the user (optional)',
                    example: 'Reviewer',
                    nullable: true,
                  },
                  note: {
                    type: 'string',
                    description: 'The content of the note',
                    example: 'This is a test note.',
                  },
                  createdOn: {
                    type: 'string',
                    format: 'date-time',
                    description: 'Timestamp when the note was created (will be set by server)',
                    example: '2024-03-20T12:00:00Z',
                    nullable: true,
                  },
                },
              },
            },
            EmailFlags: {
              type: 'object',
              description: 'Flags for email notifications',
              properties: {
                notifyReviewer: {
                  type: 'boolean',
                  description: 'Whether to notify reviewers (defaults to false if not provided)',
                  example: true,
                },
                notifyRespondent: {
                  type: 'boolean',
                  description: 'Whether to notify respondents (defaults to false if not provided)',
                  example: true,
                },
                includeHistory: {
                  type: 'boolean',
                  description: 'Whether to include note history in the email (defaults to true if not provided)',
                  example: true,
                },
              },
              nullable: true,
            },
          },
        },
      },
    },
  },
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Notes added successfully',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Inserted 1 note(s)'],
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
