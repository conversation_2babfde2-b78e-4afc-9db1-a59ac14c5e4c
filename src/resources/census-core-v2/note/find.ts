// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import { get, isError, isString } from 'lodash';
import asyncHandler from 'express-async-handler';
import { ParameterizeDirections } from '../../../utils/db-helpers/parameterize';

// Custom.
import { tryAsync, unknownAsError } from '../../../utils/general';
import { endpointSetup, logAppError, sendError } from '../../../utils/express/responses';
import Databases from '../../../utils/constants/databases';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import Messages from '../../../utils/constants/messages';

// Types.
export interface DbPageNote {
  SYSTEM_SURVEY_PAGE_NOTES_ID: string;
  PAGE_NAME: string;
  NOTES_USER: string;
  NOTES_CREATED_DATE: string;
  NOTES: string;
  NOTES_USER_ROLE: string;
  DISPLAY_PAGE_NAME: string;
  NOTES_USER_FIRST_NAME: string;
  NOTES_USER_LAST_NAME: string;
  System_ID: string;
}

export interface PageNote {
  noteId: string;
  systemId: string;
  pageName: string;
  userId: string;
  userFirst: string;
  userLast: string;
  userRole: string;
  note: string;
  createdOn: string;
}

export interface NoteFindResponse {
  count: number;
  Notes: PageNote[];
}

export const convertDbPageNoteToPageNote = (note: DbPageNote): PageNote => ({
  // This is a int in the database, but a string in the OG response.
  noteId: note.SYSTEM_SURVEY_PAGE_NOTES_ID.toString(),
  systemId: note.System_ID,
  pageName: note.PAGE_NAME,
  userId: note.NOTES_USER,
  userFirst: note.NOTES_USER_FIRST_NAME,
  userLast: note.NOTES_USER_LAST_NAME,
  userRole: note.NOTES_USER_ROLE,
  note: note.NOTES,
  createdOn: note.NOTES_CREATED_DATE,
});

const noteList = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.cedarSupport);

    try {
      const id = get(req, 'query.id');
      if (!id || !isString(id)) {
        sendError(res, 400, { message: ["Please provide required parameters 'id' and 'pageName'"] });
        return;
      }

      const pageName = get(req, 'query.pageName');
      if (!pageName || !isString(pageName)) {
        sendError(res, 400, { message: ["Please provide required parameters 'id' and 'pageName'"] });
        return;
      }

      const whereClause = {
        where: {
          operation: {
            column: 'System_ID',
            operator: '=',
            value: id,
          },
        },
        and: [
          {
            operation: {
              column: 'PAGE_NAME',
              operator: '=',
              value: pageName,
            },
          },
        ],
      };

      const [error, result] = await tryAsync(
        db.queryView(
          `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
          [
            'SYSTEM_SURVEY_PAGE_NOTES_ID',
            'PAGE_NAME',
            'NOTES_USER',
            'NOTES_CREATED_DATE',
            'NOTES',
            'NOTES_USER_ROLE',
            'DISPLAY_PAGE_NAME',
            'NOTES_USER_FIRST_NAME',
            'NOTES_USER_LAST_NAME',
            'System_ID',
          ],
          whereClause,
          { NOTES_CREATED_DATE: ParameterizeDirections.asc },
        ),
      );

      if (error || isError(result)) {
        sendError(res, 500, { message: [Messages.db_query_general_error] });
        return;
      }

      if (!result || !Array.isArray(result)) {
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }

      const notesRaw: DbPageNote[] = result[0] as DbPageNote[];
      const notes: PageNote[] = notesRaw.map<PageNote>(convertDbPageNoteToPageNote);
      const response: NoteFindResponse = {
        count: notes.length,
        Notes: notes,
      };

      res.status(200).send(response);
    } catch (error) {
      const e = unknownAsError(error);
      logAppError(app, {
        package: 'census-core-v2',
        service: 'note',
        action: 'list',
        error: e,
      });
      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export default noteList;
