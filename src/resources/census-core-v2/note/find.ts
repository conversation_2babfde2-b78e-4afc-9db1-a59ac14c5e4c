// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import {
  isArray, isError, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';
import { ParameterizeDirections } from '../../../utils/db-helpers/parameterize';

// Custom.
import { tryAsync, unknownAsError } from '../../../utils/general';
import { endpointSetup, logAppError, sendError } from '../../../utils/express/responses';
import Databases from '../../../utils/constants/databases';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import Messages from '../../../utils/constants/messages';
import QueryParams from '../../../utils/express/queryParams';

// Types.
export interface DbPageNote extends Record<string, unknown> {
  SYSTEM_SURVEY_PAGE_NOTES_ID: string;
  PAGE_NAME: string;
  NOTES_USER: string;
  NOTES_CREATED_DATE: string;
  NOTES: string;
  NOTES_USER_ROLE: string;
  DISPLAY_PAGE_NAME: string;
  NOTES_USER_FIRST_NAME: string;
  NOTES_USER_LAST_NAME: string;
  System_ID: string;
}

export type DbPageNoteQueryResult = [DbPageNote[], number];

export interface PageNote {
  noteId: string;
  systemId: string;
  pageName: string;
  userId: string;
  userFirst: string;
  userLast: string;
  userRole: string;
  note: string;
  createdOn: string;
}

export const convertDbPageNoteToPageNote = (note: DbPageNote): PageNote => ({
  // This is an int in the database, but a string in the OG response.
  noteId: note.SYSTEM_SURVEY_PAGE_NOTES_ID.toString(),
  systemId: note.System_ID,
  pageName: note.PAGE_NAME,
  userId: note.NOTES_USER,
  userFirst: note.NOTES_USER_FIRST_NAME,
  userLast: note.NOTES_USER_LAST_NAME,
  userRole: note.NOTES_USER_ROLE,
  note: note.NOTES,
  createdOn: note.NOTES_CREATED_DATE,
});

export enum NoteFindMessages {
  error_id_invalid = 'Querystring parameter id must be a number',
  error_id_missing = 'Querystring parameter id is required',
  error_page_name_invalid = 'Querystring parameter pageName must be a string',
  error_page_name_missing = 'Querystring parameter pageName is required',
}

export interface NoteFindQueryParams {
  id: string;
  pageName: string;
}

export interface NoteFindResponse {
  count: number;
  Notes: PageNote[];
}

const qsValidate = (p: NoteFindQueryParams): void => {
  if (!p.id) {
    throw new Error(NoteFindMessages.error_id_missing);
  } else if (!isString(p.id)) {
    throw new Error(NoteFindMessages.error_id_invalid);
  } else if (!p.pageName) {
    throw new Error(NoteFindMessages.error_page_name_missing);
  } else if (!isString(p.pageName)) {
    throw new Error(NoteFindMessages.error_page_name_invalid);
  }
};

const handler = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.cedarSupport);

    try {
      const params = QueryParams.fromRequest<NoteFindQueryParams>(req).getAll();

      try {
        qsValidate(params);
      } catch (e) {
        sendError(res, 400, { message: [unknownAsError(e).message] });
        return;
      }

      const { id, pageName } = params;
      const whereClause = {
        where: {
          operation: { column: 'System_ID', operator: '=', value: id },
        },
        and: [
          {
            operation: { column: 'PAGE_NAME', operator: '=', value: pageName },
          },
        ],
      };

      const [error, result] = await tryAsync(
        db.queryViewTyped<DbPageNoteQueryResult>(
          `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
          [
            'SYSTEM_SURVEY_PAGE_NOTES_ID',
            'PAGE_NAME',
            'NOTES_USER',
            'NOTES_CREATED_DATE',
            'NOTES',
            'NOTES_USER_ROLE',
            'DISPLAY_PAGE_NAME',
            'NOTES_USER_FIRST_NAME',
            'NOTES_USER_LAST_NAME',
            'System_ID',
          ],
          whereClause,
          { NOTES_CREATED_DATE: ParameterizeDirections.asc },
        ),
      );

      if (error || isError(result)) {
        app.logger.error({ error: error ?? result });
        sendError(res, 500, { message: [Messages.db_query_view_error] });
        return;
      }

      if (!isArray(result) || !isArray(result[0])) {
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }

      const notes: PageNote[] = result[0].map<PageNote>(convertDbPageNoteToPageNote);
      const response: NoteFindResponse = {
        count: notes.length,
        Notes: notes,
      };

      res.status(200).send(response);
    } catch (error) {
      const e = unknownAsError(error);
      logAppError(app, {
        package: 'census-core-v2',
        service: 'note',
        action: 'find',
        error: e,
      });
      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export default handler;
