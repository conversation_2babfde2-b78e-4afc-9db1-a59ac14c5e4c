// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { Request, Response } from 'express';
import { isArray, isEmpty, isString } from 'lodash';
import asyncHandler from 'express-async-handler';
import { unknownAsError } from '../../../utils/general';
import Databases from '../../../utils/constants/databases';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import {
  endpointSetup, logAppError, sendError, sendSuccess,
} from '../../../utils/express/responses';
import { UnitTestForcedExceptionError } from '../../../utils/express/errors';
import Messages from '../../../utils/constants/messages';
import QueryParams from '../../../utils/express/queryParams';
import { Where } from '../../../types';

export enum NoteDeleteMessages {
  error_ids_missing = 'At least one id parameter is required',
  error_ids_empty = 'The id parameter(s) must not be empty',
  error_ids_not_string = 'The id parameter(s) must be a string',
}

export interface NoteDeleteQueryParams extends Record<string, string[]> {
  id: string[];
}

const qsValidate = (p: NoteDeleteQueryParams): void => {
  let check: string[] = [];

  if (isString(p.id) && !isEmpty(p.id)) {
    check = [p.id];
  } else if (isArray(p.id)) {
    check = p.id;
  } else {
    throw new Error(NoteDeleteMessages.error_ids_empty);
  }

  check.forEach((id) => {
    if (!isString(id) || isEmpty(id)) {
      throw new Error(NoteDeleteMessages.error_ids_not_string);
    }
  });
};

const noteDeleteList = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const { app, db } = endpointSetup(req, DatabaseConfigs.cedarSupport);

  try {
    const params = QueryParams.fromQuery<NoteDeleteQueryParams>(req).getAll();

    try {
      qsValidate(params);
    } catch (e) {
      sendError(res, 400, { message: [unknownAsError(e).message] });
      return;
    }

    const ids = isArray(params.id) ? params.id : [params.id];

    const whereClause: Where = {
      where: {
        operation: { column: 'SYSTEM_SURVEY_PAGE_NOTES_ID', operator: 'IN', value: ids },
      },
    };

    let result: number;

    try {
      result = await db.wmDelete(
        `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
        whereClause,
      );

      if (!result) {
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }
    } catch (e) {
      if (e instanceof UnitTestForcedExceptionError) {
        // noinspection ExceptionCaughtLocallyJS
        throw e;
      }

      app.logger.error({ error: unknownAsError(e) });
      sendError(res, 500, { message: [Messages.db_delete_error] });
      return;
    }

    sendSuccess(res, { result: 'success', message: [`Deleted ${result} note(s)`] });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'note',
        action: 'deleteList',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: [Messages.internal_server_error] });
  }
});

export default noteDeleteList;
