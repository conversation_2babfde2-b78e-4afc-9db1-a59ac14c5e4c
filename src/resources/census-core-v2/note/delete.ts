// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { Request, Response } from 'express';
import {
  get, isArray, isError, isString,
} from 'lodash';
import asyncHandler from 'express-async-handler';
import MssqlData from '../../../data-sources/mssql';
import { getDb } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import Databases from '../../../utils/constants/databases';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import { logAppError, sendError, sendSuccess } from '../../../utils/express/responses';
import { CMSApp, Where } from '../../../types';

const noteDeleteList = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  let app: CMSApp | undefined;

  try {
    app = get(req, 'systemApp');
    if (!app) {
      sendError(res, 500, { message: [Messages.app_invalid] });
      return;
    }

    const db = getDb<MssqlData>(app, DatabaseConfigs.cedarSupport);
    if (isError(db)) {
      app.logger.error({ error: db });
      sendError(res, 500, { message: [Messages.db_unavailable] });
      return;
    }

    const ids = get(req, 'query.id');
    if (!ids || (isArray(ids) && ids.length === 0)) {
      sendError(res, 400, { message: ['The id parameter is required'] });
      return;
    }

    const idArray = isArray(ids) ? ids : [ids];

    if (!idArray.every((id) => isString(id))) {
      sendError(res, 400, { message: ['All id parameters must be strings'] });
      return;
    }

    const whereClause: Where = {
      where: {
        operation: {
          column: 'SYSTEM_SURVEY_PAGE_NOTES_ID',
          operator: 'IN',
          value: idArray,
        },
      },
    };

    const [error, result] = await tryAsync(
      db.wmDelete(
        `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
        whereClause,
      ),
    );

    if (error || isError(result)) {
      app.logger.error(error);
      sendError(res, 400, { message: ['Error deleting notes'] });
      return;
    }

    sendSuccess(res, { result: 'success', message: [`Deleted ${result} note(s)`] });
  } catch (error) {
    if (app) {
      logAppError(app, {
        package: 'census-core-v2',
        service: 'note',
        action: 'deleteList',
        error: error as Error,
      });
    }
    sendError(res, 500, { message: ['Internal server error'] });
  }
});

export default noteDeleteList;
