import { Request, Response } from 'express';
import { ResourceConfigSub } from '../../types';
import { createResourceConfigHarness } from '../../utils/express/responses';

const core = (_: Request, res: Response) => {
  res.status(200).send({ message: 'CENSUS Core v2 root' });
};

// Import each service type.
const modules = import.meta.glob<{ default:() => ResourceConfigSub }>('./**/index.ts', { eager: true });

const getResourceConfig = createResourceConfigHarness(
  [
    {
      name: 'get-core',
      path: '/',
      method: 'get',
      resource: core,
    },
  ],
  modules,
);

export default getResourceConfig;
