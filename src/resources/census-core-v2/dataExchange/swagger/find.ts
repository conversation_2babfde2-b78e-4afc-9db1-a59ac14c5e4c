import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/dataExchange',
  method: 'get',
  tags: ['System Census'],
  description: 'Retrieve data exchange information by system ID and optionally version.',
  parameters: [
    {
      name: 'systemId',
      in: 'query',
      description: 'ID of the system to retrieve data exchange information about.',
      required: true,
      schema: {
        type: 'string',
        example: '11111111-**************-************',
      },
    },
    {
      name: 'version',
      in: 'query',
      description: 'Version of the data exchanges to be retrieved.',
      required: false,
      schema: {
        type: 'string',
        example: '1',
      },
    },
  ],
  requestBody: {
    required: false,
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {},
        },
      },
    },
  },
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully retrieved data exchange information.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              result: {
                type: 'string',
                example: 'success',
              },
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['Successfully retrieved data exchange information'],
              },
              systemId: {
                type: 'string',
                example: '11111111-**************-************',
              },
              pageName: {
                type: 'string',
                example: 'DataExchange',
              },
              count: {
                type: 'integer',
                example: 1,
              },
              DataExchanges: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    direction: {
                      type: 'string',
                      enum: ['receiver', 'sender'],
                      example: 'receiver',
                      nullable: true,
                    },
                    deleted: {
                      type: 'boolean',
                      example: false,
                      nullable: true,
                    },
                    isNewExchange: {
                      type: 'boolean',
                      example: true,
                      nullable: true,
                    },
                    Exchange: {
                      type: 'object',
                      properties: {
                        exchangeId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        exchangeName: { type: 'string', example: 'Sample Exchange', nullable: true },
                        exchangeDescription: { type: 'string', example: 'Description of sample exchange', nullable: true },
                        exchangeVersion: { type: 'string', example: '1', nullable: true },
                        exchangeState: { type: 'string', example: 'Active', nullable: true },
                        exchangeStartDate: { type: 'string', format: 'date', example: '2023-01-01', nullable: true },
                        exchangeEndDate: { type: 'string', format: 'date', example: '2023-12-31', nullable: true },
                        exchangeRetiredDate: { type: 'string', format: 'date', example: null, nullable: true },
                        fromOwnerId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        fromOwnerName: { type: 'string', example: 'System A', nullable: true },
                        fromOwnerType: { type: 'string', example: 'Application', nullable: true },
                        toOwnerId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        toOwnerName: { type: 'string', example: 'System B', nullable: true },
                        toOwnerType: { type: 'string', example: 'Application', nullable: true },
                        connectionFrequency: { type: 'array', items: { type: 'string' }, example: ['Daily'], nullable: true },
                        dataExchangeAgreement: { type: 'string', example: 'MOU', nullable: true },
                        containsBeneficiaryAddress: { type: 'boolean', example: false, nullable: true },
                        businessPurposeOfAddress: { type: 'array', items: { type: 'string' }, example: ['Enrollment'], nullable: true },
                        isAddressEditable: { type: 'boolean', example: false, nullable: true },
                        containsPii: { type: 'boolean', example: true, nullable: true },
                        containsPhi: { type: 'boolean', example: false, nullable: true },
                        containsHealthDisparityData: { type: 'boolean', example: false, nullable: true },
                        containsBankingData: { type: 'boolean', example: false, nullable: true },
                        isBeneficiaryMailingFile: { type: 'boolean', example: false, nullable: true },
                        sharedViaApi: { type: 'boolean', example: true, nullable: true },
                        apiOwnership: { type: 'string', example: 'Internal', nullable: true },
                        typeOfData: {
                          type: 'array',
                          items: {
                            type: 'object',
                            properties: {
                              id: { type: 'string', example: 'TD1', nullable: true },
                              name: { type: 'string', example: 'Patient Demographics', nullable: true },
                            },
                          },
                          nullable: true,
                        },
                        numOfRecords: { type: 'string', example: '1000', nullable: true },
                        dataFormat: { type: 'string', example: 'JSON', nullable: true },
                        dataFormatOther: { type: 'string', example: null, nullable: true },
                        exchangeContainsCUI: { type: 'boolean', example: false, nullable: true },
                        exchangeCUIDescription: { type: 'string', example: null, nullable: true },
                        exchangeCUIType: { type: 'array', items: { type: 'string' }, example: ['Controlled'], nullable: true },
                        exchangeConnectionAuthenticated: { type: 'boolean', example: true, nullable: true },
                        exchangeNetworkProtocol: { type: 'array', items: { type: 'string' }, example: ['HTTPS'], nullable: true },
                        exchangeNetworkProtocolOther: { type: 'string', example: null, nullable: true },
                      },
                    },
                    Status: {
                      type: 'object',
                      properties: {
                        exchangeId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        systemId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        systemStatus: { type: 'string', example: 'Completed', nullable: true },
                        partnerId: { type: 'string', example: '11111111-**************-************', nullable: true },
                        partnerStatus: { type: 'string', example: 'Completed', nullable: true },
                        reviewerStatus: { type: 'string', example: 'Reviewed', nullable: true },
                        direction: { type: 'string', enum: ['receiver', 'sender'], example: 'receiver', nullable: true },
                        deleted: { type: 'boolean', example: false, nullable: true },
                        isNewExchange: { type: 'boolean', example: false, nullable: true },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;