// generated sha: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

// Modules.
import asyncHandler from 'express-async-handler';
import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';

// Custom.
import { sendError, logAppError } from '../../../utils/express/responses';
import {
  getDataExchangeListUtil,
  dataExchangeStatusFindUtil,
} from '../../../utils/dataExchange';
import { getDb } from '../../../utils/db-helpers';
import MssqlData from '../../../data-sources/mssql';
import Messages from '../../../utils/constants/messages';
import { Exchange, CMSApp } from '../../../types';
// Local Type Definitions for Data Exchange
// Based on CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/docTypes/Exchange/node.ndf

interface PageDataExchangeStatus {
  exchangeId: string | null;
  systemId: string | null;
  systemStatus: string | null;
  partnerId: string | null;
  partnerStatus: string | null;
  reviewerStatus: string | null;
  direction: 'receiver' | 'sender' | null;
  // in Designer, but not in the actual Webmethods
  // deleted: boolean | null;
}

interface DataExchangesItem {
  direction: 'receiver' | 'sender' | null;
  // in Designer, but not in the actual Webmethods
  // deleted: boolean | null;
  Exchange: Exchange;
  Status: PageDataExchangeStatus;
}

/**
 * Emulates cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeFindList
 * by directly querying the Sparx_System_DataExchange view.
 */

const mapDataExchanges = (
  exchanges: Exchange[],
  statuses: PageDataExchangeStatus[],
  systemId: string,
): DataExchangesItem[] => {
  const dataExchanges: DataExchangesItem[] = [];
  const statusMap = new Map<string, PageDataExchangeStatus>();

  // Use map to quickly find status by exchangeId
  statuses.forEach((s) => {
    if (s.exchangeId) {
      statusMap.set(s.exchangeId, s);
    }
  });

  exchanges.forEach((exchange) => {
    const matchingStatus = exchange.exchangeId ? statusMap.get(exchange.exchangeId) : null;

    // Default status object if no match is found, ensuring all fields are present
    const defaultStatus: PageDataExchangeStatus = {
      exchangeId: exchange.exchangeId,
      systemId,
      systemStatus: null,
      partnerId: null,
      partnerStatus: null,
      reviewerStatus: null,
      direction: null,
      // in Designer, but not in the actual Webmethods
      // deleted: null,
    };

    dataExchanges.push({
      direction: matchingStatus?.direction ?? null,
      // in Designer, but not in the actual Webmethods
      // deleted: matchingStatus?.deleted ?? null,
      Exchange: exchange,
      Status: matchingStatus || defaultStatus,
    });
  });

  return dataExchanges;
};

const pageDataExchangeFind = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    let app: CMSApp | undefined;

    try {
      app = get(req, 'systemApp');
      if (!app) {
        sendError(res, 500, { message: [Messages.app_invalid] });
        return;
      }

      const db = getDb<MssqlData>(app, 'sparxea');
      if (isError(db)) {
        app.logger.error({ error: db });
        sendError(res, 500, { message: [Messages.db_unavailable] });
        return;
      }

      const systemId = get(req, 'query.systemId');
      let version = get(req, 'query.version', '');

      // Input validation: systemId is required
      if (!systemId || !isString(systemId)) {
        const error = new Error('systemId must be provided');
        app.logger.error({ error });
        sendError(res, 400, { message: [error.message] });
        return;
      }

      // Version is not used, putting this in here so it's always a string
      if (!isString(version) || isEmpty(version)) {
        version = '';
      }

      // Fetch exchanges and statuses concurrently
      const exchangesResult = await getDataExchangeListUtil(app, db, systemId, 'both', version);
      if (isError(exchangesResult)) {
        app.logger.error({ error: exchangesResult });
        sendError(res, 500, { message: ['Error fetching data exchanges'] });
        return;
      }

      const statusesResult = await dataExchangeStatusFindUtil(app, db, systemId, 'both');
      if (isError(statusesResult)) {
        app.logger.error({ error: statusesResult });
        sendError(res, 500, { message: ['Error fetching data exchange statuses'] });
        return;
      }

      // Combine exchanges and statuses
      const combinedData = mapDataExchanges(
        exchangesResult.Exchanges,
        statusesResult.ExchangeStatus,
        systemId,
      );

      const response = {
        systemId,
        pageName: 'DataExchanges',
        count: combinedData.length,
        DataExchanges: combinedData,
      };

      res.status(200).send(response);
    } catch (error) {
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'dataExchangeStatus',
          action: 'find',
          error: error as Error,
        });
      }
      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export default pageDataExchangeFind;
