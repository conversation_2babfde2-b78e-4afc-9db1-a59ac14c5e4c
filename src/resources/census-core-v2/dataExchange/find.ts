// generated sha: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
} from 'lodash';
import {
  getDataExchangeListUtil,
  dataExchangeStatusFindUtil,
} from '../../../utils/dataExchange';
import { getDb } from '../../../utils/db-helpers';
import MssqlData from '../../../data-sources/mssql';

// Local Type Definitions for Data Exchange
// Based on CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/docTypes/Exchange/node.ndf
interface Exchange {
  exchangeId: string | null;
  exchangeName: string | null;
  exchangeDescription: string | null;
  exchangeVersion: string | null;
  exchangeState: string | null;
  exchangeStartDate: string | null;
  exchangeEndDate: string | null;
  exchangeRetiredDate: string | null;
  fromOwnerId: string | null;
  fromOwnerName: string | null;
  fromOwnerType: string | null;
  toOwnerId: string | null;
  toOwnerName: string | null;
  toOwnerType: string | null;
  connectionFrequency: string[] | null;
  dataExchangeAgreement: string | null;
  containsBeneficiaryAddress: boolean | null;
  businessPurposeOfAddress: string[] | null;
  isAddressEditable: boolean | null;
  containsPii: boolean | null;
  containsPhi: boolean | null;
  containsHealthDisparityData: boolean | null;
  isBeneficiaryMailingFile: boolean | null;
  sharedViaApi: boolean | null;
  apiOwnership: string | null;
  typeOfData: { id: string | null; name: string | null; }[] | null;
  numOfRecords: string | null;
  dataFormat: string | null;
  dataFormatOther: string | null;
  exchangeContainsCUI: boolean | null;
  exchangeCUIDescription: string | null;
  exchangeCUIType: string[] | null;
  exchangeConnectionAuthenticated: boolean | null;
  exchangeNetworkProtocol: string[] | null;
  exchangeNetworkProtocolOther: string | null;
}

interface PageDataExchangeStatus {
  exchangeId: string | null;
  systemId: string | null;
  systemStatus: string | null;
  partnerId: string | null;
  partnerStatus: string | null;
  reviewerStatus: string | null;
  direction: 'receiver' | 'sender' | null;
  deleted: boolean | null;
}

interface DataExchangesItem {
  direction: 'receiver' | 'sender' | null;
  deleted: boolean | null;
  Exchange: Exchange;
  Status: PageDataExchangeStatus;
}

/**
 * Emulates cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeFindList
 * by directly querying the Sparx_System_DataExchange view.
 */

const mapDataExchanges = (
  exchanges: Exchange[],
  statuses: PageDataExchangeStatus[],
  systemId: string,
): DataExchangesItem[] => {
  const dataExchanges: DataExchangesItem[] = [];
  const statusMap = new Map<string, PageDataExchangeStatus>();

  // Use map to quickly find status by exchangeId
  statuses.forEach((s) => {
    if (s.exchangeId) {
      statusMap.set(s.exchangeId, s);
    }
  });

  exchanges.forEach((exchange) => {
    const matchingStatus = exchange.exchangeId ? statusMap.get(exchange.exchangeId) : null;

    // Default status object if no match is found, ensuring all fields are present
    const defaultStatus: PageDataExchangeStatus = {
      exchangeId: exchange.exchangeId,
      systemId,
      systemStatus: null,
      partnerId: null,
      partnerStatus: null,
      reviewerStatus: null,
      direction: null,
      deleted: null,
    };

    dataExchanges.push({
      direction: matchingStatus?.direction ?? null,
      deleted: matchingStatus?.deleted ?? null,
      Exchange: exchange,
      Status: matchingStatus || defaultStatus,
    });
  });

  return dataExchanges;
};

const pageDataExchangeFind = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId');
  const version = get(req, 'query.version', '');

  // Input validation: systemId is required
  if (!systemId || !isString(systemId)) {
    const error = new Error('systemId must be provided');
    app.logger.error({ error });
    return res.status(400).send({ error: 'systemId must be provided' });
  }

  // Fetch exchanges and statuses concurrently
  const exchangesResult = await getDataExchangeListUtil(app, db, systemId, 'both', version);
  if (isError(exchangesResult)) {
    app.logger.error({ error: exchangesResult });
    return res.status(500).send({ error: 'Error fetching data exchanges' });
  }

  const statusesResult = await dataExchangeStatusFindUtil(app, db, systemId, 'both');
  if (isError(statusesResult)) {
    app.logger.error({ error: statusesResult });
    return res.status(500).send({ error: 'Error fetching data exchange statuses' });
  }

  // Combine exchanges and statuses
  const combinedData = mapDataExchanges(
    exchangesResult.Exchanges,
    statusesResult.ExchangeStatus,
    systemId,
  );

  const response = {
    systemId,
    pageName: 'DataExchanges',
    count: combinedData.length,
    DataExchanges: combinedData,
  };

  return res
    .status(200)
    .send(response);
};

export default pageDataExchangeFind;
