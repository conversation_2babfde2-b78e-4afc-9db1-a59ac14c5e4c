// generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import {
  get,
  isError,
  isEmpty,
  isString,
} from 'lodash';
import Jo<PERSON> from 'joi';
import asyncHandler from 'express-async-handler';
import { sendError, logAppError } from '../../../../utils/express/responses';
import MssqlData from '../../../../data-sources/mssql';
import { CMSApp } from '../../../../types';
import { getDb } from '../../../../utils/db-helpers';
import Messages from '../../../../utils/constants/messages';
import DatabaseConfigs from '../../../../utils/constants/databaseConfigs';
import {
  dataExchangeStatusFindUtil,
} from '../../../../utils/dataExchange';

const dataExchangeStatusFind = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    let app: CMSApp | undefined;

    try {
      const baseSchema = Joi.string();
      const allowedDirections = [
        'both',
        'receiver',
        'sender',
      ];

      app = get(req, 'systemApp');
      if (!app) {
        sendError(res, 500, { message: [Messages.app_invalid] });
        return;
      }

      const db = getDb<MssqlData>(app, DatabaseConfigs.cedarSupport);
      if (isError(db)) {
        app.logger.error({ error: db });
        sendError(res, 500, { message: [Messages.db_unavailable] });
        return;
      }

      const systemId = get(req, 'query.systemId');
      if (!systemId || !isString(systemId) || isEmpty(systemId)) {
        app.logger.error({ error: new Error('The systemId parameter is required and must be a string') });
        sendError(res, 400, { message: ['The systemId parameter is required and must be a string'] });
        return;
      }

      const uuidVal = baseSchema.uuid().validate(systemId);
      if (uuidVal.error) {
        app.logger.error({ error: uuidVal.error });
        sendError(res, 400, { message: ['The systemId is not a valid uuid'] });
        return;
      }

      const direction = get(req, 'query.direction', 'both') as 'receiver' | 'sender' | 'both';
      if (!allowedDirections.includes(direction)) {
        const error = new Error('The provided direction is not valid');
        app.logger.error({ error });
        sendError(res, 400, { message: ['The provided direction is not valid'] });
        return;
      }

      const statusResult = await dataExchangeStatusFindUtil(app, db, systemId, direction);

      if (isError(statusResult)) {
        app.logger.error({ error: statusResult });
        sendError(res, 500, { message: ['There was an error fetching the query'] });
        return;
      }

      res.status(200).send(statusResult);
    } catch (error) {
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'dataExchangeStatus',
          action: 'find',
          error: error as Error,
        });
      }
      sendError(res, 500, { message: ['Internal server error'] });
    }
  },
);

export default dataExchangeStatusFind;
