// generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
  isArray,
} from 'lodash';
import MssqlData from '../../../../data-sources/mssql';
import { getDb } from '../../../../utils/db-helpers';
import DatabaseConfigs from '../../../../utils/constants/databaseConfigs';
import {
  dataExchangeStatusFindUtil,
} from '../../../../utils/dataExchange';

const dataExchangeStatusFind = async (
  req: Request,
  res: Response,
) => {
  const allowedDirections = [
    'both',
    'receiver',
    'sender',
  ];

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, DatabaseConfigs.cedarSupport);
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'The database is unavailable' });
  }

  const systemId = get(req, 'query.systemId');
  if (!systemId || !isString(systemId)) {
    app.logger.error({ error: new Error('The systemId parameter is required and must be a string') });
    return res.status(400).send({ error: 'The systemId parameter is required and must be a string' });
  }

  const direction = get(req, 'query.direction', 'both') as 'receiver' | 'sender' | 'both';
  if (!allowedDirections.includes(direction)) {
    const error = new Error('The provided direction is not valid.');
    app.logger.error({ error });
    return res.status(400).send({ error });
  }

  const statusResult = await dataExchangeStatusFindUtil(app, db, systemId, direction);

  if (isError(statusResult)) {
    app.logger.error({ error: statusResult });
    return res.status(500).send({ error: 'There was an error fetching the query' });
  }

  return res.status(200).send(statusResult);
};

export default dataExchangeStatusFind;
