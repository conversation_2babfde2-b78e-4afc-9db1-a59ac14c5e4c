// /dataExchange
import dataExchangePageFind from './find';
import dataExchangeStatusFind from './status/find';
import { GetResourceConfigSub } from '../../../types';

const getResourceConfig: GetResourceConfigSub = () => ({
  path: '/page/dataExchange',
  resourceConfig: [
    {
      name: 'get-census-data-exchange-list',
      path: '/',
      method: 'get',
      resource: dataExchangePageFind,
    },
    {
      name: 'get-census-data-exchange-status',
      path: '/status',
      method: 'get',
      resource: dataExchangeStatusFind,
    },
  ],
});

export default getResourceConfig;
