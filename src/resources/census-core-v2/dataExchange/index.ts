// /dataExchange
import dataExchangePageFind from './find';
import dataExchangeStatusFind from './status/find';
import dataExchangeNotesFind from './notes/find';
import { GetResourceConfigSub } from '../../../types';

const getResourceConfig: GetResourceConfigSub = () => ({
  path: '/page/dataExchange',
  resourceConfig: [
    {
      name: 'get-census-data-exchange-list',
      path: '/',
      method: 'get',
      resource: dataExchangePageFind,
    },
    {
      name: 'get-census-data-exchange-status',
      path: '/status',
      method: 'get',
      resource: dataExchangeStatusFind,
    },
    {
      name: 'get-census-data-exchange-notes',
      path: '/notes',
      method: 'get',
      resource: dataExchangeNotesFind,
    },
  ],
});

export default getResourceConfig;
