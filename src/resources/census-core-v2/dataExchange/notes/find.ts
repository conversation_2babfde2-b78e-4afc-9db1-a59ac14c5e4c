import { Request, Response } from 'express';
import { get, isError, isString } from 'lodash';
import asyncH<PERSON><PERSON> from 'express-async-handler';
import <PERSON><PERSON> from 'joi';
import MssqlData from '../../../../data-sources/mssql';
import { getDb } from '../../../../utils/db-helpers';
import { tryAsync } from '../../../../utils/general';
import Messages from '../../../../utils/constants/messages';
import DatabaseConfigs from '../../../../utils/constants/databaseConfigs';
import { endpointSetup, sendError, logAppError } from '../../../../utils/express/responses';
import { CMSApp, Where } from '../../../../types';

// Define local types as required

export interface DbPageDataExchangeNoteQueryResult {
  [0]: DbDataExchangeNote[];
  [1]: number;
}

export interface DbDataExchangeNote {
  SYSTEM_SURVEY_EXCHANGE_NOTES_ID: string;
  EXCHANGE_ID: string;
  NOTES_USER: string;
  NOTES_CREATED_DATE: string;
  NOTES: string;
  NOTES_USER_ROLE: string;
}

export interface DataExchangeNote {
  noteId: string;
  exchangeId: string;
  userId: string;
  createdOn: string;
  note: string;
  userRole: string;
}

export const convertDbDataExchangeNoteToDataExchangeNote = (
  note: DbDataExchangeNote,
): DataExchangeNote => ({
  noteId: note.SYSTEM_SURVEY_EXCHANGE_NOTES_ID,
  exchangeId: note.EXCHANGE_ID,
  userId: note.NOTES_USER,
  createdOn: note.NOTES_CREATED_DATE,
  note: note.NOTES,
  userRole: note.NOTES_USER_ROLE,
});

const findDataExchangeNotesList = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    let app: CMSApp | undefined;

    try {
      app = req.systemApp ?? undefined;
      if (!app) {
        sendError(res, 500, { message: [Messages.app_invalid] });
        return;
      }

      const db = getDb<MssqlData>(app, DatabaseConfigs.cedarSupport);

      if (isError(db)) {
        app.logger.error({ message: db });
        sendError(res, 500, { message: [Messages.db_unavailable] });
        return;
      }
      const baseSchema = Joi.string();
      // Required exchangeId parameter
      const exchangeId = req.query.exchangeId ?? undefined;
      if (!exchangeId || !isString(exchangeId)) {
        app.logger.error({ message: 'The exchangeId parameter is required and must be a string' });
        sendError(res, 400, { message: ['The exchangeId parameter is required and must be a string'] });
        return;
      }

      const uuidVal = baseSchema.uuid().validate(exchangeId);
      if (uuidVal.error) {
        app.logger.error({ message: uuidVal.error });
        sendError(res, 400, { message: ['The exchangeId is not a valid uuid'] });
        return;
      }

      const columns = [
        'SYSTEM_SURVEY_EXCHANGE_NOTES_ID',
        'EXCHANGE_ID',
        'NOTES_USER',
        'NOTES_CREATED_DATE',
        'NOTES',
        'NOTES_USER_ROLE',
      ];

      const whereClause: Where = {
        where: {
          operation: {
            column: 'EXCHANGE_ID',
            operator: '=',
            value: exchangeId,
          },
        },
      };

      const [queryError, queryResult] = await tryAsync(
        db.queryView(
          'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_NOTES',
          columns,
          whereClause,
        ),
      );

      if (queryError || isError(queryResult)) {
        app.logger.error({ message: queryError || queryResult });
        sendError(res, 500, { message: ['Error querying database'] });
        return;
      }

      if (!queryResult || !Array.isArray(queryResult)) {
        app.logger.error({ message: new Error('Unable to get result from query') });
        sendError(res, 500, { message: ['Unable to get result from query'] });
        return;
      }

      // The Webmethods selectByExchangeId JDBC adapter output structure is an array
      // where the first element contains the actual results array and the second
      // is the count of selected rows.
      // The current queryView returns a tuple [results, count], so results are at index 0.
      const notesRaw: DbDataExchangeNote[] = queryResult[0] as DbDataExchangeNote[];
      const notes: DataExchangeNote[] = notesRaw.map(convertDbDataExchangeNoteToDataExchangeNote);

      res.status(200).send({
        count: notes.length,
        ExchangeNotes: notes,
      });
    } catch (error) {
      // TODO: this is the only part that remains uncovered by testing, but
      // not sure what case would cause it to throw an error presently
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'dataExchangeNotes',
          action: 'find',
          error: error as Error,
        });
      }
      sendError(res, 500, { message: ['Internal Server Error'] });
    }
  },
);

export default findDataExchangeNotesList;
