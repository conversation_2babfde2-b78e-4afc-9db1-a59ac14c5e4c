import { SwaggerDocumentation } from '../../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/dataExchangeNotes',
  method: 'get',
  tags: ['System Census'],
  description: 'Get list of notes for a specific data exchange',
  parameters: [
    {
      in: 'query',
      name: 'exchangeId',
      schema: {
        type: 'string',
        example: '{11111111-2222-3333-4444-555555555555}',
      },
      required: true,
      description: 'The ID of the data exchange whose notes to retrieve.',
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successful response with a list of data exchange notes',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              count: {
                type: 'integer',
                example: 2,
              },
              ExchangeNotes: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    noteId: {
                      type: 'string',
                      example: '1234',
                    },
                    exchangeId: {
                      type: 'string',
                      example: '{11111111-2222-3333-4444-555555555555}',
                    },
                    userId: {
                      type: 'string',
                      example: 'T3ST',
                    },
                    createdOn: {
                      type: 'string',
                      format: 'date-time',
                      example: '2024-03-20T12:00:00Z',
                    },
                    note: {
                      type: 'string',
                      example: 'This is a sample note',
                    },
                    userRole: {
                      type: 'string',
                      example: 'Reviewer',
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    401: {
      description: 'Unauthorized',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/Unauthorized',
          },
        },
      },
    },
    500: {
      description: 'Internal server error',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;