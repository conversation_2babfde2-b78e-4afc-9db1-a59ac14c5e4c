// noinspection SpellCheckingInspection

/**
 * Basic structure for validating fields for the Sparx_Support.Sparx_System table.
 *
 * The endpoint takes a free string when reading a property name -- this
 * allows the field to be validated.
 */
export const validSparxSystemFields = {
  'System ID': { type: 'string', nullable: true },
  'Sparx System ID': { type: 'number', nullable: false },
  'Sparx System GUID': { type: 'string', nullable: true },
  'System Name': { type: 'string', nullable: true },
  Acronym: { type: 'string', nullable: true },
  'Object State': { type: 'string', nullable: true },
  'CMS UUID': { type: 'string', nullable: true },
  '508 Review Conducted': { type: 'string', nullable: true },
  '508 Review Date': { type: 'string', nullable: true },
  '508 Review Exception': { type: 'string', nullable: true },
  '508 Review Score': { type: 'string', nullable: true },
  'Additional Comments': { type: 'string', nullable: true },
  'Agile Methodology Use': { type: 'string', nullable: true },
  'AI Solution Category': { type: 'string', nullable: true },
  'AI Solution Category Other': { type: 'string', nullable: true },
  'API Accessibility': { type: 'string', nullable: true },
  'API Data Area': { type: 'string', nullable: true },
  'API Description Location': { type: 'string', nullable: true },
  'API Description Published': { type: 'string', nullable: true },
  'API Developed': { type: 'string', nullable: true },
  'API Development Plan': { type: 'string', nullable: true },
  'API Has Portal': { type: 'string', nullable: true },
  APPLICATION_LastActive_REFSTR: { type: 'string', nullable: true },
  Application_LastActive_Version: { type: 'string', nullable: true },
  'Artificial Intelligence': { type: 'string', nullable: true },
  'Artificial Intelligence - OTHER Description': { type: 'string', nullable: true },
  'Artificial Intelligence Plan': { type: 'string', nullable: true },
  'At Rest Data Encryption': { type: 'string', nullable: true },
  'Backup Frequency': { type: 'string', nullable: true },
  'Banking Data': { type: 'string', nullable: true },
  'BC Plan': { type: 'string', nullable: true },
  'Beneficiary Address': { type: 'string', nullable: true },
  'Beneficiary Address Source': { type: 'string', nullable: true },
  'Beneficiary Address Source Other': { type: 'string', nullable: true },
  'BIA Determination Document': { type: 'string', nullable: true },
  'BIA Location': { type: 'string', nullable: true },
  'BIA Plan': { type: 'string', nullable: true },
  'BPC Location': { type: 'string', nullable: true },
  'Business Application': { type: 'string', nullable: true },
  'Business Artifact Location': { type: 'string', nullable: true },
  'Business Artifacts Location Management': { type: 'string', nullable: true },
  'Business Artifacts on Demand': { type: 'string', nullable: true },
  'Centralized Data Catalog': { type: 'string', nullable: true },
  'Changes to Sys Profile Next Prod Release': { type: 'string', nullable: true },
  Cloud: { type: 'string', nullable: true },
  'Cloud Migrated Date': { type: 'string', nullable: true },
  'Cloud Migration Plan': { type: 'string', nullable: true },
  'CMS Owned': { type: 'string', nullable: true },
  'CMS System Access': { type: 'string', nullable: true },
  'COBOL Lines of Code': { type: 'string', nullable: true },
  'Comment about the System': { type: 'string', nullable: true },
  'Contains ICD9 or ICD10 Information': { type: 'string', nullable: true },
  'Contractor Owned': { type: 'string', nullable: true },
  'Data Encryption Management': { type: 'string', nullable: true },
  'Date BIA Updated': { type: 'string', nullable: true },
  'Deployment Frequency': { type: 'string', nullable: true },
  'Design on Demand': { type: 'string', nullable: true },
  'Development Complete': { type: 'string', nullable: true },
  'Development Complete Percent': { type: 'string', nullable: true },
  'Development Work Still Underway': { type: 'string', nullable: true },
  'Does the API use FHIR': { type: 'string', nullable: true },
  'Does the API use FHIR Other': { type: 'string', nullable: true },
  'DR Exercise': { type: 'string', nullable: true },
  'DR Exercise Date': { type: 'string', nullable: true },
  'DR Exercise Type': { type: 'string', nullable: true },
  'DR Exercise Weakness': { type: 'string', nullable: true },
  'DR Plan': { type: 'string', nullable: true },
  'DR Plan Location': { type: 'string', nullable: true },
  'E-Cap Participation': { type: 'string', nullable: true },
  'EDL Plan': { type: 'string', nullable: true },
  'End Date': { type: 'string', nullable: true },
  'External System': { type: 'string', nullable: true },
  'Front End Access Type': { type: 'string', nullable: true },
  'Hard Coded IP Address': { type: 'string', nullable: true },
  'Health Disparity Data': { type: 'string', nullable: true },
  'High Value Asset': { type: 'string', nullable: true },
  'High Value Asset Agreement': { type: 'string', nullable: true },
  'Higher Level Sensitive Information': { type: 'string', nullable: true },
  'Hosted on Cloud': { type: 'string', nullable: true },
  IaaS: { type: 'string', nullable: true },
  ID: { type: 'string', nullable: true },
  'Identity Management Solution': { type: 'string', nullable: true },
  'Identity Management Solution Other': { type: 'string', nullable: true },
  'Improvement End Date': { type: 'string', nullable: true },
  'Improvement Start Date': { type: 'string', nullable: true },
  'Improvement Type': { type: 'string', nullable: true },
  'In Motion Data Encryption': { type: 'string', nullable: true },
  'Indentity Proofing': { type: 'string', nullable: true },
  INSTGUID: { type: 'string', nullable: true },
  'IP Enabled Asset Count': { type: 'string', nullable: true },
  'IPV6 Transition Completed': { type: 'string', nullable: true },
  'Is Sub-System': { type: 'string', nullable: true },
  'Last BIA Update': { type: 'string', nullable: true },
  'Last DR Plan Update': { type: 'string', nullable: true },
  'Last Major Tech Refresh Date': { type: 'string', nullable: true },
  'Long Term IPV6 Plan': { type: 'string', nullable: true },
  'Machine Learning Analysis Other': { type: 'string', nullable: true },
  'Machine Learning Classification Other': { type: 'string', nullable: true },
  'Machine Learning Clustering Other': { type: 'string', nullable: true },
  'Machine Learning Regression Other': { type: 'string', nullable: true },
  'Machine Learning Type': { type: 'string', nullable: true },
  'Major Changes': { type: 'string', nullable: true },
  'Maximum Tolerable Downtime': { type: 'string', nullable: true },
  'Metadata Glossary': { type: 'string', nullable: true },
  'Navigator Name': { type: 'string', nullable: true },
  'Next Major Tech Refresh Date': { type: 'string', nullable: true },
  'Next Planned Prod Release Date': { type: 'string', nullable: true },
  'Next System Survey': { type: 'string', nullable: true },
  'Number of Individuals with Records having Sensitive Info': { type: 'string', nullable: true },
  'Number of Records of Sensitive Info': { type: 'string', nullable: true },
  'Operations Manual': { type: 'string', nullable: true },
  'Ops and Maintenance Plan Location': { type: 'string', nullable: true },
  'Ops and Maintenance Plan Location Management': { type: 'string', nullable: true },
  'Ops and Maintenance Plans on Demand': { type: 'string', nullable: true },
  'Other Benefitiary Address Purpose': { type: 'string', nullable: true },
  'Other User MFA': { type: 'string', nullable: true },
  PaaS: { type: 'string', nullable: true },
  'Parent IT System': { type: 'string', nullable: true },
  'Percent IPV6': { type: 'string', nullable: true },
  PII: { type: 'string', nullable: true },
  'Planned Retirement Quarter': { type: 'string', nullable: true },
  'Primary User MFA Utilization': { type: 'string', nullable: true },
  'Privileged User MFA Access': { type: 'string', nullable: true },
  'Privileged User MFA Count': { type: 'string', nullable: true },
  'Privileged User MFA Utilization': { type: 'string', nullable: true },
  'Prod Start Date Confirmed': { type: 'string', nullable: true },
  'Reconcile Comments': { type: 'string', nullable: true },
  'Records Management Approved Schedule': { type: 'string', nullable: true },
  'Records Management Disposal': { type: 'string', nullable: true },
  'Records Management Format': { type: 'string', nullable: true },
  'Records Management Format Other': { type: 'string', nullable: true },
  'Records Management Metadata': { type: 'string', nullable: true },
  'Release Description': { type: 'string', nullable: true },
  'Requirements Location': { type: 'string', nullable: true },
  'Requirements Location Management': { type: 'string', nullable: true },
  'Requirements on Demand': { type: 'string', nullable: true },
  'Rest Last Updated Date': { type: 'string', nullable: true },
  'Rest Updated User': { type: 'string', nullable: true },
  'Retire or Replace': { type: 'string', nullable: true },
  'Retire or Replace Date': { type: 'string', nullable: true },
  'Retired Date Confirmed': { type: 'string', nullable: true },
  SaaS: { type: 'string', nullable: true },
  'Sends Receives Email': { type: 'string', nullable: true },
  'Sensitive Information': { type: 'string', nullable: true },
  'Software License Agreement': { type: 'string', nullable: true },
  'Souce Code Location': { type: 'string', nullable: true },
  'Source Code Location Management': { type: 'string', nullable: true },
  'Source Code on Demand': { type: 'string', nullable: true },
  'Start Date': { type: 'string', nullable: true },
  'Supporting Application': { type: 'string', nullable: true },
  'System accessed by Non Organizational users': { type: 'string', nullable: true },
  'System Backup Location': { type: 'string', nullable: true },
  'System Classification': { type: 'string', nullable: true },
  'System Cost Per Year': { type: 'string', nullable: true },
  'System Customization': { type: 'string', nullable: true },
  'System Design Location': { type: 'string', nullable: true },
  'System Design Location Management': { type: 'string', nullable: true },
  'System Fiscal Year': { type: 'string', nullable: true },
  'System has API Gateway': { type: 'string', nullable: true },
  'System Ownership': { type: 'string', nullable: true },
  'System Support Contact Title': { type: 'string', nullable: true },
  'System Type': { type: 'string', nullable: true },
  'Test Plan Location': { type: 'string', nullable: true },
  'Test Plan Location Management': { type: 'string', nullable: true },
  'Test Plan on Demand': { type: 'string', nullable: true },
  'Test Report Location': { type: 'string', nullable: true },
  'Test Reports Location Management': { type: 'string', nullable: true },
  'Test Reports on Demand': { type: 'string', nullable: true },
  'Test Script Location': { type: 'string', nullable: true },
  'Test Script Location Management': { type: 'string', nullable: true },
  'Test Script on Demand': { type: 'string', nullable: true },
  'TLC Basic Information ID': { type: 'string', nullable: true },
  'TLC Profile Last Update Date': { type: 'string', nullable: true },
  'Uses AI Technology': { type: 'string', nullable: true },
  'Uses Machine Learning': { type: 'string', nullable: true },
  'Uses UI Interface': { type: 'string', nullable: true },
  Confidentiality: { type: 'string', nullable: true },
  Integrity: { type: 'string', nullable: true },
  Availability: { type: 'string', nullable: true },
  'Current Year OpEx': { type: 'string', nullable: true },
  'Degree of Customization': { type: 'string', nullable: true },
  'DPIA Rating': { type: 'string', nullable: true },
  'Geographical Reach': { type: 'string', nullable: true },
  'Lines of Code': { type: 'string', nullable: true },
  'Mobile Capability': { type: 'string', nullable: true },
  'Multi Language Support': { type: 'string', nullable: true },
  'Number of Contractor Support FTEs': { type: 'string', nullable: true },
  'Number of Direct System Users': { type: 'string', nullable: true },
  'Number of Federal Support FTEs': { type: 'string', nullable: true },
  'Number of Users': { type: 'string', nullable: true },
  Operational_Criticality: { type: 'string', nullable: true },
  'SCA Compliance': { type: 'string', nullable: true },
  'Legacy ID': { type: 'string', nullable: true },
  'Beneficiary Address Purpose': { type: 'string', nullable: true },
  'Last Update User': { type: 'string', nullable: true },
  'System Description': { type: 'string', nullable: true },
  'Parent Object ID': { type: 'number', nullable: true },
  Version: { type: 'string', nullable: false },
  'System Data Authoritative Source': { type: 'string', nullable: true },
  'System Data Location': { type: 'string', nullable: true },
  'Deplolyment AdHoc Frequency': { type: 'string', nullable: true },
  'Locally Stored User Info': { type: 'string', nullable: true },
  'MFA Method': { type: 'string', nullable: true },
  'MFA Other': { type: 'string', nullable: true },
  'Network Traffic Encryption Management': { type: 'string', nullable: true },
  'Data At Rest Encryption Management': { type: 'string', nullable: true },
  'No Persistent Records Flag': { type: 'string', nullable: true },
  'Records Management Disposal Location': { type: 'string', nullable: true },
  'Records Management Disposal Plan': { type: 'string', nullable: true },
  'Records Under Legal Hold': { type: 'string', nullable: true },
  'Legal Hold Case Name': { type: 'string', nullable: true },
  'System Data Location Notes': { type: 'string', nullable: true },
  'AI Project Life Cycle Stage': { type: 'string', nullable: true },
  'No Contracts Flag': { type: 'string', nullable: true },
  'No Sub Systems Flag': { type: 'string', nullable: true },
  'No URLs Flag': { type: 'string', nullable: true },
  'No Major Refresh Flag': { type: 'string', nullable: true },
  'No Planned Major Refresh Flag': { type: 'string', nullable: true },
  'System has UI': { type: 'string', nullable: true },
  'Beneficiary Information': { type: 'string', nullable: true },
  'Edit Beneficiary Information': { type: 'string', nullable: true },
  'Current System Survey': { type: 'string', nullable: true },
  'Records Management Record Type Identification': { type: 'string', nullable: true },
  'System UI Accessibility': { type: 'string', nullable: true },
  Package_ID: { type: 'number', nullable: true },
} as const;

// Type for the valid fields
export type ValidSparxSystemField = keyof typeof validSparxSystemFields;

// Helper function to check if a field is valid
export function isValidSparxSystemField(field: string): field is ValidSparxSystemField {
  return field in validSparxSystemFields;
}
