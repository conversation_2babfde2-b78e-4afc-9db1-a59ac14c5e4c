// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import { Request, Response } from 'express';
import { isError, isString } from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { tryAsync } from '../../../utils/general';
import Messages from '../../../utils/constants/messages';
import DatabaseConfigs from '../../../utils/constants/databaseConfigs';
import {
  sendError, logAppError, endpointSetup, EndpointQueryParams,
} from '../../../utils/express/responses';
import { Where } from '../../../types';
import QueryParams from '../../../utils/express/queryParams';
import { isValidSparxSystemField, ValidSparxSystemField, validSparxSystemFields } from './validFields';
import { SparxSupportViews } from '../../../utils/constants/views';

// Types.
export type SparxSystemRecord = Record<ValidSparxSystemField, string>;

export interface SystemPropertyResponse {
  propertyValue: string | number | null;
}

export interface SystemPropertyQueryParams extends EndpointQueryParams {
  systemId: string;
  propertyName: string;
}

export enum SystemPropertyMessages {
  invalid_parameter_system_id = 'The systemId parameter is required and must be a string',
  invalid_parameter_property_name = 'The propertyName parameter is required and must be a string',
  invalid_property_name_value = 'The propertyName parameter value is invalid',
  property_not_found = 'No System record found for given System ID and Property Name',
}

/**
 * Note:
 *   The OG swagger lists propertyName as optional, but no results are returned
 *   without it.
 *
 *   OG returns a 500 error with details about the SQL query for an
 *   invalid propertyName (column name).
 */
const handler = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.sparxSupport);

    try {
      const {
        systemId,
        propertyName,
      } = QueryParams.fromQuery<SystemPropertyQueryParams>(req).getAll();

      if (!isString(systemId)) {
        sendError(res, 400, { message: [SystemPropertyMessages.invalid_parameter_system_id] });
        return;
      }

      if (!isString(propertyName)) {
        sendError(res, 400, { message: [SystemPropertyMessages.invalid_parameter_property_name] });
        return;
      }

      if (!isValidSparxSystemField(propertyName)) {
        res.status(500).send({ message: [SystemPropertyMessages.invalid_property_name_value] });
        return;
      }

      // Note: these are quoted by the where clause generator.
      const whereClause: Where = {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: systemId,
          },
        },
      };

      const [error, result] = await tryAsync(
        db.queryViewTyped<SparxSystemRecord[]>(
          SparxSupportViews.Sparx_System,
          [
            `[${propertyName}]`,
          ],
          whereClause,
        ),
      );

      if (error || isError(result)) {
        app.logger.error({ error: error ?? result });
        sendError(res, 500, { message: [Messages.db_query_view_error] });
        return;
      }

      if (!Array.isArray(result) || result.length === 0 || !Array.isArray(result[0])) {
        // This indicates an issue with the query result structure or an empty outer array.
        sendError(res, 500, { message: [Messages.db_query_result_missing] });
        return;
      }

      if (result?.[0].length === 0) {
        sendError(res, 500, { message: [SystemPropertyMessages.property_not_found] });
        return;
      }

      const record = result[0][0];
      const valueRaw = record[propertyName];
      const { type } = validSparxSystemFields[propertyName];

      let value: string | number | null;

      switch (type) {
        case 'string':
        case 'number':
          value = valueRaw?.toString() ?? null;
          break;
        default:
          value = valueRaw;
      }

      const response: SystemPropertyResponse = {
        propertyValue: value,
      };

      res.status(200).send(response);
    } catch (error) {
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'systemProperty',
          action: 'find',
          error: error as Error,
        });
      }
      sendError(res, 500, { message: [Messages.internal_server_error] });
    }
  },
);

export default handler;
