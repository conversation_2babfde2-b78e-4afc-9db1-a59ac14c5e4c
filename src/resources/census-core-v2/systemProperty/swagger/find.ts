// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import { SwaggerDocumentation } from '../../../../types/swagger';

const swaggerDocs: SwaggerDocumentation = {
  path: '/gateway/System Census Core API/2.0.0/systemProperty',
  method: 'get',
  tags: ['System Census'],
  description: 'Returns a specific system property by its ID and property name.',
  parameters: [
    {
      name: 'systemId',
      in: 'query',
      description: 'The unique identifier of the system.',
      required: true,
      schema: {
        type: 'string',
        example: '11111111-2222-3333-4444-555555555555',
      },
    },
    {
      name: 'propertyName',
      in: 'query',
      description: 'The name of the property to retrieve (e.g., "AI_ENABLED").',
      required: true,
      schema: {
        type: 'string',
        example: 'AI_ENABLED',
      },
    },
  ],
  produces: ['application/json'],
  responses: {
    200: {
      description: 'Successfully retrieved system property.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              propertyValue: {
                type: 'string',
                example: 'true',
              },
              systemId: {
                type: 'string',
                example: '11111111-2222-3333-4444-555555555555',
              },
              propertyName: {
                type: 'string',
                example: 'AI_ENABLED',
              },
            },
          },
        },
      },
    },
    400: {
      description: 'Bad request - Missing or invalid parameters.',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/BadRequest',
          },
        },
      },
    },
    404: {
      description: 'Not found - No record found for the given system ID and property name.',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              message: {
                type: 'array',
                items: {
                  type: 'string',
                },
                example: ['No System record found for given System ID and Property Name'],
              },
            },
          },
        },
      },
    },
    500: {
      description: 'Internal server error.',
      content: {
        'application/json': {
          schema: {
            $ref: '#/components/schemas/ErrorResponse',
          },
        },
      },
    },
  },
};

export default swaggerDocs;
