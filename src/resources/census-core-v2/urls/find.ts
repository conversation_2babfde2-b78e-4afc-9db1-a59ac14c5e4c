import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';

// Custom.
import { censusUrlsUtil } from '../../../utils/urls';
import {
  getDb,
} from '../../../utils/db-helpers';
import MssqlData from '../../../data-sources/mssql';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/System Census Core API/2.0.0/page/Urls'
#swagger.method = 'get'
#swagger.tags = ['page']
#swagger.description = 'Retrieve the URLs that are used to access the system, for a given System ID'
#swagger.operationId: 'pageUrlsFind'
#swagger.produces = ['application/json']

#swagger.parameters['systemId'] = {
  in: 'path',
  description: 'ID of system to retrieve system component information about',
  required: true,
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.responses[200] = {
  description: 'Successfully retrieved a list of URLs associated with an object in CEDAR',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'count',
          'Url',
        ],
        properties: {
          count: {
            type: 'integer',
            example: 1
          },
          pageName: {
            type: 'string',
          },
          noURLsFlag: {
            type: 'boolean',
          },
          Urls: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                urlId: {
                  description: 'Unique key that uniquely identified the URL in database',
                  type: 'string',
                  example: '218-10-0'
                },
                'link': {
                  description: 'A valid and full system URL',
                  type: 'string'
                },
                'urlApiEndpoint': {
                  description: 'A boolean flag to indicate whether URL is an API Endpoint',
                  type: 'string',
                  example: 'Yes/No'
                },
                'urlApiWaf': {
                  description: 'A boolean flag to indicate whether application is behind a Web Application Firewall (WAF)',
                  type: 'string',
                  example: 'Yes/No'
                },
                'providesVerCodeAccess': {
                  description: 'A boolean flag to indicate Does this URL provide access to a versioned code repository?',
                  type: 'string',
                  example: 'Yes/No'
                },
                'urlHostingEnv': {
                  description: 'A string that contains What hosting environment is this URL for?',
                  type: 'string'
                }
              }
            }
          },
        },
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const pageUrlsFind = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');

  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const systemId = get(req, 'query.systemId', '');

  if (!isString(systemId) || isEmpty(systemId)) {
    const error = new Error('Please provide required parameters \'systemId\'');
    app.logger.error({ error });
    return res.status(400).send({ error: 'Please provide required parameters \'systemId\'' });
  }

  const systemIdVal = baseSchema.uuid().validate(systemId.slice(1).slice(0, -1));
  if (systemIdVal.error) {
    app.logger.error({ error: systemIdVal.error });
    return res.status(400).send({ error: 'The system ID is not valid' });
  }

  const urlsResult = await censusUrlsUtil(app, db, systemId);

  if (isError(urlsResult)) {
    app.logger.error({ error: urlsResult });
    return res.status(500).send({ error: 'There was an error fetching the URLs page' });
  }

  return res.status(200).send({
    count: urlsResult.length,
    pageName: 'Urls',
    Urls: urlsResult,
  });
};

export default pageUrlsFind;
