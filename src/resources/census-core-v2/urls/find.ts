// Modules.
import { Response, Request } from 'express';
import { get, isError } from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { getDb } from 'src/utils/db-helpers';
import MssqlData from 'src/data-sources/mssql';
import QueryParams from 'src/utils/express/queryParams';
import { censusUrlsUtil, UrlFindCedarQueryParams } from 'src/utils/urls';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/System Census Core API/2.0.0/page/Urls'
#swagger.method = 'get'
#swagger.tags = ['page']
#swagger.description = 'Retrieve the URLs that are used to access the system, for a given System ID'
#swagger.operationId: 'pageUrlsFind'
#swagger.produces = ['application/json']

#swagger.parameters['systemId'] = {
  in: 'path',
  description: 'ID of system to retrieve system component information about',
  required: true,
  type: 'string',
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}

#swagger.responses[200] = {
  description: 'Successfully retrieved a list of URLs associated with an object in CEDAR',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'count',
          'Url',
        ],
        properties: {
          count: {
            type: 'integer',
            example: 1
          },
          pageName: {
            type: 'string',
          },
          noURLsFlag: {
            type: 'boolean',
          },
          Urls: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                urlId: {
                  description: 'Unique key that uniquely identified the URL in database',
                  type: 'string',
                  example: '218-10-0'
                },
                'link': {
                  description: 'A valid and full system URL',
                  type: 'string'
                },
                'urlApiEndpoint': {
                  description: 'A boolean flag to indicate whether URL is an API Endpoint',
                  type: 'string',
                  example: 'Yes/No'
                },
                'urlApiWaf': {
                  description: 'A boolean flag to indicate whether application is behind a Web Application Firewall (WAF)',
                  type: 'string',
                  example: 'Yes/No'
                },
                'providesVerCodeAccess': {
                  description: 'A boolean flag to indicate Does this URL provide access to a versioned code repository?',
                  type: 'string',
                  example: 'Yes/No'
                },
                'urlHostingEnv': {
                  description: 'A string that contains What hosting environment is this URL for?',
                  type: 'string'
                }
              }
            }
          },
        },
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const pageUrlsFind = asyncHandler(async (
  req: Request,
  res: Response,
): Promise<void> => {
  const app = get(req, 'systemApp');
  if (!app) {
    res.status(500).send({ error: 'Unable to get the application from request' });
    return;
  }

  const db = getDb<MssqlData>(app, 'sparxea');

  if (isError(db)) {
    app.logger.error({ error: db });
    res.status(500).send({ error: 'Database Unavailable' });
    return;
  }

  const { systemId } = QueryParams.fromQuery<UrlFindCedarQueryParams>(req).getAll();
  const urlsResult = await censusUrlsUtil(app, db, systemId);

  if (isError(urlsResult)) {
    app.logger.error({ error: urlsResult });
    res.status(500).send({ error: 'There was an error fetching the URLs page' });
    return;
  }

  // Yes, the output changes if there aren't any records.
  if (!urlsResult.length) {
    res.status(200).send({
      count: 0,
      pageName: 'Urls',
    });
    return;
  }

  res.status(200).send({
    count: urlsResult.length.toString(), // Webmethods returns a string.
    // pageName: 'Urls', // Not included in Webmethods output.
    Urls: urlsResult,
  });
});

export default pageUrlsFind;
