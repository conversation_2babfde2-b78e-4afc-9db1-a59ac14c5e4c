// Generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import { User as LDAPUserType, Person, PersonFindResponse } from '../../../types';
import { person as ldapPersonSearch, personIds as ldapPersonSearchById } from '../../../subsystems/ldap';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/System Census Core API/2.0.0/person'
#swagger.method = 'get'
#swagger.tags = ['System Census']
#swagger.description = 'Retrieve a list of persons from LDAP based on query criteria. At least one parameter must be provided for the search.'
#swagger.parameters['id'] = {
  in: 'query',
  description: 'A person\'s unique identifier (e.g., UID). If provided, this will be the primary search criterion.',
  required: false,
  type: 'string',
  example: '11111111-**************-************',
  nullable: true
}
#swagger.parameters['userName'] = {
  in: 'query',
  description: 'A person\'s username (often maps to commonName or uid in LDAP).',
  required: false,
  type: 'string',
  example: 'JDOE',
  nullable: true
}
#swagger.parameters['firstName'] = {
  in: 'query',
  description: 'A person\'s first name. LDAP searches require a minimum of 2 alpha-characters.',
  required: false,
  type: 'string',
  example: 'John',
  nullable: true
}
#swagger.parameters['lastName'] = {
  in: 'query',
  description: 'A person\'s last name. LDAP searches require a minimum of 2 alpha-characters.',
  required: false,
  type: 'string',
  example: 'Doe',
  nullable: true
}
#swagger.parameters['phone'] = {
  in: 'query',
  description: 'A person\'s phone number.',
  required: false,
  type: 'string',
  example: '************',
  nullable: true
}
#swagger.parameters['email'] = {
  in: 'query',
  description: 'A person\'s email address.',
  required: false,
  type: 'string',
  example: '<EMAIL>',
  nullable: true
}
#swagger.produces = ['application/json']
#swagger.responses[200] = {
  description: 'Successful response with a list of persons',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: ['count', 'Users'],
        properties: {
          count: {
            type: 'integer',
            description: 'The number of persons found.',
            example: 1
          },
          Users: {
            type: 'array',
            description: 'A list of person objects matching the search criteria.',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'The unique identifier of the person.',
                  example: 'JDOE',
                  nullable: true
                },
                userName: {
                  type: 'string',
                  description: 'The username of the person.',
                  example: 'JDOE',
                  nullable: true
                },
                firstName: {
                  type: 'string',
                  description: 'The first name of the person.',
                  example: 'John',
                  nullable: true
                },
                lastName: {
                  type: 'string',
                  description: 'The last name of the person.',
                  example: 'Doe',
                  nullable: true
                },
                phone: {
                  type: 'string',
                  description: 'The phone number of the person.',
                  example: '************',
                  nullable: true
                },
                email: {
                  type: 'string',
                  description: 'The email address of the person.',
                  example: '<EMAIL>',
                  nullable: true
                }
              }
            }
          }
        }
      }
    }
  }
}
#swagger.responses[400] = {
  description: 'Bad request - invalid or missing search parameters.',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}
#swagger.responses[401] = {
  description: 'Unauthorized - authentication required or failed.',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}
#swagger.responses[500] = {
  description: 'Internal server error - an unexpected error occurred on the server.',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.requestBody = {
  required: false,
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {}
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

function removeEmpty(obj: object) {
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v !== ''));
}

const mapLdapUserToPerson = (ldapUser: LDAPUserType): Person => ({
  id: ldapUser.uid,
  userName: ldapUser.uid,
  firstName: ldapUser.givenName,
  lastName: ldapUser.sn,
  phone: ldapUser.telephoneNumber,
  email: ldapUser.mail,
});

const personFindList = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const id = get(req, 'query.id', null);
  const userName = get(req, 'query.userName', null);
  const firstName = get(req, 'query.firstName', null);
  const lastName = get(req, 'query.lastName', null);
  const phone = get(req, 'query.phone', null);
  const email = get(req, 'query.email', null);

  const hasId = isString(id) && id !== '';
  const hasUserName = isString(userName) && userName !== '';
  const hasFirstName = isString(firstName) && firstName !== '';
  const hasLastName = isString(lastName) && lastName !== '';
  const hasPhone = isString(phone) && phone !== '';
  const hasEmail = isString(email) && email !== '';

  if (
    !hasId && !hasUserName && !hasFirstName && !hasLastName && !hasPhone && !hasEmail
  ) {
    return res.status(400).send({ error: 'At least one search parameter (id, userName, firstName, lastName, phone, email) is required.' });
  }

  let personsRaw: LDAPUserType[] | Error;

  if (hasId) {
    personsRaw = await ldapPersonSearchById(app, id as string);
  } else {
    const personObject = removeEmpty({
      firstName: hasFirstName ? (firstName as string) : '',
      lastName: hasLastName ? (lastName as string) : '',
      commonName: hasUserName ? (userName as string) : '',
      email: hasEmail ? (email as string) : '',
      telephone: hasPhone ? (phone as string) : '',
    });

    personsRaw = await ldapPersonSearch(app, personObject);
  }

  if (isError(personsRaw)) {
    app.logger.error({ error: personsRaw });
    return res.status(400).send({ error: `Error querying LDAP: ${personsRaw.message}` });
  }

  if (isEmpty(personsRaw)) {
    return res.status(200).send({
      count: 0,
      Users: [],
    } as PersonFindResponse);
  }

  if (!Array.isArray(personsRaw)) {
    return res.status(500).send({ error: 'Unexpected LDAP query result format' });
  }

  const persons: Person[] = personsRaw.map(mapLdapUserToPerson);

  return res.status(200).send({
    count: persons.length,
    Users: persons,
  } as PersonFindResponse);
};

export {
  mapLdapUserToPerson,
  personFindList,
};
