import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import Jo<PERSON> from 'joi';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
} from '../../../utils/db-helpers';
import { getSoftwareProductsList } from '../../../utils/softwareProducts';

const pageSoftwareProductsFind = async (
  req: Request,
  res: Response,
) => {
  const baseSchema = Joi.string();
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'query.id');

  if (!isString(id) || isEmpty(id)) {
    const error = new Error('Please provide required parameters \'id\'');
    app.logger.error({ error });
    return res.status(400).send({ error: 'Please provide required parameters \'id\'' });
  }

  const idVal = baseSchema.uuid().validate(id.slice(1).slice(0, -1));
  if (idVal.error) {
    app.logger.error({ error: idVal.error });
    return res.status(400).send({ error: 'The system ID is not valid' });
  }

  const softwareProductsListCall = await getSoftwareProductsList(app, db, id);

  if (isError(softwareProductsListCall)) {
    app.logger.error({ error: softwareProductsListCall });
    return res.status(500).send({ error: softwareProductsListCall.message });
  }
  return res.status(200).send(softwareProductsListCall);
};

export default pageSoftwareProductsFind;
