// Modules.
import { Response, Request } from 'express';
import { isString } from 'lodash';
import asyncHandler from 'express-async-handler';

// Custom.
import { getSoftwareProductsList } from 'src/utils/softwareProducts';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import {
  endpointSetup, logAppError, EndpointQueryParams, SendError,
} from 'src/utils/express/responses';
import QueryParams from 'src/utils/express/queryParams';
import Messages from 'src/utils/constants/messages';
import { UnitTestForcedExceptionError } from 'src/utils/express/errors';
import { unknownAsError } from 'src/utils/general';

enum SoftwareProductsMessages {
  invalid_parameter_system_id = 'The systemId parameter is required and must be a string',
}

interface SoftwareProductsQueryParams extends EndpointQueryParams {
  id: string;
}

const qsValidate = (p: SoftwareProductsQueryParams): void => {
  if (!isString(p.id) || p.id.trim() === '') {
    throw new Error(SoftwareProductsMessages.invalid_parameter_system_id);
  }

  // Yep, webmethods returns an empty object if the id isn't found,
  // throw new Error(SoftwareProductsMessages.invalid_uuid_format);
};

const handler = asyncHandler(
  async (req: Request, res: Response): Promise<void> => {
    const { app, db } = endpointSetup(req, DatabaseConfigs.sparxSupport);
    const sender = new SendError(app, res);

    try {
      const params = QueryParams.fromQuery<SoftwareProductsQueryParams>(req).getAll();

      try {
        qsValidate(params);
      } catch (error) {
        const e = unknownAsError(error);
        sender.generic(400, { message: [e.message] });
        return;
      }

      const { id } = params;

      try {
        const result = await getSoftwareProductsList(db, id);

        res.status(200).send(result ?? {});
      } catch (e) {
        // Unit testing.
        if (e instanceof UnitTestForcedExceptionError) {
          // noinspection ExceptionCaughtLocallyJS
          throw e;
        }
        sender.database(500, { message: [Messages.db_query_view_error] }, e);
      }
    } catch (error) {
      if (app) {
        logAppError(app, {
          package: 'census-core-v2',
          service: 'softwareProducts',
          action: 'find',
          error: error as Error,
        });
      }

      sender.generic(500, { message: [Messages.internal_server_error] });
    }
  },
);

export {
  SoftwareProductsMessages,
  SoftwareProductsQueryParams,
};

export default handler;
