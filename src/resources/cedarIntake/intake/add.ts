import { Request, Response } from 'express';
import {
  get,
  attempt,
  isError,
  isEmpty,
} from 'lodash';
import { XMLParser } from 'fast-xml-parser';
import MssqlData from '../../../data-sources/mssql';
import { StoredProcedureParam, StoredProcedureData, StoredProcedureQuery } from '../../../types';
import { getDb, SP_INSERT_INTAKE } from '../../../utils/db-helpers';
import { strToBool, tryAsync } from '../../../utils/general';
import { getSchema, validateWithSchema } from '../../../utils/intake-schema';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/CEDARIntake/1.0/intake'
#swagger.method = 'post'
#swagger.tags = ['intake']
#swagger.description = 'Add an intake'
#swagger.produces = ['application/json']

#swagger.parameters['CLIENT_NAME'] = {
  in: 'header',
  description: 'The client name to associate to the request',
  required: true,
  type: 'string',
  example: 'EASi'
}
#swagger.parameters['validatePayload'] = {
  in: 'query',
  description: 'Determines if schema validation of the payload is performed synchronously before persisting the record or asynchronously after the record has been persisted',
  schema: {
    type: 'string',
    '@enum': ['true', 'false'],
  },
  example: 'false'
}

#swagger.requestBody = {
  required: true,
  content: {
    "application/json": {
      schema: {
        type: 'object',
        required: [
          'schema',
          'clientId',
          'clientStatus',
          'body',
          'bodyFormat',
          'type',
          'version',
          'clientCreatedDate'
        ],
        properties: {
          body: {
            type: 'string',
            description: 'The encoded, string representation of the object being transmitted'
          },
          bodyFormat: {
            type: 'string',
            '@enum': [
              'JSON',
              'XML'
            ],
            example: 'JSON'
          },
          clientCreatedDate: {
            type: 'string',
            description: 'Creation date associated with the object being transmitted',
            format: 'date-time'
          },
          clientId: {
            type: 'string',
            description: 'Unqiue ID associated with the object in body'
          },
          clientLastUpdatedDate: {
            type: 'string',
            description: 'Last update date associated with the object being transmitted',
            format: 'date-time'
          },
          clientStatus: {
            type: 'string',
            description: 'Client\'s status associated with the object being transmitted, i.e. Initiated, Final, etc.'
          },
          schema: {
            type: 'string',
            description: 'The name and version of the schema associated with the object being transmitted, i.e. SystemIntake_v01'
          },
          type: {
            type: 'string',
            description: 'The type of object being transmitted, i.e. SystemIntake, BusinessCase, etc'
          },
          version: {
            type: 'string',
            description: 'The version associated with the object in the body. This value can be incremented in the event a transaction needs to be resubmitted.'
          }
        }
      }
    }
  }
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Ok',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'result',
          'message'
        ],
        properties: {
          message: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          result: {
            type: 'string'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const add = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  // * Get CLIENT_NAME header
  const clientName = req.get('CLIENT_NAME');
  if (!clientName) {
    app.logger.error({ error: new Error('Client name not provided in the header') });
    return res.status(400).send({ error: 'HTTP Headers missing CLIENT_NAME' });
  }

  // * Validate POST body
  const reqBody = get(req, 'body', {});
  if (isEmpty(reqBody)) {
    app.logger.error({ error: new Error('No body provided to the request') });
    return res.status(400).send({ error: 'No body provided to the request' });
  }

  // * Get schema from body
  const schemaName = get(reqBody, 'schema');
  if (!schemaName) {
    app.logger.error({ error: new Error('No schema provided') });
    return res.status(400).send({ error: 'No schema provided' });
  }

  // * Validate the schema is an accepted one
  // * Get body from body
  const body = get(reqBody, 'body');
  if (!body) {
    app.logger.error({ error: new Error('No body provided') });
    return res.status(400).send({ error: 'No body provided' });
  }

  // * Get bodyFormat from body
  const bodyFormat = get(reqBody, 'bodyFormat');
  if (!bodyFormat) {
    app.logger.error({ error: new Error('No bodyFormat provided') });
    return res.status(400).send({ error: 'No bodyFormat provided' });
  }

  const validBodyFormats = ['JSON', 'XML'];
  if (!validBodyFormats.includes(bodyFormat)) {
    app.logger.error({ error: new Error('Invalid bodyFormat provided') });
    return res.status(400).send({ error: 'Invalid bodyFormat provided' });
  }

  // * Parse body with JSON or XML based on bodyFormat
  let parsedBody: unknown;
  if (bodyFormat === 'JSON') {
    const parsedJson = attempt(JSON.parse.bind(null, body));
    if (isError(parsedJson)) {
      app.logger.error({ error: new Error('The body is not valid JSON') });
      return res.status(400).send({ error: 'The body is not valid JSON' });
    }
    parsedBody = parsedJson;
  }

  if (bodyFormat === 'XML') {
    try {
      const parser = new XMLParser();
      parsedBody = parser.parse(body);
      if (isEmpty(parsedBody)) {
        throw new Error('No parsable XML data');
      }
    } catch (error) {
      app.logger.error({ error });
      return res.status(400).send({ error: 'The body is not valid XML' });
    }
  }

  // * Validate schema against body using the schema version
  const validatePayloadString = get(req, 'query.validatePayload', 'true') as string;
  const validatePayload = strToBool(validatePayloadString);
  if (validatePayload) {
    const schemaObject = await getSchema(app, schemaName);
    if (isError(schemaObject)) {
      app.logger.error({ error: schemaObject });
      return res.status(500).send({ error: 'Unable to retrieve validation schema' });
    }

    const schema = get(schemaObject, 'schema', '');

    const validPayload = await validateWithSchema(app, schema, parsedBody);
    if (isError(validPayload)) {
      app.logger.error({ error: validPayload });
      return res.status(500).send({ error: 'An error occurred while setting up the schema validator' });
    }
  }

  // get clientId from reqBody
  const clientId = get(reqBody, 'clientId');
  if (!clientId) {
    app.logger.error({ error: new Error('No client ID provided') });
    return res.status(400).send({ error: 'No client ID provided' });
  }

  // get type from reqBody
  const type = get(reqBody, 'type');
  if (!type) {
    app.logger.error({ error: new Error('No type provided') });
    return res.status(400).send({ error: 'No type provided' });
  }

  // get clientStatus from reqBody
  const clientStatus = get(reqBody, 'clientStatus');
  if (!clientStatus) {
    app.logger.error({ error: new Error('No client status provided') });
    return res.status(400).send({ error: 'No client status provided' });
  }

  // get clientCreatedDate from reqBody
  const clientCreatedDate = get(reqBody, 'clientCreatedDate');
  if (!clientCreatedDate) {
    app.logger.error({ error: new Error('No client created date provided') });
    return res.status(400).send({ error: 'No client created date provided' });
  }

  // get clientLastUpdatedDate from reqBody
  const clientLastUpdatedDate = get(reqBody, 'clientLastUpdatedDate');
  if (!clientLastUpdatedDate) {
    app.logger.error({ error: new Error('No client last updated date provided') });
    return res.status(400).send({ error: 'No client last updated date provided' });
  }

  // get version from reqBody
  const version = get(reqBody, 'version');
  if (!version) {
    app.logger.error({ error: new Error('No version provided') });
    return res.status(400).send({ error: 'No version provided' });
  }

  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];
  const queryData: StoredProcedureData[] = [{
    name: 'ID',
    value: 'result',
    isOutput: true,
  }, {
    name: 'CLIENT_ID',
    value: clientId,
  }, {
    name: 'CLIENT_NAME',
    value: clientName,
  }, {
    name: 'REQUEST_TYPE',
    value: type,
  }, {
    name: 'REQUEST_TYPE_SCHEMA',
    value: schemaName,
  }, {
    name: 'REQUEST_CLIENT_STATUS',
    value: clientStatus,
  }, {
    name: 'REQUEST_CEDAR_STATUS',
    value: 'New',
  }, {
    name: 'REQUEST_DATA_FORMAT',
    value: bodyFormat,
  }, {
    name: 'CLIENT_CREATED_DATE',
    value: clientCreatedDate,
  }, {
    name: 'CLIENT_LAST_UPDATED_DATE',
    value: clientLastUpdatedDate,
  }, {
    name: 'CLIENT_VERSION',
    value: version,
  }];
  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  // * Insert into the temp table
  const [error, result] = await tryAsync(
    db.queryStoredProcedures(SP_INSERT_INTAKE, queryParams, queryData, queryResults),
  );

  if (error || isError(result)) {
    app.logger.error({ error: error || result });
    return res.status(500).send({ error: 'An error occurred inserting the intake request' });
  }

  const queryStatus = get(result, '[0][0].queryStatus');
  if (queryStatus !== 0) {
    const invalidInsertError = new Error('Unable to insert the intake');
    app.logger.error({ error: invalidInsertError });
    return res.status(500).send({ error: invalidInsertError.message });
  }

  const queryResult = get(result, '[0][0].result');
  return res.status(200).send({
    result: 'success',
    message: [
      `Record successfully persisted. Cedar ID: ${queryResult}`,
    ],
  });
};

export default add;
