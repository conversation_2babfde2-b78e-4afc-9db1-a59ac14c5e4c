import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
} from 'lodash';
import MssqlData from '../../../../data-sources/mssql';
import {
  getDb,
  INTAKE_REQUEST,
} from '../../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../../utils/general';
import {
  AndObject,
  Where,
} from '../../../../types';

const intakeStatusByClientId = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'cedarSupport');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'params.id', '');
  const clientStatus = get(req, 'query.clientStatus' as string, '');
  const version = get(req, 'query.version' as string, '');
  const clientName = req.get('CLIENT_NAME');

  if ((!isString(id) || isEmpty(id)) || !clientName || !clientStatus || !version) {
    app.logger.error({ error: new Error('Missing required parameters: id, CLIENT_NAME, clientStatus, version') });
    return res.status(400).send({ error: 'Missing required parameters: id, CLIENT_NAME, clientStatus, version' });
  }

  const whereObj: Where = {
    where: {
      operation: {
        column: 'CLIENT_ID',
        operator: '=',
        value: id,
      },
    },
  };

  whereObj.and = [];
  const andObjClientStatus: AndObject = {
    operation: {
      column: 'REQUEST_CLIENT_STATUS',
      operator: '=',
      value: clientStatus,
    },
  };
  whereObj.and.push(andObjClientStatus);

  const andObjClientVersion: AndObject = {
    operation: {
      column: 'CLIENT_VERSION',
      operator: '=',
      value: version,
    },
  };
  whereObj.and.push(andObjClientVersion);

  const andObjClientName: AndObject = {
    operation: {
      column: 'CLIENT_NAME',
      operator: '=',
      value: clientName,
    },
  };
  whereObj.and.push(andObjClientName);

  const [viewError, viewResult] = await tryAsync(db.queryView(INTAKE_REQUEST, [
    'CAST("INTAKE_REQUEST_ID" AS VARCHAR) as cedarId',
    '"CLIENT_ID" as clientId',
    '"CLIENT_VERSION" as version',
    '"REQUEST_CEDAR_STATUS" as cedarStatus',
    // This next line hard codes '' for the field for backwards compatibility
    '\'\' as cedarStatusMessage',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve intake status by Client Id' });
  }

  const viewResultError = get(viewResult, '[1]');
  if (viewResultError === 0) {
    app.logger.error({ error: new Error('Intake could not be found with those parameters') });
    return res.status(400).send({
      result: 'error',
      message: [
        'Intake could not be found with those parameters',
      ],
    });
  }

  const systemIntake = get(viewResult, '[0]', []).map((item: object) => ({ ...item }));

  return res.status(200).send(systemIntake[0]);
};

export default intakeStatusByClientId;
