import { Request, Response } from 'express';
import { isError } from 'lodash';
import health from './healthCheck';
import intake from './intake';
import intakeByCedarId from './intakeFind';
import intakeByClientId from './clientFind';
import intakeStatusByCedarId from './status/cedarStatus';
import intakeStatusByClientId from './status/clientStatus';
import intakeStatusList from './statusList';
import { GetResourceConfig } from '../../types';
import { prependPathToResources } from '../../utils/resources';

const cedarIntake = (_: Request, res: Response) => {
  res.status(200).send({ message: 'CEDAR Intake root' });
};

const updatedHealthCheck = prependPathToResources('/healthCheck', health());
if (isError(updatedHealthCheck)) {
  throw new Error('An error occurred updating the path for healthCheck');
}

const updatedIntake = prependPathToResources('/intake', intake());
if (isError(updatedIntake)) {
  throw new Error('An error occurred updating the path for healthCheck');
}

const updatedIntakeByCedarId = prependPathToResources('/intake/cedar', intakeByCedarId());
if (isError(updatedIntakeByCedarId)) {
  throw new Error('An error occurred updating the path for intakeByCedarId');
}

const updatedIntakeByClientId = prependPathToResources('/intake/client', intakeByClientId());
if (isError(updatedIntakeByClientId)) {
  throw new Error('An error occurred updating the path for intakeByCedarId');
}

const updatedIntakeStatusByCedarId = prependPathToResources('/intake/status/cedar', intakeStatusByCedarId());
if (isError(updatedIntakeStatusByCedarId)) {
  throw new Error('An error occurred updating the path for intakeStatusByCedarId');
}

const updatedIntakeStatusByClientId = prependPathToResources('/intake/status/client', intakeStatusByClientId());
if (isError(updatedIntakeStatusByClientId)) {
  throw new Error('An error occurred updating the path for intakeStatusByClientId');
}

const updatedIntakeStatusList = prependPathToResources('/intake/status', intakeStatusList());
if (isError(updatedIntakeStatusList)) {
  throw new Error('An error occurred updating the path for intakeStatusByClientId');
}

const getResourceConfig: GetResourceConfig = () => [
  // FIXME: Remove once core is complete
  {
    name: 'get-cedar-intake',
    path: '/',
    method: 'get',
    resource: cedarIntake,
  },
  ...updatedHealthCheck,
  ...updatedIntake,
  ...updatedIntakeByCedarId,
  ...updatedIntakeByClientId,
  ...updatedIntakeStatusByCedarId,
  ...updatedIntakeStatusByClientId,
  ...updatedIntakeStatusList,
];

export default getResourceConfig;
