import {
  Response,
  Request,
} from 'express';
import {
  attempt,
  get,
  isError,
  map,
  pick,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  StoredProcedureParam,
  StoredProcedureData,
  StoredProcedureQuery,
  Where,
} from '../../../types';
import {
  getDb,
  INTAKE_REQUEST,
  SP_GET_EASIINTAKE_BY_STATUS_PAGINATED,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';

/*
#swagger.start
#swagger.path = '/gateway/CEDARIntake/1.0/intake/status'
#swagger.method = 'get'
#swagger.tags = ['Intake']
#swagger.description = 'Intake Status list'
#swagger.parameters['cedarStatus'] = {
  in: 'query',
  required: true,
  description: 'CEDAR status describing the outcome of validation, and  mapping of the payload',
  type: 'string',
  schema: {
    type: 'string',
    '@enum':[
      'New',
      'Error',
      'In Process',
      'Processed'
  ],
  },
  example: '{550e8400-e29b-41d4-a716-446655440000}'
}
#swagger.parameters['clientCreatedStartDate'] = {
  in: 'query',
  required: false,
  description: 'The starting point searching for records using the clientCreatedDate value.',
  type: 'string',
  example: '1900-01-01 00:00:00.001'
}
#swagger.parameters['pageSize'] = {
  in: 'query',
  required: false,
  description: 'Number of results to return per page. If used, pageNumber must also be provided.',
  type: 'string',
  example: '6'
}
  #swagger.parameters['pageNumber'] = {
  in: 'query',
  required: false,
  description: 'Page number to return. If used, pageSize must also be provided.',
  type: 'string',
  example: '6'
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'System Intakes found',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          count: {
            type: 'integer'
          },
          Statuses: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                cedarId: {
                  type: 'string',
                  description: 'Unique ID associated with the object in body'
                },
                clientId: {
                  type: 'string',
                  description: 'Unique ID assigned by CEDAR'
                },
                version: {
                  type: 'string',
                  description: 'The version associated with the object in the body.
                  This value can be incremented in the event a transaction needs to be resubmitted.'
                },
                cedarStatus: {
                  type: 'string',
                  description: 'CEDAR status describing the outcome of parsing, validation,
                  and mapping of the payload'
                },
                cedarStatusMessage: {
                  type: 'string',
                  description: 'Message related to errors with the parsing, validation,
                  and mapping of the payload (if applicable)'
                }
              }
            }
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/

const intakeStatusList = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const cedarStatus = get(req, 'query.cedarStatus' as string, '');
  const pageSize = get(req, 'query.pageSize' as string, '');
  const pageNumber = get(req, 'query.pageNumber' as string, '');
  const clientCreatedStartDate = get(req, 'query.clientCreatedStartDate' as string, '1900-01-01 00:00:00.001');

  if (!cedarStatus) {
    app.logger.error({ error: new Error('cedarStatus is required') });
    return res.status(400).send({ error: 'cedarStatus is required' });
  }

  if ((pageSize && !pageNumber) || (!pageSize && pageNumber)) {
    app.logger.error({ error: new Error('pageSize and pageNumber must be used in conjunction') });
    return res.status(400).send({ error: 'pageSize and pageNumber must be used in conjunction' });
  }

  // No pagination requested,just query by status and date
  if (!pageSize) {
    const db = getDb<MssqlData>(app, 'cedarSupport');
    if (isError(db)) {
      app.logger.error({ error: db });
      return res.status(500).send({ error: 'Database Unavailable' });
    }

    const whereObj: Where = {
      where: {
        operation: {
          column: 'REQUEST_CEDAR_STATUS',
          operator: '=',
          value: cedarStatus,
        },
      },
      and: [{
        operation: {
          column: 'CLIENT_CREATED_DATE',
          operator: '>=',
          value: clientCreatedStartDate,
        },
      }],
    };

    const [viewError, viewResult] = await tryAsync(db.queryView(INTAKE_REQUEST, [
      'CAST("INTAKE_REQUEST_ID" AS VARCHAR) as cedarId',
      '"CLIENT_ID" as clientId',
      '"CLIENT_VERSION" as version',
      '"REQUEST_CEDAR_STATUS" as cedarStatus',
      // This next line hard codes '' for the field for backwards compatibility
      '\'\' as cedarStatusMessage',
    ], whereObj));

    if (viewError || isError(viewResult)) {
      app.logger.error({ error: viewError || viewResult });
      return res.status(500).send({ error: 'Unable to retrieve intake status' });
    }

    const systemIntake = get(viewResult, '[0]', []).map((item: object) => ({ ...item }));
    return res.status(200).send({
      count: systemIntake.length,
      Statuses: systemIntake,
    });
  }
  const db = getDb<MssqlData>(app, 'sparxea');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }
  const intPageNumber = Number(pageNumber) - 1;
  const offSet = intPageNumber * pageSize;
  const queryParams: StoredProcedureParam[] = [{
    name: 'result',
    type: 'nvarchar',
    param: 'max',
  }];
  const queryData: StoredProcedureData[] = [{
    name: 'Outputjson',
    value: 'result',
    isOutput: true,
  },
  {
    name: 'request_cedar_status',
    value: cedarStatus,
  },
  {
    name: 'client_created_date',
    value: clientCreatedStartDate,
  },
  {
    name: 'offset',
    value: offSet.toString(),
  },
  {
    name: 'num_rows',
    value: pageSize,
  },
  ];
  const queryResults: StoredProcedureQuery[] = [{
    resultKey: 'result',
    paramName: 'result',
  }];

  const spQuery = SP_GET_EASIINTAKE_BY_STATUS_PAGINATED;

  const [intakeStatusListError, intakeStatusListResults] = await tryAsync(
    db.queryStoredProcedures(spQuery, queryParams, queryData, queryResults),
  );

  if (intakeStatusListError || isError(intakeStatusListResults)) {
    app.logger.error({ error: intakeStatusListError || intakeStatusListResults });
    return res.status(500).send({ message: 'Unable to retrieve the intake list' });
  }

  const intakeStatusListQueryStatus: number = get(intakeStatusListResults, '[0][0].queryStatus', -1);
  if (intakeStatusListQueryStatus === -1) {
    return res.status(500).send({ message: 'Status of the query was invalid' });
  }

  if (intakeStatusListQueryStatus === 1) {
    return res.status(200).send({});
  }

  const intakeStatusListResult = get(intakeStatusListResults, '[0][0].result');
  if (!intakeStatusListResult) {
    return res.status(500).send({ message: 'Unable to get get result from query' });
  }

  const parsedintakeStatusListResults = attempt(JSON.parse.bind(null, intakeStatusListResult));
  if (isError(parsedintakeStatusListResults)) {
    return res.status(500).send({ message: 'Unable to parse database response' });
  }
  const intakes = get(parsedintakeStatusListResults, 'ResultSet', []);
  const idIntakes = map(intakes, (value) => pick(value, ['cedarId', 'name']));

  return res.status(200).send({
    count: idIntakes.length,
    Statuses: intakes,
  });
};
export default intakeStatusList;
