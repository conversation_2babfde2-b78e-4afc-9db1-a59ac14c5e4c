import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import MssqlData from '../../../data-sources/mssql';
import {
  getDb,
  INTAKE_REQUEST,
} from '../../../utils/db-helpers';
import {
  tryAsync,
  renameKeys,
} from '../../../utils/general';
import {
  AndObject,
  Where,
} from '../../../types';

const intakeByCedarId = async (
  req: Request,
  res: Response,
) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<MssqlData>(app, 'cedarSupport');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const id = get(req, 'params.id', '');

  const clientName = req.get('CLIENT_NAME');

  if (!clientName) {
    app.logger.error({ msg: 'Missing CLIENT_NAME' });
    return res.status(400).send({ error: 'HTTP Headers missing CLIENT_NAME' });
  }

  const whereObj: Where = {
    where: {
      operation: {
        column: 'INTAKE_REQUEST_ID',
        operator: '=',
        value: id,
      },
    },
  };

  whereObj.and = [];
  const andObj: AndObject = {
    operation: {
      column: 'CLIENT_NAME',
      operator: '=',
      value: clientName,
    },
  };
  whereObj.and.push(andObj);

  const [viewError, viewResult] = await tryAsync(db.queryView(INTAKE_REQUEST, [
    'CAST("INTAKE_REQUEST_ID" AS VARCHAR) as cedarId',
    '"CLIENT_ID" as clientId',
    '"CLIENT_VERSION" as version',
    '"REQUEST_TYPE" as type',
    '"REQUEST_CEDAR_STATUS" as cedarStatus',
    // This nextlinehardcodes'' for the fieldforbackwards compatability
    '\'\' as cedarStatusMessage',
    '"REQUEST_CLIENT_STATUS" as clientStatus',
    '"REQUEST_TYPE_SCHEMA" as clientschema',
    '"REQUEST_DATA_FORMAT" as bodyFormat',
    '"REQUEST_DATA" as body',
    '"CLIENT_CREATED_DATE" as clientCreatedDate',
    '"CLIENT_LAST_UPDATED_DATE" as clientLastUpdatedDate',
    '"CREATED_DATE" as cedarCreatedDate',
    '"LAST_UPDATED_DATE" as cedarLastUpdatedDate',
  ], whereObj));

  if (viewError || isError(viewResult)) {
    app.logger.error({ error: viewError || viewResult });
    return res.status(500).send({ error: 'Unable to retrieve intake by cedar ID' });
  }

  const viewResultError = get(viewResult, '[1]');
  if (viewResultError === 0) {
    return res.status(400).send({
      result: 'error',
      message: [
        'Intake could not be found with that CEDAR ID',
      ],
    });
  }

  const systemIntake = get(viewResult, '[0]', []).map((item: object) => ({ ...item }));

  const mappedSystem = renameKeys(systemIntake, [{
    oldKey: 'clientschema',
    newKey: 'schema',
  }]);

  return res.status(200).send(mappedSystem[0]);
};

export default intakeByCedarId;
