import { Request, Response } from 'express';
import { GetResourceConfig } from '../../types';

/*
#swagger.start
#swagger.path = '/'
#swagger.method = 'get'
#swagger.tags = ['Health']
#swagger.description = 'Health check endpoint'
#swagger.produces = ['application/json']
#swagger.responses['200'] = {
  description: 'Health check OK'
  content: {
    'application/json': {
      schema: {
        type: 'object',
        properties: {
          message: {
            type: string
          },
        },
      }
    }
  }
}
#swagger.end
*/
const health = (_: Request, res: Response) => {
  res.status(200).json({ message: 'Server is healthy' });
};

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-health',
  path: '/',
  method: 'get',
  resource: health,
  public: true,
}];

export default getResourceConfig;
export {
  health,
};
