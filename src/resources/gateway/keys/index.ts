// /gateway/keys
import list from './list';
import create from './create';
import remove from './remove';
import { GetResourceConfig } from '../../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-gateway-key-list',
  path: '/',
  method: 'get',
  resource: list,
}, {
  name: 'post-gateway-key-create',
  path: '/',
  method: 'post',
  resource: create,
}, {
  name: 'delete-gateway-key-remove',
  path: '/:id',
  method: 'delete',
  resource: remove,
}];

export default getResourceConfig;
