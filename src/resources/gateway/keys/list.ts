import { Response, Request } from 'express';
import {
  get,
  isError,
} from 'lodash';
import PgData from '../../../data-sources/pg';
import { getDb, GATEWAY } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';

const list = async (req: Request, res: Response) => {
  const authMethod = get(req, 'authMethod', 'none');
  if (authMethod !== 'jwt') {
    return res.status(401).send({ error: 'Invalid authorization method' });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const [critError, results] = await tryAsync(db.findAll(GATEWAY));
  if (critError || isError(results)) {
    return res.status(500).send({ error: 'An error occurred while receiving the gateway keys' });
  }

  return res.status(200).send({ results });
};

export default list;
