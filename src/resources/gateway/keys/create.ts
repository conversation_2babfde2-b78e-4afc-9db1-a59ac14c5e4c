import { Response, Request } from 'express';
import {
  get,
  isError,
} from 'lodash';
import { createKey } from '../../../subsystems/authentication/gateway';

/*
#swagger.start
#swagger.path = '/gateway/keys'
#swagger.method = 'post'
#swagger.tags = ['Gateway']
#swagger.security = [{
  "JWTKey": []
}]
#swagger.produces = ['application/json']
#swagger.end
*/
const create = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const authMethod = get(req, 'authMethod', 'none');
  if (authMethod !== 'jwt') {
    return res.status(401).send({ error: 'Invalid authorization method' });
  }

  // get user
  const user = get(req, 'user');
  if (!user) {
    return res.status(401).send({ error: 'Invalid user' });
  }

  const key = await createKey(app, user);

  if (isError(key)) {
    return res.status(500).send({ error: 'An error occurred creating a gateway key' });
  }

  return res.status(200).send({ key });
};

export default create;
