import { Response, Request } from 'express';
import {
  get,
  isError,
} from 'lodash';
import { removeKey } from '../../../subsystems/authentication/gateway';

const remove = async (req: Request, res: Response) => {
  const authMethod = get(req, 'authMethod', 'none');
  if (authMethod !== 'jwt') {
    return res.status(401).send({ error: 'Invalid authorization method' });
  }

  const id = get(req, 'params.id');
  if (!id) {
    return res.status(400).send({ error: 'No gateway key provided' });
  }

  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const user = get(req, 'user');
  if (!user) {
    return res.status(401).send({ error: 'Invalid user' });
  }

  const key = await removeKey(app, id);

  if (isError(key)) {
    return res.status(500).send({ error: 'An error occurred creating a gateway key' });
  }

  return res.status(204).send();
};

export default remove;
