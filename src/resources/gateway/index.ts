import { isError } from 'lodash';
import keys from './keys';
import globalVariables from './global-variables';
import intakeSchema from './intake-schema';
import { GetResourceConfig } from '../../types';
import { prependPathToResources } from '../../utils/resources';

const updatedKeys = prependPathToResources('/keys', keys());
if (isError(updatedKeys)) {
  throw new Error('An error occurred updating the path for gateway keys');
}

const updatedGlobalVariables = prependPathToResources('/globalVariables', globalVariables());
if (isError(updatedGlobalVariables)) {
  throw new Error('An error occurred updating the path for global variables');
}

const updatedIntakeSchema = prependPathToResources('/intakeSchema', intakeSchema());
if (isError(updatedIntakeSchema)) {
  throw new Error('An error occurred updating the path for intake schema');
}

const getResourceConfig: GetResourceConfig = () => [
  ...updatedKeys,
  ...updatedGlobalVariables,
  ...updatedIntakeSchema,
];

export default getResourceConfig;
