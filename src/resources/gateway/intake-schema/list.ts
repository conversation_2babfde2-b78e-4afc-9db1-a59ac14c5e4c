import { Request, Response } from 'express';
import { get, isError } from 'lodash';
import PgData from '../../../data-sources/pg';
import { getDb, INTAKE_SCHEMA } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/intake-schema'
#swagger.method = 'get'
#swagger.tags = ['Gateway - Intake Schema']
#swagger.description = 'Find all intake schemas'
#swagger.produces = ['application/json']

#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Ok',
  content: {
    'application/json': {
      schema: {
        type: 'array',
        items: {
          type: 'object',
          required: [
            'id',
            'name',
            'schema',
            'createdBy',
            'createdDate',
            'updatedBy',
            'updatedDate'
          ],
          properties: {
            id: {
              type: 'number',
              example: 1
            },
            name: {
              type: 'string',
              example: 'SampleSchema'
            },
            schema: {
              type: 'string',
              example: '{\"foo\":\"bar\"}'
            },
            createdBy: {
              type: 'string',
              example: 'TST1'
            },
            createdDate: {
              type: 'string',
              example: '2025-03-19T14:36:00.000Z'
            },
            updatedBy: {
              type: 'string',
              example: 'TST2'
            },
            updatedDate: {
              type: 'string',
              example: '2025-03-20T14:36:00.000Z'
            }
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const list = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const [error, result] = await tryAsync(db.findAll(INTAKE_SCHEMA));
  if (error || isError(result)) {
    app.logger.error({ error: error || result });
    return res.status(500).send({ error: 'An error occurred while retrieving the schemas' });
  }

  return res.status(200).send({
    schemas: result,
  });
};

export default list;
