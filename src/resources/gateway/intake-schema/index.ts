import list from './list';
import byId from './by-id';
import create from './create';
import update from './update';
import remove from './remove';
import { GetResourceConfig } from '../../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-intake-schema-list',
  path: '/',
  method: 'get',
  resource: list,
}, {
  name: 'get-intake-schema-by-id',
  path: '/:id',
  method: 'get',
  resource: byId,
}, {
  name: 'post-intake-schema-create',
  path: '/',
  method: 'post',
  resource: create,
}, {
  name: 'put-intake-schema-update',
  path: '/:id',
  method: 'put',
  resource: update,
}, {
  name: 'delete-intake-schema-remove',
  path: '/:id',
  method: 'delete',
  resource: remove,
}];

export default getResourceConfig;
