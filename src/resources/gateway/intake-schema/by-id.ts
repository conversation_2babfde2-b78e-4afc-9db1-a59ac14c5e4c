import { Request, Response } from 'express';
import {
  get,
  isString,
  isEmpty,
  isError,
} from 'lodash';
import { getSchema } from '../../../utils/intake-schema';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/intake-schema/{id}'
#swagger.method = 'get'
#swagger.tags = ['Gateway - Intake Schema']
#swagger.description = 'Find an intake schema by name'
#swagger.produces = ['application/json']

#swagger.parameters['id'] = {
  in: 'path',
  description: 'The name of the schema',
  required: true,
  type: 'string',
  example: 'SampleSchema'
}

#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Ok',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'id',
          'name',
          'schema',
          'createdBy',
          'createdDate',
          'updatedBy',
          'updatedDate'
        ],
        properties: {
          id: {
            type: 'number',
            example: 1
          },
          name: {
            type: 'string',
            example: 'SampleSchema'
          },
          schema: {
            type: 'string',
            example: '{\"foo\":\"bar\"}'
          },
          createdBy: {
            type: 'string',
            example: 'TST1'
          },
          createdDate: {
            type: 'string',
            example: '2025-03-19T14:36:00.000Z'
          },
          updatedBy: {
            type: 'string',
            example: 'TST2'
          },
          updatedDate: {
            type: 'string',
            example: '2025-03-20T14:36:00.000Z'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[404] = {
  description: 'Not Found',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/NotFound'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const byId = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const name = get(req, 'params.id');
  if (!isString(name) || isEmpty(name)) {
    app.logger.error({ error: new Error('No name provided') });
    return res.status(400).send({ error: 'No name provided' });
  }

  const schema = await getSchema(app, name);
  if (isError(schema)) {
    app.logger.error({ error: schema });
    return res.status(500).send({ error: 'An error occurred while searching for the schema of this name' });
  }

  return res.status(200).send(schema);
};

export default byId;
