import { Request, Response } from 'express';
import {
  get,
  isString,
  isEmpty,
  isError,
} from 'lodash';
import PgData from '../../../data-sources/pg';
import { getDb, INTAKE_SCHEMA } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';
import { getSchema } from '../../../utils/intake-schema';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/intake-schema/{id}'
#swagger.method = 'delete'
#swagger.tags = ['Gateway - Intake Schema']
#swagger.description = 'Delete an intake schema by name'
#swagger.produces = ['application/json']

#swagger.parameters['id'] = {
  in: 'path',
  description: 'The name of the schema',
  required: true,
  type: 'string',
  example: 'SampleSchema'
}

#swagger.produces = ['application/json']

#swagger.responses[204] = {
  description: 'No Content',
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[404] = {
  description: 'Not Found',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/NotFound'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const remove = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const name = get(req, 'params.id');
  if (!isString(name) || isEmpty(name)) {
    app.logger.error({ error: new Error('No name provided') });
    return res.status(400).send({ error: 'No name provided' });
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const dupCheck = await getSchema(app, name);
  if (isError(dupCheck) && dupCheck.message !== 'No result returned') {
    app.logger.error({ error: dupCheck });
    return res.status(500).send({ error: 'An error occurred while checking for a duplicate schema of this name' });
  }

  if (!dupCheck) {
    app.logger.error({ error: new Error('Schema not found') });
    return res.status(404).send({ error: 'A schema with that name cannot be found' });
  }

  const [error, result] = await tryAsync(db.deleteOne(INTAKE_SCHEMA, { name }));
  if (error || isError(result) || result === 'failed') {
    app.logger.error({ error: error || result });
    return res.status(500).send({ error: 'An error occurred while deleting the schema' });
  }

  return res.status(204).send();
};

export default remove;
