import { Request, Response } from 'express';
import {
  get,
  isError,
  isString,
  isEmpty,
  attempt,
} from 'lodash';
import PgData from '../../../data-sources/pg';
import { getDb, INTAKE_SCHEMA } from '../../../utils/db-helpers';
import { tryAsync } from '../../../utils/general';
import { getSchema } from '../../../utils/intake-schema';
import { nowUtcFormatted } from '../../../utils/time';

/* eslint-disable max-len */
/*
#swagger.start
#swagger.path = '/gateway/intake-schema/{id}'
#swagger.method = 'put'
#swagger.tags = ['Gateway - Intake Schema']
#swagger.description = 'Update an intake schema by name'
#swagger.produces = ['application/json']

#swagger.parameters['id'] = {
  in: 'path',
  description: 'The name of the schema',
  required: true,
  type: 'string',
  example: 'SampleSchema'
}

#swagger.requestBody = {
  required: true,
  content: {
    "application/json": {
      schema: {
        type: 'object',
        required: [
          'schema',
        ],
        properties: {
          schema: {
            type: 'string',
            example: '{\"foo\":\"bar\"}'
          }
        }
      }
    }
  }
}
#swagger.produces = ['application/json']

#swagger.responses[200] = {
  description: 'Ok',
  content: {
    'application/json': {
      schema: {
        type: 'object',
        required: [
          'id',
          'name',
          'schema',
          'createdBy',
          'createdDate',
          'updatedBy',
          'updatedDate'
        ],
        properties: {
          id: {
            type: 'number',
            example: 1
          },
          name: {
            type: 'string',
            example: 'SampleSchema'
          },
          schema: {
            type: 'string',
            example: '{\"foo\":\"bar\"}'
          },
          createdBy: {
            type: 'string',
            example: 'TST1'
          },
          createdDate: {
            type: 'string',
            example: '2025-03-19T14:36:00.000Z'
          },
          updatedBy: {
            type: 'string',
            example: 'TST2'
          },
          updatedDate: {
            type: 'string',
            example: '2025-03-20T14:36:00.000Z'
          }
        }
      }
    }
  }
}

#swagger.responses[400] = {
  description: 'Bad request',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/BadRequest'
      }
    }
  }
}

#swagger.responses[401] = {
  description: 'Unauthorized',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/Unauthorized'
      }
    }
  }
}

#swagger.responses[404] = {
  description: 'Not Found',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/NotFound'
      }
    }
  }
}

#swagger.responses[500] = {
  description: 'Internal server error',
  content: {
    'application/json': {
      schema: {
        $ref: '#/components/schemas/ErrorResponse'
      }
    }
  }
}
#swagger.end
*/
/* eslint-enable max-len */

const update = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to retrieve system application' });
  }

  const authMethod = get(req, 'authMethod', 'none');
  if (authMethod !== 'jwt') {
    app.logger.error({ error: new Error('Invalid authorization method') });
    return res.status(401).send({ error: 'Invalid authorization method' });
  }

  const user = get(req, 'user');
  if (!user) {
    app.logger.error({ error: new Error('No user found in the request') });
    return res.status(401).send({ error: 'Invalid user' });
  }

  const name = get(req, 'params.id');
  if (!isString(name) || isEmpty(name)) {
    app.logger.error({ error: new Error('No name provided') });
    return res.status(400).send({ error: 'No name provided' });
  }

  const schema = get(req, 'body.schema');
  if (!isString(schema) || isEmpty(schema)) {
    app.logger.error({ error: new Error('No schema provided') });
    return res.status(400).send({ error: 'No schema provided' });
  }

  const parsedSchema = attempt(JSON.parse.bind(null, schema));
  if (isError(parsedSchema)) {
    app.logger.error({ error: new Error('Invalid schema provided') });
    return res.status(400).send({ error: 'Invalid schema provided' });
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    app.logger.error({ error: db });
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const dupCheck = await getSchema(app, name);
  if (isError(dupCheck) && dupCheck.message !== 'No result returned') {
    app.logger.error({ error: dupCheck });
    return res.status(500).send({ error: 'An error occurred while checking for a duplicate schema of this name' });
  }

  if (!dupCheck) {
    app.logger.error({ error: new Error('Schema not found') });
    return res.status(404).send({ error: 'A schema with that name cannot be found' });
  }

  const [error, result] = await tryAsync(db.updateOne(INTAKE_SCHEMA, {
    schema,
    updatedBy: user.uid,
    updatedDate: nowUtcFormatted('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
  }, { name }));
  if (error || isError(result) || result === 'failed') {
    app.logger.error({ error: error || result });
    return res.status(500).send({ error: 'An error occurred while updating the schema' });
  }

  return res.status(200).send({
    ...dupCheck,
    schema,
    updatedBy: user.uid,
    updatedDate: nowUtcFormatted('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
  });
};

export default update;
