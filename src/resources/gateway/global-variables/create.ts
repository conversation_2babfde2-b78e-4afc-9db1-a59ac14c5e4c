import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import {
  GlobalVariableBody,
} from '../../../types/index';
import {
  createGlobalVariable,
} from '../../../subsystems/global-variables/util';

const create = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const user = get(req, 'user');
  if (!user) {
    return res.status(401).send({ error: 'Invalid user' });
  }

  const body: GlobalVariableBody | undefined = get(req, 'body');
  if (isEmpty(body)) {
    return res.status(500).send({ error: 'Unable to get the global variable from request' });
  }

  const response = await createGlobalVariable(app, user, body);

  if (isError(response)) {
    app.logger.error({ error: response });
    return res.status(500).send({
      error: 'An error occurred while creating a global variable',
    });
  }

  return res.status(201).send({
    message: `Created global variable ${body.globalName}`,
  });
};

export default create;
