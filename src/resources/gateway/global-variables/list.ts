import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
} from 'lodash';
import PgData from '../../../data-sources/pg';
import {
  getDb,
  GLOBAL_VARIABLES,
} from '../../../utils/db-helpers';
import {
  tryAsync,
} from '../../../utils/general';

const list = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const db = getDb<PgData>(app, 'core');
  if (isError(db)) {
    return res.status(500).send({ error: 'Database Unavailable' });
  }

  const [critError, globalVariables] = await tryAsync(db.findAll(GLOBAL_VARIABLES));

  if (critError || isError(globalVariables)) {
    app.logger.error({ error: critError || globalVariables });
    return res.status(500).send({ error: 'An error occurred while receiving the global variables' });
  }

  return res.status(200).send({ globalVariables });
};

export default list;
