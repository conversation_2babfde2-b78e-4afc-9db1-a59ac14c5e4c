// /global
// /global/{id}
// /global/list

import list from './list';
import byId from './by-id';
import create from './create';
import update from './update';
import remove from './remove';
import { GetResourceConfig } from '../../../types';

const getResourceConfig: GetResourceConfig = () => [{
  name: 'get-global-variable-list',
  path: '/',
  method: 'get',
  resource: list,
}, {
  name: 'get-global-variable-by-id',
  path: '/:id',
  method: 'get',
  resource: byId,
}, {
  name: 'post-global-variable-create',
  path: '/',
  method: 'post',
  resource: create,
}, {
  name: 'put-global-variable-update',
  path: '/:id',
  method: 'put',
  resource: update,
}, {
  name: 'delete-global-variable-remove',
  path: '/:id',
  method: 'delete',
  resource: remove,
}];

export default getResourceConfig;
