import {
  Response,
  Request,
} from 'express';
import {
  get,
  isEmpty,
  isError,
} from 'lodash';
import {
  findGlobalVariable,
} from '../../../subsystems/global-variables/util';

const byId = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const user = get(req, 'user');
  if (!user) {
    return res.status(401).send({ error: 'Invalid user' });
  }

  const name = get(req, 'params.id', '');
  if (!name) {
    return res.status(400).send({ error: 'No global variable name provided' });
  }

  const globalVariable = await findGlobalVariable(app, name);
  if (isError(globalVariable)) {
    app.logger.error({ error: globalVariable });
    return res.status(500).send({ error: 'An error occurred while finding the global variable.' });
  }

  if (isEmpty(globalVariable)) {
    return res.status(404).send({ error: 'Global variable not found.' });
  }

  return res.status(200).send({ globalVariable });
};

export default byId;
