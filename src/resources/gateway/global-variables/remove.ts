import {
  Response,
  Request,
} from 'express';
import {
  get,
  isError,
  isEmpty,
} from 'lodash';
import {
  removeGlobalVariable,
} from '../../../subsystems/global-variables/util';

const remove = async (req: Request, res: Response) => {
  const app = get(req, 'systemApp');
  if (!app) {
    return res.status(500).send({ error: 'Unable to get the application from request' });
  }

  const user = get(req, 'user');
  if (!user) {
    return res.status(401).send({ error: 'Invalid user' });
  }

  const name: string = get(req, 'params.id', '');
  if (isEmpty(name)) {
    return res.status(500).send({ error: 'Unable to get the global variable name from request' });
  }

  const response = await removeGlobalVariable(app, name);
  if (isError(response)) {
    app.logger.error({ error: response });
    return res.status(500).send({ error: 'An error occurred while removing a global variable' });
  }

  return res.status(204).send(true);
};

export default remove;
