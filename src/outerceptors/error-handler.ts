// Modules.
import {
  ErrorRequestHandler,
  Request,
  Response,
  NextFunction,
} from 'express';
// Custom.
import Messages from '../utils/constants/messages';

const errorHandler: ErrorRequestHandler = (
  err,
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  if (res.headersSent) {
    return next(err);
  }

  const appLogger = req.systemApp?.logger;
  if (appLogger && typeof appLogger.error === 'function') {
    appLogger.error(err);
  } else {
    // eslint-disable-next-line no-console
    console.error(err);
  }

  // Handle specific error messages from endpointSetup
  if (err.message === Messages.app_invalid) {
    return res.status(500).send({ result: 'error', message: [Messages.app_invalid] });
  }

  if (err.message === Messages.db_unavailable) {
    req?.systemApp?.logger.error({ error: err });
    return res.status(500).send({ result: 'error', message: [Messages.db_unavailable] });
  }

  // Return a generic message for unhandled exceptions to match test expectations
  // @todo-structures This doesn't conform to the Webmethods Response schema.  Will require a
  //                  separate ticket due to size of refactor. --hrivera
  return res.status(500).send({
    result: 'error',
    message: [Messages.internal_server_error],
  });
};

export default errorHandler;
