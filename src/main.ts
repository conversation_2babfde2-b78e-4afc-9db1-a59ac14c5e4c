import 'dotenv/config';
import express from 'express';
import { isError, get } from 'lodash';
import { seedDb } from './utils/db-helpers';
import initApp from './core';
import PgData from './data-sources/pg';
import MssqlData from './data-sources/mssql';
import auth from './resources/authentication';
import cedarCoreV2 from './resources/cedar-core-v2';
import censusCoreV2 from './resources/census-core-v2';
import cedarIntake from './resources/cedarIntake';
import healthConfig from './resources/health';
import rootConfig from './resources/root';
import person from './resources/person';
import sparx from './resources/sparxea';
import gateway from './resources/gateway';
import enablePgSession from './session-managers/pg';
import { initLdap } from './utils/auth';
import {
  CMSApp,
  DSSetup,
  SessionSetup,
  // MssqlDataOptions,
  // PgDataOptions,
} from './types';
import { initSparx } from './subsystems/sparxea';

const baseApp = express() as CMSApp;
const dsSetup: DSSetup[] = [{
  // registerDS: (app: CMSApp) => app.dataSources.register<PgData>(app, 'core', PgData),
  dsType: PgData,
  dsName: 'core',
}, {
  // registerDS: (app: CMSApp) => app.dataSources.register<MssqlData>(app, 'sparxea', MssqlData, {
  //   dbName: 'sparx_support_db',
  // }),
  dsType: MssqlData,
  dsName: 'sparxea',
  options: {
    dbName: 'sparx_support_db',
  },
}];
const sessionSetup: SessionSetup = {
  enableSession: enablePgSession,
  dsName: 'core',
};
const app = await initApp(baseApp, dsSetup, sessionSetup);

app.resources.register(app, '/', rootConfig);
app.resources.register(app, '/gateway/LDAP/1.0/authenticate', auth);
app.resources.register(app, '/gateway/LDAP/1.0/person', person);
app.resources.register(app, '/gateway/CEDAR%20Core%20API/2.0.0', cedarCoreV2);
app.resources.register(app, '/gateway/System%20Census%20Core%20API/2.0.0', censusCoreV2);
app.resources.register(app, '/gateway/CEDARIntake/1.0', cedarIntake);
app.resources.register(app, '/gateway', gateway);
app.resources.register(app, '/health', healthConfig);
app.resources.register(app, '/sparx', sparx);

app.resources.initResources(app);

// Init Subsystems
// Sparx Subsystem
await initSparx(app);

// LDAP Subsystem
const ldapSetup = await initLdap(app);
if (isError(ldapSetup)) {
  app.logger.error(new Error('Unable to initialize LDAP stopping the application'));
  throw ldapSetup;
}

// @TODO This will be controlled by versioning once schema is in place
if (get(app, 'config.seedDb', false)) {
  const seeded = await seedDb(app);
  if (!seeded || isError(seeded)) {
    app.logger.error('Application failed to seed database', seeded);
    throw seeded;
  }
} else {
  app.logger.info("Seeding is disabled. To enable, please change the 'seedDb' config option.");
}

const PORT = get(app, 'config.application.port', 3001);
app.listen(PORT, () => {
  app.logger.info(`Server is running on port ${PORT}`);
});

export default app;
