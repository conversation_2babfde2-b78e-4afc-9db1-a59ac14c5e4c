import {
  get,
  isEmpty,
  isNumber,
  isError,
} from 'lodash';
import session from 'express-session';
import connectPgSimple from 'connect-pg-simple';
import { Pool } from 'pg';
import sessionConfig from '../common';
import { getSecret } from '../../subsystems/secrets';
import { CMSApp } from '../../types';

/**
 *
 * @param app CMSApp application
 * @param dsName DataSource name
 */
const enablePgSession = async (app: CMSApp, dataSourceName: string) => {
  if (!dataSourceName) {
    throw new Error('No dataSource name provided');
  }

  const sessionSecret = get(app, 'config.systems.session.secretString');
  if (!sessionSecret) {
    throw new Error('Unable to retrieve session secret string');
  }

  const sessionSecrets = await getSecret(app, sessionSecret);
  if (isError(sessionSecrets)) {
    throw sessionSecrets;
  }

  const dbSecretString = get(app, `config.systems.${dataSourceName}.dbSecretString`);
  if (!dbSecretString) {
    throw new Error(`Unable to retrieve ${dataSourceName} database secret string`);
  }

  const dbSecrets = await getSecret(app, dbSecretString);
  if (isError(dbSecrets)) {
    throw dbSecrets;
  }

  const dbUser = get(dbSecrets, `${dataSourceName.toUpperCase()}_DB_USER`);
  const dbPass = get(dbSecrets, `${dataSourceName.toUpperCase()}_DB_PASS`);
  const databaseHost = get(dbSecrets, `${dataSourceName.toUpperCase()}_HOST`);
  const databasePort = parseInt(get(dbSecrets, `${dataSourceName.toUpperCase()}_PORT`, '3000'), 10);
  const databaseName = get(dbSecrets, `${dataSourceName.toUpperCase()}_DB`, 'postgres');
  let credentialString = '';

  if (!isEmpty(dbUser) && !isEmpty(dbPass)) {
    credentialString = `${dbUser}:${dbPass}@`;
  }

  if (!databaseHost || !isNumber(databasePort) || !databaseName) {
    throw new Error('Invalid database credentials entered');
  }

  const secret = get(sessionSecrets, `${dataSourceName.toUpperCase()}_SESSION_SECRET_KEY`);
  const sessionTable = get(sessionSecrets, `${dataSourceName.toUpperCase()}_SESSION_TABLE`);
  const cookieName = get(sessionSecrets, `${dataSourceName.toUpperCase()}_COOKIE_NAME`, '');

  if (!secret || !sessionTable || !cookieName) {
    throw new Error('Invalid session parameters entered');
  }

  const pool = new Pool({
    connectionString: `postgresql://${credentialString}${databaseHost}:${databasePort}/${databaseName}`,
    idleTimeoutMillis: 15000,
  });

  const PgSession = connectPgSimple(session);
  const store = new PgSession({
    pool,
    tableName: sessionTable,
  });

  app.use(session({
    store,
    secret,
    ...sessionConfig(cookieName),
  }));
};

export default enablePgSession;
