import { CMSApp, User } from './index';
import MssqlData from '../data-sources/mssql';

declare module 'express' {
  interface Request {
    // app: CMSApp;
    systemApp?: CMSApp;
    authMethod?: string;
    user?: User;
  }

  interface Response {
    systemApp?: CMSApp;
  }

  interface Router {
    [key: string]: IRouterMatcher<this>;
  }
}

declare module 'express-session' {
  export interface Session {
    csrf: {
      secret: string;
    },
    jwt: {
      secret: string;
    }
  }
}

declare global {
  interface EndpointSetup {
    app: CMSApp;
    db: MssqlData;
  }

  type TryAsyncResult<T> = [ Error | null, T | undefined];
}
