// Swagger Documentation Types
// ---------------------------

interface SwaggerSchema {
  type?: string;
  description?: string;
  properties?: Record<string, SwaggerSchema>;
  items?: SwaggerSchema;
  required?: string[];
  example?: unknown;
  nullable?: boolean;
  format?: string;
  $ref?: string;
}

interface SwaggerContent {
  schema: SwaggerSchema;
}

interface SwaggerRequestBody {
  required: boolean;
  content: Record<string, SwaggerContent>;
}

interface SwaggerResponse {
  description: string;
  content?: Record<string, SwaggerContent>;
}

interface SwaggerParameter {
  name: string;
  in: 'query' | 'path' | 'header' | 'cookie';
  description: string;
  required: boolean;
  schema: SwaggerSchema;
}

export interface SwaggerDocumentation {
  path: string;
  method: string;
  tags: string[];
  description: string;
  requestBody?: SwaggerRequestBody;
  produces: string[];
  responses: Record<string, SwaggerResponse>;
  parameters?: SwaggerParameter[];
}
