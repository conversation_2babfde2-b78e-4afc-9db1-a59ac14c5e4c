import Logger, { LoggerOptions } from 'bunyan';
import {
  Express,
  Router,
  RequestHandler,
} from 'express';
// Mock is required for the testLogger type
// eslint-disable-next-line import/no-extraneous-dependencies
import { Mock } from 'vitest';
import { JwtPayload } from 'jsonwebtoken';
import { Entry } from 'ldapts';
import { ModelAttributes } from 'sequelize';

// LDAP
export type LDAPFilter = string;

export type LDAPAttributes = string[];

export type LDAPLoginValues = {
  username: string;
  password: string;
};

// Data Sources
export interface DataOptions {
  app: CMSApp;
  name: string;
  secretString: string;
}

// PG
export interface PgDataOptions extends DataOptions {
  allowLocal?: boolean;
}

export interface MssqlDataOptions extends DataOptions {
  dbName: string;
}

// Models
export interface ModelDefinition {
  name: string;
  // Due to model constraints from sequelize, this needs to be any
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  define: ModelAttributes<any, unknown>;
  options?: Record<string, unknown>;
}

export interface DataSourceType {
  initDb(): Promise<void | Error>;
  closeConnection(): Promise<void | boolean>;
}

export interface RegisterDataSourceOptions<D extends DataSourceType, DO extends DataOptions> {
  app: CMSApp;
  dataSourceName: string;
  UserDataSource: (options: DO) => D;
  dataSourceOptions?: Partial<DO>;
}

export interface AppConfig {
  application: {
    port: number,
    corsWhitelist: string[],
    rootPath: string,
    cookie: {
      prefix: string,
      secure: boolean,
      httpOnly: boolean,
    }
  },
  logging: {
    logNames: {
      [key: string]: string
    },
    loggers: LoggerOptions[]
  },
  secretStrings: string[]
  seedDb: boolean
  systems: {
    [key: string]: {
      [key: string]: string
    }
  }
}

export interface CMSApp extends Express {
  config: AppConfig;
  loggingService: {
    get: (name: string) => Logger;
    create: (loggerConfig: LoggerOptions) => Logger;
    getNames: () => string[];
  };
  logger: Logger;
  resources: {
    router: Router;
    initResources: InitResources;
    register: RegisterResources;
    registry: ResourceConfig[];
    publicRoutes: PublicRoute[];
  };
  dataSources: {
    register: <D extends DataSourceType, DO extends DataOptions>(
      app: CMSApp,
      dataSourceName: string,
      UserDataSource: new (options: DO) => D,
      dataSourceOptions?: Partial<DO>
    ) => Promise<void>;
  } & Record<string, DataSourceType>; // Allow other key-value pairs as well
}

export interface SparxPackage {
  Package_GUID: string;
  Package_Name: string;
}

export interface StoredProcedureParam {
  name: string;
  type: string;
  param?: number | 'max';
  param2?: number;
}

export interface StoredProcedureData {
  name: string;
  value: string | null;
  isOutput?: boolean;
}

export interface StoredProcedureQuery {
  resultKey: string;
  paramName: string;
  wrapAsParam?: boolean;
}

export interface StoredProcedureResult {
  queryStatus: number;
  [key: string]: unknown;
}

export interface RenameKeysProps {
  oldKey: string;
  newKey: string;
  format?: string;
  emptyToNull?: boolean;
}

export interface SecretItems {
  [key: string]: string;
}

export interface SecretStoreItem {
  secretString: string;
  secrets: SecretItems;
}

// Types for processWhere
export type OperationObject = {
  column: string,
  operator: string,
  value: string | string[] | number[],
};

export type BetweenObject = {
  column: string,
  start: string | number,
  stop: string | number,
};

export type LikeObject = {
  column: string,
  value: string,
};

export type BaseWhereObject = {
  operation?: OperationObject,
  between?: BetweenObject,
  like?: LikeObject,
};

export interface WhereObject extends BaseWhereObject {
  grouped?: GroupedObject,
}

export interface AndObject extends BaseWhereObject {}

export interface OrObject extends BaseWhereObject {}

export type GroupedObject = {
  and?: AndObject[],
  or?: OrObject[],
};

export interface Where {
  where: WhereObject;
  and?: AndObject[];
  or?: OrObject[];
  operation?: OperationObject;
  between?: BetweenObject;
  like?: LikeObject;
  grouped?: GroupedObject;
}

export interface ParamsObject {
  [key: string]: string;
}

export interface WhereResult {
  query: string;
  params: ParamsObject;
}

export type InitResources = (app: CMSApp) => void;

export type RegisterResources = (
  app: CMSApp,
  path: string,
  resourceRouter: string | GetResourceConfig,
) => Promise<Error | boolean>;

export interface FoundGlobalVariableBody {
  globalName: string,
  globalValue: string,
  createdBy: string,
  createdDate: Date,
  updatedBy: string,
  updatedDate: Date,
}

export interface GlobalVariableBody {
  globalName: string;
  globalValue: string;
}

type HttpMethods = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS' | 'TRACE' | 'CONNECT' | 'get' | 'post' | 'put' | 'delete' | 'patch' | 'head' | 'options' | 'trace' | 'connect';

export interface PublicRoute {
  path: string;
  method: HttpMethods;
}

export interface ResourceConfig {
  name: string;
  path: string;
  method: HttpMethods;
  resource: RequestHandler;
  public?: boolean;
}

export interface ResourceConfigSub {
  path: string;
  resourceConfig: ResourceConfig[];
}

export type GetResourceConfig = () => ResourceConfig[];

export type GetResourceConfigSub = () => ResourceConfigSub;

export interface ResourceRouter {
  getResourceConfig: () => ResourceConfig[];
}

export interface AuthHeaders {
  gatewayKey: string;
  jwtKey: string;
}

export interface DSSetup {
  dsType: unknown;
  dsName: string;
  options?: {
    [key: string]: string;
  }
}
export interface SessionSetup {
  enableSession: (app: CMSApp, dsName: string) => Promise<void>;
  dsName: string;
}

export interface SecureJWT extends JwtPayload {
  csrf: string;
  id: string;
}

export interface User extends Entry {
  dn: string;
  cn: string;
  mail: string;
  givenName: string;
  sn: string;
  telephoneNumber: string;
  uid: string;
  ismemberof: string[];
}

export interface ExchangeListResult {
  exchangeId: string | null,
  exchangeName: string | null,
  exchangeDescription: string | null;
  exchangeVersion: string | null,
  exchangeState: string | null,
  exchangeStartDate: string | null,
  exchangeEndDate: string | null,
  exchangeRetiredDate: string | null,
  fromOwnerId: string | null,
  fromOwnerName: string | null,
  fromOwnerType: string | null,
  toOwnerId: string | null,
  toOwnerName: string | null,
  toOwnerType: string | null,
  connectionFrequency: string[],
  dataExchangeAgreement: string | null;
  businessPurposeOfAddress: string[],
  containsHealthDisparityData: boolean,
  isBeneficiaryMailingFile: boolean | null;
  sharedViaApi: boolean | null;
  apiOwnership: string | null;
  typeOfData: { id: string | null; name: string | null; }[] | null;
  typeOfDataId?: string | null,
  typeOfDataName?: string | null,
  numOfRecords: string | null;
  dataFormat: string | null;
  dataFormatOther: string | null;
  exchangeContainsCUI: boolean,
  exchangeCUIDescription: string | null,
  exchangeNetworkProtocol: string[],
  exchangeNetworkProtocolOther: string,
}

export interface ThreatListResult {
  id: string | null,
  parentId: string | null,
  alternativeId: string | null,
  controlFamily: string | null,
  daysOpen: string | null,
  weaknessRiskLevel: string | null,
}

export interface CreateBadAppOptions {
  includeApp?: boolean;
  includeUser?: boolean;
  includeLogger?: boolean;
  middleware?: RequestHandler | RequestHandler[];
}

export interface BudgetFindQueryData {
  systemId: string,
  projectTitle: string,
  projectId: string,
}

export interface BudgetResult {
  count: string | number,
  Budgets?: { FiscalYear: string; }[],
}

export interface ContractObject {
  id: string,
  parentAwardId: string,
  awardId: string,
  Cost: string,
  ContractName: string,
  POPStartDate: string,
  POPEndDate: string,
  OrderNumber: string,
  ProjectTitle: string,
  ProductServiceDescription: string,
  ServiceProvided: string,
  ContractNumber: string,
}

export interface ContractResult {
  count?: string | number,
  Contracts: ContractObject[],
}

export interface RoleTypeInitialResult {
  id: string,
  name: string,
  description: string,
}

export interface RoleObject {
  objectId: string;
  roleTypeId: string;
  assigneeId?: string | null;
  assigneeFirstName?: string | null;
  assigneeLastName?: string | null;
  assigneeUserName: string | null;
  assigneeEmail?: string | null;
  assigneePhone?: string | null;
}

export interface SessionObject {
  [key: string]: unknown;
  session?: SessionObject;
}

export interface IntakeSchema {
  id: number;
  name: string;
  schema: string;
  createdBy: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
}

export type ArrayRule = string | IntakeValidationSchema;

export type IntakeValues = string | ArrayRule[] | ArrayRule[][] | IntakeValidationSchema;

export interface IntakeValidationSchema {
  [key: string]: IntakeValues;
}

type MockableLogger = Logger & {
  trace: Mock;
  debug: Mock;
  info: Mock;
  warn: Mock;
  error: Mock;
  fatal: Mock;
  child: Mock;
  resetMocks: () => void;
};

export interface BudgetSystemCostInitialResult {
  FiscalYear: number,
  ActualSystemCost: number,
  systemId: string,
}

export interface LDAPPersonMap {
  id?: string,
  firstName?: string,
  lastName?: string,
  commonName?: string,
  email?: string,
  telephone?: string,
}

export interface LDAPPersonData extends LDAPPersonMap {
  userName?: string,
}

export interface Person {
  id?: string;
  userName?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
}

export interface PersonFindResponse {
  count?: number;
  Users?: Person[];
}

export type AsyncResult<T> = [Error | null, T | Error];

export interface UnformattedSparxUserData {
  ClassName: string,
  id: string,
  Values: SparxUserData,
}

export interface SparxUserData {
  id?: string,
  userName: string,
  firstName: string,
  lastName: string,
  phone: string,
  email: string,
  commonName?: string,
}

export interface MappedSparxUserResults {
  queryStatus: number | null,
  result: {
    Objects: Error | UnformattedSparxUserData[],
  } | Error | null,
}

export interface RawSystemResult {
  apisDeveloped: string,
  apiDescPublished: string,
  apiDescPubLocation: string,
  apiDataArea: string,
  apiAccessibility: string,
  apiFHIRUse: string,
  apiFHIRUseOther: string,
  systemHasApiGateway: string,
  apiHasPortal: string,
  usesAiTech: string,
  developmentStage: string,
  aiSolnCatgOther: string,
}

export interface RawCategoryObj {
  category: string,
}

export interface RawSoftwareObj {
  software_name: string,
  softwareProductId: string,
  technopedia_id: string,
  vendor_name: string,
  category: string,
  api_gateway_use: string,
  provides_ai_capability: string,
  ela_purchase?: string,
  ela_vendor_id?: string,
  ela_organization?: string,
  systemSoftwareConnectionGuid: string,
  softwareCatagoryConnectionGUID: string,
  softwareVendorConnectionGuid: string,
}

export interface InsertPersonSubDataSP {
  externalSource: string,
  firstName: string,
  lastName: string,
  phone: string,
  techName: string,
  userName: string,
  email: string,
  GUID: string,
}

export interface InsertPersonSPResult {
  queryStatus: number,
  results: object[],
}

export interface UpdateRelationObj {
  fromid: string,
  property: string,
  toref: string,
}

export interface UpdateMainObj {
  ClassName: string,
  Id: string,
  Values: {
    name: string,
  },
}

export interface UpdateTotalObject {
  Object: UpdateMainObj,
  Relations: {
    relationOne: UpdateRelationObj,
    relationTwo: UpdateRelationObj,
    relationThree: UpdateRelationObj,
  }
}

export interface RoleAddResultRaw {
  queryStatus: number,
  result: string, // this is a json string that needs to be parsed
}

export interface RoleParsedObject {
  NewObjects: string[],
  Count: number,
}

export interface RawDeploymentDataCenter {
  id: string,
  name: string,
  description: string,
  deploymentType: string,
  systemId: string,
  systemName: string,
  systemVersion: string,
  status: string,
  state: string,
  startDate: string,
  endDate: string,
  deploymentElementId: string,
  contractorName: string,
  hasProductionData: string,
  isHotSite: string,
  replicatedSystemElements: string,
  wanType: string,
  wanTypeOther: string,
  movingToCloud: string,
  movingToCloudDate: string,
  usersRequiringMFA: string,
  otherSpecialUsers: string,
  networkEncryption: string,
  awsEnclave: string,
  awsEnclaveOther: string,
  DataCenterId: string,
  DataCenterName: string,
  DataCenterVersion: string,
  DataCenterDescription: string,
  DataCenterStatus: string,
  DataCenterState: string,
  DataCenterStartDate: string,
  DataCenterEndDate: string,
  DataCenterAddress1: string,
  DataCenterAddress2: string,
  DataCenterCity: string,
  DataCenterAddressState: string,
  DataCenterZip: string,
}

export interface XmlParamsObject {
  title?: string,
  type?: string,
  resourceType?: string,
  parentResourceIdentifier?: string,
  stereotype?: string,
}
export interface UserWithRole extends User {
  objectId: string
  roleTypeId: string
  assigneeId?: string | null
  assigneeUserName: string | null
}

export type Updatable = RoleObject | UserWithRole;

export type SparxResults = { count: number; users: LDAPPersonMap[] };

export type CreatePromiseResult = Partial<RoleObject>[] | Error;

export interface DbReviewer {
  SYSTEM_SURVEY_REVIEWER_ID: string;
  REVIEWER_USERNAME: string;
  REVIEWER_FULLNAME: string;
  REVIEWER_TYPE: string;
}

export interface Reviewer {
  id: string;
  userName: string;
  fullName: string;
  type: string;
}

export interface ReviewerFindResponse {
  count: number;
  Reviewers: Reviewer[];
}
interface Exchange {
  exchangeId: string | null;
  exchangeName: string | null;
  exchangeDescription: string | null;
  exchangeVersion: string | null;
  exchangeState: string | null;
  exchangeStartDate: string | null;
  exchangeEndDate: string | null;
  exchangeRetiredDate: string | null;
  fromOwnerId: string | null;
  fromOwnerName: string | null;
  fromOwnerType: string | null;
  toOwnerId: string | null;
  toOwnerName: string | null;
  toOwnerType: string | null;
  connectionFrequency: string[] | null;
  dataExchangeAgreement: string | null;
  containsBeneficiaryAddress: boolean | null;
  containsBankingData: boolean | null;
  businessPurposeOfAddress: string[] | null;
  isAddressEditable: boolean | null;
  containsPii: boolean | null;
  containsPhi: boolean | null;
  containsHealthDisparityData: boolean | null;
  isBeneficiaryMailingFile: boolean | null;
  sharedViaApi: boolean | null;
  apiOwnership: string | null;
  typeOfData: { id: string | null; name: string | null; }[] | null;
  numOfRecords: string | null;
  dataFormat: string | null;
  dataFormatOther: string | null;
  exchangeContainsCUI: boolean | null;
  exchangeCUIDescription: string | null;
  exchangeCUIType: string[] | null;
  exchangeConnectionAuthenticated: boolean | null;
  exchangeNetworkProtocol: string[] | null;
  exchangeNetworkProtocolOther: string | null;
}
