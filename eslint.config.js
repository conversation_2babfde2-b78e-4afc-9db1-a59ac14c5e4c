import tsParser from '@typescript-eslint/parser';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import js from '@eslint/js';
import { FlatCompat } from '@eslint/eslintrc';

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);
const compat = new FlatCompat({
  baseDirectory: dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

export default [
  ...compat.extends(
    'plugin:@typescript-eslint/recommended',
    'airbnb-base',
    'airbnb-typescript/base',
  ),
  {
    ignores: [
      'eslint.config.js',
      'vitest.config.js',
    ],
  },
  {
    files: ['**/*.ts', '**/*.d.ts'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        project: path.join(dirname, 'tsconfig.eslint.json'),
        tsconfigRootDir: dirname,
        extraFileExtensions: ['.d.ts'],
        ecmaVersion: 2018,
        createDefaultProgram: true,
        sourceType: 'module',
      },
    },
    rules: {
      'import/no-extraneous-dependencies': [
        'error',
        {
          devDependencies: [
            'tests/**/*.test.ts',
            'tests/shared/**/*.ts',
            'src/integration/**/*.ts',
            'eslint.config.js',
          ],
        },
      ],
      '@typescript-eslint/lines-between-class-members': 'off',
    },
  },
];
