// Custom: App.
import { CMSApp } from 'src/types';

// Custom: Integration.
import RequestConfig from 'integration/library/RequestConfig';
import { FGetDatabaseRecordsForParams, IIntegrationHarnessCall } from 'integration/library/types';

// Custom: Local.
import { SoftwareProductsQueryParams } from 'src/resources/census-core-v2/softwareProducts/list';
import {
  getIntegrationRecordsMissingCategory,
  getIntegrationRecordsMissingSoftware,
  getIntegrationRecordsValid,
} from './support';

/**
 * Creates a list of calls that define the API call and uses the
 * configs (querystring, body, path params) for each HTTP request.
 */
const getSoftwareProductsCalls = (
  callback: (
    app: CMSApp,
    callback: FGetDatabaseRecordsForParams,
  ) => Promise<RequestConfig[]>,
): IIntegrationHarnessCall[] => [
  {
    description: 'softwareProducts/find main+category+software',
    expects: 'success',
    method: 'get',
    path: '/',
    statusCode: 200,
    async: true,
    getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => (
      callback(app, getIntegrationRecordsValid)
    ),
  },
  {
    description: 'softwareProducts/find main +category -software',
    expects: 'success',
    method: 'get',
    path: '/',
    statusCode: 200,
    async: true,
    getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => (
      callback(app, getIntegrationRecordsMissingSoftware)
    ),
  },
  {
    description: 'softwareProducts/find main -category +software',
    expects: 'success',
    method: 'get',
    path: '/',
    statusCode: 200,
    async: true,
    getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => (
      callback(app, getIntegrationRecordsMissingCategory)
    ),
  },
  {
    description: 'softwareProducts/find -main -category -software',
    expects: 'success',
    method: 'get',
    path: '/',
    statusCode: 200,
    getRequestConfigs: async (): Promise<RequestConfig[]> => [
      RequestConfig.fromObject({
        query: {
          id: 'INVALID',
        } as SoftwareProductsQueryParams,
      }),
    ],
  },
];

export {
  // eslint-disable-next-line import/prefer-default-export
  getSoftwareProductsCalls,
};
