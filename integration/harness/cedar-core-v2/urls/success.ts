// Custom: App.
import { CMSApp } from 'src/types';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import { UrlFindCedarQueryParams } from 'src/utils/urls';

// Custom: Integration.
import { getHarnessDb } from 'integration/library/database';
import RequestConfig from 'integration/library/RequestConfig';
import { IIntegrationHarnessRoute } from 'integration/library/types';
import { validateNotFound } from 'integration/library/harness';

// Custom: Local.
import { getIntegrationRecordsIdsValid } from './support';

const TEST_RECORD_COUNT = 30;

const harness: IIntegrationHarnessRoute = {
  path: '/url',
  calls: [
    {
      description: 'Urls/find single',
      expects: 'success',
      method: 'get',
      path: '/:id',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => {
        const db = getHarnessDb(app, DatabaseConfigs.sparxSupport);
        const result = await getIntegrationRecordsIdsValid(db, TEST_RECORD_COUNT);

        return result.map((r) => RequestConfig.fromObject({
          pathParams: {
            id: r['Sparx System GUID'],
          } as UrlFindCedarQueryParams,
        }));
      },
    },
    {
      description: 'Urls/find single invalid id',
      expects: 'success',
      method: 'get',
      path: '/:id',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({
          pathParams: { id: 'INVALID' } as UrlFindCedarQueryParams,
        }),
      ],
    },
    {
      description: 'Urls/find single missing id',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 404,
      async: true,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({}),
      ],
      validate: validateNotFound,
    },
  ],
};

export default harness;
