// Custom: App.
import { CMSApp } from 'src/types';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import { SoftwareProductsQueryParams } from 'src/resources/census-core-v2/softwareProducts/list';

// Custom: Integration.
import { FGetDatabaseRecordsForParams, IIntegrationHarnessRoute } from 'integration/library/types';
import { getHarnessDb } from 'integration/library/database';
import { getSoftwareProductsCalls } from 'integration/harness/shared/softwareProducts';
import RequestConfig from 'integration/library/RequestConfig';

/**
 * Creates a list of configs (querystring, body, path params) to use for API calls.
 */
const getRequestConfigs = async (
  app: CMSApp,
  callback: FGetDatabaseRecordsForParams,
): Promise<RequestConfig[]> => {
  const db = getHarnessDb(app, DatabaseConfigs.sparxSupport);

  const records = await callback(db);

  return records.map((
    record: Record<string, unknown>,
  ): RequestConfig => RequestConfig.fromObject(
    {
      query: {
        id: record['Sparx System GUID'],
      } as SoftwareProductsQueryParams,
    },
  ));
};

const harness: IIntegrationHarnessRoute = {
  path: '/softwareProducts',
  calls: getSoftwareProductsCalls(getRequestConfigs),
};

export default harness;
