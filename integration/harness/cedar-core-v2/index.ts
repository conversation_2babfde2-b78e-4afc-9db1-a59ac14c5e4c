// Custom: Integration.
import { IIntegrationHarnessPackage } from 'integration/library/types';

import rootFailure from './root/failure';
import softwareProductsSuccess from './softwareProducts/success';
import urlsSuccess from './urls/success';

const harnessPackage: IIntegrationHarnessPackage = {
  path: '/gateway/CEDAR%20Core%20API/2.0.0',
  description: 'CEDAR Core v2',
  name: 'cedar',
  routes: [
    rootFailure,
    softwareProductsSuccess,
    urlsSuccess,
  ],
};

export default harnessPackage;
