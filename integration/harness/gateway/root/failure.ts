// Custom: Integration.
import {
  IIntegrationHarnessRoute,
} from 'integration/library/types';
import { getRequestConfigsEmptySingle, validateNotFound } from 'integration/library/harness';

const harness: IIntegrationHarnessRoute = {
  path: '/',
  calls: [
    {
      description: 'Gateway /',
      expects: 'failure',
      method: 'get',
      path: '/',
      statusCode: 404,
      getRequestConfigs: getRequestConfigsEmptySingle,
      validate: validateNotFound,
    },
    {
      description: 'Gateway /nonexistent',
      expects: 'failure',
      method: 'get',
      path: '/nonexistent',
      statusCode: 404,
      getRequestConfigs: getRequestConfigsEmptySingle,
      validate: validateNotFound,
    },
  ],
};

export default harness;
