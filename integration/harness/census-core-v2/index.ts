// Custom: Integration.
import { IIntegrationHarnessPackage } from 'integration/library/types';

import harnessNotesEdge from './notes/edge';
import harnessNotesFailure from './notes/failure';
import harnessNotesSuccess from './notes/success';
import harnessRootFailure from './root/failure';
import harnessSoftwareProducts from './softwareProducts/success';
import harnessSystemMaintainer from './systemMaintainer/success';
import harnessSystemProperty from './systemProperty/success';
import harnessSystemsList from './systemsList/success';
import harnessUrls from './urls/success';

const harnessPackage: IIntegrationHarnessPackage = {
  path: '/gateway/System%20Census%20Core%20API/2.0.0',
  description: 'Census Core v2',
  name: 'census',
  routes: [
    harnessNotesEdge,
    harnessNotesFailure,
    harnessNotesSuccess,
    harnessRootFailure,
    harnessSoftwareProducts,
    harnessSystemMaintainer,
    harnessSystemProperty,
    harnessSystemsList,
    harnessUrls,
  ],
};

export default harnessPackage;
