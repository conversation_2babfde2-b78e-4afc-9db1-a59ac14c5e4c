// Custom: Integration.
import RequestConfig from 'integration/library/RequestConfig';
import {
  IIntegrationHarnessRoute,
} from 'integration/library/types';

const harness: IIntegrationHarnessRoute = {
  path: '/note',
  calls: [
    {
      description: 'note/find empty result',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({
          query: {
            id: undefined,
            pageName: 'test',
          },
        }),
      ],
    },
  ],
};

export default harness;
