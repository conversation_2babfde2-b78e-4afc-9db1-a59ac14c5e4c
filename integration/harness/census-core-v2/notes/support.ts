// Custom.
import MssqlData from 'src/data-sources/mssql';
import { CedarSupportTables } from 'src/utils/constants/tables';

export interface DbNoteFind extends Record<string, unknown> {
  System_ID: string
  PAGE_NAME: string
}

export interface DbNoteDelete extends Record<string, unknown> {
  SYSTEM_SURVEY_PAGE_NOTES_ID: number
}

const getIntegrationRecordsValidDelete = async (
  db: MssqlData,
  pageName: string,
): Promise<DbNoteDelete[]> => {
  const result = await db.queryViewTyped<[DbNoteDelete[], number]>(
    `${CedarSupportTables.SYSTEM_SURVEY_PAGE_NOTES}`,
    ['SYSTEM_SURVEY_PAGE_NOTES_ID'],
    {
      where: {
        operation: { column: 'PAGE_NAME', operator: '=', value: pageName },
      },
    },
  );

  // @todo-database Extract just the result array from the tuple [results, count] --hrivera
  return result[0];
};

const getIntegrationRecordsValidFind = async (
  db: MssqlData,
  pageName: string,
): Promise<DbNoteFind[]> => {
  const orderBy = undefined;
  const distinct = true;

  const result = await db.queryViewTyped<[DbNoteFind[], number]>(
    `${CedarSupportTables.SYSTEM_SURVEY_PAGE_NOTES}`,
    ['System_ID', 'PAGE_NAME'],
    {
      where: {
        operation: { column: 'PAGE_NAME', operator: '=', value: pageName },
      },
    },
    orderBy,
    distinct,
  );

  // @todo-database Extract just the result array from the tuple [results, count] --hrivera
  return result[0];
};

export {
  getIntegrationRecordsValidDelete,
  getIntegrationRecordsValidFind,
};
