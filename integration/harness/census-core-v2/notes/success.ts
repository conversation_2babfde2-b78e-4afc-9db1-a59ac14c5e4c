// Modules.
import { isArray } from 'lodash';

// Custom: App.
import { CMSApp } from 'src/types';
import { NoteFindQueryParams } from 'src/resources/census-core-v2/note/find';
import { NoteDeleteQueryParams } from 'src/resources/census-core-v2/note/delete';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import RequestConfig from 'integration/library/RequestConfig';

// Custom: Integration.
import { IIntegrationHarnessRoute, TLastResult } from 'integration/library/types';
import RequestConfigSplit from 'integration/library/RequestConfigSplit';
import { getHarnessDb } from 'integration/library/database';
import { assert, assertDiff } from 'integration/library/assert';
import {
  DbNoteFind,
  getIntegrationRecordsValidDelete,
  getIntegrationRecordsValidFind,
} from './support';

const USER = process.env.INTEGRATION_USERNAME;
const NOTE_PAGE_NAME = `INTEGRATION_TEST_${USER}_${Date.now()}`;

/**
 * This data is hardcoded here, the only other option would be
 * to put it in a JSON file, but that would also be hardcoded.
 */
const getNoteBody = () => ({
  Notes: [{
    systemId: '{11111111-2222-3333-4444-555555555555}',
    pageName: NOTE_PAGE_NAME,
    userId: USER,
    userFirst: 'Henry',
    userLast: 'Rivera',
    userRole: 'Reviewer',
    note: 'This is an integration test note.',
  }],
  EmailFlags: {
    notifyReviewer: false,
    notifyRespondent: false,
    includeHistory: false,
  },
});

type THeaderLastInsertedIds = string[];

const getRequestConfigsFind = async (
  app: CMSApp,
  lastResult?: TLastResult,
): Promise<RequestConfig[]> => {
  const last = isArray(lastResult) ? lastResult[0] : lastResult;
  const affected = last?.lastResponse.headers.get('inserted_ids') ?? '[]';
  const insertedIds: THeaderLastInsertedIds = JSON.parse(affected) ?? [];

  if (!insertedIds || insertedIds.length === 0) {
    throw new Error('getRequestConfigsFind: insertedIds not found in HTTP header');
  }

  const db = getHarnessDb(app, DatabaseConfigs.cedarSupport);
  const dbNotes = await getIntegrationRecordsValidFind(db, NOTE_PAGE_NAME);

  return dbNotes.map((note: DbNoteFind): RequestConfig => RequestConfig.fromObject({
    query: {
      id: note.System_ID,
      pageName: note.PAGE_NAME,
    } as NoteFindQueryParams,
  }));
};

/**
 * Returns a custom-split request config so that one note
 * is deleted locally and the other is deleted via webmethods.
 */
const getRequestConfigsDelete = async (
  app: CMSApp,
): Promise<RequestConfigSplit> => {
  const db = getHarnessDb(app, DatabaseConfigs.cedarSupport);
  const dbNotes = await getIntegrationRecordsValidDelete(db, NOTE_PAGE_NAME);

  return new RequestConfigSplit(
    {
      query: {
        id: [dbNotes[0].SYSTEM_SURVEY_PAGE_NOTES_ID.toString()],
      } as NoteDeleteQueryParams,
    },
    {
      target: 'webmethods',
      query: {
        id: [dbNotes[1].SYSTEM_SURVEY_PAGE_NOTES_ID.toString()],
      } as NoteDeleteQueryParams,
    },
  );
};

/**
 * @todo-database Webmethods is mangling milliseconds values in DATETIME columns.
 *                values that end with zero, ala, `2025-06-28T20:44:32.820Z` are
 *                being mangled to `2025-06-28T20:44:32.082Z`.  Need to follow
 *                up with Don.
 */
const validate = async (local: Response, remote: Response) => {
  const localBody = await local.json();
  const remoteBody = await remote.json();

  // Normalize timestamps by removing the millisecond precision value.
  const normalizeTimestamps = (
    notes: Array<{ createdOn: string; [key: string]: unknown }>,
  ) => notes.map((note) => ({
    ...note,
    createdOn: note.createdOn
      ? `${new Date(note.createdOn).toISOString().split('.')[0]}Z`
      : note.createdOn,
  }));

  // Apply normalization to both responses
  if (localBody.Notes) {
    localBody.Notes = normalizeTimestamps(localBody.Notes);
  }
  if (remoteBody.Notes) {
    remoteBody.Notes = normalizeTimestamps(remoteBody.Notes);
  }

  assert(local.status === 200, `local status is not 200: ${local.status}`);
  assert(remote.status === 200, `remote status is not 200: ${remote.status}`);
  assertDiff(remoteBody, localBody);
};

const harness: IIntegrationHarnessRoute = {
  path: '/note',
  calls: [
    {
      description: 'note/add single',
      expects: 'success',
      method: 'post',
      path: '/',
      statusCode: 200,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromArgs({}, getNoteBody()),
      ],
      callback: (headers: Headers, _body: object) => ({
        INSERTED_IDS: headers.get('INSERTED_IDS'),
        AFFECTED_ROWS: headers.get('AFFECTED_ROWS'),
      }),
    },
    {
      description: 'note/find single',
      expects: 'success',
      method: 'get',
      path: '/',
      input: true,
      statusCode: 200,
      getRequestConfigs: getRequestConfigsFind,
      validate,
    },
    {
      description: 'note/delete single',
      expects: 'success',
      method: 'delete',
      path: '/',
      statusCode: 200,
      getRequestConfigs: getRequestConfigsDelete,
    },
  ],
};

export default harness;
