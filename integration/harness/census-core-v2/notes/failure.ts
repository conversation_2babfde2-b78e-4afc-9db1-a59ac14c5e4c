// Custom: Integration.
import RequestConfig from 'integration/library/RequestConfig';
import { IIntegrationHarnessRoute } from 'integration/library/types';

const harness: IIntegrationHarnessRoute = {
  path: '/note',
  calls: [
    {
      description: 'note/add single',
      expects: 'failure',
      method: 'post',
      path: '/',
      statusCode: 400,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromArgs({}, {}),
      ],
    },
  ],
};

export default harness;
