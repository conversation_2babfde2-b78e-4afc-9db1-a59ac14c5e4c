// Custom: App.
import { CMSApp } from 'src/types';
import { SystemPropertyQueryParams } from 'src/resources/census-core-v2/systemProperty/find';
import { validSparxSystemFields } from 'src/resources/census-core-v2/systemProperty/validFields';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';

// Custom: Integration.
import { IIntegrationHarnessRoute } from 'integration/library/types';
import { getHarnessDb } from 'integration/library/database';
import RequestConfig from 'integration/library/RequestConfig';
import { getIntegrationRecordsValid } from './support';

const TEST_RECORD_COUNT = 1;

const getRequestConfigs = async (
  app: CMSApp,
): Promise<RequestConfig[]> => {
  const db = getHarnessDb(app, DatabaseConfigs.sparxSupport);
  const result = await getIntegrationRecordsValid(db, TEST_RECORD_COUNT);

  // Get all valid field names
  const validFieldNames = Object.keys(validSparxSystemFields);

  // Create request configs for each system ID and each valid field
  return result.flatMap(
    (record) => validFieldNames.map(
      (fieldName) => RequestConfig.fromObject({
        query: {
          systemId: record['Sparx System GUID'],
          propertyName: fieldName,
        } as SystemPropertyQueryParams,
      }),
    ),
  );
};

const harness: IIntegrationHarnessRoute = {
  path: '/systemProperty',
  calls: [
    {
      description: 'systemProperty/find single',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs,
    },
  ],
};

export default harness;
