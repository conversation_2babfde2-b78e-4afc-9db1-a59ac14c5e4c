// Modules.
import { isNaN } from 'lodash';

// Custom.
import MssqlData from 'src/data-sources/mssql';
import { SparxSupportViews } from 'src/utils/constants/views';
import { SparxSystemRecord } from 'src/resources/census-core-v2/systemProperty/find';

const getIntegrationRecordsValid = async (
  db: MssqlData,
  limit: number = 10,
): Promise<SparxSystemRecord[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  const sql = `
    SELECT TOP ${limit}
      ${SparxSupportViews.Sparx_System}.[Sparx System GUID]
    FROM
        ${SparxSupportViews.Sparx_System}
  `;
  const result = await db.db?.query(sql);

  return result?.[0] as SparxSystemRecord[];
};

export {
  // Expecting additional exports.
  // eslint-disable-next-line import/prefer-default-export
  getIntegrationRecordsValid,
};
