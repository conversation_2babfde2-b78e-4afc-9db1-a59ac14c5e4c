// Custom: Integration.
import { IIntegrationHarnessRoute } from 'integration/library/types';
import RequestConfig from 'integration/library/RequestConfig';

const harness: IIntegrationHarnessRoute = {
  path: '/page/systemsList',
  calls: [
    {
      description: 'systemsList/find all',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      // This endpoint doesn't take any parameters, it just returns a giant blob.
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({}),
      ],
    },
  ],
};

export default harness;
