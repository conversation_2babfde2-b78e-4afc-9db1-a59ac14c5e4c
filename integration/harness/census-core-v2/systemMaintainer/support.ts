import { isNaN } from 'lodash';
import MssqlData from 'src/data-sources/mssql';
import { DbSystemMaintainer } from 'src/resources/census-core-v2/systemMaintainer/types/find';
import { SparxSupportViews } from 'src/utils/constants/views';

/**
 * Fetches valid maintainer record sets, which require results from two tables.
 */
const getIntegrationRecordsIdsValid = async (
  db: MssqlData,
  limit: number = 10,
): Promise<DbSystemMaintainer[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  const sql = `
    SELECT TOP ${limit}
      ${SparxSupportViews.Sparx_System}.[Sparx System GUID]
    FROM
        ${SparxSupportViews.Sparx_System}
    INNER JOIN
      ${SparxSupportViews.Sparx_System_RecordsManagementBucket_Census}
      ON
          ${SparxSupportViews.Sparx_System}.[Sparx System GUID]
          =
          ${SparxSupportViews.Sparx_System_RecordsManagementBucket_Census}.[Sparx System GUID]
    WHERE
      ${SparxSupportViews.Sparx_System}.[Sparx System GUID] IS NOT NULL
      AND ${SparxSupportViews.Sparx_System_RecordsManagementBucket_Census}.[Records Management Bucket] IS NOT NULL
  `;
  const result = await db.db?.query(sql);

  return result?.[0] as DbSystemMaintainer[];
};

export {
  // Expecting additional exports.
  // eslint-disable-next-line import/prefer-default-export
  getIntegrationRecordsIdsValid,
};
