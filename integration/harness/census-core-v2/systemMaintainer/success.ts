// Custom.
import { IIntegrationHarnessRoute } from 'integration/library/types';
import { CMSApp } from 'src/types';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import { getHarnessDb } from 'integration/library/database';
import { SystemMaintainerQueryParams } from 'src/resources/census-core-v2/systemMaintainer/types/find';
import RequestConfig from 'integration/library/RequestConfig';
import { getIntegrationRecordsIdsValid } from './support';

const TEST_RECORD_COUNT = 5;

const harness: IIntegrationHarnessRoute = {
  path: '/page/systemMaintainer',
  calls: [
    {
      description: 'systemMaintainer/find single',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => {
        const db = getHarnessDb(app, DatabaseConfigs.sparxSupport);
        const result = await getIntegrationRecordsIdsValid(db, TEST_RECORD_COUNT);

        return result.map((r) => RequestConfig.fromObject({
          query: {
            id: r['Sparx System GUID'],
          } as SystemMaintainerQueryParams,
        }));
      },
    },
    // @todo-integration Need failure calls. --hrivera
  ],
};

export default harness;
