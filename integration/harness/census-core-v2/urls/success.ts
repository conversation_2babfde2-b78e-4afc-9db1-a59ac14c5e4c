// Custom: App.
import { CMSApp } from 'src/types';
import DatabaseConfigs from 'src/utils/constants/databaseConfigs';
import { UrlFindCensusQueryParams } from 'src/utils/urls';

// Custom: Integration.
import { getHarnessDb } from 'integration/library/database';
import RequestConfig from 'integration/library/RequestConfig';
import { IIntegrationHarnessRoute } from 'integration/library/types';

// Custom: Local.
import { getIntegrationRecordsIdsValid } from './support';

const TEST_RECORD_COUNT = 10;

const harness: IIntegrationHarnessRoute = {
  path: '/page/Urls',
  calls: [
    {
      description: 'Urls/find single',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (app: CMSApp): Promise<RequestConfig[]> => {
        const db = getHarnessDb(app, DatabaseConfigs.sparxSupport);
        const result = await getIntegrationRecordsIdsValid(db, TEST_RECORD_COUNT);

        return result.map((r) => RequestConfig.fromObject({
          query: {
            systemId: r['Sparx System GUID'],
          } as UrlFindCensusQueryParams,
        }));
      },
    },
    {
      description: 'Urls/find single invalid systemId',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({
          query: { systemId: 'INVALID' } as UrlFindCensusQueryParams,
        }),
      ],
    },
    {
      description: 'Urls/find single empty systemId',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({
          query: { systemId: '' } as UrlFindCensusQueryParams,
        }),
      ],
    },
    {
      description: 'Urls/find single undefined systemId',
      expects: 'success',
      method: 'get',
      path: '/',
      statusCode: 200,
      async: true,
      getRequestConfigs: async (): Promise<RequestConfig[]> => [
        RequestConfig.fromObject({
          query: { systemId: undefined as unknown as string } as UrlFindCensusQueryParams,
        }),
      ],
    },
  ],
};

export default harness;
