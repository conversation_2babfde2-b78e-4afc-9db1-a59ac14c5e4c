import { isNaN } from 'lodash';
import MssqlData from 'src/data-sources/mssql';
import { SparxSupportViews } from 'src/utils/constants/views';

interface IUrlsFindResult {
  'Sparx System GUID': string;
}

/**
 * Fetches valid maintainer record sets, which require results from two tables.
 */
const getIntegrationRecordsIdsValid = async (
  db: MssqlData,
  limit: number = 1,
): Promise<IUrlsFindResult[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  const result = await db.queryViewTypedFixed<IUrlsFindResult[]>(
    SparxSupportViews.Sparx_System_URL,
    ['[Sparx System GUID]'],
    {
      limit,
    },
  );

  return result;
};

export {
  // Expecting additional exports.
  // eslint-disable-next-line import/prefer-default-export
  getIntegrationRecordsIdsValid,
};
