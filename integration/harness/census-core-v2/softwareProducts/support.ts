// Modules.
import { isNaN } from 'lodash';

// Custom: App.
import { SparxSupportViews } from 'src/utils/constants/views';
import MssqlData from 'src/data-sources/mssql';

// Custom: Integration.
import { printDBError } from 'integration/library/errors';

const TSoftwareProductsSupport = {
  'Sparx System GUID': '' as string,
} as const;

type TSoftwareProductsSupport = typeof TSoftwareProductsSupport;

const getIntegrationRecordsValid = async (
  db: MssqlData,
  limit: number = 100,
): Promise<TSoftwareProductsSupport[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  try {
    // noinspection SqlResolve
    const sql = `
      SELECT TOP ${limit}
        SYS.[Sparx System GUID]
      FROM
          ${SparxSupportViews.Sparx_System} AS SYS
      INNER JOIN
        ${SparxSupportViews.Sparx_System_API_Category} as CAT
        ON
            SYS.[Sparx System GUID] = CAT.[Sparx System GUID]
      INNER JOIN
          ${SparxSupportViews.Sparx_System_Software_Full} as SOFT
          ON
              SYS.[Sparx System GUID] = SOFT.[Sparx System GUID]
      WHERE
        SYS.[Sparx System GUID] IS NOT NULL
    `;
    const result = await db.db?.query(sql);

    return result?.[0] as TSoftwareProductsSupport[];
  } catch (e) {
    printDBError(e);
    throw e;
  }
};

const getIntegrationRecordsMissingCategory = async (
  db: MssqlData,
  limit: number = 100,
): Promise<TSoftwareProductsSupport[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  try {
    // noinspection SqlResolve
    const sql = `
        SELECT TOP ${limit}
            SYS.[Sparx System GUID]
        FROM
            ${SparxSupportViews.Sparx_System} AS SYS
        LEFT JOIN
            ${SparxSupportViews.Sparx_System_API_Category} as CAT
            ON SYS.[Sparx System GUID] = CAT.[Sparx System GUID]
        LEFT JOIN
            ${SparxSupportViews.Sparx_System_Software_Full} as SOFT
            ON SYS.[Sparx System GUID] = SOFT.[Sparx System GUID]
        WHERE
            SYS.[Sparx System GUID] IS NOT NULL
        AND
            SOFT.[Sparx System GUID] IS NOT NULL
    `;
    const result = await db.db?.query(sql);

    return result?.[0] as TSoftwareProductsSupport[];
  } catch (e) {
    printDBError(e);
    throw e;
  }
};

const getIntegrationRecordsMissingSoftware = async (
  db: MssqlData,
  limit: number = 100,
): Promise<TSoftwareProductsSupport[]> => {
  const check = parseInt(limit.toString(), 10);
  if (isNaN(check)) {
    throw new Error('limit must be a number');
  }

  try {
    // noinspection SqlResolve
    const sql = `
        SELECT TOP ${limit}
            SYS.[Sparx System GUID]
        FROM
            ${SparxSupportViews.Sparx_System} AS SYS
                LEFT JOIN
            ${SparxSupportViews.Sparx_System_API_Category} as CAT
            ON SYS.[Sparx System GUID] = CAT.[Sparx System GUID]
                LEFT JOIN
            ${SparxSupportViews.Sparx_System_Software_Full} as SOFT
            ON SYS.[Sparx System GUID] = SOFT.[Sparx System GUID]
        WHERE
            SYS.[Sparx System GUID] IS NOT NULL
          AND
            CAT.[Sparx System GUID] IS NOT NULL
    `;
    const result = await db.db?.query(sql);

    return result?.[0] as TSoftwareProductsSupport[];
  } catch (e) {
    printDBError(e);
    throw e;
  }
};

export {
  TSoftwareProductsSupport,
  getIntegrationRecordsMissingCategory,
  getIntegrationRecordsMissingSoftware,
  getIntegrationRecordsValid,
};
