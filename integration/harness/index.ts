// Custom: Integration.
import { IIntegrationHarnessPackage } from 'integration/library/types';

import harnessCedarCoreV2 from './cedar-core-v2';
import harnessCedarIntake from './cedarintake';
import harnessCensusCoreV2 from './census-core-v2';
import harnessGateway from './gateway';
import harnessLdap from './ldap';

// noinspection JSUnusedGlobalSymbols
const harnessAll: IIntegrationHarnessPackage[] = [
  harnessCedarCoreV2,
  harnessCedarIntake,
  harnessCensusCoreV2,
  harnessGateway,
  harnessLdap,
];

export default harnessAll;
