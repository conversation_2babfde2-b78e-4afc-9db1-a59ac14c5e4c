// Custom: App.
import { CMSApp } from 'src/types';

// Custom: Integration.
import RequestConfig from './RequestConfig';
import { assert, assertDiff } from './assert';

const getRequestConfigsEmptySingle = async (
  _app: CMSApp,
): Promise<RequestConfig[]> => [
  RequestConfig.fromObject({}),
];

const validateNotFound = async (
  local: Response,
  remote: Response,
) => {
  const localBody = await local.json();
  const remoteBody = await remote.json();

  const regexes = [
    // These could be combined, but keeping them separate makes them easier to grok.
    {
      regex: /(Invocation Time:)[^,]+,/,
      replacement: '$1,',
    },
    {
      regex: /(Date:)[^,]+,/,
      replacement: '$1,',
    },
    {
      regex: /(Client IP - )[^,]+,/,
      replacement: '$1,',
    },
  ];

  regexes.forEach((r) => {
    localBody.Exception = localBody.Exception.replace(r.regex, r.replacement);
    remoteBody.Exception = remoteBody.Exception.replace(r.regex, r.replacement);
  });

  assert(local.status === 404, 'local status is not 404');
  assert(remote.status === 404, 'remote status is not 404');
  assertDiff(remoteBody, localBody);
};

export {
  getRequestConfigsEmptySingle,
  validateNotFound,
};
