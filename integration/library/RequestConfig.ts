// Custom: App.
import { EndpointQueryParams } from 'src/utils/express/responses';

// Custom: Integration.
import { TUndefinable } from 'integration/library/types';

interface IRequestConfig {
  target?: 'local' | 'webmethods'
  body?: object
  pathParams?: Record<string, string>
  query?: Record<string, string | string[] | undefined>
}

/**
 * Implements class for IRequestConfig so that it can be checked at runtime.
 */
class RequestConfig implements IRequestConfig {
  public body: TUndefinable<object> = undefined;
  public pathParams: TUndefinable<Record<string, string>> = undefined;
  public query: TUndefinable<Record<string, string | string[]>> = undefined;

  static fromArgs(
    query: TUndefinable<EndpointQueryParams>,
    body?: TUndefinable<object>,
    pathParams?: TUndefinable<Record<string, string>>,
  ): RequestConfig {
    return Object.assign(
      new RequestConfig(),
      { query, body, pathParams },
    );
  }

  static fromObject(
    config: IRequestConfig,
  ): RequestConfig {
    return Object.assign(
      new RequestConfig(),
      config,
    );
  }
}

export { IRequestConfig };

export default RequestConfig;
