// Custom: Integration.
import { TUndefinable } from 'integration/library/types';

interface IRequestConfig {
  target?: 'local' | 'webmethods'
  body?: object
  query?: Record<string, string | string[] | undefined>
}

/**
 * Implements class for IRequestConfig so that it can be checked at runtime.
 */
class RequestConfig implements IRequestConfig {
  public body: TUndefinable<object> = undefined;
  public query: TUndefinable<Record<string, string | string[]>> = undefined;

  static fromArgs(
    query: Record<string, string | string[]>,
    body?: object,
  ): RequestConfig {
    return Object.assign(
      new RequestConfig(),
      { query, body },
    );
  }

  static fromObject(
    config: IRequestConfig,
  ): RequestConfig {
    return Object.assign(
      new RequestConfig(),
      config,
    );
  }
}

export { IRequestConfig };

export default RequestConfig;
