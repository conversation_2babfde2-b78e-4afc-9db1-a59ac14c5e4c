import { isError } from 'lodash';
import { DatabaseError, AggregateError } from 'sequelize';
import { inspect } from 'util';
import Printer from './Printer';

const printDBError = (
  error: unknown,
) => {
  if (error && error instanceof DatabaseError) {
    if (error.original instanceof AggregateError) {
      const aggregateErrors = error.original.errors.map((e) => e.message);

      Printer.p(inspect(aggregateErrors));
    }
  } else if (isError(error)) {
    Printer.p(inspect(error));
  }
};

export {
  // eslint-disable-next-line import/prefer-default-export
  printDBError,
};
