// Modules.
import { isArray } from 'lodash';
import pMap from 'p-map';
import { inspect } from 'util';

// Custom: App.
import { CMSApp } from 'src/types';
import { unknownAsError } from 'src/utils/general';

// Custom: Integration.
import { sendApiRequest } from 'integration/reader';
import {
  IAuthentication,
  IConfig,
  IIntegrationHarnessCall,
  ITestResponse, TLastResult,
  TUndefinable,
} from './types';
import { assert, assertDiff } from './assert';
import Printer from './Printer';
import RequestConfig, { IRequestConfig } from './RequestConfig';
import RequestConfigSplit from './RequestConfigSplit';

class ProcessCall {
  public config: IConfig;
  public auth: IAuthentication;
  public call: IIntegrationHarnessCall;
  public routePath: string;
  public app: CMSApp;

  constructor(
    config: IConfig,
    auth: IAuthentication,
    call: IIntegrationHarnessCall,
    routePath: string,
    app: CMSApp,
  ) {
    this.config = config;
    this.auth = auth;
    this.call = call;
    this.routePath = routePath;
    this.app = app;
  }

  public async process(
    lastResult: TLastResult,
  ): Promise<TLastResult> {
    const requestConfigs = await this.call.getRequestConfigs(this.app, lastResult);

    if (this.call.skip) {
      Printer.pi(`skipping call ${this.call.method} ${this.routePath}/${this.call.path}`, 3);
      return [];
    }

    // Some tests (like addition before deletion) result in two records
    // being added to the database (one local, one remote, same db).
    //
    // The list of delete records is split, with one going to express
    // and one to webmethods.
    if (requestConfigs instanceof RequestConfigSplit) {
      // noinspection UnnecessaryLocalVariableJS
      const response = await this.processRequestConfigSplit(requestConfigs);

      return response;
    }

    // First check there is at least one config.
    if (isArray(requestConfigs) && !requestConfigs.length) {
      throw new Error('process: requestConfigs is empty');
    }

    if (isArray(requestConfigs) && requestConfigs[0] instanceof RequestConfig) {
      // The async flag was set, process the configs concurrently.
      if (this.call.async) {
        let completed = 0;
        const total = requestConfigs.length;

        const mapper = async (v: RequestConfig, _index: number) => {
          const result = await this.processRequestConfig(v);
          completed += 1;

          // Print running total in place on the same line.
          process.stdout.write(`\r${'\t'.repeat(3)}Completed: ${completed}/${total}`);

          return result;
        };

        const results = await pMap(
          requestConfigs,
          mapper,
          { concurrency: this.call.asyncConcurrency ?? 10 },
        );

        return results.filter((r) => r !== undefined).flat();
      }

      // Otherwise run normally.
      const results: TLastResult = [];

      await requestConfigs.reduce(async (promise, requestConfig): Promise<void> => {
        await promise;

        const result = await this.processRequestConfig(requestConfig);
        if (result) {
          results.push(...result);
        }
      }, Promise.resolve());

      return results;
    }

    throw new Error(`process: invalid request config:\n${inspect(requestConfigs)}`, {
      cause: requestConfigs,
    });
  }

  public async processConfig(
    responseLocal: Response,
    responseWebmethods: Response,
  ): Promise<TLastResult> {
    let lastResponse: ITestResponse;

    // Call defines a custom validator, use it instead of the default below.
    if (this.call.validate) {
      await this.call.validate(responseLocal, responseWebmethods);
      lastResponse = {
        json: {},
        status: 200,
        headers: new Headers(),
      };
    } else {
      const jsonLocal = await ProcessCall.getJsonFromResponse(responseLocal, 'local');
      const jsonWebmethods = await ProcessCall.getJsonFromResponse(responseWebmethods, 'webmethods');

      lastResponse = {
        json: jsonLocal,
        status: responseLocal.status,
        headers: responseLocal.headers,
      };

      // @todo-refactor It would be nice to allow graceful failure
      //                and print report at the end. --hrivera
      assert(
        responseLocal.status === this.call.statusCode,
        `local status is not ${this.call.statusCode}\n${JSON.stringify(jsonLocal, null, 2)}`,
      );
      assert(
        responseWebmethods.status === this.call.statusCode,
        `webmethods status is not ${this.call.statusCode}\n${JSON.stringify(jsonWebmethods, null, 2)}`,
      );
      assert(
        responseLocal.status === responseWebmethods.status,
        `local and webmethods status do not match local:${responseLocal.status} != wm:${responseWebmethods.status}`,
      );
      assertDiff(
        jsonWebmethods,
        jsonLocal,
      );
    }

    const finalResponse = {
      resultLocal: true,
      resultWebmethods: true,
      lastResponse,
      callbackValues: {},
    };

    if (this.call.callback) {
      const callback = this.call.callback(lastResponse.headers, lastResponse.json);

      finalResponse.callbackValues = {
        ...callback,
      };
    }

    return [finalResponse];
  }

  public async processRequestConfig(
    config: RequestConfig,
  ): Promise<TUndefinable<TLastResult>> {
    const responseLocal = await this.requestLocal(config);
    const responseWebmethods = await this.requestWebmethods(config);

    return this.processConfig(responseLocal, responseWebmethods);
  }

  public async processRequestConfigSplit(
    config: RequestConfigSplit,
  ): Promise<TLastResult> {
    const responseLocal = await this.requestLocal(config.local);
    const responseWebmethods = await this.requestWebmethods(config.webmethods);

    return this.processConfig(responseLocal, responseWebmethods);
  }

  private static async getJsonFromResponse(
    response: Response,
    source: string,
  ): Promise<Record<string, unknown>> {
    // Read the text first so it can be printed if there is an error.
    const text = await response.text();

    try {
      return JSON.parse(text);
    } catch (e) {
      Printer.p(`ERROR: Failed to parse ${source} response as JSON:`, unknownAsError(e).toString());
      Printer.p('Response text was:\n', text);
      throw e;
    }
  }

  public async requestLocal(
    requestConfig: IRequestConfig,
  ): Promise<Response> {
    const headers = {
      'x-jwt-key': this.auth.jwt,
      Cookie: this.auth.cookie,
    };

    return sendApiRequest(
      this.call,
      this.config.environments.local.url,
      this.routePath,
      headers,
      requestConfig,
    );
  }

  public async requestWebmethods(
    requestConfig: IRequestConfig,
  ): Promise<Response> {
    const headers = {
      'x-gateway-apikey': process.env.WEBMETHODS_API_KEY_DEV ?? '',
    };

    return sendApiRequest(
      this.call,
      this.config.environments.dev.url,
      this.routePath,
      headers,
      requestConfig,
    );
  }
}

export default ProcessCall;
