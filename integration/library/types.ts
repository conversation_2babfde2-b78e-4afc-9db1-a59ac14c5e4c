// Custom: App.
import { CMSApp } from 'src/types';

// Custom: Integration.
import MssqlData from 'src/data-sources/mssql';
import RequestConfig from './RequestConfig';
import RequestConfigSplit from './RequestConfigSplit';

interface IAuthentication {
  jwt: string
  cookie: string
}

interface IConfig {
  environments: {
    [K in 'local' | 'dev' | 'impl' | 'prod']: {
      url: string
    }
  }
}

interface IIntegrationHarnessCall {
  // Required.
  description: string
  expects: 'success' | 'failure'
  method: 'get' | 'post' | 'put' | 'delete'
  path: string
  statusCode: number
  getRequestConfigs: FGetRequestConfigsCallback
  // Optional.
  async?: boolean
  asyncConcurrency?: number

  input?: boolean
  database?: string
  body?: object
  query?: Record<string, string>
  headers?: Record<string, string>
  skip?: boolean
  callback?: (headers: Headers, body: object) => object
  handler?: () => Promise<void>
  validate?(localResponse: Response, remoteResponse: Response): Promise<void>
}

interface IIntegrationHarnessRoute {
  calls: IIntegrationHarnessCall[]
  path: string
}

interface IIntegrationHarnessPackage {
  description: string
  name: string
  path: string
  routes: IIntegrationHarnessRoute[]
}

interface ITestResponse {
  json: object
  status: number
  headers: Headers
}

interface ICallResponse {
  lastResponse: ITestResponse
  callbackValues: object
}

interface ILastResult extends ICallResponse {
  resultLocal: boolean
  resultWebmethods: boolean
}

// Types
// -----
type FGetDatabaseRecordsForParams = (
  db: MssqlData,
  limit?: number
) => Promise<Record<string, unknown>[]>;

type FGetRequestConfigsCallback = (
  app: CMSApp,
  lastResult?: TLastResult,
) => Promise<RequestConfig[] | RequestConfigSplit>;

type TLastResult = ILastResult[];

type TUndefinable<T> = T | undefined;

export {
  FGetDatabaseRecordsForParams,
  FGetRequestConfigsCallback,
  IAuthentication,
  ICallResponse,
  IConfig,
  IIntegrationHarnessCall,
  IIntegrationHarnessPackage,
  IIntegrationHarnessRoute,
  ILastResult,
  ITestResponse,
  TLastResult,
  TUndefinable,
};
