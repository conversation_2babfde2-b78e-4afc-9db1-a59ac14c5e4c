// Custom: App.
import { CMSApp } from 'src/types';

// Custom: Integration.
import RequestConfig from './RequestConfig';
import RequestConfigSplit from './RequestConfigSplit';

export const NOTE_PAGE_NAME = 'TEST_INTEGRATION_PAGE_10';

interface IAuthentication {
  jwt: string
  cookie: string
}

interface IConfig {
  environments: {
    [K in 'local' | 'dev' | 'impl' | 'prod']: {
      url: string
    }
  }
}

interface IHeaders {
  [key: string]: string
}

interface IIntegrationHarnessCall {
  // Required.
  description: string
  expects: 'success' | 'failure'
  method: 'get' | 'post' | 'put' | 'delete'
  path: string
  statusCode: number
  getRequestConfigs(
    app: CMSApp,
    lastResult?: TLastResult,
  ): Promise<RequestConfigSplit | RequestConfig[]>

  // Optional.
  async?: boolean
  asyncConcurrency?: number

  input?: boolean
  database?: string
  body?: object
  query?: Record<string, string>
  headers?: Record<string, string>
  skip?: boolean
  callback?: (headers: Headers, body: object) => object
  handler?: () => Promise<void>
  validate?(localResponse: Response, remoteResponse: Response): Promise<void>
}

interface IIntegrationHarnessRoute {
  calls: IIntegrationHarnessCall[]
  path: string
}

interface IIntegrationHarnessPackage {
  description: string
  name: string
  path: string
  routes: IIntegrationHarnessRoute[]
}

interface ITestResponse {
  json: object
  status: number
  headers: Headers
}

interface ICallResponse {
  lastResponse: ITestResponse
  callbackValues: object
}

interface ILastResult extends ICallResponse {
  resultLocal: boolean
  resultWebmethods: boolean
}

interface IWebmethodsResponse {
  result: string
  message: string | string[]
}

// Types.
// ------

type TLastResult = ILastResult[];

type TUndefinable<T> = T | undefined;

export {
  IAuthentication,
  ICallResponse,
  IConfig,
  IHeaders,
  IIntegrationHarnessCall,
  IIntegrationHarnessPackage,
  IIntegrationHarnessRoute,
  ILastResult,
  ITestResponse,
  IWebmethodsResponse,
  TLastResult,
  TUndefinable,
};
