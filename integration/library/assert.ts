// Modules.
import { diff } from 'jest-diff';

// Custom: Integration.
import Printer from 'integration/library/Printer';

// Types.
class AssertionError extends Error {}

const assert = (condition: boolean, message: string) => {
  if (!condition) {
    throw new AssertionError(message);
  }
};

const assertDiff = (expected: object, received: object, message: string = 'object mismatch') => {
  const d = diff(expected, received);

  if (!d) {
    throw new AssertionError('diff failed');
  }

  if (d && !d.includes('Compared values have no visual difference.')) {
    Printer.p(d);
    throw new AssertionError(message);
  }
};

const assertSuccess = (response: Response) => {
  assert(response.status === 200, 'status is not 200');
};

const diffCheck = (expected: object, received: object): boolean => {
  const d = diff(expected, received);

  return d !== null && !d.includes('Compared values have no visual difference.');
};

export {
  assert,
  assertDiff,
  assertSuccess,
  diffCheck,
};
