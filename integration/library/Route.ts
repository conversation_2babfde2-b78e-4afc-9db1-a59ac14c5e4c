/**
 * Basic utility class for managing integration routes.
 */
class Route {
  /**
   * Construct an endpoint route URL.
   */
  public static getUrl(
    baseUrl: string,
    prefix: string,
    urlPath: string,
    query?: Record<string, string | string[] | undefined>,
    pathParams?: Record<string, string>,
  ): string {
    // Ensure baseUrl has a protocol
    if (!baseUrl.startsWith('http:') && !baseUrl.startsWith('https:')) {
      throw new Error(`baseUrl must start with a protocol (http: or https:), got: ${baseUrl}`);
    }

    // noinspection RegExpUnnecessaryNonCapturingGroup
    const pathParts = [prefix, urlPath]
      .map((part) => part.replace(/(?:^\/+)|(?:\/+$)/g, '')) // Remove leading/trailing slashes.
      .filter((part) => part.length > 0 && part !== '/'); // Remove empty or slash parts.

    const fixed = baseUrl.replace(/(\/)*$/, '');
    const url = new URL(`/${pathParts.join('/')}`, fixed);

    if (pathParams) {
      Object.entries(pathParams).forEach(([key, value]) => {
        url.pathname = url.pathname.replace(`:${key}`, value);
      });
    }

    // Add query parameters if provided
    if (query) {
      Object.entries(query).forEach(([key, value]) => {
        // Keep this for when your hair is falling out:
        // if (value === undefined) {
        //   Printer.pi(`WARNING: query parameter ${key} is undefined`, 3);
        // }

        // Allow undefined to be passed for certain tests.
        url.searchParams.set(key, value as string);
      });
    }

    return url.toString();
  }
}

export default Route;
