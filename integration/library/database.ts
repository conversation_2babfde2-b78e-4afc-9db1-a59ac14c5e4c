// Modules.
import express from 'express';
import { isError } from 'lodash';

// Custom.
import { getDb } from 'src/utils/db-helpers';
import initConfig from 'src/core/app-factory/config';
import { addLoggingToApp } from 'src/core/app-factory/logger';
import { registerAppDataSources } from 'src/core/app-factory/data-sources';
import { initSecrets } from 'src/subsystems/secrets';
import MssqlData from 'src/data-sources/mssql';
import { CMSApp, MssqlDataOptions } from 'src/types';

/**
 * Creates a minimal CMSApp instance for integration testing with database connections
 */
const createIntegrationApp = async (): Promise<CMSApp> => {
  const baseApp = express() as CMSApp;

  // Initialize config, logging, and secrets (minimal setup)
  initConfig(baseApp);
  addLoggingToApp(baseApp);
  registerAppDataSources(baseApp);

  // Initialize secrets from config
  const { secretStrings } = baseApp.config;
  await initSecrets(baseApp, secretStrings);

  return baseApp;
};

/**
 * Connects to the Sparx database using existing app factory functions
 */
export const connectToSparx = async (app?: CMSApp): Promise<CMSApp> => {
  const integrationApp = app || await createIntegrationApp();

  await integrationApp.dataSources.register<MssqlData, MssqlDataOptions>(
    integrationApp,
    'sparxea',
    MssqlData,
    {
      dbName: 'sparx_support_db',
    },
  );

  return integrationApp;
};

/**
 * Connects to the Cedar database using existing app factory functions
 */
export const connectToCedar = async (app?: CMSApp): Promise<CMSApp> => {
  const integrationApp = app || await createIntegrationApp();

  await integrationApp.dataSources.register<MssqlData, MssqlDataOptions>(
    integrationApp,
    'cedarSupport',
    MssqlData,
    {
      dbName: 'cedar_support_db',
    },
  );

  return integrationApp;
};

/**
 * Connects to both Sparx and Cedar databases
 */
export const connectToSparxAndCedar = async (): Promise<CMSApp> => {
  const app = await createIntegrationApp();
  await connectToSparx(app);
  await connectToCedar(app);
  return app;
};

/**
 * Closes all database connections for cleanup
 */
export const closeConnections = async (app: CMSApp): Promise<void> => {
  const promises: Promise<void | boolean>[] = [];

  if (app.dataSources.sparxea) {
    promises.push(app.dataSources.sparxea.closeConnection());
  }

  if (app.dataSources.cedarSupport) {
    promises.push(app.dataSources.cedarSupport.closeConnection());
  }

  await Promise.all(promises);
};

export const getHarnessDb = (app: CMSApp, name: string): MssqlData => {
  const db = getDb<MssqlData>(app, name);
  if (isError(db)) {
    throw db;
  }

  return db;
};
