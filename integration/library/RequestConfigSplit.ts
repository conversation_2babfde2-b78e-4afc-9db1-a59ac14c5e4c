// Custom: Integration.
import { IRequestConfig } from 'integration/library/RequestConfig';

interface IRequestConfigSplit {
  local: IRequestConfig
  webmethods: IRequestConfig
}

class RequestConfigSplit implements IRequestConfigSplit {
  public local: IRequestConfig;
  public webmethods: IRequestConfig;

  constructor(
    local: IRequestConfig,
    webmethods: IRequestConfig,
  ) {
    this.local = local;
    this.webmethods = webmethods;
  }
}

export {
  IRequestConfigSplit,
};

export default RequestConfigSplit;
