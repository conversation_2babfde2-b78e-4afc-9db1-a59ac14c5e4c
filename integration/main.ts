// Modules.
import dotenv from 'dotenv';
import { hideBin } from 'yargs/helpers';
import yargs from 'yargs';

// Custom: Harness.
import { IIntegrationHarnessPackage } from 'integration/library/types';
import { connectToSparxAndCedar, closeConnections } from 'integration/library/database';
import authenticate from 'integration/library/authenticate';
import harnessAll from 'integration/harness';
import config from 'integration/harnessConfig';
import { processPackage } from 'integration/runner';
import Printer from './library/Printer';

/**
 * Filters package routes based on the provided filter string
 */
const filterPackage = (
  harnessPackage: IIntegrationHarnessPackage,
  filter?: string,
): IIntegrationHarnessPackage => {
  if (!filter) {
    return harnessPackage;
  }

  const filteredRoutes = harnessPackage.routes.filter(
    (r) => r.calls.some((c) => {
      const callPath = [harnessPackage.path, r.path, c.path].join('');
      return callPath.startsWith(filter);
    }),
  );

  return {
    ...harnessPackage,
    routes: filteredRoutes,
  };
};

const filterHarnesses = (harnesses: IIntegrationHarnessPackage[], filter?: string) => {
  if (!filter) {
    return harnesses;
  }

  const filteredHarness = harnesses.map((harness) => filterPackage(harness, filter));

  if (filteredHarness.flatMap((h) => h.routes).length === 0) {
    process.stdout.write(`No routes found matching filter: ${filter}\n`);
    return [];
  }

  if (filteredHarness.length === 0) {
    throw new Error(`No harnesses found matching filter: ${filter}\n`);
  }

  return filteredHarness;
};

// noinspection FunctionWithMultipleLoopsJS -- nested here is acceptable.
/**
 * Dumps (prints) the top level configuration, which is a list of packages.
 */
const dumpHarness = (all: IIntegrationHarnessPackage[]) => {
// noinspection FunctionWithMultipleLoopsJS -- nested here is acceptable.
  all.forEach((harness) => {
    Printer.p(`${harness.name}: ${harness.routes.length} route(s)`);
    Printer.pi(`harness.path: ${harness.path}\n`, 1);
    Printer.pi('harness.routes: ', 1);

    harness.routes.forEach((route) => {
      Printer.pi(`route.path: ${route.path} ${harness.path}${route.path}`, 2);

      route.calls.forEach((call) => {
        Printer.pi(`call.path: ${call.path} ${harness.path}${route.path}${call.path}`, 3);
      });
    });
  });
};

/**
 * Main entrypoint.
 */
const main = async (filter?: string, debug?: boolean) => {
  dotenv.config();

  if (filter) {
    process.stdout.write(`Applying filter: ${filter}\n`);
  }

  if (debug) {
    dumpHarness(harnessAll);
    return;
  }

  // Setup database connections for Sparx and Cedar
  const app = await connectToSparxAndCedar();

  try {
    const auth = await authenticate(config);

    // Filter the harness package if a filter is provided.
    const filteredHarness = filterHarnesses(harnessAll, filter);

    if (filteredHarness.length === 0) {
      return;
    }

    // Pass the app with database connections to your processing
    await filteredHarness.reduce(async (promise, harness) => {
      await promise;

      Printer.p([
        `Processing ${harness.routes.length} route(s) for ${harness.name}`,
        ...harness.routes.map((r) => `  ${harness.path}${r.path}`),
      ].join('\n'));

      return processPackage(config, auth, harness, app);
    }, Promise.resolve());
  } finally {
    // Clean up database connections
    await closeConnections(app);
  }
};

(async () => {
  // Parse command line arguments.
  const { argv } = yargs(hideBin(process.argv))
    .option('filter', {
      alias: 'f',
      type: 'string',
      description: 'Filter routes by path prefix',
    })
    .option('debug', {
      alias: 'd',
      type: 'boolean',
      description: 'Enable debug mode',
      default: false,
    })
    .help();

  const args = await argv;

  await main(args.filter, args.debug);
})();
