import { defineConfig } from 'vitest/config';
import path from 'path';

const config = defineConfig({
  logLevel: 'error',
  resolve: {
    alias: [
      {
        find: /^src\/(.*)/,
        replacement: path.resolve(process.cwd(), 'src/$1'),
      },
      {
        find: /^integration\/(.*)/,
        replacement: path.resolve(process.cwd(), 'integration/$1'),
      },
    ],
  },
  test: {
    globals: true,
  },
});

export default config;
