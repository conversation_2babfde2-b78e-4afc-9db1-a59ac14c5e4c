import Printer from 'integration/library/Printer';
import Route from 'integration/library/Route';
import {
  IIntegrationHarnessCall,
} from 'integration/library/types';
import { IRequestConfig } from 'integration/library/RequestConfig';

// @todo-refactor This can probably be moved into ProcessCall. --hrivera
// noinspection OverlyComplexFunctionJS
const sendApiRequest = async (
  call: IIntegrationHarnessCall,
  baseUrl: string,
  routePath: string,
  headers: Record<string, string>,
  requestConfig: IRequestConfig,
): Promise<Response> => {
  const requestOptions: RequestInit = {
    method: call.method,
    headers: {
      'content-type': 'application/json',
      ...headers,
    },
  };

  // Double-check config.
  if (requestConfig.body && call.method === 'get') {
    throw new Error('sendApiRequest: body not allowed for GET requests');
  }

  if (requestConfig.body) {
    requestOptions.body = JSON.stringify(requestConfig.body);
  }

  const url = Route.getUrl(
    baseUrl,
    routePath,
    call.path,
    requestConfig.query,
    requestConfig.pathParams,
  ).replace(/\/$/, '');

  const request = new Request(url, requestOptions);
  const response = await fetch(url, request);

  if (!call.async) {
    const debug = [
      `status=${response.status}`,
      `host=${new URL(url).hostname}`,
      `content-length=${response.headers.get('content-length')}`,
    ];
    Printer.pi(debug.join(' '), 3);
  }

  return response;
};

export {
  // eslint-disable-next-line import/prefer-default-export
  sendApiRequest,
};
