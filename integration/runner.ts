import { CMSApp } from 'src/types';
import {
  IAuthentication, IConfig, IIntegrationHarnessCall,
  IIntegrationHarnessPackage, IIntegrationHarnessRoute, TLastResult, TUndefinable,
} from 'integration/library/types';
import ProcessCall from 'integration/library/ProcessCall';
import Printer from 'integration/library/Printer';

// noinspection OverlyComplexFunctionJS
const processCall = async (
  config: IConfig,
  auth: IAuthentication,
  call: IIntegrationHarnessCall,
  routePath: string,
  app: CMSApp,
  lastResult: TLastResult,
): Promise<TLastResult> => {
  Printer.pi(
    [
      'processing call ',
      `description="${call.description}" `,
      `expects="${call.expects}" `,
      `method="${call.method.toUpperCase()}" `,
      `async="${call.async}" `,
      // `url="${routePath}${call.path.replace(/\/$/, '')}"`,
    ].join(''),
    2,
  );

  const processor = new ProcessCall(config, auth, call, routePath, app);

  if (call.async) {
    return processor.process(lastResult);
  }

  return processor.process(lastResult);
};

const processRoute = async (
  config: IConfig,
  auth: IAuthentication,
  route: IIntegrationHarnessRoute,
  packagePath: string,
  app: CMSApp,
): Promise<void> => {
  Printer.pi('-'.repeat(80), 1);
  Printer.pi(`processing route url="${packagePath}${route.path}"`, 1);

  let lastResult: TUndefinable<TLastResult>;
  const routePath = `${packagePath}${route.path}`;

  await route.calls.reduce(async (promise, call) => {
    await promise;

    if (call.async) {
      const responses = await processCall(
        config,
        auth,
        call,
        routePath,
        app,
        [],
      );

      if (Array.isArray(responses)) {
        const foundError = responses.some((r) => !r?.resultLocal || !r?.resultWebmethods);

        // Print a symbol and finish the line.
        if (foundError) {
          Printer.p(' ❌');
        } else {
          Printer.p(' ✅');
        }
      }
    } else {
      lastResult = await processCall(
        config,
        auth,
        call,
        routePath,
        app,
        lastResult ?? [],
      );
    }
  }, Promise.resolve());
};

const processPackage = async (
  config: IConfig,
  auth: IAuthentication,
  harnessPackage: IIntegrationHarnessPackage,
  app: CMSApp,
): Promise<void> => {
  Printer.p([
    'processing package ',
    `name="${harnessPackage.name}" `,
    `description="${harnessPackage.description}" `,
    `path="${harnessPackage.path}"`,
  ].join(''));

  await harnessPackage.routes.reduce(async (promise, route) => {
    await promise;
    return processRoute(config, auth, route, harnessPackage.path, app);
  }, Promise.resolve());
};

export {
  processCall,
  processPackage,
  processRoute,
};
