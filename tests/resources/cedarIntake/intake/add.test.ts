import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get, cloneDeep } from 'lodash';
import intakeAddModule from '../../../../src/resources/cedarIntake/intake/add';
import intakeAddConfig from '../../../../src/resources/cedarIntake/intake';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
  getSchema: vi.fn(),
  validateWithSchema: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/intake-schema'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSchema: mocks.getSchema,
    validateWithSchema: mocks.validateWithSchema,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeAddConfig);
app.resources.initResources(app);

type RequestBody = {
  body?: string;
  bodyFormat?: string;
  clientCreatedDate?: string;
  clientId?: string;
  clientLastUpdatedDate?: string;
  clientStatus?: string;
  schema?: string;
  type?: string;
  version?: string;
};

const body: RequestBody = {
  body: '{"foo":"bar"}',
  bodyFormat: 'JSON',
  clientCreatedDate: '2024-12-23T12:59:27.070Z',
  clientId: 'ba03e366-48c5-49ac-919a-3f79fef70b9c',
  clientLastUpdatedDate: '2024-12-23T13:07:12.857Z',
  clientStatus: 'INITIAL_REQUEST_FORM_SUBMITTED',
  schema: 'BusinessCase05',
  type: 'EASIIntake',
  version: '1',
};

describe('Intake Add tests', () => {
  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/', 'post', intakeAddModule);

    const response = await request(badApp).post('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve system application',
    });
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Database Unavailable');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if CLIENT_NAME is not in the header', async () => {
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('HTTP Headers missing CLIENT_NAME');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Client name not provided in the header');
  });

  it('Should return an error if there is no body', async () => {
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi');

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No body provided to the request');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No body provided to the request');
  });

  it('Should return an error if schema is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.schema;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No schema provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if body is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.body;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No body provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No body provided');
  });

  it('Should return an error if bodyFormat is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.bodyFormat;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No bodyFormat provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No bodyFormat provided');
  });

  it('Should return an error if the bodyFormat is not JSON or XML', async () => {
    const badBody = cloneDeep(body);
    badBody.bodyFormat = 'SOAP';
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid bodyFormat provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid bodyFormat provided');
  });

  it('Should return an error if the bodyFormat is JSON and it does not parse', async () => {
    const badBody = cloneDeep(body);
    badBody.bodyFormat = 'JSON';
    badBody.body = '{"foo":"bar"';
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('The body is not valid JSON');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('The body is not valid JSON');
  });

  it('Should return an error if the bodyFormat is XML and it does not parse', async () => {
    const badBody = cloneDeep(body);
    badBody.bodyFormat = 'XML';
    badBody.body = 'htmk>body>h1>Boom/head>';
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('The body is not valid XML');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No parsable XML data');
  });

  it('Should return an error if validatePayload is true and getSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body)
      .query({ validatePayload: 'true' });

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to retrieve validation schema');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if validatePayload is true and validateWithSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce({});
    mocks.validateWithSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body)
      .query({ validatePayload: 'true' });

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred while setting up the schema validator');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if clientId is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.clientId;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No client ID provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No client ID provided');
  });

  it('Should return an error if type is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.type;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No type provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No type provided');
  });

  it('Should return an error if clientStatus is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.clientStatus;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No client status provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No client status provided');
  });

  it('Should return an error if clientCreatedDate is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.clientCreatedDate;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No client created date provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No client created date provided');
  });

  it('Should return an error if clientUpdatedDate is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.clientLastUpdatedDate;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No client last updated date provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No client last updated date provided');
  });

  it('Should return an error if version is not included in the body', async () => {
    const badBody = cloneDeep(body);
    delete badBody.version;
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(badBody);

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No version provided');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('No version provided');
  });

  it('Should return an error if queryStoredProcedures returns a critical error', async () => {
    mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Bad Critical Connection'));
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body);

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred inserting the intake request');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return an error if queryStoredProcedures returns an error', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body);

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred inserting the intake request');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if queryStatus does not equal 0', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 1, result: null }]]);
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body);

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to insert the intake');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to insert the intake');
  });

  it('Should return a message with a successful insert', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0, result: 1234 }]]);
    const response = await request(app)
      .post('/')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .send(body);

    expect(response.status).toEqual(200);
    expect(response.body).toHaveProperty('result');
    expect(response.body).toHaveProperty('message');
    expect(response.body.result).toEqual('success');
    expect(response.body.message).toEqual(['Record successfully persisted. Cedar ID: 1234']);
  });
});
