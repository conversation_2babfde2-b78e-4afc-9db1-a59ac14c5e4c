import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeByCedarIdModule from '../../../../src/resources/cedarIntake/intakeFind/intakeByCedarId';
import intakeByCedarIdConfig from '../../../../src/resources/cedarIntake/intakeFind';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeByCedarIdConfig);
app.resources.initResources(app);

const dbResults = [{
  cedarId: '16',
  clientId: '12356',
  version: 'v01',
  type: 'System Intake',
  cedarStatus: 'On Hold',
  cedarStatusMessage: '',
  clientStatus: 'New',
  bodyFormat: 'JSON',
  body: "{'adminLead':'','archivedAt':'','businessNeed':'Test Business Need','businessOwner':'Test Business Owner','businessOwnerComponent':'Test Business Owner Component','component':'Test Component','contractEndDate':'2021-03-05T11:04:00.123Z','contractStartDate':'2021-03-05T11:04:00.123Z','contractVehicle':'','contractor':'','costIncrease':'NO','costIncreaseAmount':'','decidedAt':'','decisionNextSteps':'','eaCollaborator':'Test EA Collaborator','eaCollaboratorName':'','eaSupportRequest':'false','existingContract':'NOT_NEEDED','existingFunding':'true','fundingNumber':'123456','fundingSource':'CLIA','grbDate':'','grtDate':'','grtReviewEmailBody':'','isso':'Test ISSO','issoName':'','lifecycleExpiresAt':'','lifecycleID':'12345','lifecycleScope':'','oitSecurityCollaborator':'Test OIT Collaborator','oitSecurityCollaboratorName':'','processStatus':'Just an idea','productManager':'Test Product Manager','productManagerComponent':'Test Product Manager Component','projectAcronym':'','projectName':'Test Project Name','rejectionReason':'','requestType':'NEW','requester':'Test Requester','requesterEmailAddress':'','solution':'Test Solution','status':'INTAKE_DRAFT','submittedAt':'','trbCollaborator':'Test TRB Collaborator','trbCollaboratorName':'','userEUA':'P8DB'}",
  clientCreatedDate: '2021-03-05T06:04:00.123Z',
  clientLastUpdatedDate: null,
  cedarCreatedDate: '2022-04-01T13:05:21.727Z',
  cedarLastUpdatedDate: '2024-02-02T20:00:19.183Z',
  schema: 'System Intake v01',
}];

describe('Find Intake By Cedar Id', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', intakeByCedarIdModule);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/16').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 400 and an error if http header CLIENT_NAME is missing', async () => {
    const response = await request(app).get('/12').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'HTTP Headers missing CLIENT_NAME' });
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app).get('/16').set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake by cedar ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app).get('/16').set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake by cedar ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 400 and an error if Intake could not be found', async () => {
    mocks.queryView.mockResolvedValueOnce([[], 0]);

    const response = await request(app).get('/12').set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      result: 'error',
      message: ['Intake could not be found with that CEDAR ID'],
    });
  });

  it('Should return results for the provided intake id and no other params', async () => {
    mocks.queryView.mockResolvedValueOnce([[dbResults[0]]]);

    const response = await request(app).get('/16').set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      cedarId: '16',
      clientId: '12356',
      version: 'v01',
      type: 'System Intake',
      cedarStatus: 'On Hold',
      cedarStatusMessage: '',
      clientStatus: 'New',
      bodyFormat: 'JSON',
      body: "{'adminLead':'','archivedAt':'','businessNeed':'Test Business Need','businessOwner':'Test Business Owner','businessOwnerComponent':'Test Business Owner Component','component':'Test Component','contractEndDate':'2021-03-05T11:04:00.123Z','contractStartDate':'2021-03-05T11:04:00.123Z','contractVehicle':'','contractor':'','costIncrease':'NO','costIncreaseAmount':'','decidedAt':'','decisionNextSteps':'','eaCollaborator':'Test EA Collaborator','eaCollaboratorName':'','eaSupportRequest':'false','existingContract':'NOT_NEEDED','existingFunding':'true','fundingNumber':'123456','fundingSource':'CLIA','grbDate':'','grtDate':'','grtReviewEmailBody':'','isso':'Test ISSO','issoName':'','lifecycleExpiresAt':'','lifecycleID':'12345','lifecycleScope':'','oitSecurityCollaborator':'Test OIT Collaborator','oitSecurityCollaboratorName':'','processStatus':'Just an idea','productManager':'Test Product Manager','productManagerComponent':'Test Product Manager Component','projectAcronym':'','projectName':'Test Project Name','rejectionReason':'','requestType':'NEW','requester':'Test Requester','requesterEmailAddress':'','solution':'Test Solution','status':'INTAKE_DRAFT','submittedAt':'','trbCollaborator':'Test TRB Collaborator','trbCollaboratorName':'','userEUA':'P8DB'}",
      clientCreatedDate: '2021-03-05T06:04:00.123Z',
      clientLastUpdatedDate: null,
      cedarCreatedDate: '2022-04-01T13:05:21.727Z',
      cedarLastUpdatedDate: '2024-02-02T20:00:19.183Z',
      schema: 'System Intake v01',
    });
  });
});
