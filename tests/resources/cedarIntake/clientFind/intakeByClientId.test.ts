import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeByClientIdModule from '../../../../src/resources/cedarIntake/clientFind/intakeByClientId';
import intakeByClientIdConfig from '../../../../src/resources/cedarIntake/clientFind';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeByClientIdConfig);
app.resources.initResources(app);

const dbResults = [{
  cedarId: '17',
  clientId: '1b2719e0-b939-4162-a699-4b7c84e33255',
  version: '1',
  type: 'EASIIntake',
  cedarStatus: 'On Hold',
  cedarStatusMessage: '',
  clientStatus: 'Initiated',
  bodyFormat: 'JSON',
  body: '{\'archivedAt\':\'\',\'businessNeed\':\'This new contract will be a 3-month follow-on contract to transition the existing TMSIS contract to the new contract that is currently being re-competed. The life cycle ID for the new contract is Lifecycle ID: 212861. The POP for the 3-month follow on contract will be 9/18/22 - 12/18/22.\',\'businessOwner\':\'Tinu Arowojolu\',\'businessOwnerComponent\':\'Center for Medicaid and CHIP Services\',\'component\':\'Center for Medicaid and CHIP Services\',\'contractEndDate\':\'2022-12-17\',\'contractStartDate\':\'2022-09-18\',\'contractVehicle\':\'8A Sole Source\',\'contractor\':\'Tista\',\'costIncrease\':\'NO\',\'decidedAt\':\'\',\'eaSupportRequest\':false,\'existingContract\':\'IN_PROGRESS\',\'existingFunding\':true,\'fundingNumber\':\'001044\',\'fundingSource\':\'Prog Ops\',\'grbDate\':\'\',\'grtDate\':\'\',\'issoName\':\'Louis Gamerman\',\'lifecycleExpiresAt\':\'\',\'processStatus\':\'Initial development underway\',\'productManager\':\'Tinu Arowojolu\',\'productManagerComponent\':\'Center for Medicaid and CHIP Services\',\'projectName\':\'TMSIS Transition\',\'requestType\':\'RECOMPETE\',\'requester\':\'EBONY BRANDON\',\'solution\':\'The transition activities will occur by a series of information sharing sessions between the current contractor and new contractor. In addition to shadowing of current staff with new staff and ongoing transition meetings.\',\'status\':\'INTAKE_SUBMITTED\',\'submittedAt\':\'2022-04-25T15:38:23Z\',\'userEUA\':\'E2DE\'}',
  clientCreatedDate: '2022-04-25T11:26:52.753Z',
  clientLastUpdatedDate: '2022-04-25T11:38:23.593Z',
  cedarCreatedDate: '2022-04-25T15:38:23.870Z',
  cedarLastUpdatedDate: '2024-02-02T20:00:19.183Z',
  schema: 'EASIIntakeV02',
},
];

describe('Find Intake By Cedar Id', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', intakeByClientIdModule);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 400 with an error if no id is provided', async () => {
    const badApp = createBadApp(
      '/:badParam',
      'get',
      intakeByClientIdModule,
      {
        includeApp: true,
        includeLogger: true,
      },
    );
    const response = await request(badApp)
      .get('/test')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Missing required parameters: id, clientStatus, clientName and version',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, clientStatus, clientName and version');
  });

  it('Should return a 400 and an error CLIENT_NAME is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, clientStatus, clientName and version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, clientStatus, clientName and version');
  });

  it('Should return a 400 and an error clientStatus is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, clientStatus, clientName and version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, clientStatus, clientName and version');
  });

  it('Should return a 400 and an error if version is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated' });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, clientStatus, clientName and version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, clientStatus, clientName and version');
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake by client Id',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake by client Id',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 400 and an error if Intake could not be found', async () => {
    mocks.queryView.mockResolvedValueOnce([[], 0]);

    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      result: 'error',
      message: ['Intake could not be found with that client Id'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Intake could not be found with that client Id');
  });

  it('Should return results for the provided client id and required parameters', async () => {
    mocks.queryView.mockResolvedValueOnce([[dbResults[0]]]);

    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(dbResults[0]);
  });
});
