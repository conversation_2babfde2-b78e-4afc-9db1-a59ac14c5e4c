import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeStatusListConfig from '../../../../../src/resources/cedarIntake/statusList';
import intakeStatusListModule from '../../../../../src/resources/cedarIntake/statusList/intakeStatusList';
import { createApp, createBadApp, testLogger } from '../../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeStatusListConfig);
app.resources.initResources(app);

const fullResults = {
  ResultSet: [
    {
      cedarId: 155,
      clientId: '77dba591-31c8-4193-8f7a-9b4773011a41',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: 77dba591-31c8-4193-8f7a-9b4773011a41 not found in Alfabet. Inserting as refstr: 394-193-0',
    },
    {
      cedarId: 156,
      clientId: 'e17538d6-9f92-4047-ba97-0bf1b7de75ca',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: e17538d6-9f92-4047-ba97-0bf1b7de75ca not found in Alfabet. Inserting as refstr: 394-194-0',
    },
    {
      cedarId: 157,
      clientId: '3a43f401-2faf-4797-a9f4-953d4f38abe5',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: 3a43f401-2faf-4797-a9f4-953d4f38abe5 not found in Alfabet. Inserting as refstr: 394-195-0',
    },
    {
      cedarId: 158,
      clientId: '4d20569b-b2ac-421c-baa0-0a94fef1522e',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: 4d20569b-b2ac-421c-baa0-0a94fef1522e not found in Alfabet. Inserting as refstr: 394-196-0',
    },
    {
      cedarId: 159,
      clientId: 'a86d22a7-0ad7-445e-bac5-e38513c0ecb0',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: a86d22a7-0ad7-445e-bac5-e38513c0ecb0 not found in Alfabet. Inserting as refstr: 394-197-0',
    },
    {
      cedarId: 160,
      clientId: '81106e15-611c-43fa-99e5-d3b28192e80a',
      cedarStatus: 'On Hold',
      version: '1',
      cedarStatusMessage: 'Client ID: 81106e15-611c-43fa-99e5-d3b28192e80a not found in Alfabet. Inserting as refstr: 394-198-0',
    },
  ],
};

const fullProcessedResults = [
  {
    cedarId: 155,
    clientId: '77dba591-31c8-4193-8f7a-9b4773011a41',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: 77dba591-31c8-4193-8f7a-9b4773011a41 not found in Alfabet. Inserting as refstr: 394-193-0',
  },
  {
    cedarId: 156,
    clientId: 'e17538d6-9f92-4047-ba97-0bf1b7de75ca',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: e17538d6-9f92-4047-ba97-0bf1b7de75ca not found in Alfabet. Inserting as refstr: 394-194-0',
  },
  {
    cedarId: 157,
    clientId: '3a43f401-2faf-4797-a9f4-953d4f38abe5',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: 3a43f401-2faf-4797-a9f4-953d4f38abe5 not found in Alfabet. Inserting as refstr: 394-195-0',
  },
  {
    cedarId: 158,
    clientId: '4d20569b-b2ac-421c-baa0-0a94fef1522e',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: 4d20569b-b2ac-421c-baa0-0a94fef1522e not found in Alfabet. Inserting as refstr: 394-196-0',
  },
  {
    cedarId: 159,
    clientId: 'a86d22a7-0ad7-445e-bac5-e38513c0ecb0',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: a86d22a7-0ad7-445e-bac5-e38513c0ecb0 not found in Alfabet. Inserting as refstr: 394-197-0',
  },
  {
    cedarId: 160,
    clientId: '81106e15-611c-43fa-99e5-d3b28192e80a',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: 81106e15-611c-43fa-99e5-d3b28192e80a not found in Alfabet. Inserting as refstr: 394-198-0',
  },
];

const queryProcessedResult = [
  {
    cedarId: 155,
    clientId: '77dba591-31c8-4193-8f7a-9b4773011a41',
    cedarStatus: 'On Hold',
    version: '1',
    cedarStatusMessage: 'Client ID: 77dba591-31c8-4193-8f7a-9b4773011a41 not found in Alfabet. Inserting as refstr: 394-193-0',
  },
];

const queryProcessedResultEnd = [
  {
    count: 1,
    Statuses: [
      {
        cedarId: 155,
        clientId: '77dba591-31c8-4193-8f7a-9b4773011a41',
        cedarStatus: 'On Hold',
        version: '1',
        cedarStatusMessage: 'Client ID: 77dba591-31c8-4193-8f7a-9b4773011a41 not found in Alfabet. Inserting as refstr: 394-193-0',
      },
    ],
  },
];

describe('Intake List tests', () => {
  it('Should return a 500 with an error if cedarSupport getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 200 with results for the provided Hold status', async () => {
    mocks.queryView.mockResolvedValueOnce([[queryProcessedResult[0]]]);

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'On Hold',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(queryProcessedResultEnd[0]);
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const response = await request(app).get('/')
      .query({
        cedarStatus: 'On Hold',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake status',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app).get('/')
      .query({
        cedarStatus: 'On Hold',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake status',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 200 with a list of 6 intakes from page 2', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([
      [{
        result: JSON.stringify(fullResults),
        queryStatus: 0,
      }],
      1,
    ]);

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'On Hold',
        pageSize: 6,
        pageNumber: 2,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      Statuses: fullProcessedResults,
      count: 6,
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_EASiIntake_By_Status_Paginated',
      expect.anything(),
      [{
        isOutput: true,
        name: 'Outputjson',
        value: 'result',
      }, {
        name: 'request_cedar_status',
        value: 'On Hold',
      }, {
        name: 'client_created_date',
        value: '1900-01-01 00:00:00.001',
      }, {
        name: 'offset',
        value: '6',
      }, {
        name: 'num_rows',
        value: '6',
      }],
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the stored procedure returns an error', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('SP Error'));

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve the intake list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP Error');
  });

  it('Should return a 500 with an error if the stored procedure returns a critical error', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((_, reject) => {
      reject(new Error('SP Critical Error'));
    }));

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve the intake list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP Critical Error');
  });

  it('Should return a 500 with an error if the query returns status -1', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: null,
        queryStatus: -1,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Status of the query was invalid',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_EASiIntake_By_Status_Paginated',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 200 with an empty object if the query returns status 1', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([
      [{
        result: null,
        queryStatus: 1,
      }],
      1,
    ]);

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({});
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_EASiIntake_By_Status_Paginated',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the results are not in the query response', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([
      [{
        result: null,
        queryStatus: 0,
      }],
      1,
    ]);

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'abc',
        pageSize: 6,
        pageNumber: 2,
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get get result from query',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_EASiIntake_By_Status_Paginated',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the results cannot be parsed', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([
      [{
        result: '{"bad": "json"',
        queryStatus: 0,
      }],
      1,
    ]);

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to parse database response',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_EASiIntake_By_Status_Paginated',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/:bad', 'get', intakeStatusListModule);
    const response = await request(badApp).get('/bad').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if sparxea getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
        pageNumber: 2,
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 with an error if ceadrStatus param fails validation', async () => {
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'cedarStatus is required',
    });
  });

  it('Should return a 500 with an error if pageNumber param is missing', async () => {
    const response = await request(app).get('/')
      .query({
        cedarStatus: 'Hold',
        pageSize: 6,
      }).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'pageSize and pageNumber must be used in conjunction',
    });
  });
});
