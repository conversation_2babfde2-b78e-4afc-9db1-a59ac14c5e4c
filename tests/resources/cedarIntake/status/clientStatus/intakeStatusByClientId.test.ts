import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeStatusByClientIdModule from '../../../../../src/resources/cedarIntake/status/clientStatus/intakeStatusByClientId';
import intakeStatusByClientIdConfig from '../../../../../src/resources/cedarIntake/status/clientStatus';
import { createApp, createBadApp, testLogger } from '../../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeStatusByClientIdConfig);
app.resources.initResources(app);

const dbResults = [{
  cedarId: '17',
  clientId: '1b2719e0-b939-4162-a699-4b7c84e33255',
  version: '1',
  cedarStatus: 'On Hold',
  cedarStatusMessage: '',
}];

describe('Find Intake By Cedar Id', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', intakeStatusByClientIdModule);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get('/b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 400 with an error if no id is provided', async () => {
    const badApp = createBadApp(
      '/:badParam',
      'get',
      intakeStatusByClientIdModule,
      {
        includeApp: true,
        includeLogger: true,
      },
    );
    const response = await request(badApp)
      .get('/test')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Missing required parameters: id, CLIENT_NAME, clientStatus, version',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, CLIENT_NAME, clientStatus, version');
  });

  it('Should return a 400 and an error if http header CLIENT_NAME is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, CLIENT_NAME, clientStatus, version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, CLIENT_NAME, clientStatus, version');
  });

  it('Should return a 400 and an error clientStatus is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, CLIENT_NAME, clientStatus, version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, CLIENT_NAME, clientStatus, version');
  });

  it('Should return a 400 and an error if version is missing', async () => {
    const response = await request(app)
      .get('/99999999-b939-4162-a699-4b7c84e33255?')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated' });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Missing required parameters: id, CLIENT_NAME, clientStatus, version' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Missing required parameters: id, CLIENT_NAME, clientStatus, version');
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass').set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake status by Client Id',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app)
      .get('/16')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve intake status by Client Id',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 400 and an error if Intake could not be found', async () => {
    mocks.queryView.mockResolvedValueOnce([[], 0]);

    const response = await request(app)
      .get('/16')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      result: 'error',
      message: ['Intake could not be found with those parameters'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Intake could not be found with those parameters');
  });

  it('Should return results for the provided intake id and required params', async () => {
    mocks.queryView.mockResolvedValueOnce([[dbResults[0]]]);

    const response = await request(app)
      .get('/1b2719e0-b939-4162-a699-4b7c84e33255')
      .set('x-jwt-key', 'pass')
      .set('CLIENT_NAME', 'EASi')
      .query({ clientStatus: 'Initiated', version: 1 });

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(dbResults[0]);
  });
});
