import {
  describe,
  it,
  expect,
  vi,
  beforeEach,
  afterEach,
} from 'vitest';
import request from 'supertest';
import health from '../../../../src/resources/cedarIntake/healthCheck';
import { nowUtc, nowUtcFormatted } from '../../../../src/utils/time';
import { createApp } from '../../../test-utils';

const app = await createApp();
app.resources.register(app, '/', health);
app.resources.initResources(app);
const testingDate = new Date('2024-10-31 17:45:12');
describe('Health Router tests', () => {
  beforeEach(() => {
    vi.useFakeTimers({ shouldAdvanceTime: true });
    vi.setSystemTime(testingDate);
  });
  afterEach(() => {
    vi.useRealTimers();
  });
  describe('/', () => {
    it('Should perform a GET request', async () => {
      const response = await request(app).get('/').set('x-jwt-key', 'pass');
      expect(response.status).toEqual(200);
      expect(response.body.datetime).toEqual(nowUtcFormatted('YYYY-MM-DD HH:mm:ss'));
      expect(response.body.status).toEqual('pass');
      expect(response.body.version).toEqual('1');
      expect(response.body.timestamp).toEqual(nowUtc().unix());
    });
  });
});
