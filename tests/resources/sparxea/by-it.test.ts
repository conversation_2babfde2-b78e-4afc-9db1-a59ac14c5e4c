import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import sparxeaByIdModule from '../../../src/resources/sparxea/by-id';
import sparxeaByIdConfig from '../../../src/resources/sparxea';
import { createApp, createBadApp, testLogger } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  getQuery: vi.fn(),
}));

vi.mock(import('../../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getQuery: mocks.getQuery,
  };
});

const app = await createApp();
app.resources.register(app, '/', sparxeaByIdConfig);
app.resources.initResources(app);

const testId = '8F068AFA-990E-4c8b-877E-6BB6C29F52D4';

const responseXml = `<?xml version="1.0" encoding="UTF-8"?>
<rdf:RDF
  xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
  xmlns:rdfs="http://www.w3.org/2000/01/rdf-schema#"
  xmlns:dcterms="http://purl.org/dc/terms/"
  xmlns:foaf="http://xmlns.com/foaf/0.1/"
  xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"
  xmlns:oslc_am="http://open-services.net/ns/am#">
  <oslc_am:Resource rdf:about="https://sparxea.example.com/model/el_{8F068AFA-990E-4c8b-877E-6BB6C29F52D4}/">
    <dcterms:title>Testing Sparx via new API</dcterms:title>
    <dcterms:type>Class</dcterms:type>
    <dcterms:identifier>el_{8F068AFA-990E-4c8b-877E-6BB6C29F52D4}</dcterms:identifier>
    <dcterms:created>2024-12-20 22:42:18</dcterms:created>
    <dcterms:modified>2024-12-20 22:42:18</dcterms:modified>
    <ss:status>Proposed</ss:status>
    <ss:complexity>Easy</ss:complexity>
    <ss:phase>1.0</ss:phase>
    <ss:version>1.0</ss:version>
    <ss:language>Java</ss:language>
    <ss:parentresourceidentifier>pk_{53C2E7BD-D7B1-44f7-BEE2-88B1E26C160B}</ss:parentresourceidentifier>
    <ss:resourcetype>Element</ss:resourcetype>
    <ss:features rdf:resource="https://sparxea.example.com/model/el_{8F068AFA-990E-4c8b-877E-6BB6C29F52D4}/"/>
  </oslc_am:Resource>
</rdf:RDF>`;

describe('SparxEA By ID tests', () => {
  it('Should return a 400 and an error if there is no ID provided', async () => {
    const badApp = createBadApp('/:bad', 'get', sparxeaByIdModule);

    const response = await request(badApp).get('/bye');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No object id provided' });
  });

  it('Should return a 400 and an error if the ID does not validate', async () => {
    const response = await request(app).get('/not-a-uuid').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Invalid object id' });
  });

  it('Should return a 500 and an error if the app is not found in the request', async () => {
    const badApp = createBadApp('/:id', 'get', sparxeaByIdModule);
    const response = await request(badApp).get(`/${testId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return a 500 and an error if getQuery returns an error', async () => {
    mocks.getQuery.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app).get(`/${testId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'Failed' });
    expect(mocks.getQuery).toHaveBeenCalledWith(app, `el_{${testId}}`);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return a 200 and an XML response', async () => {
    mocks.getQuery.mockImplementationOnce(() => responseXml);
    const response = await request(app).get(`/${testId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.headers['content-type']).toEqual('application/xml; charset=utf-8');
    expect(response.text).toEqual(responseXml);
    expect(mocks.getQuery).toHaveBeenCalledWith(app, `el_{${testId}}`);
  });
});
