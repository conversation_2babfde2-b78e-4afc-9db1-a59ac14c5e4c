import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import sparxeaRemoveModule from '../../../src/resources/sparxea/remove';
import sparxeaRemoveConfig from '../../../src/resources/sparxea';
import { createApp, createBadApp, testLogger } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  deleteQuery: vi.fn(),
}));

vi.mock(import('../../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    deleteQuery: mocks.deleteQuery,
  };
});

const app = await createApp();
app.resources.register(app, '/', sparxeaRemoveConfig);
app.resources.initResources(app);

const testId = '8F068AFA-990E-4c8b-877E-6BB6C29F52D4';

describe('SparxEA By ID tests', () => {
  it('Should return a 400 and an error if there is no ID provided', async () => {
    const badApp = createBadApp('/:bad', 'delete', sparxeaRemoveModule);

    const response = await request(badApp).delete('/bye');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No object id provided' });
  });

  it('Should return a 400 and an error if the ID does not validate', async () => {
    const response = await request(app).delete('/not-a-uuid').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Invalid object id' });
  });

  it('Should return a 500 and an error if the app is not found in the request', async () => {
    const badApp = createBadApp('/:id', 'delete', sparxeaRemoveModule);
    const response = await request(badApp).delete(`/${testId}`);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return a 500 an error if deleteQuery returns an error', async () => {
    mocks.deleteQuery.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app).delete(`/${testId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'Failed' });
    expect(mocks.deleteQuery).toHaveBeenCalledWith(app, `el_{${testId}}`);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return a 200 and an XML response', async () => {
    mocks.deleteQuery.mockImplementationOnce(() => null);
    const response = await request(app).delete(`/${testId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.headers['content-type']).toEqual('application/xml; charset=utf-8');
    expect(response.body).toEqual({});
    expect(mocks.deleteQuery).toHaveBeenCalledWith(app, `el_{${testId}}`);
  });
});
