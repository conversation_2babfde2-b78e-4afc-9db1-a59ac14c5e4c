import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import sparxeaCreateModule from '../../../src/resources/sparxea/create';
import sparxeaByIdConfig from '../../../src/resources/sparxea';
import { createApp, createBadApp, testLogger } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  postQuery: vi.fn(),
  getToken: vi.fn(),
  getPackageParentId: vi.fn(),
}));

vi.mock(import('../../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    postQuery: mocks.postQuery,
    getToken: mocks.getToken,
    getPackageParentId: mocks.getPackageParentId,
  };
});

const app = await createApp();
app.resources.register(app, '/', sparxeaByIdConfig);
app.resources.initResources(app);

const parentId = '{53C2E7BD-D7B1-44f7-BEE2-88B1E26C160B}';
const authToken = '{a2affe4e-276a-4ff2-8a92-3241ff1716cc}';
const getQueryXml = (type: string) => `<?xml version="1.0"?><rdf:RDF xmlns:oslc_am="http://open-services.net/ns/am#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"><oslc_am:Resource><dcterms:title>This is a test</dcterms:title><dcterms:type>${type}</dcterms:type><ss:resourcetype>Element</ss:resourcetype><ss:parentresourceidentifier>pk_{53C2E7BD-D7B1-44f7-BEE2-88B1E26C160B}</ss:parentresourceidentifier><ss:useridentifier>{a2affe4e-276a-4ff2-8a92-3241ff1716cc}</ss:useridentifier><ss:stereotype><ss:stereotypename><ss:name></ss:name></ss:stereotypename></ss:stereotype></oslc_am:Resource></rdf:RDF>`;
const responseXml = `<rdf:RDF
  xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
  xmlns:oslc_am="http://open-services.net/ns/am#">
  <oslc_am:Resource rdf:about="https://sparxea.cedardev.cms.gov/CMS_Dev_Model/oslc/am/resource/el_{8F068AFA-990E-4c8b-877E-6BB6C29F52D4}/"/>
</rdf:RDF>`;

describe('SparxEA Create tests', () => {
  it('Should return a 400 and an error if no body is provided', async () => {
    const response = await request(app).post('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No body provided' });
  });

  it('Should return a 400 and an error if no title is sent in the body', async () => {
    const payload = JSON.stringify({
      packageName: 'Software Product',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'title is required and can not be empty' });
  });

  it('Should return a 400 and an error if no packageName is sent in the body', async () => {
    const payload = JSON.stringify({
      title: 'This is a test',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'packageName is required and can not be empty' });
  });

  it('Should return a 403 and an error if the parent package ID cannot be found', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => undefined);
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Software Product',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(403);
    expect(response.body).toEqual({ message: 'Unable to get find the parent package ID' });
  });

  it('Should return a 500 and an error if the app is not found in the request', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    const badApp = createBadApp('/', 'post', sparxeaCreateModule);
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Software Product',
    });
    const response = await request(badApp)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return a 500 and an error if getToken returns an error', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    mocks.getToken.mockImplementationOnce(() => new Error('Bad Connection'));
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Software Product',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'Unable to get token to make the request' });
  });

  it('Should return a 500 and an error if postQuery returns an error', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    mocks.getToken.mockImplementationOnce(() => authToken);
    mocks.postQuery.mockImplementationOnce(() => new Error('Bad Connection'));
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Software Product',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'The object failed to create' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return a 200 and xml text with type Class', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    mocks.getToken.mockImplementationOnce(() => authToken);
    mocks.postQuery.mockImplementationOnce(() => responseXml);
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Software Product',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.headers['content-type']).toEqual('application/xml; charset=utf-8');
    expect(response.text).toEqual(responseXml);
    expect(mocks.postQuery).toHaveBeenCalledWith(app, getQueryXml('Class'));
  });

  it('Should return a 200 and xml text with type Artifact', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    mocks.getToken.mockImplementationOnce(() => authToken);
    mocks.postQuery.mockImplementationOnce(() => responseXml);
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Intake',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.headers['content-type']).toEqual('application/xml; charset=utf-8');
    expect(response.text).toEqual(responseXml);
    expect(mocks.postQuery).toHaveBeenCalledWith(app, getQueryXml('Artifact'));
  });

  it('Should return a 200 and xml text with type Actor', async () => {
    mocks.getPackageParentId.mockImplementationOnce(() => parentId);
    mocks.getToken.mockImplementationOnce(() => authToken);
    mocks.postQuery.mockImplementationOnce(() => responseXml);
    const payload = JSON.stringify({
      title: 'This is a test',
      packageName: 'Person',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/xml')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.headers['content-type']).toEqual('application/xml; charset=utf-8');
    expect(response.text).toEqual(responseXml);
    expect(mocks.postQuery).toHaveBeenCalledWith(app, getQueryXml('Actor'));
  });
});
