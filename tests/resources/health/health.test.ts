import { describe, it, expect } from 'vitest';
import request from 'supertest';
import healthConfig from '../../../src/resources/health';
import { createApp } from '../../test-utils';

const app = await createApp();
app.resources.register(app, '/', healthConfig);
app.resources.initResources(app);

describe('Health Router tests', () => {
  describe('/', () => {
    it('Should perform a GET request', async () => {
      const response = await request(app).get('/');

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ message: 'Server is healthy' });
    });
  });
});
