import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import authConfig from '../../../src/resources/authentication';
import authModule from '../../../src/resources/authentication/authenticate';
import { createApp, createBadApp, testLogger } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  bindPerson: vi.fn(),
  getJwt: vi.fn().mockImplementation(() => 'abcd1234'),
}));

vi.mock(import('../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    bindPerson: mocks.bindPerson,
  };
});

vi.mock(import('../../../src/subsystems/authentication/jwt'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getJwt: mocks.getJwt,
  };
});

const app = await createApp();
app.resources.register(app, '/', authConfig);
app.resources.initResources(app);

const validPayload = JSON.stringify({
  username: 'TST1',
  password: 'pa$$wor1',
});

describe('Auth Router tests', () => {
  afterEach(() => {
    testLogger.resetMocks();
  });

  it('Should return 200 success if the client bind succeeds', async () => {
    const payload = JSON.stringify({
      username: 'TST1',
      password: 'pa$$wor1',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    const {
      headers,
      status,
      body,
    } = response;
    expect(headers).toHaveProperty('x-set-jwt');
    expect(headers['x-set-jwt']).toEqual('abcd1234');
    expect(status).toEqual(200);
    expect(body.message).toEqual('Success');

    expect(mocks.bindPerson).toBeCalledWith(app, {
      username: 'TST1',
      password: 'pa$$wor1',
    });
  });

  it('Should return 400 if the username fails validation', async () => {
    const payload = JSON.stringify({
      username: 'BAD!',
      password: 'pa$$wor1',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body.message).toEqual('Account validation error: "username" is an invalid value');
  });

  it('Should return 400 if the password fails validation', async () => {
    const payload = JSON.stringify({
      username: 'TST1',
      password: 'badPass',
    });
    const response = await request(app)
      .post('/')
      .send(payload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body.message).toEqual('Account validation error: "password" is a minimum of 8 characters');
  });

  it('Should return a 500 status if the app is not in the req', async () => {
    const badApp = createBadApp('/', 'post', authModule, { includeLogger: true });
    const response = await request(badApp)
      .post('/')
      .send(validPayload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to retrieve system application');
  });

  it('Should return 400 if bindPerson returns an error', async () => {
    mocks.bindPerson.mockImplementationOnce(() => new Error('Unable to connect to the ldap service'));

    const response = await request(app)
      .post('/')
      .send(validPayload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body.message).toEqual('Failed');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to connect to the ldap service');
  });

  it('Should return 500 if getJwt returns an error', async () => {
    mocks.getJwt.mockImplementationOnce(() => new Error('Bad Connection'));

    const response = await request(app)
      .post('/')
      .send(validPayload)
      .set('Content-Type', 'application/json')
      .set('Accept', 'application/json')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body.message).toEqual('Failed');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });
});
