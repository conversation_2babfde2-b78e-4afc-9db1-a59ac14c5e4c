import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { Request } from 'express';
import request from 'supertest';
import keysConfig from '../../../../src/resources/gateway/keys';
import keysRemoveModule from '../../../../src/resources/gateway/keys/remove';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
} from '../../../test-utils';

const testKey = '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b';
const mocks = vi.hoisted(() => ({
  removeKey: vi.fn().mockImplementation(() => true),
}));

vi.mock(import('../../../../src/subsystems/authentication/gateway'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    removeKey: mocks.removeKey,
  };
});

const app = await createApp();
app.resources.register(app, '/', keysConfig);
app.resources.initResources(app);

describe('Keys Remove Resource', () => {
  it('Should return a 401 status with an error if the auth method is not JWT', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'gateway';
      return true;
    });
    const response = await request(app).delete(`/${testKey}`).set('x-Gateway-APIKey', 'not-a-jwt');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid authorization method');
  });

  it('Should return a 400 status with an error if there is no ID in the parameter', async () => {
    const badApp = createBadApp(
      '/:notId',
      'delete',
      keysRemoveModule,
      {
        includeApp: true,
        includeUser: true,
      },
    );
    const response = await request(badApp).delete(`/${testKey}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('No gateway key provided');
  });

  it('Should return a 401 status with an error if the user is not in the request', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app).delete(`/${testKey}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid user');
  });

  it('Should return a 500 status with an error if the app is not in the request', async () => {
    const badApp = createBadApp(
      '/:id',
      'delete',
      keysRemoveModule,
      {
        includeApp: false,
        includeUser: true,
      },
    );
    const response = await request(badApp).delete(`/${testKey}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to get the application from request');
  });

  it('Should return a 500 status with an error if removeKey returns an error', async () => {
    mocks.removeKey.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app).delete(`/${testKey}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred creating a gateway key');
  });

  it('Should return a 204 status with an empty body', async () => {
    const response = await request(app).delete(`/${testKey}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(204);
    expect(response.body).toEqual({});
  });
});
