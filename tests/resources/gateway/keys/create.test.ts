import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { Request } from 'express';
import request from 'supertest';
import keysConfig from '../../../../src/resources/gateway/keys';
import keysCreateModule from '../../../../src/resources/gateway/keys/create';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
} from '../../../test-utils';

const testKey = '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b';
const mocks = vi.hoisted(() => ({
  createKey: vi.fn().mockImplementation(() => testKey),
}));

vi.mock(import('../../../../src/subsystems/authentication/gateway'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    createKey: mocks.createKey,
  };
});

const app = await createApp();
app.resources.register(app, '/', keysConfig);
app.resources.initResources(app);

describe('Keys Create Resource', () => {
  it('Should return a 401 status with an error if the auth method is not JWT', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'gateway';
      return true;
    });
    const response = await request(app).post('/').set('x-Gateway-APIKey', 'not-a-jwt');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid authorization method');
  });

  it('Should return a 401 status with an error if the user is not in the request', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app).post('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid user');
  });

  it('Should return a 500 status with an error if the app is not in the request', async () => {
    const badApp = createBadApp(
      '/',
      'post',
      keysCreateModule,
    );
    const response = await request(badApp).post('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to get the application from request');
  });

  it('Should return a 500 status with an error if createKey returns an error', async () => {
    mocks.createKey.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app).post('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred creating a gateway key');
  });

  it('Should return a 200 status with a key', async () => {
    const response = await request(app).post('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toHaveProperty('key');
    expect(response.body.key).toEqual(testKey);
  });
});
