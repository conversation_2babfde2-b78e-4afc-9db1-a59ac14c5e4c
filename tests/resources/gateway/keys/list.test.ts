import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { Request } from 'express';
import request from 'supertest';
import keysConfig from '../../../../src/resources/gateway/keys';
import keysListModule from '../../../../src/resources/gateway/keys/list';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
} from '../../../test-utils';

const testKeys = [{
  id: 1,
  key: '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b',
  createdBy: 'RJ3R',
  createdDate: '2025-02-24T14:13:52.867Z',
}, {
  id: 2,
  key: 'a8bcabd1-98e8-4792-83e9-6d27a2fdb123',
  createdBy: 'RJ3R',
  createdDate: '2025-02-25T14:13:52.867Z',
}];

const mocks = vi.hoisted(() => ({
  findAll: vi.fn().mockResolvedValue([{
    id: 1,
    key: '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b',
    createdBy: 'RJ3R',
    createdDate: '2025-02-24T14:13:52.867Z',
  }, {
    id: 2,
    key: 'a8bcabd1-98e8-4792-83e9-6d27a2fdb123',
    createdBy: 'RJ3R',
    createdDate: '2025-02-25T14:13:52.867Z',
  }]),
  getDb: vi.fn().mockImplementation(() => ({
    findAll: mocks.findAll,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', keysConfig);
app.resources.initResources(app);

describe('Keys List Resource', () => {
  it('Should return a 401 status with an error if the auth method is not JWT', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'gateway';
      return true;
    });
    const response = await request(app).get('/').set('x-Gateway-APIKey', 'not-a-jwt');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid authorization method');
  });

  it('Should return a 500 status with an error if the app is not in the request', async () => {
    const badApp = createBadApp(
      '/',
      'get',
      keysListModule,
      {
        includeApp: false,
        includeUser: true,
      },
    );
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to get the application from request');
  });

  it('Should return a 500 status with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Database Unavailable');
  });

  it('Should return a 500 status with an error if findAll returns an error', async () => {
    mocks.findAll.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred while receiving the gateway keys');
  });

  it('Should return a 500 status with an error if findAll returns a critical error', async () => {
    mocks.findAll.mockRejectedValueOnce(new Error('Critical Bad Connection'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('An error occurred while receiving the gateway keys');
  });

  it('Should return a 200 status with a key', async () => {
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({ results: testKeys });
  });
});
