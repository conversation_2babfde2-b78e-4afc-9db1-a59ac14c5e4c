import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import { Request } from 'express';
import updateModule from '../../../../src/resources/gateway/global-variables/update';
import globalVariableConfig from '../../../../src/resources/gateway/global-variables';
import {
  testLogger,
  createApp,
  createBadApp,
  mocks as utilMocks,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  updateGlobalVariable: vi.fn().mockResolvedValue(true),
}));

vi.mock(import('../../../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    updateGlobalVariable: mocks.updateGlobalVariable,
  };
});

const app = await createApp();
app.resources.register(app, '/', globalVariableConfig);
app.resources.initResources(app);

const global = {
  globalName: 'name',
};

describe('Update Global Variable tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp(
      '/:id',
      'put',
      updateModule,
      {
        includeUser: true,
      },
    );

    const response = await request(badApp)
      .put('/name')
      .send({
        globalName: 'name',
        globalValue: 'value',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 401 and an error if no user is present', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .put(`/${global.globalName}`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      error: 'Invalid user',
    });
  });

  it('Should return a 500 and an error if no body is provided', async () => {
    const response = await request(app)
      .put('/name')
      .send({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the global variable from request',
    });
  });

  it('Should return a 500 and an error if updateGlobalVariable returns an error', async () => {
    mocks.updateGlobalVariable.mockResolvedValueOnce(new Error('Update Global Variable Error'));
    const response = await request(app)
      .put('/name')
      .send({
        globalName: 'name',
        globalValue: 'value',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while updating the global variable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Update Global Variable Error');
  });

  it('Should return a 201 and if the variable is successfully updated', async () => {
    mocks.updateGlobalVariable.mockResolvedValueOnce(true);

    const response = await request(app)
      .put('/name')
      .send({
        globalName: 'name',
        globalValue: 'differentValue',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(201);
    expect(response.body).toEqual({
      message: 'Updated global variable name',
    });
  });
});
