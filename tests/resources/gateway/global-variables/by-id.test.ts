import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import { Request } from 'express';
import request from 'supertest';
import globalVariableConfig from '../../../../src/resources/gateway/global-variables';
import byIdModule from '../../../../src/resources/gateway/global-variables/by-id';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
  testLogger,
} from '../../../test-utils';

const apiValue = {
  globalVariable: {
    id: 1,
    globalName: 'name',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  },
};

const mocks = vi.hoisted(() => ({
  findGlobalVariable: vi.fn().mockResolvedValue(true),
}));

vi.mock(import('../../../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    findGlobalVariable: mocks.findGlobalVariable,
  };
});

const dbValue = {
  id: 1,
  globalName: 'name',
  globalValue: 'value',
  createdBy: 'Test String',
  createdDate: '2025-03-03T19:33:13.529Z',
  updatedBy: 'Test String',
  updatedDate: '2025-03-03T19:33:13.529Z',
};

const app = await createApp();
app.resources.register(app, '/', globalVariableConfig);
app.resources.initResources(app);

describe('Get Global Variable by ID tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp(
      '/:id',
      'get',
      byIdModule,
      {
        includeUser: true,
      },
    );
    const response = await request(badApp)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 401 with an error if there is not a valid user', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid user');
  });

  it('Should return a 400 with an error if no global variable name is provided', async () => {
    const badApp = createBadApp(
      '/:otherName',
      'get',
      byIdModule,
      {
        includeApp: true,
        includeUser: true,
      },
    );
    const response = await request(badApp)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'No global variable name provided',
    });
  });

  it('Should return a 500 and an error if finding a global variable returns an error', async () => {
    mocks.findGlobalVariable.mockResolvedValueOnce(new Error('Find Global Variable Error'));

    const response = await request(app)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while finding the global variable.',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Find Global Variable Error');
  });

  it('Should return a 404 and an error if there is no global variable found.', async () => {
    mocks.findGlobalVariable.mockResolvedValueOnce({});

    const response = await request(app)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({
      error: 'Global variable not found.',
    });
  });

  it('Should return a 200 and if there is a matching variable.', async () => {
    mocks.findGlobalVariable.mockResolvedValueOnce(dbValue);

    const response = await request(app)
      .get('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiValue);
  });
});
