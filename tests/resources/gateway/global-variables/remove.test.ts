import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import { Request } from 'express';
import removeModule from '../../../../src/resources/gateway/global-variables/remove';
import globalVariableConfig from '../../../../src/resources/gateway/global-variables';
import {
  testLogger,
  createApp,
  createBadApp,
  mocks as utilMocks,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  removeGlobalVariable: vi.fn().mockResolvedValue(true),
}));

vi.mock(import('../../../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    removeGlobalVariable: mocks.removeGlobalVariable,
  };
});

const app = await createApp();
app.resources.register(app, '/', globalVariableConfig);
app.resources.initResources(app);

describe('Remove Global Variable tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp(
      '/:id',
      'delete',
      removeModule,
      {
        includeUser: true,
      },
    );

    const response = await request(badApp)
      .delete('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 401 and an error if no user is present', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .delete(`/${global.globalName}`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      error: 'Invalid user',
    });
  });

  it('Should return a 500 and an error if getting the global variable name returns an error', async () => {
    const badApp = createBadApp(
      '/:badId',
      'delete',
      removeModule,
      {
        includeApp: true,
        includeUser: true,
      },
    );

    const response = await request(badApp)
      .delete('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the global variable name from request',
    });
  });

  it('Should return a 500 and an error if removeGlobalVariable returns an error', async () => {
    mocks.removeGlobalVariable.mockResolvedValueOnce(new Error('Remove Global Variable Error'));

    const response = await request(app)
      .delete('/name')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while removing a global variable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Remove Global Variable Error');
  });

  it('Should return a 204 and if there is a matching variable and it has been deleted.', async () => {
    const response = await request(app)
      .delete(`/${global.globalName}`)
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(204);
  });
});
