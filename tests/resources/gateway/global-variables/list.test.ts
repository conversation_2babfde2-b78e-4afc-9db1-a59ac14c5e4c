import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import listModule from '../../../../src/resources/gateway/global-variables/list';
import globalVariableConfig from '../../../../src/resources/gateway/global-variables';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const dbValue = [
  {
    id: 1,
    globalName: 'name',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  }, {
    id: 2,
    globalName: 'color',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  },
  {
    id: 3,
    globalName: 'shape',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  }, {
    id: 4,
    globalName: 'size',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  },
];

const apiValue = {
  globalVariables: [
    {
      id: 1,
      globalName: 'name',
      globalValue: 'value',
      createdBy: 'Test String',
      createdDate: '2025-03-03T19:33:13.529Z',
      updatedBy: 'Test String',
      updatedDate: '2025-03-03T19:33:13.529Z',
    }, {
      id: 2,
      globalName: 'color',
      globalValue: 'value',
      createdBy: 'Test String',
      createdDate: '2025-03-03T19:33:13.529Z',
      updatedBy: 'Test String',
      updatedDate: '2025-03-03T19:33:13.529Z',
    },
    {
      id: 3,
      globalName: 'shape',
      globalValue: 'value',
      createdBy: 'Test String',
      createdDate: '2025-03-03T19:33:13.529Z',
      updatedBy: 'Test String',
      updatedDate: '2025-03-03T19:33:13.529Z',
    }, {
      id: 4,
      globalName: 'size',
      globalValue: 'value',
      createdBy: 'Test String',
      createdDate: '2025-03-03T19:33:13.529Z',
      updatedBy: 'Test String',
      updatedDate: '2025-03-03T19:33:13.529Z',
    },
  ],
};

const mocks = vi.hoisted(() => ({
  findAll: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    findAll: mocks.findAll,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', globalVariableConfig);
app.resources.initResources(app);

describe('Get Global Variables List tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp(
      '/',
      'get',
      listModule,
      {
        includeUser: true,
      },
    );
    const response = await request(badApp)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 and an error if findAll returns an error', async () => {
    mocks.findAll.mockResolvedValueOnce(new Error('DB Error'));

    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while receiving the global variables',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('DB Error');
  });

  it('Should return a 500 and an error if findAll returns a critical error', async () => {
    mocks.findAll.mockRejectedValueOnce(new Error('DB Critical Error'));

    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while receiving the global variables',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('DB Critical Error');
  });

  it('Should return a 200 and a list of all global variables.', async () => {
    mocks.findAll.mockResolvedValueOnce(dbValue);

    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiValue);
  });
});
