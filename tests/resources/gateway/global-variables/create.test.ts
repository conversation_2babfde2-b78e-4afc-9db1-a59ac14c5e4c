import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import { Request } from 'express';
import createModule from '../../../../src/resources/gateway/global-variables/create';
import globalVariableConfig from '../../../../src/resources/gateway/global-variables';
import {
  createApp,
  createBadApp,
  mocks as utilMocks,
  testLogger,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  createGlobalVariable: vi.fn().mockResolvedValue(true),
}));

vi.mock(import('../../../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    createGlobalVariable: mocks.createGlobalVariable,
  };
});

const app = await createApp();
app.resources.register(app, '/', globalVariableConfig);
app.resources.initResources(app);

const global = {
  globalName: 'name',
  globalValue: 'test',
};

describe('Create Global Variable tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp(
      '/',
      'post',
      createModule,
      {
        includeUser: true,
      },
    );

    const response = await request(badApp)
      .post('/')
      .send({
        globalName: global.globalName,
        globalValue: global.globalValue,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 401 and an error if no user is present', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .post('/')
      .send({
        globalName: global.globalName,
        globalValue: global.globalValue,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({
      error: 'Invalid user',
    });
  });

  it('Should return a 500 and an error if the global variable body does not exist', async () => {
    const response = await request(app)
      .post('/')
      .send({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the global variable from request',
    });
  });

  it('Should return a 500 and an error if createGlobalVariable returns an error', async () => {
    mocks.createGlobalVariable.mockResolvedValueOnce(new Error('Create Global Variable Error'));

    const response = await request(app)
      .post('/')
      .send({
        globalName: 'name',
        globalValue: 'value',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred while creating a global variable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Create Global Variable Error');
  });

  it('Should return a 201 if there is no pre-existing variable.', async () => {
    const response = await request(app)
      .post('/')
      .send({
        globalName: 'name',
        globalValue: 'value',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(201);
    expect(response.body).toEqual({
      message: 'Created global variable name',
    });
  });
});
