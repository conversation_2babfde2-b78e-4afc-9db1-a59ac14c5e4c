import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { Request } from 'express';
import { get } from 'lodash';
import intakeConfig from '../../../../src/resources/gateway/intake-schema';
import createModule from '../../../../src/resources/gateway/intake-schema/create';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
  testLogger,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  insertOne: vi.fn().mockResolvedValue(2),
  getSchema: vi.fn().mockResolvedValue(null),
  getDb: vi.fn().mockImplementation(() => ({
    insertOne: mocks.insertOne,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/intake-schema'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSchema: mocks.getSchema,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeConfig);
app.resources.initResources(app);

const requestBody = {
  name: 'test2',
  schema: '{"foo":"string,required"}',
};

describe('Intake Schema Create tests', () => {
  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/', 'post', createModule);
    const response = await request(badApp)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return an error if the authMethod is not "jwt"', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'gateway';
      return true;
    });

    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid authorization method');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid authorization method');
  });

  it('Should return an error if the user is not in the request', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({ error: 'Invalid user' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No user found in the request');
  });

  it('Should return an error if the name is not in the body', async () => {
    const response = await request(app)
      .post('/')
      .send({
        schema: '{"foo":"string,required"}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if the name is not a string', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: { bad: 'test2' },
        schema: '{"foo":"string,required"}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if the name is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: '',
        schema: '{"foo":"string,required"}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if the schema is not in the body', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: 'test2',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema is not a string', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: 'test2',
        schema: { foo: 'string,required' },
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: 'test2',
        schema: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema does not parse', async () => {
    const response = await request(app)
      .post('/')
      .send({
        name: 'test2',
        schema: '{"foo":"string,required"',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Invalid schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid schema provided');
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Database Unavailable' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while checking for a duplicate schema of this name' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema returns a value', async () => {
    mocks.getSchema.mockResolvedValueOnce({
      id: '1',
      name: 'test1',
      schema: '{"foo":"string,required"}',
      createdBy: 'TST1',
      createdDate: '20251202T13:15:18.123Z',
      updatedBy: 'TST1',
      updatedDate: '20251202T13:15:18.123Z',
    });
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(409);
    expect(response.body).toEqual({ error: 'A schema with that name already exists' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Name already exists');
  });

  it('Should return an error if insertOne returns an error', async () => {
    mocks.insertOne.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while creating the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if insertOne returns a critical error', async () => {
    mocks.insertOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while creating the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return the schema id and name', async () => {
    const result = {
      schemaId: 2,
      name: 'test2',
    };
    const response = await request(app)
      .post('/')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(201);
    expect(response.body).toEqual(result);
  });
});
