import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeConfig from '../../../../src/resources/gateway/intake-schema';
import byIdModule from '../../../../src/resources/gateway/intake-schema/by-id';
import {
  createApp,
  createBadApp,
  testLogger,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  getSchema: vi.fn().mockResolvedValue({
    id: '1',
    name: 'test1',
    schema: '{"foo":"string,required"}',
    createdBy: 'TST1',
    createdDate: '20251202T13:15:18.123Z',
    updatedBy: 'TST1',
    updatedDate: '20251202T13:15:18.123Z',
  }),
}));

vi.mock(import('../../../../src/utils/intake-schema'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSchema: mocks.getSchema,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeConfig);
app.resources.initResources(app);

const dbResult = {
  id: '1',
  name: 'test1',
  schema: '{"foo":"string,required"}',
  createdBy: 'TST1',
  createdDate: '20251202T13:15:18.123Z',
  updatedBy: 'TST1',
  updatedDate: '20251202T13:15:18.123Z',
};

describe('Intake Schema By ID tests', () => {
  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/:id', 'get', byIdModule);
    const response = await request(badApp)
      .get('/test2')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return a 400 with an error if no name is provided', async () => {
    const badApp = createBadApp(
      '/:otherName',
      'get',
      byIdModule,
      {
        includeApp: true,
        includeLogger: true,
      },
    );
    const response = await request(badApp)
      .get('/test2')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if getSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .get('/test2')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while searching for the schema of this name' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return the schema details', async () => {
    const response = await request(app)
      .get('/test2')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(dbResult);
  });
});
