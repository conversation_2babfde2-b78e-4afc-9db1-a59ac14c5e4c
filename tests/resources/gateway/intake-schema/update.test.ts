import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { Request } from 'express';
import { get } from 'lodash';
import intakeConfig from '../../../../src/resources/gateway/intake-schema';
import updateModule from '../../../../src/resources/gateway/intake-schema/update';
import {
  mocks as utilMocks,
  createApp,
  createBadApp,
  testLogger,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  updateOne: vi.fn().mockResolvedValue('success'),
  getSchema: vi.fn().mockResolvedValue({
    id: '1',
    name: 'test1',
    schema: '{"foo":"string,required"}',
    createdBy: 'TST1',
    createdDate: '2024-12-02T13:15:18.123Z',
    updatedBy: 'TST1',
    updatedDate: '2024-12-02T13:15:18.123Z',
  }),
  getDb: vi.fn().mockImplementation(() => ({
    updateOne: mocks.updateOne,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/intake-schema'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSchema: mocks.getSchema,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeConfig);
app.resources.initResources(app);

const requestBody = {
  schema: '{"foo":"boolean,required,strict"}',
};

const updatedResult = {
  id: '1',
  name: 'test1',
  schema: '{"foo":"boolean,required,strict"}',
  createdBy: 'TST1',
  createdDate: '2024-12-02T13:15:18.123Z',
  updatedBy: 'TST1',
  updatedDate: '2025-02-19T17:45:12.000Z',
};

const testingDate = new Date('2025-02-19 17:45:12+00:00');

describe('Intake Schema Update tests', () => {
  beforeEach(() => {
    vi.useFakeTimers({ shouldAdvanceTime: true });
    vi.setSystemTime(testingDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/:id', 'put', updateModule);
    const response = await request(badApp)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return an error if the authMethod is not "jwt"', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'gateway';
      return true;
    });

    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Invalid authorization method');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid authorization method');
  });

  it('Should return an error if the user is not in the request', async () => {
    utilMocks.authentication.mockImplementationOnce((req: Request) => {
      req.authMethod = 'jwt';
      return true;
    });
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(401);
    expect(response.body).toEqual({ error: 'Invalid user' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No user found in the request');
  });

  it('Should return a 400 with an error if no name is provided', async () => {
    const badApp = createBadApp(
      '/:otherName',
      'put',
      updateModule,
      {
        includeApp: true,
        includeLogger: true,
        includeUser: true,
      },
    );
    const response = await request(badApp)
      .put('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if the schema is not in the body', async () => {
    const response = await request(app)
      .put('/test1')
      .send({
        shcema: '{"foo":"boolean,required,strict"',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema is not a string', async () => {
    const response = await request(app)
      .put('/test1')
      .send({
        schema: { foo: 'string,required' },
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema is empty', async () => {
    const response = await request(app)
      .put('/test1')
      .send({
        schema: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No schema provided');
  });

  it('Should return an error if the schema does not parse', async () => {
    const response = await request(app)
      .put('/test1')
      .send({
        schema: '{"foo":"string,required"',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'Invalid schema provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid schema provided');
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Database Unavailable' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while checking for a duplicate schema of this name' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema does not return a value', async () => {
    mocks.getSchema.mockResolvedValueOnce(null);
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({ error: 'A schema with that name cannot be found' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Schema not found');
  });

  it('Should return an error if updateOne returns failed', async () => {
    mocks.updateOne.mockResolvedValueOnce('failed');
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while updating the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toEqual('failed');
  });

  it('Should return an error if updateOne returns an error', async () => {
    mocks.updateOne.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while updating the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if updateOne returns a critical error', async () => {
    mocks.updateOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while updating the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return the schema id and name', async () => {
    const response = await request(app)
      .put('/test1')
      .send(requestBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(updatedResult);
  });
});
