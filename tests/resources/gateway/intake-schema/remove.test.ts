import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeConfig from '../../../../src/resources/gateway/intake-schema';
import deleteModule from '../../../../src/resources/gateway/intake-schema/remove';
import {
  createApp,
  createBadApp,
  testLogger,
} from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  deleteOne: vi.fn().mockResolvedValue('success'),
  getSchema: vi.fn().mockResolvedValue({
    id: '1',
    name: 'test1',
    schema: '{"foo":"string,required"}',
    createdBy: 'TST1',
    createdDate: '20241202T13:15:18.123Z',
    updatedBy: 'TST1',
    updatedDate: '20241202T13:15:18.123Z',
  }),
  getDb: vi.fn().mockImplementation(() => ({
    deleteOne: mocks.deleteOne,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/intake-schema'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSchema: mocks.getSchema,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeConfig);
app.resources.initResources(app);

describe('Intake Schema Remove tests', () => {
  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/:id', 'delete', deleteModule);
    const response = await request(badApp)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return a 400 with an error if no name is provided', async () => {
    const badApp = createBadApp(
      '/:otherName',
      'delete',
      deleteModule,
      {
        includeApp: true,
        includeLogger: true,
      },
    );
    const response = await request(badApp)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ error: 'No name provided' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No name provided');
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Database Unavailable' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema returns an error', async () => {
    mocks.getSchema.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while checking for a duplicate schema of this name' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if getSchema does not return a value', async () => {
    mocks.getSchema.mockResolvedValueOnce(null);
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({ error: 'A schema with that name cannot be found' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Schema not found');
  });

  it('Should return an error if deleteOne returns failed', async () => {
    mocks.deleteOne.mockResolvedValueOnce('failed');
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while deleting the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toEqual('failed');
  });

  it('Should return an error if deleteOne returns an error', async () => {
    mocks.deleteOne.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while deleting the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if deleteOne returns a critical error', async () => {
    mocks.deleteOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while deleting the schema' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return the schema id and name', async () => {
    const response = await request(app)
      .delete('/test1')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(204);
    expect(response.body).toEqual({});
  });
});
