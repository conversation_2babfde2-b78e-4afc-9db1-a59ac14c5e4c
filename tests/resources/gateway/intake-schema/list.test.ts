import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import intakeConfig from '../../../../src/resources/gateway/intake-schema';
import listModule from '../../../../src/resources/gateway/intake-schema/list';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  findAll: vi.fn().mockResolvedValue([{
    id: '1',
    name: 'test1',
    schema: '{"foo":"string,required"}',
    createdBy: 'TST1',
    createdDate: '20251202T13:15:18.123Z',
    updatedBy: 'TST1',
    updatedDate: '20251202T13:15:18.123Z',
  }]),
  getDb: vi.fn().mockImplementation(() => ({
    findAll: mocks.findAll,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', intakeConfig);
app.resources.initResources(app);

describe('Intake Schema List tests', () => {
  it('Should return an error if the app is not in the request', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Unable to retrieve system application' });
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'Database Unavailable' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if findAll returns an error', async () => {
    mocks.findAll.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while retrieving the schemas' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return an error if findAll returns a critical error', async () => {
    mocks.findAll.mockRejectedValueOnce(new Error('Bad Critical Connection'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred while retrieving the schemas' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return an array of schemas', async () => {
    const result = {
      schemas: [{
        id: '1',
        name: 'test1',
        schema: '{"foo":"string,required"}',
        createdBy: 'TST1',
        createdDate: '20251202T13:15:18.123Z',
        updatedBy: 'TST1',
        updatedDate: '20251202T13:15:18.123Z',
      }],
    };
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(result);
  });
});
