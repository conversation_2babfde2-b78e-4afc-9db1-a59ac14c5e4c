import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import personSearchConfig from '../../../src/resources/person';
import personSearchModule from '../../../src/resources/person/person';
import { createApp, createBadApp } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  person: vi.fn(),
}));

vi.mock(import('../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    person: mocks.person,
  };
});

const app = await createApp();
app.resources.register(app, '/', personSearchConfig);
app.resources.initResources(app);

const testResponse = {
  dn: 'uid=TST1,ou=People,dc=example,dc=com',
  cn: 'Test User',
  mail: '<EMAIL>',
  givenName: 'Test',
  sn: 'User',
  telephoneNumber: '7035551234',
  uid: 'TST1',
  ismemberof: [
    'cn=USERS,ou=Groups,dc=example,dc=com',
  ],
};
const email = '<EMAIL>';

describe('Person Search tests', () => {
  it('Should return a 500 status if the app is not in the req', async () => {
    const badApp = createBadApp('/:id', 'get', personSearchModule);
    const response = await request(badApp).get('/bye').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to retrieve system application');
  });

  it('Should return a 200 and the query filter will have provided items in it', async () => {
    mocks.person.mockResolvedValueOnce([testResponse]);

    const firstName = 'Test';
    const lastName = 'User';
    const telephone = '7035551234';
    const response = await request(app).get('/')
      .query({
        first_name: firstName,
        last_name: lastName,
        email,
        telephone,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual([testResponse]);
    expect(mocks.person).toBeCalledWith(
      app,
      {
        commonName: '',
        firstName,
        lastName,
        email,
        telephone,
      },
    );
  });

  it('Should return a 400 and an error if the filter has no properties', async () => {
    mocks.person.mockResolvedValueOnce(new Error('No items provided for the filter'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No items provided for the filter' });
  });

  it('Should return a 500 and an error if person returns an error', async () => {
    mocks.person.mockResolvedValueOnce(new Error('Bad Connection'));
    const response = await request(app).get('/')
      .query({
        email,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'Failed' });
  });
});
