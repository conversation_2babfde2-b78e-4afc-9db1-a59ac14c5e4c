import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import personByIdModule from '../../../src/resources/person/by-id';
import personConfig from '../../../src/resources/person';
import { createApp, createBadApp } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  personIds: vi.fn(),
}));

vi.mock(import('../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    personIds: mocks.personIds,
  };
});

const app = await createApp();
app.resources.register(app, '/', personConfig);
app.resources.initResources(app);

const testResponse = [{
  dn: 'uid=TST1,ou=People,dc=example,dc=com',
  cn: 'Test User',
  mail: '<EMAIL>',
  givenName: 'Test',
  sn: 'User',
  telephoneNumber: '7035551234',
  uid: 'TST1',
  ismemberof: [
    'cn=USERS,ou=Groups,dc=example,dc=com',
  ],
}];

describe('Person By ID tests', () => {
  it('Should return a 500 status if the app is not in the req', async () => {
    const badApp = createBadApp('/:id', 'get', personByIdModule);
    const response = await request(badApp).get('/bye').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toHaveProperty('error');
    expect(response.body.error).toEqual('Unable to retrieve system application');
  });

  it('Should return a 400 if no ID is provided', async () => {
    const badApp = createBadApp('/:bad', 'get', personByIdModule, { includeApp: true });
    const response = await request(badApp).get('/bye').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No person id provided' });
  });

  it('Should return a 400 if the ID is invalid', async () => {
    const response = await request(app).get('/bad').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Invalid person id' });
  });

  it('Should return a 500 if queryPersons returns an error', async () => {
    mocks.personIds.mockResolvedValueOnce(new Error('Bad connection'));

    const response = await request(app).get('/TST1').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: 'Failed' });
  });

  it('Should return a 200 and the user', async () => {
    mocks.personIds.mockResolvedValueOnce(testResponse);

    const response = await request(app).get('/TST1').set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(testResponse);
  });
});
