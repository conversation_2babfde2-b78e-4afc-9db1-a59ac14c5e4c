import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import addModule from '../../../../src/resources/cedar-core-v2/role/add';
import roleConfig from '../../../../src/resources/cedar-core-v2/role';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import { createRelation } from '../../../../src/utils/role';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
  createPromise: vi.fn(),
  createUpdateObj: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/role'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    createPromise: mocks.createPromise,
    createUpdateObj: mocks.createUpdateObj,
  };
});

const application = 'alfabet';
const objectId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';
const roleTypeId = '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}';
const assigneeId = '{4A840588-8531-4353-8B05-1E4CAEC1EE0C}';

const reqBody = {
  application,
  Roles: [
    {
      assigneeId,
      roleTypeId,
      objectId,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneeUserName: 'TST1',
      assigneeEmail: '<EMAIL>',
    },
  ],
};

const validPromise = {
  objectId,
  roleTypeId,
  assigneeId,
};

const validCreateObj = {
  Object: {
    ClassName: 'Role',
    Id: '1',
    Values: { name: '' },
  },
  Relations: {
    relationOne: createRelation('1', 'object', validPromise.objectId),
    relationTwo: createRelation('1', 'responsible', validPromise.assigneeId),
    relationThree: createRelation('1', 'roletype', validPromise.roleTypeId),
  },
};

const app = await createApp();
app.resources.register(app, '/', roleConfig);
app.resources.initResources(app);
mocks.createUpdateObj.mockReturnValue(validCreateObj);

afterEach(() => {
  mocks.queryStoredProcedures.mockClear();
  mocks.createPromise.mockClear();
  testLogger.resetMocks();
});

describe('Role Add tests', () => {
  it('Should return an error if no app is present', async () => {
    const badApp = createBadApp('/', 'post', addModule);
    const response = await request(badApp)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Unable to get the application from request' });
  });

  it('Should return an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('DB fail'));
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Database Unavailable' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('DB fail');
  });

  it('Should return an error if application is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({ Roles: reqBody.Roles })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({ message: 'Please specify a valid application.' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid application given');
  });

  it('Should return an error if application is not an accepted application', async () => {
    const response = await request(app)
      .post('/')
      .send({ application: 'foo', Roles: reqBody.Roles })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({ message: 'Please specify a valid application.' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid application given');
  });

  it('Should return an error if there is a critical error in the promises', async () => {
    mocks.createPromise.mockRejectedValueOnce(new Error('Promise critical Failure'));
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'No roles were able to be seeded' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Promise critical Failure');
  });

  it('Should return an error if the promise results are empty', async () => {
    mocks.createPromise.mockResolvedValueOnce([]);
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'No roles were able to be seeded' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error.message).toEqual('No roles were able to be seeded');
  });

  it('Should return an error if there is an error in the promise array', async () => {
    mocks.createPromise.mockResolvedValueOnce([new Error('Promise Error'), validPromise]);
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'There was an error creating the roles' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error[0].message).toEqual('Promise Error');
  });

  it('Should return an error if there is an error creating the updated role object', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.createUpdateObj.mockReturnValueOnce(new Error('There was no assigneeId or username provided'));
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'An error occurred while creating one or more of the roles' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error.message).toEqual('There was no assigneeId or username provided');
  });

  it('Should return an error if the roleAdd call returns a critical error', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Critical SP Error'));

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Unable to get results from stored procedure' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical SP Error');
  });

  it('Should return an error if the roleAdd call results return an error', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('SP Error'));

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Unable to get results from stored procedure' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP Error');
  });

  it('Should return an error if the roleAdd call results are empty', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([]);
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Unable to get results from stored procedure' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toEqual('No results received from stored procedure');
  });

  it('Should return an empty response if the roleAdd status is a 1', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 1 }]]);
    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual({});
  });

  it('Should return an error if the roleAdd status is -1', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: -1 }]]);

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Status of the query was invalid' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toEqual({ queryStatus: -1 });
  });

  it('Should return an error if the object in roleAdd does not contain a result', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'The query stored procedures did not yield a result' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('The query stored procedures did not yield a result');
  });

  it('Should return an error if the result can not be parsed', async () => {
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0, result: 'invalid' }]]);

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({ error: 'Invalid Sparx results' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Invalid Sparx results');
  });

  it('Should return a success message with a unique list of guids', async () => {
    const raw = '{"NewObjects": {"GUID": "{05666630-4199-4142-91BB-66B4B5E976AA}"},"Count":1}';
    mocks.createPromise.mockResolvedValueOnce([validPromise]);
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0, result: raw }]]);

    const response = await request(app)
      .post('/')
      .send(reqBody)
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body.message).toContain('{05666630-4199-4142-91BB-66B4B5E976AA}');
    expect(response.body.result).toBe('success');
  });
});
