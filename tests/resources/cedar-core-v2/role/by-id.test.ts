import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import byIdModule from '../../../../src/resources/cedar-core-v2/role/by-id';
import roleConfig from '../../../../src/resources/cedar-core-v2/role';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', roleConfig);
app.resources.initResources(app);

const application = 'alfabet';
const objectId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';
const roleTypeId = '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}';
const roleId = '{1674C4CC-A1B5-4014-83D1-AE1B1F7D6F39}';

// count: 2,
const objectAndRoleTypeIdResponse = [
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{1674C4CC-A1B5-4014-83D1-AE1B1F7D6F39}',
    roleTypeId: '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}',
    roleTypeName: 'Government Task Lead (GTL)',
    roleTypeDesc: 'Government Task Lead for the system.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{6739FAD3-DB81-4748-BF85-A17E27F9F0AF}',
    roleTypeId: '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}',
    roleTypeName: 'Government Task Lead (GTL)',
    roleTypeDesc: 'Government Task Lead for the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
];

// count: 1,
const roleIdResponse = [
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{1674C4CC-A1B5-4014-83D1-AE1B1F7D6F39}',
    roleTypeId: '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}',
    roleTypeName: 'Government Task Lead (GTL)',
    roleTypeDesc: 'Government Task Lead for the system.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
];

// count: 25,
const objectIdResponse = [
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{1674C4CC-A1B5-4014-83D1-AE1B1F7D6F39}',
    roleTypeId: '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}',
    roleTypeName: 'Government Task Lead (GTL)',
    roleTypeDesc: 'Government Task Lead for the system.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{184ADE5B-4200-4836-A68A-C4F11AACAA8C}',
    roleTypeId: '{36335B21-40F4-48de-8D16-8F85277C54B8}',
    roleTypeName: 'System Maintainer',
    roleTypeDesc: 'A Federal employee lead of the system maintainer team for the system or the person responsible for overseeing the technical operations of the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{1EF5267F-A3EA-45f1-9A79-6246832407C4}',
    roleTypeId: '{C95EA2F9-1A08-4b1a-AEE7-A83011D06113}',
    roleTypeName: 'Business Owner',
    roleTypeDesc: 'A person on the business owner team for the system or the person officially responsible for the business decisions of the system and the budgeting for the system.',
    assigneeId: '{75752CAA-5455-4505-A056-190686EDEA8E}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{29FA7CD0-1191-480A-A9B5-93F3F81DB294}',
    roleTypeId: '{4EC5F774-6F7A-49f2-B0C3-BCBE3AC8D3DF}',
    roleTypeName: 'ISSO Support',
    roleTypeDesc: null,
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{2B46ED93-534D-4d8d-A999-8758F56D6AA5}',
    roleTypeId: '{9DAEBF37-868B-408c-8C7A-1ADECE73F4C4}',
    roleTypeName: 'Subject Matter Expert (SME)',
    roleTypeDesc: 'Federal support staff for a system who provide policy, process, or business expertise.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{3F0A9788-F6F9-4251-9156-6362F18456B7}',
    roleTypeId: '{C95EA2F9-1A08-4b1a-AEE7-A83011D06113}',
    roleTypeName: 'Business Owner',
    roleTypeDesc: 'A person on the business owner team for the system or the person officially responsible for the business decisions of the system and the budgeting for the system.',
    assigneeId: '{F7FB6C94-F0CC-4976-B9A7-1D9D3F79050B}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{4299FB7A-AFCB-4CF9-BF30-7A4B7BBFEA06}',
    roleTypeId: '{4A1704D7-B7D1-4ef8-8D9F-D1B7B5C9A825}',
    roleTypeName: 'DA Reviewer',
    roleTypeDesc: 'The person assigned to QA the Data Exchange page of the Annual System Census',
    assigneeId: '{9FA52B6A-4D80-4d17-AB45-616675650EF5}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{512EFA0C-D08F-4673-A5F7-EE8C1EFFB1F4}',
    roleTypeId: '{8C6FCC75-FCDE-4133-8C54-BCA6160E34C1}',
    roleTypeName: 'Survey Point of Contact',
    roleTypeDesc: 'The primary point of contact throughout this survey in terms of survey completion and survey status.',
    assigneeId: '{4A840588-8531-4353-8B05-1E4CAEC1EE0C}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{625C49B3-E438-4D34-B5AA-F5D63B44F480}',
    roleTypeId: '{CAE32572-2A36-4d50-9F73-A0EE0A1A6437}',
    roleTypeName: 'ISSO',
    roleTypeDesc: 'An Information System Security Officer for the system.',
    assigneeId: '{AF479CE6-0DA4-4dd4-80B0-48F9B0D0B412}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{6504264C-AFA5-4BE5-9FB2-25D4983B0CAE}',
    roleTypeId: '{260896F7-76AB-4e8d-8FF6-E8A0431B1F6A}',
    roleTypeName: 'Business Question Contact',
    roleTypeDesc: 'A contact who can answer business questions for the system.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{6739FAD3-DB81-4748-BF85-A17E27F9F0AF}',
    roleTypeId: '{77D11ECA-9995-40fe-B0AE-19DEAD33A338}',
    roleTypeName: 'Government Task Lead (GTL)',
    roleTypeDesc: 'Government Task Lead for the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{674CB444-8491-4d11-B903-47CED28219E0}',
    roleTypeId: '{C266D5BF-C298-4ec9-AE49-24DBB8577947}',
    roleTypeName: 'Data Center Contact',
    roleTypeDesc: 'The person on the team to contact if there is a question regarding the system data center or hosting environment.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{6D5B2D3C-BA1B-4C3F-A5A5-234401C0B1EE}',
    roleTypeId: '{D757BD92-2922-4d9e-A70A-B623D5DC52A8}',
    roleTypeName: 'QA Reviewer',
    roleTypeDesc: 'The person assigned to QA the data enetered by respondents for the Annual System Census (except Data Exchange page)',
    assigneeId: '{B62DFD4C-5EC0-4879-9377-40F77F540B38}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{6E7221D5-0A3A-4F09-B9CE-5392BEA6B014}',
    roleTypeId: '{518E9DF7-3111-44ad-8E80-FD6317B019A5}',
    roleTypeName: 'Other',
    roleTypeDesc: null,
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{95B0BFB4-8E8D-48db-BE17-EA25F11960B0}',
    roleTypeId: '{AB303F45-E6ED-488d-95C1-065457F46028}',
    roleTypeName: 'Contracting Officer\'s Representative (COR)',
    roleTypeDesc: 'Contracting Officer\'s Representative for the system.',
    assigneeId: '{363AEF0B-7326-4fea-9E26-E4BBCF45820A}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{968E44EA-7E38-4b54-B4C0-50221C56CA5E}',
    roleTypeId: '{9DAEBF37-868B-408c-8C7A-1ADECE73F4C4}',
    roleTypeName: 'Subject Matter Expert (SME)',
    roleTypeDesc: 'Federal support staff for a system who provide policy, process, or business expertise.',
    assigneeId: '{83BC0150-77D5-4f1d-8901-CD4232CC17CF}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{AED5BC7B-FBAC-4A7F-B210-04A4E0A2CE6A}',
    roleTypeId: '{8C6FCC75-FCDE-4133-8C54-BCA6160E34C1}',
    roleTypeName: 'Survey Point of Contact',
    roleTypeDesc: 'The primary point of contact throughout this survey in terms of survey completion and survey status.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{BF1CAA96-B8B0-41fb-981B-3F6C1D066A1E}',
    roleTypeId: '{E324B687-8A7F-4463-BD1D-5A7D04EEB17A}',
    roleTypeName: 'Project Lead',
    roleTypeDesc: 'Project Manager or Project Lead for the system.',
    assigneeId: '{83BC0150-77D5-4f1d-8901-CD4232CC17CF}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{C491CDE1-4FA6-46c0-B17A-E59B909D7233}',
    roleTypeId: '{E324B687-8A7F-4463-BD1D-5A7D04EEB17A}',
    roleTypeName: 'Project Lead',
    roleTypeDesc: 'Project Manager or Project Lead for the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{C758CD3B-E890-429c-A857-5750F7D7D5D7}',
    roleTypeId: '{1FD4F238-56A1-46d2-8CB1-8A7C87F63A01}',
    roleTypeName: 'API Contact',
    roleTypeDesc: 'A person knowledgeable on API related information for the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{CCB58A91-AF6F-4178-8929-7A1DEC0E1D11}',
    roleTypeId: '{60457806-27EC-406e-BD29-331E286DD314}',
    roleTypeName: 'Support Staff',
    roleTypeDesc: 'A person who supports this system. Select this option if none of the other role options are applicable.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{D3ECA017-E120-42f3-BFD8-37006F1AB2D5}',
    roleTypeId: '{ED77C4FD-3078-4f65-8FD4-7350DBAE7283}',
    roleTypeName: 'Technical System Issues Contact',
    roleTypeDesc: 'The person on the team to contact if technical issues are found with the system.',
    assigneeId: '{2CE330B1-ED7E-48c4-8892-0575079AE1D4}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{E291F742-E4FC-4A53-B316-40611C89FD18}',
    roleTypeId: '{9DAEBF37-868B-408c-8C7A-1ADECE73F4C4}',
    roleTypeName: 'Subject Matter Expert (SME)',
    roleTypeDesc: 'Federal support staff for a system who provide policy, process, or business expertise.',
    assigneeId: '{B62DFD4C-5EC0-4879-9377-40F77F540B38}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{F13C8EB1-F3CB-4A55-8B4B-FCF9384C4A47}',
    roleTypeId: '{E324B687-8A7F-4463-BD1D-5A7D04EEB17A}',
    roleTypeName: 'Project Lead',
    roleTypeDesc: 'Project Manager or Project Lead for the system.',
    assigneeId: '{1339FEC0-EC91-469c-A0E4-7C20F209A29D}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
  {
    application: 'alfabet',
    objectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    roleId: '{F663713C-B5EA-4421-94BF-DEF6CBF55877}',
    roleTypeId: '{C95EA2F9-1A08-4b1a-AEE7-A83011D06113}',
    roleTypeName: 'Business Owner',
    roleTypeDesc: 'A person on the business owner team for the system or the person officially responsible for the business decisions of the system and the budgeting for the system.',
    assigneeId: '{4A840588-8531-4353-8B05-1E4CAEC1EE0C}',
    assigneeUserName: 'ABCD',
    assigneeIsDeleted: null,
    assigneeFirstName: 'Test',
    assigneeLastName: 'User',
    assigneeEmail: '<EMAIL>',
    assigneePhone: '************',
    assigneeOrgId: null,
    assigneeOrgName: null,
    assigneeDesc: null,
    assigneeType: 'Person',
  },
];

describe('Roles By ID tests', () => {
  it('Should return a 400 and an error if there is no application', async () => {
    const response = await request(app)
      .get('/')
      .query({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Please specify a valid application.' });
  });

  it('Should return a 400 and an error if there is no objectId or roleId', async () => {
    const response = await request(app)
      .get('/')
      .query({
        application,
        objectId: null,
        roleId: null,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Either role ID or object ID must be provided.' });
  });

  it('Should return a 400 and an error if there is both objectId and roleId', async () => {
    const response = await request(app)
      .get('/')
      .query({
        application,
        objectId,
        roleId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'If role ID is provided then object ID should not be provided.' });
  });

  it('Should return a 400 and an error if there is both roleId and roleTypeId', async () => {
    const response = await request(app)
      .get('/')
      .query({
        application,
        roleId,
        roleTypeId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'If role ID is provided then role type ID should not be provided.' });
  });

  it('Should return a 200 with a result and count with an object id and a role type id.', async () => {
    mocks.queryView.mockResolvedValueOnce([objectAndRoleTypeIdResponse]);

    const response = await request(app)
      .get('/')
      .query({
        application,
        objectId,
        roleTypeId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 2,
      Roles: objectAndRoleTypeIdResponse,
    });
  });

  it('Should return a 200 with a result and count with just a role id.', async () => {
    mocks.queryView.mockResolvedValueOnce([roleIdResponse]);

    const response = await request(app)
      .get('/')
      .query({
        application,
        roleId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Roles: roleIdResponse,
    });
  });

  it('Should return a 200 with a result and count with just an object id.', async () => {
    mocks.queryView.mockResolvedValueOnce([objectIdResponse]);

    const response = await request(app)
      .get('/')
      .query({
        application,
        objectId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 25,
      Roles: objectIdResponse,
    });
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', byIdModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        application,
        roleTypeId: '{776611ECA-9996-406e-60AE-1966EA6633A338}',
        objectId: '{666666166C-97C1-4b29-86A6-A2066C862481}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve role by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        application,
        roleTypeId: '{776611ECA-9996-406e-60AE-1966EA6633A338}',
        objectId: '{666666166C-97C1-4b29-86A6-A2066C862481}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve role by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });
});
