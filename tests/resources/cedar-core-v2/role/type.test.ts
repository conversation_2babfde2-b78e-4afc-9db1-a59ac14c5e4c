import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import roleTypeModule from '../../../../src/resources/cedar-core-v2/role/type/type';
import roleConfig from '../../../../src/resources/cedar-core-v2/role';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', roleConfig);
app.resources.initResources(app);

// count: 3,
const dbResponse = [
  {
    id: '{1FD4F238-56A1-46d2-8CB1-8A7C87F63A01}',
    name: 'API Contact',
    description: 'A person knowledgeable about API related information for the system.',
  },
  {
    id: '{36335B21-40F4-48de-8D16-8F85277C54B8}',
    name: 'System Maintainer',
    description: 'A lead of the system maintainer team for the system or the person responsible for overseeing the technical operations of the system. Every system should have at least one System Maintainer, and this role must be a federal employee.',
  },
  {
    id: '{260896F7-76AB-4e8d-8FF6-E8A0431B1F6A}',
    name: 'Business Question Contact',
    description: 'A contact who can answer business questions for the system. Every system should have a team member with this role.',
  },
];

const getApiResponse = (application: string) => {
  const apiData = dbResponse.map((item) => ({
    id: item.id,
    application,
    name: item.name,
    description: item.description,
  }));

  return {
    count: 3,
    RoleTypes: apiData,
  };
};

describe('Roles by types tests', () => {
  it('Should return a 500 with an error if db is not in the req', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/type/all')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 and an error if there is no application', async () => {
    const badApp = createBadApp('/:application', 'get', roleTypeModule);
    const response = await request(badApp)
      .get('/all')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 400 and an error if there is no application param', async () => {
    const badApp = createBadApp(
      '/:notApplication',
      'get',
      roleTypeModule,
      {
        includeApp: true,
        includeUser: true,
      },
    );

    const response = await request(badApp).get('/bad').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Please specify a valid application' });
  });

  it('Should return a 400 and an error if the application param is not \'all\' or \'alfabet\'', async () => {
    const response = await request(app)
      .get('/type/nope')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Please specify a valid application' });
  });

  it('Should return a 200 with a result and count when the application param is \'all\'', async () => {
    mocks.queryView.mockResolvedValueOnce([dbResponse]);

    const response = await request(app)
      .get('/type/all')
      .set('x-jwt-key', 'pass');

    const apiResponse = getApiResponse('all');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiResponse);
  });

  it('Should return a 200 with a result and count when the application param is \'alfabet\'', async () => {
    mocks.queryView.mockResolvedValueOnce([dbResponse]);

    const response = await request(app)
      .get('/type/alfabet')
      .set('x-jwt-key', 'pass');

    const apiResponse = getApiResponse('alfabet');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiResponse);
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/type/all')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve role type',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app)
      .get('/type/all')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve role type',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });
});
