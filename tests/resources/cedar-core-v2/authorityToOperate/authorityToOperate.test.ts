import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import atoModule from '../../../../src/resources/cedar-core-v2/authorityToOperate/ato';
import atoConfig from '../../../../src/resources/cedar-core-v2/authorityToOperate';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', atoConfig);
app.resources.initResources(app);

const dbResults = [{
  cedarId: '{0B14AA22-B98F-4de3-9ACD-3874B750B6D0}',
  uuid: 'A1837A4F-4EF5-42D1-9537-82178667A8EB',
  fismaSystemName: 'CMS National Training Program Learning Management System',
  fismaSystemAcronym: 'NTP LMS',
  actualDispositionDate: null,
  countOfOpenPoams: 1,
  countOfTotalNonPrivilegedUserPopulation: 75000,
  countOfTotalPrivilegedUserPopulation: 20,
  containsPersonallyIdentifiableInformation: 'Yes',
  dateAuthorizationMemoSigned: '2023-12-06',
  dateAuthorizationMemoExpires: '2026-12-05',
  eAuthenticationLevel: '1',
  fips199OverallImpactRating: 2,
  isAccessedByNonOrganizationalUsers: 'Yes',
  isPiiLimitedToUserNameAndPass: 'No',
  isProtectedHealthInformation: 'No',
  lastActScaDate: '2023-10-06',
  lastAssessmentDate: '2023-10-06',
  lastContingencyPlanCompletionDate: '2023-07-26',
  lastPenTestDate: '2023-06-20',
  piaCompletionDate: '2021-07-12',
  primaryCyberRiskAdvisor: 'STWO',
  privacySubjectMatterExpert: 'DEZC',
  recoveryPointObjective: 168,
  recoveryTimeObjective: 72,
  systemOfRecordsNotice: '09-70-0542',
  tlcPhase: 'Operate',
  xlcPhase: null,
}, {
  cedarId: '{2511240D-2365-4c02-B032-17C432B475B7}',
  uuid: 'ee3b806e1b8520102e8743bae54bcb3f',
  fismaSystemName: 'Competitive Bidding Survey',
  fismaSystemAcronym: 'CBS',
  actualDispositionDate: '2022-09-16',
  countOfOpenPoams: 0,
  countOfTotalNonPrivilegedUserPopulation: 0,
  countOfTotalPrivilegedUserPopulation: 0,
  containsPersonallyIdentifiableInformation: 'Yes',
  dateAuthorizationMemoSigned: '2021-10-07',
  dateAuthorizationMemoExpires: '2022-10-07',
  eAuthenticationLevel: 'N/A',
  fips199OverallImpactRating: 2,
  isAccessedByNonOrganizationalUsers: 'No',
  isPiiLimitedToUserNameAndPass: 'No',
  isProtectedHealthInformation: 'Yes',
  lastActScaDate: '2022-06-27',
  lastAssessmentDate: '2022-06-27',
  lastContingencyPlanCompletionDate: '2022-06-03',
  lastPenTestDate: '2022-05-02',
  piaCompletionDate: '2021-06-10',
  primaryCyberRiskAdvisor: 'Y113',
  privacySubjectMatterExpert: 'NQOB',
  recoveryPointObjective: 24,
  recoveryTimeObjective: 48,
  systemOfRecordsNotice: '09-70-0530|09-70-0571',
  tlcPhase: 'Retire',
  xlcPhase: null,
}, {
  cedarId: '{EB70DA4F-F293-468b-9C7B-0C6DC4B7A2C0}',
  uuid: '441a2d4fdbfbab00560cf9531f961911',
  fismaSystemName: 'Continuously Available CMS Hosting Environment',
  fismaSystemAcronym: 'DRaaS-CACHE',
  actualDispositionDate: null,
  countOfOpenPoams: 5,
  countOfTotalNonPrivilegedUserPopulation: 0,
  countOfTotalPrivilegedUserPopulation: 0,
  containsPersonallyIdentifiableInformation: 'Yes',
  dateAuthorizationMemoSigned: '2023-09-28',
  dateAuthorizationMemoExpires: '2026-09-27',
  eAuthenticationLevel: 'N/A',
  fips199OverallImpactRating: 3,
  isAccessedByNonOrganizationalUsers: 'No',
  isPiiLimitedToUserNameAndPass: 'No',
  isProtectedHealthInformation: 'Yes',
  lastActScaDate: '2023-09-11',
  lastAssessmentDate: '2023-10-06',
  lastContingencyPlanCompletionDate: '2023-08-25',
  lastPenTestDate: '2023-07-07',
  piaCompletionDate: '2020-07-21',
  primaryCyberRiskAdvisor: 'GFG3',
  privacySubjectMatterExpert: null,
  recoveryPointObjective: 24,
  recoveryTimeObjective: 24,
  systemOfRecordsNotice: null,
  tlcPhase: 'Operate',
  xlcPhase: null,
}];

const results = [{
  cedarId: '{0B14AA22-B98F-4de3-9ACD-3874B750B6D0}',
  uuid: 'A1837A4F-4EF5-42D1-9537-82178667A8EB',
  fismaSystemName: 'CMS National Training Program Learning Management System',
  fismaSystemAcronym: 'NTP LMS',
  actualDispositionDate: null,
  countOfOpenPoams: 1,
  countOfTotalNonPrivilegedUserPopulation: 75000,
  countOfTotalPrivilegedUserPopulation: 20,
  containsPersonallyIdentifiableInformation: true,
  dateAuthorizationMemoSigned: '2023-12-06',
  dateAuthorizationMemoExpires: '2026-12-05',
  eAuthenticationLevel: '1',
  fips199OverallImpactRating: 2,
  isAccessedByNonOrganizationalUsers: true,
  isPiiLimitedToUserNameAndPass: false,
  isProtectedHealthInformation: false,
  lastActScaDate: '2023-10-06',
  lastAssessmentDate: '2023-10-06',
  lastContingencyPlanCompletionDate: '2023-07-26',
  lastPenTestDate: '2023-06-20',
  piaCompletionDate: '2021-07-12',
  primaryCyberRiskAdvisor: 'STWO',
  privacySubjectMatterExpert: 'DEZC',
  recoveryPointObjective: 168,
  recoveryTimeObjective: 72,
  systemOfRecordsNotice: [
    '09-70-0542',
  ],
  tlcPhase: 'Operate',
  xlcPhase: null,
}, {
  cedarId: '{2511240D-2365-4c02-B032-17C432B475B7}',
  uuid: 'ee3b806e1b8520102e8743bae54bcb3f',
  fismaSystemName: 'Competitive Bidding Survey',
  fismaSystemAcronym: 'CBS',
  actualDispositionDate: '2022-09-16',
  countOfOpenPoams: 0,
  countOfTotalNonPrivilegedUserPopulation: 0,
  countOfTotalPrivilegedUserPopulation: 0,
  containsPersonallyIdentifiableInformation: true,
  dateAuthorizationMemoSigned: '2021-10-07',
  dateAuthorizationMemoExpires: '2022-10-07',
  eAuthenticationLevel: 'N/A',
  fips199OverallImpactRating: 2,
  isAccessedByNonOrganizationalUsers: false,
  isPiiLimitedToUserNameAndPass: false,
  isProtectedHealthInformation: true,
  lastActScaDate: '2022-06-27',
  lastAssessmentDate: '2022-06-27',
  lastContingencyPlanCompletionDate: '2022-06-03',
  lastPenTestDate: '2022-05-02',
  piaCompletionDate: '2021-06-10',
  primaryCyberRiskAdvisor: 'Y113',
  privacySubjectMatterExpert: 'NQOB',
  recoveryPointObjective: 24,
  recoveryTimeObjective: 48,
  systemOfRecordsNotice: [
    '09-70-0530',
    '09-70-0571',
  ],
  tlcPhase: 'Retire',
  xlcPhase: null,
}, {
  cedarId: '{EB70DA4F-F293-468b-9C7B-0C6DC4B7A2C0}',
  uuid: '441a2d4fdbfbab00560cf9531f961911',
  fismaSystemName: 'Continuously Available CMS Hosting Environment',
  fismaSystemAcronym: 'DRaaS-CACHE',
  actualDispositionDate: null,
  countOfOpenPoams: 5,
  countOfTotalNonPrivilegedUserPopulation: 0,
  countOfTotalPrivilegedUserPopulation: 0,
  containsPersonallyIdentifiableInformation: true,
  dateAuthorizationMemoSigned: '2023-09-28',
  dateAuthorizationMemoExpires: '2026-09-27',
  eAuthenticationLevel: 'N/A',
  fips199OverallImpactRating: 3,
  isAccessedByNonOrganizationalUsers: false,
  isPiiLimitedToUserNameAndPass: false,
  isProtectedHealthInformation: true,
  lastActScaDate: '2023-09-11',
  lastAssessmentDate: '2023-10-06',
  lastContingencyPlanCompletionDate: '2023-08-25',
  lastPenTestDate: '2023-07-07',
  piaCompletionDate: '2020-07-21',
  primaryCyberRiskAdvisor: 'GFG3',
  privacySubjectMatterExpert: null,
  recoveryPointObjective: 24,
  recoveryTimeObjective: 24,
  systemOfRecordsNotice: [],
  tlcPhase: 'Operate',
  xlcPhase: null,
}];

describe('Authority To Operate tests', () => {
  afterEach(() => {
    mocks.getDb.mockImplementation(() => ({
      queryView: mocks.queryView,
    }));
  });

  it('Should return a 400 and an error if the systemId is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        systemId: ['bad', 'query'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The systemId is a string',
    });
  });

  it('Should return a 400 and an error if the systemId is not a bracket wrapped uuid', async () => {
    const response = await request(app).get('/')
      .query({
        systemId: 'bad',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The systemId is not valid',
    });
  });

  it('Should return a 400 and an error if the uuid is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        uuid: 'bad',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The uuid is not valid',
    });
  });

  it('Should return a 400 and an error if the fismaSystemAcronym is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        fismaSystemAcronym: ['bad', 'query'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The fismaSystemAcronym is not valid',
    });
  });

  it('Should return a 400 and an error if the tlcPhase is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        tlcPhase: ['bad', 'query'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The tlcPhase is not valid',
    });
  });

  it('Should return a 400 and an error if the containsPersonallyIdentifiableInformation is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        containsPersonallyIdentifiableInformation: 'yes',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The containsPersonallyIdentifiableInformation is not valid',
    });
  });

  it('Should return a 400 and an error if the isProtectedHealthInformation is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        isProtectedHealthInformation: 'yes',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The isProtectedHealthInformation is not valid',
    });
  });

  it('Should return a 400 and an error if the dispositionDateAfter is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        dispositionDateAfter: ['05-21-2021', 'bad'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The dispositionDateAfter is not a string',
    });
  });

  it('Should return a 400 and an error if the dispositionDateAfter is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        dispositionDateAfter: '05-21-2021',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The dispositionDateAfter is not a valid format',
    });
  });

  it('Should return a 400 and an error if the dispositionDateBefore is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        dispositionDateBefore: ['05-21-2021', 'bad'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The dispositionDateBefore is not a string',
    });
  });

  it('Should return a 400 and an error if the dispositionDateBefore is not valid', async () => {
    const response = await request(app).get('/')
      .query({
        dispositionDateBefore: '05-21-2021',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The dispositionDateBefore is not a valid format',
    });
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', atoModule);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Database Unavailable',
    });
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((_, reject) => {
      reject(new Error('Critical Bad Connection'));
    }));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve authority to operate data',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Bad Connection');
  });

  it('Should return a 500 and an error if queryView returns an error', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve(new Error('Bad Connection'));
    }));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve authority to operate data',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return results for the provided systemId and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        systemId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
          },
        },
      },
    );
  });

  it('Should return results for the provided systemId and other params but ignore the other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        systemId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
        uuid: 'ee3b806e1b8520102e8743bae54bcb3f',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
          },
        },
      },
    );
  });

  it('Should return results for the provided uuid and fismaSystemAcronym', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        uuid: 'ee3b806e1b8520102e8743bae54bcb3f',
        fismaSystemAcronym: 'CMS National Training Program Learning Management System',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'CMS_UUID',
            operator: '=',
            value: 'ee3b806e1b8520102e8743bae54bcb3f',
          },
        },
        or: [{
          operation: {
            column: 'Acronym',
            operator: '=',
            value: 'CMS National Training Program Learning Management System',
          },
        }],
      },
    );
  });

  it('Should return results for the provided uuid and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        uuid: 'ee3b806e1b8520102e8743bae54bcb3f',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'CMS_UUID',
            operator: '=',
            value: 'ee3b806e1b8520102e8743bae54bcb3f',
          },
        },
      },
    );
  });

  it('Should return results for the provided fismaSystemAcronym and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        fismaSystemAcronym: 'CMS National Training Program Learning Management System',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Acronym',
            operator: '=',
            value: 'CMS National Training Program Learning Management System',
          },
        },
      },
    );
  });

  it('Should return results for the provided tlcPhase and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        tlcPhase: 'Operate',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'TLC Phase',
            operator: '=',
            value: 'Operate',
          },
        },
      },
    );
  });

  it('Should return results for the provided containsPersonallyIdentifiableInformation and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        containsPersonallyIdentifiableInformation: 'true',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Collect Maintain Share PII',
            operator: '=',
            value: 'Yes',
          },
        },
      },
    );
  });

  it('Should return results for the provided isProtectedHealthInformation and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        isProtectedHealthInformation: 'true',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Has PHI',
            operator: '=',
            value: 'Yes',
          },
        },
      },
    );
  });

  it('Should return results for the provided dispositionDateAfter and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        dispositionDateAfter: '2023-12-06',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Effective Date',
            operator: '=',
            value: '2023-12-06',
          },
        },
      },
    );
  });

  it('Should return results for the provided dispositionDateBefore and no other params', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[dbResults[0]]]);
    }));

    const response = await request(app).get('/')
      .query({
        dispositionDateBefore: '2026-12-05',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: [results[0]],
      count: 1,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      {
        where: {
          operation: {
            column: 'Expiration Date',
            operator: '=',
            value: '2026-12-05',
          },
        },
      },
    );
  });

  it('Should return all results', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([dbResults]);
    }));

    const response = await request(app).get('/')
      .query({
        dispositionDateBefore: '2026-12-05',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      AuthorityToOperateList: results,
      count: 3,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_API.Sparx_System_ATO_Full_Tbl',
      expect.anything(),
      undefined,
    );
  });
});
