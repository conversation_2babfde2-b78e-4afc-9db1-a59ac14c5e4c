import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import listModule from '../../../../src/resources/cedar-core-v2/deployment/list';
import deploymentConfig from '../../../../src/resources/cedar-core-v2/deployment';
import {
  formattedDeploymentAllQuery,
  formattedDeploymentSelectedQuery,
} from './deploymentListTestData';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  deploymentListUtil: vi.fn(),
  getDb: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/deployments'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    deploymentListUtil: mocks.deploymentListUtil,
  };
});

const app = await createApp();
app.resources.register(app, '/', deploymentConfig);
app.resources.initResources(app);

const systemId = '{11111111-1111-1111-1111-111111111111}';

describe('Deployment List tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if db is not in the req', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 200 with a list of all data centers with no systemId', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce({
      count: formattedDeploymentAllQuery.length,
      Deployments: formattedDeploymentAllQuery,
    });
    const response = await request(app)
      .get('/')
      .query({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: formattedDeploymentAllQuery.length,
      Deployments: formattedDeploymentAllQuery,
    });
  });

  it('Should return a 200 with a list of all systems with a systemId', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce({
      count: formattedDeploymentSelectedQuery.length,
      Deployments: formattedDeploymentSelectedQuery,
    });

    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: formattedDeploymentSelectedQuery.length,
      Deployments: formattedDeploymentSelectedQuery,
    });
  });

  it('Should return a 500 and an error if secondary params are provided and systemId is not', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce(new Error('System ID is required if other parameters are provided'));
    const response = await request(app)
      .get('/')
      .query({
        state: 'active',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an issue fetching the deployments/data centers',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('System ID is required if other parameters are provided');
  });

  it('Should return a 500 and an error if the util returns an error', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an issue fetching the deployments/data centers',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });
});
