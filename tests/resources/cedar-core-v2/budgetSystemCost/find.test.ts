import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import findModule from '../../../../src/resources/cedar-core-v2/budgetSystemCost/find';
import budgetSystemCostConfig from '../../../../src/resources/cedar-core-v2/budgetSystemCost';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import {
  systemId,
  systemIdDbResponse,
  systemIdApiResponse,
  allDbResponse,
  allApiResponse,
} from './budgetSystemCostTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', budgetSystemCostConfig);
app.resources.initResources(app);

describe('Budget System Cost tests', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', findModule);
    const response = await request(badApp)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve budget system cost',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve budget system cost',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 200 with a result and count with a system id', async () => {
    mocks.queryView.mockResolvedValueOnce([systemIdDbResponse]);

    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 2,
      BudgetActualCost: systemIdApiResponse,
    });
  });

  it('Should return a 200 with a result and count with no system id', async () => {
    mocks.queryView.mockResolvedValueOnce([allDbResponse]);

    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 4,
      BudgetActualCost: allApiResponse,
    });
  });
});
