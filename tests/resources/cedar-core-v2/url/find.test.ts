import { get } from 'lodash';
import request from 'supertest';
import listModule from 'src/resources/cedar-core-v2/url/find';
import urlConfig from 'src/resources/cedar-core-v2/url';
import { createApp, createBadApp, testLogger } from 'tests/test-utils';

const mocks = vi.hoisted(() => ({
  cedarUrlsUtil: vi.fn(),
  getDb: vi.fn(),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('src/utils/urls'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    cedarUrlsUtil: mocks.cedarUrlsUtil,
  };
});

const app = await createApp();
await app.resources.register(app, '/', urlConfig);
app.resources.initResources(app);

const systemId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';

const fullProcessedResults = [{
  urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'webmethods-apiportal.cedar.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'API Gateway/API Portal',
},
{
  urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'www.cedarimpl.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'Implementation',
}];

describe('Cedar URL Find Tests', () => {
  it('Should return a 200 with a list of all URLs with the systemId', async () => {
    mocks.cedarUrlsUtil.mockResolvedValueOnce(fullProcessedResults);
    const response = await request(app).get(`/${systemId}`).set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 2,
      UrlList: fullProcessedResults,
    });
  });

  it('Should return a 200 and count: 0 if no URLs are found', async () => {
    mocks.cedarUrlsUtil.mockResolvedValueOnce([]);
    const response = await request(app).get('/{113BE457-783E-413e-99A2-FB52C55A1616}').set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
    });
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get(`/${systemId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Database Unavailable',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 404 without the path parameter', async () => {
    const response = await request(app).get('/').set('x-jwt-key', 'pass');
    expect(response.status).toEqual(404);
  });

  it('Should return a 500 with an error if the util was not successful', async () => {
    mocks.cedarUrlsUtil.mockResolvedValueOnce(new Error('Util Error'));
    const response = await request(app).get(`/${systemId}`).set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body.error).toEqual('There was an issue fetching the urls');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Util Error');
  });
});
