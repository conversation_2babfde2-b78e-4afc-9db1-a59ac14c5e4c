import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import listModule from '../../../../src/resources/cedar-core-v2/threat/list';
import contractConfig from '../../../../src/resources/cedar-core-v2/threat';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import {
  oneIdArrStr,
  oneIdDBResponse,
  oneIdAPIResponse,
  multiIdArrStr,
  multiIdDBResponse,
  multiIdAPIResponse,
} from './threatListTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', contractConfig);
app.resources.initResources(app);

describe('Threat List tests', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 200 with a count and a list given an array of one ID', async () => {
    mocks.queryView.mockResolvedValueOnce([oneIdDBResponse]);
    const response = await request(app)
      .get('/')
      .query({
        ids: oneIdArrStr,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(oneIdAPIResponse);
  });

  it('Should return a 200 with a count and a list given an array of multiple IDs', async () => {
    mocks.queryView.mockResolvedValueOnce([multiIdDBResponse]);
    const response = await request(app)
      .get('/')
      .query({
        ids: multiIdArrStr,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(multiIdAPIResponse);
  });

  it('Should return a 200 with just a count if threats with the ID are not found', async () => {
    mocks.queryView.mockResolvedValueOnce([]);
    const response = await request(app)
      .get('/')
      .query({
        ids: ['IJKL'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
    });
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 400 and an error if ids are not present', async () => {
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameter \'ids\'',
    });
  });

  it('Should return a 400 and an error if ids are just an empty string', async () => {
    const response = await request(app)
      .get('/')
      .query({
        ids: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameter \'ids\'',
    });
  });

  it('Should return a 500 and an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        ids: oneIdArrStr,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve threats by ids',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if the query view returns a critical error using keyword as a parameter.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        ids: oneIdArrStr,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve threats by ids',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });
});
