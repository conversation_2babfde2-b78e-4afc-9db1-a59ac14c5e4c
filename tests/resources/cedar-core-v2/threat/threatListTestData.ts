export const oneIdArrStr = 'ABCD';

export const oneIdDBResponse = [
  {
    id: '1',
    parentId: 'ABCD',
    alternativeId: '12345678',
    controlFamily: 'Music Studies',
    daysOpen: '62',
    weaknessRiskLevel: 'Moderate',
  },
  {
    id: '2',
    parentId: 'ABCD',
    alternativeId: '37696435',
    controlFamily: 'Fast Food Analysis',
    daysOpen: '26',
    weaknessRiskLevel: 'High',
  },
];

export const oneIdAPIResponse = {
  count: 2,
  Threats: [
    {
      id: '1',
      parentId: 'ABCD',
      alternativeId: '12345678',
      controlFamily: 'Music Studies',
      daysOpen: 62,
      weaknessRiskLevel: 'Moderate',
      type: 'POA&M',
    },
    {
      id: '2',
      parentId: 'ABCD',
      alternativeId: '37696435',
      controlFamily: 'Fast Food Analysis',
      daysOpen: 26,
      weaknessRiskLevel: 'High',
      type: 'POA&M',
    },
  ],
};

export const multiIdArrStr = 'ABCD,EFGH';

export const multiIdDBResponse = [
  {
    id: '1',
    parentId: 'ABCD',
    alternativeId: '12345678',
    controlFamily: 'Music Studies',
    daysOpen: '62',
    weaknessRiskLevel: 'Moderate',
  },
  {
    id: '2',
    parentId: 'ABCD',
    alternativeId: '37696435',
    controlFamily: 'Fast Food Analysis',
    daysOpen: '26',
    weaknessRiskLevel: 'High',
  },
  {
    id: '3',
    parentId: 'EFGH',
    alternativeId: '12345678',
    controlFamily: 'Water Studies',
    daysOpen: '62',
    weaknessRiskLevel: 'Moderate',
  },
  {
    id: '4',
    parentId: 'EFGH',
    alternativeId: '37696435',
    controlFamily: 'Fancy Food Analysis',
    daysOpen: '26',
    weaknessRiskLevel: 'High',
  },
];

export const multiIdAPIResponse = {
  count: 4,
  Threats: [
    {
      id: '1',
      parentId: 'ABCD',
      alternativeId: '12345678',
      controlFamily: 'Music Studies',
      daysOpen: 62,
      weaknessRiskLevel: 'Moderate',
      type: 'POA&M',
    },
    {
      id: '2',
      parentId: 'ABCD',
      alternativeId: '37696435',
      controlFamily: 'Fast Food Analysis',
      daysOpen: 26,
      weaknessRiskLevel: 'High',
      type: 'POA&M',
    },
    {
      id: '3',
      parentId: 'EFGH',
      alternativeId: '12345678',
      controlFamily: 'Water Studies',
      daysOpen: 62,
      weaknessRiskLevel: 'Moderate',
      type: 'POA&M',
    },
    {
      id: '4',
      parentId: 'EFGH',
      alternativeId: '37696435',
      controlFamily: 'Fancy Food Analysis',
      daysOpen: 26,
      weaknessRiskLevel: 'High',
      type: 'POA&M',
    },
  ],
};
