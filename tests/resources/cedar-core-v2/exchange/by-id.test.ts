import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import byIdModule from '../../../../src/resources/cedar-core-v2/exchange/by-id';
import exchangeConfig from '../../../../src/resources/cedar-core-v2/exchange';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', exchangeConfig);
app.resources.initResources(app);

const exchangeId = '{176C18FE-AE2D-4a12-9861-C9A464DD3962}';

const dbResponse = [[{
  exchangeId,
  exchangeName: 'IT Project Business Cases',
  exchangeVersion: '25',
  exchangeState: 'Active',
  exchangeStartDate: null,
  exchangeEndDate: null,
  exchangeRetiredDate: null,
  fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  fromOwnerName: 'CMS Enterprise Data Analytics Repository',
  fromOwnerType: 'System',
  toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
  toOwnerName: 'Easy Access to System Information',
  toOwnerType: 'System',
  connectionFrequency: 'Ad Hoc/As Needed',
  businessPurposeOfAddress: 'Eligibility and Enrollment|Program Oversight|Audit Support',
  containsHealthDisparityData: false,
  typeOfData: 'Information Resources',
  exchangeContainsCUI: true,
  exchangeCUIDescription: 'Potential pre-contract award information',
  exchangeNetworkProtocol: 'HTTPS',
  exchangeNetworkProtocolOther: null,
}]];

const apiResponse = {
  exchangeId,
  exchangeName: 'IT Project Business Cases',
  exchangeVersion: '25',
  exchangeState: 'Active',
  exchangeStartDate: null,
  exchangeEndDate: null,
  exchangeRetiredDate: null,
  fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  fromOwnerName: 'CMS Enterprise Data Analytics Repository',
  fromOwnerType: 'System',
  toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
  toOwnerName: 'Easy Access to System Information',
  toOwnerType: 'System',
  connectionFrequency: [
    'Ad Hoc/As Needed',
  ],
  businessPurposeOfAddress: [
    'Eligibility and Enrollment',
    'Program Oversight',
    'Audit Support',
  ],
  containsHealthDisparityData: false,
  typeOfData: [
    'Information Resources',
  ],
  exchangeContainsCUI: true,
  exchangeCUIDescription: 'Potential pre-contract award information',
  exchangeNetworkProtocol: [
    'HTTPS',
  ],
  exchangeNetworkProtocolOther: null,
};

describe('Exchange By ID tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', byIdModule);

    const response = await request(badApp)
      .get('/')
      .query({
        id: 'bad app id',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if db is not in the req', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app).get(`/${exchangeId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 500 and an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app).get(`/${exchangeId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve exchange by system ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if the query view returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app).get(`/${exchangeId}`).set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve exchange by system ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 200 with a result', async () => {
    mocks.queryView.mockResolvedValueOnce(dbResponse);
    const response = await request(app).get(`/${exchangeId}`).set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiResponse);
  });

  it('Should return a 400 and an error if objectId is invalid.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('"value" must be a valid GUID'));
    const response = await request(app).get('/{superrr1-bad2-uuid-and3-superrealll4}').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Invalid object id',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
  });
});
