import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import listModule from '../../../../src/resources/cedar-core-v2/exchange/list';
import exchangeConfig from '../../../../src/resources/cedar-core-v2/exchange';
import {
  databaseSenderExchangeListComp,
  apiSenderExchangeListComp,
  databaseReceiverExchangeListComp,
  apiReceiverExchangeListComp,
  databaseBothExchangeListComp,
  apiBothExchangeListComp,
} from './exchangeListTestData';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', exchangeConfig);
app.resources.initResources(app);

const systemId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';

describe('Exchange List tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if db is not in the req', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
  });

  it('Should return a 400 with an error that there is no systemId', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId: null,
        direction: 'sender',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'systemId\' and \'direction\'',
    });
  });

  it('Should return a 400 with an error that the systemId is invalid.', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{BBFDF1DC-97C1-4b29-8XXX-X20X5C8B2481}',
        direction: 'sender',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The system ID is not valid',
    });
  });

  it('Should return a 400 with an error that there is no direction', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId,
        direction: null,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'systemId\' and \'direction\'',
    });
  });

  it('Should return a 400 with an error that the direction is invalid', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId,
        direction: 'bad',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The provided direction is not valid.',
    });
  });

  it('Should return a 200 with a list of all systems with the systemId and the "sender" parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce([databaseSenderExchangeListComp]);

    const response = await request(app)
      .get('/')
      .query({
        systemId,
        direction: 'sender',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 9,
      Exchanges: apiSenderExchangeListComp,
    });
  });

  it('Should return a 200 with a list of all systems with the systemId and the "receiver" parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce([databaseReceiverExchangeListComp]);

    const response = await request(app)
      .get('/')
      .query({
        systemId,
        direction: 'receiver',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 9,
      Exchanges: apiReceiverExchangeListComp,
    });
  });

  it('Should return a 200 with a list of all systems with the systemId and the "both" parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce([databaseBothExchangeListComp]);

    const response = await request(app)
      .get('/')
      .query({
        systemId,
        direction: 'both',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 18,
      Exchanges: apiBothExchangeListComp,
    });
  });

  it('Should return a 500 and an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        systemId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
        direction: 'both',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve exchange by system ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 with an error if the query view returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app)
      .get('/')
      .query({
        systemId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
        direction: 'both',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve exchange by system ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });
});
