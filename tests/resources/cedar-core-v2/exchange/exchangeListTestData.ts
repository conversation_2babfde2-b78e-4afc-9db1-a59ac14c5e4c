// count = 9
export const databaseSenderExchangeListComp = [
  {
    exchangeId: '{176C18FE-AE2D-4a12-9861-C9A464DD3962}',
    exchangeName: 'IT Project Business Cases',
    exchangeDescription: 'After IT Project Business Cases are submitted via EASi and stored in CEDAR, they are available from CEDAR to view in EASi. All past IT Intakes are available.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{43E10A6E-1284-42a1-A270-242246E16D06}',
    exchangeName: 'CEDAR/MINT - To look up CMS users so they can be tagged as collaborators',
    exchangeDescription: 'To look up CMS users so they can be tagged as collaborators',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{2337D9FF-A899-4b4e-A9F4-08A01DE89335}',
    toOwnerName: 'Model INnovation Tool',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
    typeOfDataName: 'Administrative Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{60A13150-C6D6-45b8-B361-E42B4FCD4BC8}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'After IT Project Intake forms have been submitted via EASi UI and stored in CEDAR, they are available from CEDAR to be viewed in EASi. All past IT Intakes are available for viewing in EASi.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{********-0C6D-4c08-9F74-31DE1194C246}',
    exchangeName: 'Business Architecture and Enterprise Architecture models and reports',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture use CEDAR to develop views, models, reports, and extracts of the Enterprise Architecture data showing dependencies and alignments to support data calls and planning analysis for staff throughout CMS.',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{E2BCDF5F-82E9-4597-BAAB-00E928EC858E}',
    toOwnerName: 'CMS Component',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Real-Time|Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Custom Format',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A35BF3B8-CEFD-4E24-A16E-4E756C53EA3E}',
    exchangeName: 'System Inventory Info',
    exchangeDescription: 'System Inventory Info sent to KMP in the BDC.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{6122F0F3-395E-446f-A78E-65AB3BEA8E64}',
    toOwnerName: 'CMS Baltimore Data Center - EDC4',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A57485B8-8209-4888-BCDD-51F6364A49C0}',
    exchangeName: 'System Profile',
    exchangeDescription: 'EASi shows for each system, core information about CMS Systems: Description, links, ATO expiration, contacts, owner org, business owner, data center hosting, and a few other details. It enables EASi users to edit the contacts and their roles.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6044E3E5-ACBF-46E3-9575-0BCAEE4CFDF3}',
    exchangeName: 'Testing Data Exchange Very Serious',
    exchangeDescription: 'Yes',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-10-03',
    exchangeRetiredDate: '2024-10-03',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{7B81F587-E83F-4651-8B19-CEEBE63651BE}',
    toOwnerName: 'ACO API',
    toOwnerType: 'System',
    connectionFrequency: 'Every Two Weeks|Weekly|Daily',
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Coordination of Benefits|Premium Calculation|Risk Adjustment|Eligibility and Enrollment|Innovation, Research and Demonstrations',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
    typeOfDataName: 'Beneficiary Attribution',
    numOfRecords: '1000 - 100,000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'HTTP|Other',
    exchangeNetworkProtocolOther: 'sdfdsfdsfds',
  },
  {
    exchangeId: '{C5116CCD-514A-4184-B361-E41A7EA5B453}',
    exchangeName: 'test',
    exchangeDescription: 'test',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{DEEA654F-7C16-4ed8-B785-A4553E1F8A4C}',
    toOwnerName: '1115 Demos PMDA Contractor (CVP)',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Annually',
    dataExchangeAgreement: 'Information Security Agreement (ISA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Coordination of Benefits',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    isBeneficiaryMailingFile: null,
    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
    typeOfDataName: 'Beneficiary Benefit Utilization',
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: '',
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Law Enforcement',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'Other',
    exchangeNetworkProtocolOther: 'gfdgfd',
  },
  {
    exchangeId: '{DB8A7FFE-6AEC-4B4A-8E03-34EFC2942C29}',
    exchangeName: 'Test',
    exchangeDescription: 'descrip',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-14',
    exchangeRetiredDate: '2024-09-14',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{4E10FAFC-9D7A-45ff-9600-DFAB85CEF92D}',
    toOwnerName: '1-800-Medicare Beneficiary Contact Center Operations Contractor',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Quarterly',
    dataExchangeAgreement: 'Memorandum of Understanding (MOU)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
    typeOfDataName: 'Beneficiary Attribution',
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'FTP',
    exchangeNetworkProtocolOther: null,
  },
];

export const apiSenderExchangeListComp = [
  {
    exchangeId: '{176C18FE-AE2D-4a12-9861-C9A464DD3962}',
    exchangeName: 'IT Project Business Cases',
    exchangeDescription: 'After IT Project Business Cases are submitted via EASi and stored in CEDAR, they are available from CEDAR to view in EASi. All past IT Intakes are available.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{43E10A6E-1284-42a1-A270-242246E16D06}',
    exchangeName: 'CEDAR/MINT - To look up CMS users so they can be tagged as collaborators',
    exchangeDescription: 'To look up CMS users so they can be tagged as collaborators',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{2337D9FF-A899-4b4e-A9F4-08A01DE89335}',
    toOwnerName: 'Model INnovation Tool',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
        name: 'Administrative Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{60A13150-C6D6-45b8-B361-E42B4FCD4BC8}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'After IT Project Intake forms have been submitted via EASi UI and stored in CEDAR, they are available from CEDAR to be viewed in EASi. All past IT Intakes are available for viewing in EASi.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{********-0C6D-4c08-9F74-31DE1194C246}',
    exchangeName: 'Business Architecture and Enterprise Architecture models and reports',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture use CEDAR to develop views, models, reports, and extracts of the Enterprise Architecture data showing dependencies and alignments to support data calls and planning analysis for staff throughout CMS.',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{E2BCDF5F-82E9-4597-BAAB-00E928EC858E}',
    toOwnerName: 'CMS Component',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Real-Time',
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Custom Format',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A35BF3B8-CEFD-4E24-A16E-4E756C53EA3E}',
    exchangeName: 'System Inventory Info',
    exchangeDescription: 'System Inventory Info sent to KMP in the BDC.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{6122F0F3-395E-446f-A78E-65AB3BEA8E64}',
    toOwnerName: 'CMS Baltimore Data Center - EDC4',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A57485B8-8209-4888-BCDD-51F6364A49C0}',
    exchangeName: 'System Profile',
    exchangeDescription: 'EASi shows for each system, core information about CMS Systems: Description, links, ATO expiration, contacts, owner org, business owner, data center hosting, and a few other details. It enables EASi users to edit the contacts and their roles.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6044E3E5-ACBF-46E3-9575-0BCAEE4CFDF3}',
    exchangeName: 'Testing Data Exchange Very Serious',
    exchangeDescription: 'Yes',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-10-03',
    exchangeRetiredDate: '2024-10-03',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{7B81F587-E83F-4651-8B19-CEEBE63651BE}',
    toOwnerName: 'ACO API',
    toOwnerType: 'System',
    connectionFrequency: [
      'Every Two Weeks',
      'Weekly',
      'Daily',
    ],
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Coordination of Benefits',
      'Premium Calculation',
      'Risk Adjustment',
      'Eligibility and Enrollment',
      'Innovation, Research and Demonstrations',
    ],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
        name: 'Beneficiary Attribution',
      },
    ],
    numOfRecords: '1000 - 100,000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'HTTP',
      'Other',
    ],
    exchangeNetworkProtocolOther: 'sdfdsfdsfds',
  },
  {
    exchangeId: '{C5116CCD-514A-4184-B361-E41A7EA5B453}',
    exchangeName: 'test',
    exchangeDescription: 'test',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{DEEA654F-7C16-4ed8-B785-A4553E1F8A4C}',
    toOwnerName: '1115 Demos PMDA Contractor (CVP)',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Annually',
    ],
    dataExchangeAgreement: 'Information Security Agreement (ISA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Coordination of Benefits',
    ],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
        name: 'Beneficiary Benefit Utilization',
      },
    ],
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: '',
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Law Enforcement',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'Other',
    ],
    exchangeNetworkProtocolOther: 'gfdgfd',
  },
  {
    exchangeId: '{DB8A7FFE-6AEC-4B4A-8E03-34EFC2942C29}',
    exchangeName: 'Test',
    exchangeDescription: 'descrip',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-14',
    exchangeRetiredDate: '2024-09-14',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{4E10FAFC-9D7A-45ff-9600-DFAB85CEF92D}',
    toOwnerName: '1-800-Medicare Beneficiary Contact Center Operations Contractor',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Quarterly',
    ],
    dataExchangeAgreement: 'Memorandum of Understanding (MOU)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
        name: 'Beneficiary Attribution',
      },
    ],
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
];

// count = 9
export const databaseReceiverExchangeListComp = [
  {
    exchangeId: '{3B22DB27-9697-4bec-B24B-E02CEE8CC6E6}',
    exchangeName: 'ATO List and Data',
    exchangeDescription: 'A list of the current ATOs (FISMA Reportable Systems) is updated in CEDAR with core data about each entry.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{35405DE7-5B79-4d7c-9249-50C763A1E399}',
    fromOwnerName: 'CMS FISMA Controls Tracking System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Weekly',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Mailing',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '1000 - 100,000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Financial Information|Legal Information',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{4D5D7A6E-8B8D-4b82-B5B2-6FFDA12023BC}',
    exchangeName: 'CMS Active Contracts List',
    exchangeDescription: 'Each month DEA staff receive via email the list CMS Active Contracts generated by OAGM contractors from CAMS. The list includes basic data about each active contract and identifies the Budget Project IDs funding each. EDA manually imports that data into CEDAR. CLIN data for each contract has been added.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}',
    fromOwnerName: 'Comprehensive Acquisition Management System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Monthly',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
    typeOfDataName: 'Administrative Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'contracts data - CLIN data',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'Other',
    exchangeNetworkProtocolOther: 'SMTP',
  },
  {
    exchangeId: '{0F212C4C-8E87-4690-A6A3-28A8929D44D3}',
    exchangeName: 'CMS Operating Budget Plan',
    exchangeDescription: 'DEA staff each quarter manually download the CMS Operating Budget Plan and import as a reference set the Budget Project ID entries with basic data. No dollar amounts or Funding Source data is loaded. DEA staff manually import this data into CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{DE636697-C148-4ac6-B8CC-429AC7F68ED9}',
    fromOwnerName: 'CMS Component (Office of Financial Management (OFM))',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Quarterly',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{AC33E0B0-CD18-4809-A4D8-98E80C4F16DC}',
    typeOfDataName: 'Financial Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{9ADCEC6B-E662-4b91-85AC-B4A7626155D5}',
    exchangeName: 'Ad hoc Business Architecture and Enterprise Architecture Information',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture enter details to include in models, extracts, and reports from other parts of CMS.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{976005AD-BB1C-4f7d-8925-FDF2189C103B}',
    fromOwnerName: 'CMS Component (Division of Enterprise Architecture)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'UI Entry',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A6B62731-0031-466a-B726-80B979E093D4}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'IT Intake form is submitted in EASi by CMS staff to explain their IT Project and stored in CEDAR so that IT Governance staff can review and consider the amount of IT Governance oversight the IT Project needs.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{AB285BEA-E6AA-40bf-B9F8-52015AE6AAD3}',
    exchangeName: 'IT Project Business Case',
    exchangeDescription: 'The Governance Review Team (GRT) upon review of a new IT Project Intake, may determine that a Business Case needs to be completed for the IT Governance of that IT Project. The Business Case collects information to show the alternatives for design of the proposed IT solution. The Business Case form is completed by IT Project leads in EASi which stores the information in CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6EC4D848-9604-485C-B55D-186FCEDE0ABD}',
    exchangeName: 'Testing',
    exchangeDescription: 'Test Description',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '09/13/2024',
    exchangeRetiredDate: '09/13/2024',
    fromOwnerId: '{58F2F1D4-CA08-4970-9F79-D67B864A015C}',
    fromOwnerName: 'Administrative Qualified Independent Contractor (QIC)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Annually',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{5CF8BD3E-409F-47e5-A98A-DEFBE6E85386}',
    typeOfDataName: 'Beneficiary Cost Sharing',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'Encrypted FTP',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C225B978-FF95-44E3-A698-4A18586ACE1C}',
    exchangeName: 'Test 3',
    exchangeDescription: 'TEst',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '09/13/2024',
    exchangeRetiredDate: '09/13/2024',
    fromOwnerId: '{FE9123FD-7141-41db-B7CF-B998A473EE5B}',
    fromOwnerName: '2020 (CWF)',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Semi-Annually|Quarterly',
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Mailing|Payment Calculation|Coordination of Benefits',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
    typeOfDataName: 'Beneficiary Benefit Utilization',
    numOfRecords: '< 1000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Critical Infrastructure Information|Financial Information|Law Enforcement',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTP|Encrypted FTP',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{F051FEE8-7EDD-40C9-BE1A-9EBE2730A107}',
    exchangeName: 'System Profile Edit Capability',
    exchangeDescription: 'Edits requested by EASi System Profile users, currently only supported for the \'Teams\' sub-page but edit functionality for other sub pages are planned for development.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Financial Information',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
];

export const apiReceiverExchangeListComp = [
  {
    exchangeId: '{3B22DB27-9697-4bec-B24B-E02CEE8CC6E6}',
    exchangeName: 'ATO List and Data',
    exchangeDescription: 'A list of the current ATOs (FISMA Reportable Systems) is updated in CEDAR with core data about each entry.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{35405DE7-5B79-4d7c-9249-50C763A1E399}',
    fromOwnerName: 'CMS FISMA Controls Tracking System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Weekly',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Mailing',
    ],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '1000 - 100,000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Financial Information',
      'Legal Information',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{4D5D7A6E-8B8D-4b82-B5B2-6FFDA12023BC}',
    exchangeName: 'CMS Active Contracts List',
    exchangeDescription: 'Each month DEA staff receive via email the list CMS Active Contracts generated by OAGM contractors from CAMS. The list includes basic data about each active contract and identifies the Budget Project IDs funding each. EDA manually imports that data into CEDAR. CLIN data for each contract has been added.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}',
    fromOwnerName: 'Comprehensive Acquisition Management System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Monthly',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
        name: 'Administrative Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'contracts data - CLIN data',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'Other',
    ],
    exchangeNetworkProtocolOther: 'SMTP',
  },
  {
    exchangeId: '{0F212C4C-8E87-4690-A6A3-28A8929D44D3}',
    exchangeName: 'CMS Operating Budget Plan',
    exchangeDescription: 'DEA staff each quarter manually download the CMS Operating Budget Plan and import as a reference set the Budget Project ID entries with basic data. No dollar amounts or Funding Source data is loaded. DEA staff manually import this data into CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{DE636697-C148-4ac6-B8CC-429AC7F68ED9}',
    fromOwnerName: 'CMS Component (Office of Financial Management (OFM))',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Quarterly',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{AC33E0B0-CD18-4809-A4D8-98E80C4F16DC}',
        name: 'Financial Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{9ADCEC6B-E662-4b91-85AC-B4A7626155D5}',
    exchangeName: 'Ad hoc Business Architecture and Enterprise Architecture Information',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture enter details to include in models, extracts, and reports from other parts of CMS.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{976005AD-BB1C-4f7d-8925-FDF2189C103B}',
    fromOwnerName: 'CMS Component (Division of Enterprise Architecture)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'UI Entry',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A6B62731-0031-466a-B726-80B979E093D4}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'IT Intake form is submitted in EASi by CMS staff to explain their IT Project and stored in CEDAR so that IT Governance staff can review and consider the amount of IT Governance oversight the IT Project needs.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{AB285BEA-E6AA-40bf-B9F8-52015AE6AAD3}',
    exchangeName: 'IT Project Business Case',
    exchangeDescription: 'The Governance Review Team (GRT) upon review of a new IT Project Intake, may determine that a Business Case needs to be completed for the IT Governance of that IT Project. The Business Case collects information to show the alternatives for design of the proposed IT solution. The Business Case form is completed by IT Project leads in EASi which stores the information in CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6EC4D848-9604-485C-B55D-186FCEDE0ABD}',
    exchangeName: 'Testing',
    exchangeDescription: 'Test Description',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '09/13/2024',
    exchangeRetiredDate: '09/13/2024',
    fromOwnerId: '{58F2F1D4-CA08-4970-9F79-D67B864A015C}',
    fromOwnerName: 'Administrative Qualified Independent Contractor (QIC)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Annually',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{5CF8BD3E-409F-47e5-A98A-DEFBE6E85386}',
        name: 'Beneficiary Cost Sharing',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'Encrypted FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C225B978-FF95-44E3-A698-4A18586ACE1C}',
    exchangeName: 'Test 3',
    exchangeDescription: 'TEst',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '09/13/2024',
    exchangeRetiredDate: '09/13/2024',
    fromOwnerId: '{FE9123FD-7141-41db-B7CF-B998A473EE5B}',
    fromOwnerName: '2020 (CWF)',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Semi-Annually',
      'Quarterly',
    ],
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Mailing',
      'Payment Calculation',
      'Coordination of Benefits',
    ],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
        name: 'Beneficiary Benefit Utilization',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Critical Infrastructure Information',
      'Financial Information',
      'Law Enforcement',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTP',
      'Encrypted FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{F051FEE8-7EDD-40C9-BE1A-9EBE2730A107}',
    exchangeName: 'System Profile Edit Capability',
    exchangeDescription: 'Edits requested by EASi System Profile users, currently only supported for the \'Teams\' sub-page but edit functionality for other sub pages are planned for development.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Financial Information',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
];

// count = 18
export const databaseBothExchangeListComp = [
  {
    exchangeId: '{176C18FE-AE2D-4a12-9861-C9A464DD3962}',
    exchangeName: 'IT Project Business Cases',
    exchangeDescription: 'After IT Project Business Cases are submitted via EASi and stored in CEDAR, they are available from CEDAR to view in EASi. All past IT Intakes are available.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{3B22DB27-9697-4bec-B24B-E02CEE8CC6E6}',
    exchangeName: 'ATO List and Data',
    exchangeDescription: 'A list of the current ATOs (FISMA Reportable Systems) is updated in CEDAR with core data about each entry.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{35405DE7-5B79-4d7c-9249-50C763A1E399}',
    fromOwnerName: 'CMS FISMA Controls Tracking System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Weekly',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Mailing',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '1000 - 100,000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Financial Information|Legal Information',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{43E10A6E-1284-42a1-A270-242246E16D06}',
    exchangeName: 'CEDAR/MINT - To look up CMS users so they can be tagged as collaborators',
    exchangeDescription: 'To look up CMS users so they can be tagged as collaborators',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{2337D9FF-A899-4b4e-A9F4-08A01DE89335}',
    toOwnerName: 'Model INnovation Tool',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
    typeOfDataName: 'Administrative Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{4D5D7A6E-8B8D-4b82-B5B2-6FFDA12023BC}',
    exchangeName: 'CMS Active Contracts List',
    exchangeDescription: 'Each month DEA staff receive via email the list CMS Active Contracts generated by OAGM contractors from CAMS. The list includes basic data about each active contract and identifies the Budget Project IDs funding each. EDA manually imports that data into CEDAR. CLIN data for each contract has been added.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}',
    fromOwnerName: 'Comprehensive Acquisition Management System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Monthly',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
    typeOfDataName: 'Administrative Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'contracts data - CLIN data',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'Other',
    exchangeNetworkProtocolOther: 'SMTP',
  },
  {
    exchangeId: '{60A13150-C6D6-45b8-B361-E42B4FCD4BC8}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'After IT Project Intake forms have been submitted via EASi UI and stored in CEDAR, they are available from CEDAR to be viewed in EASi. All past IT Intakes are available for viewing in EASi.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{********-0C6D-4c08-9F74-31DE1194C246}',
    exchangeName: 'Business Architecture and Enterprise Architecture models and reports',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture use CEDAR to develop views, models, reports, and extracts of the Enterprise Architecture data showing dependencies and alignments to support data calls and planning analysis for staff throughout CMS.',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{E2BCDF5F-82E9-4597-BAAB-00E928EC858E}',
    toOwnerName: 'CMS Component',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Real-Time|Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Custom Format',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{0F212C4C-8E87-4690-A6A3-28A8929D44D3}',
    exchangeName: 'CMS Operating Budget Plan',
    exchangeDescription: 'DEA staff each quarter manually download the CMS Operating Budget Plan and import as a reference set the Budget Project ID entries with basic data. No dollar amounts or Funding Source data is loaded. DEA staff manually import this data into CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{DE636697-C148-4ac6-B8CC-429AC7F68ED9}',
    fromOwnerName: 'CMS Component (Office of Financial Management (OFM))',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Quarterly',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{AC33E0B0-CD18-4809-A4D8-98E80C4F16DC}',
    typeOfDataName: 'Financial Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6044E3E5-ACBF-46E3-9575-0BCAEE4CFDF3}',
    exchangeName: 'Testing Data Exchange Very Serious',
    exchangeDescription: 'Yes',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-10-03',
    exchangeRetiredDate: '2024-10-03',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{7B81F587-E83F-4651-8B19-CEEBE63651BE}',
    toOwnerName: 'ACO API',
    toOwnerType: 'System',
    connectionFrequency: 'Every Two Weeks|Weekly|Daily',
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Coordination of Benefits|Premium Calculation|Risk Adjustment|Eligibility and Enrollment|Innovation, Research and Demonstrations',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
    typeOfDataName: 'Beneficiary Attribution',
    numOfRecords: '1000 - 100,000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'HTTP|Other',
    exchangeNetworkProtocolOther: 'sdfdsfdsfds',
  },
  {
    exchangeId: '{9ADCEC6B-E662-4b91-85AC-B4A7626155D5}',
    exchangeName: 'Ad hoc Business Architecture and Enterprise Architecture Information',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture enter details to include in models, extracts, and reports from other parts of CMS.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{976005AD-BB1C-4f7d-8925-FDF2189C103B}',
    fromOwnerName: 'CMS Component (Division of Enterprise Architecture)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'UI Entry',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A35BF3B8-CEFD-4E24-A16E-4E756C53EA3E}',
    exchangeName: 'System Inventory Info',
    exchangeDescription: 'System Inventory Info sent to KMP in the BDC.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{6122F0F3-395E-446f-A78E-65AB3BEA8E64}',
    toOwnerName: 'CMS Baltimore Data Center - EDC4',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A57485B8-8209-4888-BCDD-51F6364A49C0}',
    exchangeName: 'System Profile',
    exchangeDescription: 'EASi shows for each system, core information about CMS Systems: Description, links, ATO expiration, contacts, owner org, business owner, data center hosting, and a few other details. It enables EASi users to edit the contacts and their roles.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A6B62731-0031-466a-B726-80B979E093D4}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'IT Intake form is submitted in EASi by CMS staff to explain their IT Project and stored in CEDAR so that IT Governance staff can review and consider the amount of IT Governance oversight the IT Project needs.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{AB285BEA-E6AA-40bf-B9F8-52015AE6AAD3}',
    exchangeName: 'IT Project Business Case',
    exchangeDescription: 'The Governance Review Team (GRT) upon review of a new IT Project Intake, may determine that a Business Case needs to be completed for the IT Governance of that IT Project. The Business Case collects information to show the alternatives for design of the proposed IT solution. The Business Case form is completed by IT Project leads in EASi which stores the information in CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{F051FEE8-7EDD-40C9-BE1A-9EBE2730A107}',
    exchangeName: 'System Profile Edit Capability',
    exchangeDescription: 'Edits requested by EASi System Profile users, currently only supported for the \'Teams\' sub-page but edit functionality for other sub pages are planned for development.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Ad Hoc/As Needed',
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfDataId: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
    typeOfDataName: 'Information Resources',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Financial Information',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTPS',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6EC4D848-9604-485C-B55D-186FCEDE0ABD}',
    exchangeName: 'Testing',
    exchangeDescription: 'Test Description',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{58F2F1D4-CA08-4970-9F79-D67B864A015C}',
    fromOwnerName: 'Administrative Qualified Independent Contractor (QIC)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Annually',
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{5CF8BD3E-409F-47e5-A98A-DEFBE6E85386}',
    typeOfDataName: 'Beneficiary Cost Sharing',
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'Encrypted FTP',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C225B978-FF95-44E3-A698-4A18586ACE1C}',
    exchangeName: 'Test 3',
    exchangeDescription: 'TEst',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{FE9123FD-7141-41db-B7CF-B998A473EE5B}',
    fromOwnerName: '2020 (CWF)',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: 'Semi-Annually|Quarterly',
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Mailing|Payment Calculation|Coordination of Benefits',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
    typeOfDataName: 'Beneficiary Benefit Utilization',
    numOfRecords: '< 1000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Critical Infrastructure Information|Financial Information|Law Enforcement',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'HTTP|Encrypted FTP',
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C5116CCD-514A-4184-B361-E41A7EA5B453}',
    exchangeName: 'test',
    exchangeDescription: 'test',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{DEEA654F-7C16-4ed8-B785-A4553E1F8A4C}',
    toOwnerName: '1115 Demos PMDA Contractor (CVP)',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Annually',
    dataExchangeAgreement: 'Information Security Agreement (ISA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: 'Coordination of Benefits',
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfDataId: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
    typeOfDataName: 'Beneficiary Benefit Utilization',
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: '',
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: 'Law Enforcement',
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: 'Other',
    exchangeNetworkProtocolOther: 'gfdgfd',
  },
  {
    exchangeId: '{DB8A7FFE-6AEC-4B4A-8E03-34EFC2942C29}',
    exchangeName: 'Test',
    exchangeDescription: 'descrip',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-14',
    exchangeRetiredDate: '2024-09-14',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{4E10FAFC-9D7A-45ff-9600-DFAB85CEF92D}',
    toOwnerName: '1-800-Medicare Beneficiary Contact Center Operations Contractor',
    toOwnerType: 'Exchange Role',
    connectionFrequency: 'Quarterly',
    dataExchangeAgreement: 'Memorandum of Understanding (MOU)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: '',
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfDataId: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
    typeOfDataName: 'Beneficiary Attribution',
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: '',
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: 'FTP',
    exchangeNetworkProtocolOther: null,
  },
];

export const apiBothExchangeListComp = [
  {
    exchangeId: '{176C18FE-AE2D-4a12-9861-C9A464DD3962}',
    exchangeName: 'IT Project Business Cases',
    exchangeDescription: 'After IT Project Business Cases are submitted via EASi and stored in CEDAR, they are available from CEDAR to view in EASi. All past IT Intakes are available.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{3B22DB27-9697-4bec-B24B-E02CEE8CC6E6}',
    exchangeName: 'ATO List and Data',
    exchangeDescription: 'A list of the current ATOs (FISMA Reportable Systems) is updated in CEDAR with core data about each entry.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{35405DE7-5B79-4d7c-9249-50C763A1E399}',
    fromOwnerName: 'CMS FISMA Controls Tracking System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Weekly',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Mailing',
    ],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '1000 - 100,000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Financial Information',
      'Legal Information',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{43E10A6E-1284-42a1-A270-242246E16D06}',
    exchangeName: 'CEDAR/MINT - To look up CMS users so they can be tagged as collaborators',
    exchangeDescription: 'To look up CMS users so they can be tagged as collaborators',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{2337D9FF-A899-4b4e-A9F4-08A01DE89335}',
    toOwnerName: 'Model INnovation Tool',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
        name: 'Administrative Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{4D5D7A6E-8B8D-4b82-B5B2-6FFDA12023BC}',
    exchangeName: 'CMS Active Contracts List',
    exchangeDescription: 'Each month DEA staff receive via email the list CMS Active Contracts generated by OAGM contractors from CAMS. The list includes basic data about each active contract and identifies the Budget Project IDs funding each. EDA manually imports that data into CEDAR. CLIN data for each contract has been added.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}',
    fromOwnerName: 'Comprehensive Acquisition Management System',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Monthly',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{8D8EA239-DC0C-42db-AE07-B9806D644D68}',
        name: 'Administrative Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'contracts data - CLIN data',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'Other',
    ],
    exchangeNetworkProtocolOther: 'SMTP',
  },
  {
    exchangeId: '{60A13150-C6D6-45b8-B361-E42B4FCD4BC8}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'After IT Project Intake forms have been submitted via EASi UI and stored in CEDAR, they are available from CEDAR to be viewed in EASi. All past IT Intakes are available for viewing in EASi.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: 'Potential pre-contract award information',
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{********-0C6D-4c08-9F74-31DE1194C246}',
    exchangeName: 'Business Architecture and Enterprise Architecture models and reports',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture use CEDAR to develop views, models, reports, and extracts of the Enterprise Architecture data showing dependencies and alignments to support data calls and planning analysis for staff throughout CMS.',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{E2BCDF5F-82E9-4597-BAAB-00E928EC858E}',
    toOwnerName: 'CMS Component',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Real-Time',
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Custom Format',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{0F212C4C-8E87-4690-A6A3-28A8929D44D3}',
    exchangeName: 'CMS Operating Budget Plan',
    exchangeDescription: 'DEA staff each quarter manually download the CMS Operating Budget Plan and import as a reference set the Budget Project ID entries with basic data. No dollar amounts or Funding Source data is loaded. DEA staff manually import this data into CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{DE636697-C148-4ac6-B8CC-429AC7F68ED9}',
    fromOwnerName: 'CMS Component (Office of Financial Management (OFM))',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Quarterly',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{AC33E0B0-CD18-4809-A4D8-98E80C4F16DC}',
        name: 'Financial Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6044E3E5-ACBF-46E3-9575-0BCAEE4CFDF3}',
    exchangeName: 'Testing Data Exchange Very Serious',
    exchangeDescription: 'Yes',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-10-03',
    exchangeRetiredDate: '2024-10-03',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{7B81F587-E83F-4651-8B19-CEEBE63651BE}',
    toOwnerName: 'ACO API',
    toOwnerType: 'System',
    connectionFrequency: [
      'Every Two Weeks',
      'Weekly',
      'Daily',
    ],
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Coordination of Benefits',
      'Premium Calculation',
      'Risk Adjustment',
      'Eligibility and Enrollment',
      'Innovation, Research and Demonstrations',
    ],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
        name: 'Beneficiary Attribution',
      },
    ],
    numOfRecords: '1000 - 100,000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'HTTP',
      'Other',
    ],
    exchangeNetworkProtocolOther: 'sdfdsfdsfds',
  },
  {
    exchangeId: '{9ADCEC6B-E662-4b91-85AC-B4A7626155D5}',
    exchangeName: 'Ad hoc Business Architecture and Enterprise Architecture Information',
    exchangeDescription: 'Staff from the OIT Division of Enterprise Architecture enter details to include in models, extracts, and reports from other parts of CMS.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{976005AD-BB1C-4f7d-8925-FDF2189C103B}',
    fromOwnerName: 'CMS Component (Division of Enterprise Architecture)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'UI Entry',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A35BF3B8-CEFD-4E24-A16E-4E756C53EA3E}',
    exchangeName: 'System Inventory Info',
    exchangeDescription: 'System Inventory Info sent to KMP in the BDC.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{6122F0F3-395E-446f-A78E-65AB3BEA8E64}',
    toOwnerName: 'CMS Baltimore Data Center - EDC4',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'Excel',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A57485B8-8209-4888-BCDD-51F6364A49C0}',
    exchangeName: 'System Profile',
    exchangeDescription: 'EASi shows for each system, core information about CMS Systems: Description, links, ATO expiration, contacts, owner org, business owner, data center hosting, and a few other details. It enables EASi users to edit the contacts and their roles.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    toOwnerName: 'Easy Access to System Information',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{A6B62731-0031-466a-B726-80B979E093D4}',
    exchangeName: 'IT Project Intake',
    exchangeDescription: 'IT Intake form is submitted in EASi by CMS staff to explain their IT Project and stored in CEDAR so that IT Governance staff can review and consider the amount of IT Governance oversight the IT Project needs.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{AB285BEA-E6AA-40bf-B9F8-52015AE6AAD3}',
    exchangeName: 'IT Project Business Case',
    exchangeDescription: 'The Governance Review Team (GRT) upon review of a new IT Project Intake, may determine that a Business Case needs to be completed for the IT Governance of that IT Project. The Business Case collects information to show the alternatives for design of the proposed IT solution. The Business Case form is completed by IT Project leads in EASi which stores the information in CEDAR.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{F051FEE8-7EDD-40C9-BE1A-9EBE2730A107}',
    exchangeName: 'System Profile Edit Capability',
    exchangeDescription: 'Edits requested by EASi System Profile users, currently only supported for the \'Teams\' sub-page but edit functionality for other sub pages are planned for development.',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    fromOwnerName: 'Easy Access to System Information',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Ad Hoc/As Needed',
    ],
    dataExchangeAgreement: 'No',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Receiver',
    typeOfData: [
      {
        id: '{7ECAE820-3553-4eb2-B5D3-6B80DEAB4E45}',
        name: 'Information Resources',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Financial Information',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTPS',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{6EC4D848-9604-485C-B55D-186FCEDE0ABD}',
    exchangeName: 'Testing',
    exchangeDescription: 'Test Description',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{58F2F1D4-CA08-4970-9F79-D67B864A015C}',
    fromOwnerName: 'Administrative Qualified Independent Contractor (QIC)',
    fromOwnerType: 'Exchange Role',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Annually',
    ],
    dataExchangeAgreement: 'Data Use Agreement (DUA)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{5CF8BD3E-409F-47e5-A98A-DEFBE6E85386}',
        name: 'Beneficiary Cost Sharing',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'JSON',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'Encrypted FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C225B978-FF95-44E3-A698-4A18586ACE1C}',
    exchangeName: 'Test 3',
    exchangeDescription: 'TEst',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-13',
    exchangeRetiredDate: '2024-09-13',
    fromOwnerId: '{FE9123FD-7141-41db-B7CF-B998A473EE5B}',
    fromOwnerName: '2020 (CWF)',
    fromOwnerType: 'System',
    toOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    toOwnerName: 'CMS Enterprise Data Analytics Repository',
    toOwnerType: 'System',
    connectionFrequency: [
      'Semi-Annually',
      'Quarterly',
    ],
    dataExchangeAgreement: 'Inter-Agency Agreement (IAA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Mailing',
      'Payment Calculation',
      'Coordination of Benefits',
    ],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
        name: 'Beneficiary Benefit Utilization',
      },
    ],
    numOfRecords: '< 1000',
    dataFormat: 'CSV',
    dataFormatOther: null,
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Critical Infrastructure Information',
      'Financial Information',
      'Law Enforcement',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'HTTP',
      'Encrypted FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
  {
    exchangeId: '{C5116CCD-514A-4184-B361-E41A7EA5B453}',
    exchangeName: 'test',
    exchangeDescription: 'test',
    exchangeVersion: '25',
    exchangeState: 'Active',
    exchangeStartDate: null,
    exchangeEndDate: null,
    exchangeRetiredDate: null,
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{DEEA654F-7C16-4ed8-B785-A4553E1F8A4C}',
    toOwnerName: '1115 Demos PMDA Contractor (CVP)',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Annually',
    ],
    dataExchangeAgreement: 'Information Security Agreement (ISA)',
    containsBeneficiaryAddress: true,
    businessPurposeOfAddress: [
      'Coordination of Benefits',
    ],
    isAddressEditable: null,
    containsPii: true,
    containsPhi: true,
    containsHealthDisparityData: true,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: true,
    apiOwnership: 'Sender',
    typeOfData: [
      {
        id: '{D2A9841F-9793-42db-886C-E74C82598B6B}',
        name: 'Beneficiary Benefit Utilization',
      },
    ],
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: '',
    exchangeContainsCUI: true,
    exchangeCUIDescription: null,
    exchangeCUIType: [
      'Law Enforcement',
    ],
    exchangeConnectionAuthenticated: true,
    exchangeNetworkProtocol: [
      'Other',
    ],
    exchangeNetworkProtocolOther: 'gfdgfd',
  },
  {
    exchangeId: '{DB8A7FFE-6AEC-4B4A-8E03-34EFC2942C29}',
    exchangeName: 'Test',
    exchangeDescription: 'descrip',
    exchangeVersion: '25',
    exchangeState: 'Retired',
    exchangeStartDate: null,
    exchangeEndDate: '2024-09-14',
    exchangeRetiredDate: '2024-09-14',
    fromOwnerId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    fromOwnerName: 'CMS Enterprise Data Analytics Repository',
    fromOwnerType: 'System',
    toOwnerId: '{4E10FAFC-9D7A-45ff-9600-DFAB85CEF92D}',
    toOwnerName: '1-800-Medicare Beneficiary Contact Center Operations Contractor',
    toOwnerType: 'Exchange Role',
    connectionFrequency: [
      'Quarterly',
    ],
    dataExchangeAgreement: 'Memorandum of Understanding (MOU)',
    containsBeneficiaryAddress: false,
    businessPurposeOfAddress: [],
    isAddressEditable: null,
    containsPii: false,
    containsPhi: false,
    containsHealthDisparityData: false,
    containsBankingData: null,
    isBeneficiaryMailingFile: null,

    sharedViaApi: false,
    apiOwnership: null,
    typeOfData: [
      {
        id: '{138A982A-83C4-44cc-97EC-8712E48865F0}',
        name: 'Beneficiary Attribution',
      },
    ],
    numOfRecords: 'Not Known',
    dataFormat: 'XML',
    dataFormatOther: null,
    exchangeContainsCUI: false,
    exchangeCUIDescription: null,
    exchangeCUIType: [],
    exchangeConnectionAuthenticated: false,
    exchangeNetworkProtocol: [
      'FTP',
    ],
    exchangeNetworkProtocolOther: null,
  },
];
