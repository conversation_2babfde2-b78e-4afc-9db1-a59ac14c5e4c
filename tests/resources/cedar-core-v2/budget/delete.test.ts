import request from 'supertest';
import handler from '../../../../src/resources/cedar-core-v2/budget/delete.js';
import { BudgetDeleteMessages } from '../../../../src/utils/budget/index.js';
import { createUnitTestApp, testLogger } from '../../../test-utils.js';
import { CMSApp } from '../../../../src/types/index.js';

const mocks = vi.hoisted(() => ({
  deleteQuery: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    // Mock minimal required properties for endpointSetup to succeed
    closeConnection: vi.fn().mockResolvedValue(true),
  })),
  consoleError: vi.fn(),
  // Add wmDelete mock for shared test framework compatibility
  wmDelete: vi.fn(),
}));

vi.mock(import('../../../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    deleteQuery: mocks.deleteQuery,
  };
});

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

let app: CMSApp;

const mockId1 = '11111111-2222-3333-4444-555555555555';
const mockId2 = '22222222-3333-4444-5555-666666666666';
const mockId3 = '33333333-4444-5555-6666-777777777777';

const getDefaultRequest = async (ids: string | string[] = [mockId1]) => request(app)
  .delete('/')
  .query({ id: ids })
  .set('x-jwt-key', 'pass');

describe('Census Budget Delete Handler', () => {
  beforeEach(async () => {
    const routeConfig = () => ({
      path: '/',
      resourceConfig: [{
        name: 'delete-budget',
        path: '/',
        method: 'delete' as const,
        resource: handler,
      }],
    });
    app = await createUnitTestApp(routeConfig);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('sends 200 when deleting a single budget item', async () => {
    mocks.deleteQuery.mockResolvedValueOnce('success');

    const response = await getDefaultRequest(mockId1);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['1 object(s) successfully deleted'],
    });
    expect(mocks.deleteQuery).toHaveBeenCalledWith(
      expect.anything(),
      `cn_${mockId1}`,
    );
  });

  it('sends 200 when deleting multiple budget items', async () => {
    mocks.deleteQuery
      .mockResolvedValueOnce('success')
      .mockResolvedValueOnce('success')
      .mockResolvedValueOnce('success');

    const response = await getDefaultRequest([mockId1, mockId2, mockId3]);

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['3 object(s) successfully deleted'],
    });
    expect(mocks.deleteQuery).toHaveBeenCalledTimes(3);
    expect(mocks.deleteQuery).toHaveBeenCalledWith(expect.anything(), `cn_${mockId1}`);
    expect(mocks.deleteQuery).toHaveBeenCalledWith(expect.anything(), `cn_${mockId2}`);
    expect(mocks.deleteQuery).toHaveBeenCalledWith(expect.anything(), `cn_${mockId3}`);
  });

  it('sends 400 when all budget items fail to delete', async () => {
    mocks.deleteQuery
      .mockRejectedValueOnce(new Error('Sparx Error'))
      .mockRejectedValueOnce(new Error('Sparx Error'));

    const response = await getDefaultRequest([mockId1, mockId2]);

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_all_failed],
    });
    expect(mocks.deleteQuery).toHaveBeenCalledTimes(2);
  });

  it('sends 400 when some budget items fail to delete (partial success/failure)', async () => {
    mocks.deleteQuery
      .mockResolvedValueOnce('success')
      .mockRejectedValueOnce(new Error('Sparx Error'));

    const response = await getDefaultRequest([mockId1, mockId2]);

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_partial_deletion],
    });
    expect(mocks.deleteQuery).toHaveBeenCalledTimes(2);
  });

  it('sends 400 when no ids are provided', async () => {
    const response = await getDefaultRequest('');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_ids_missing_or_empty],
    });
  });

  it('sends 400 when an empty array of ids is provided', async () => {
    const response = await getDefaultRequest([]);

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_ids_missing_or_empty],
    });
  });

  it('sends 400 when ids are not strings', async () => {
    // Test with empty string values which will fail the isString && !isEmpty check
    const response = await request(app)
      .delete('/')
      .query({ id: ['', '   '] }) // Empty and whitespace-only strings
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_ids_not_string],
    });
  });

  it('sends 400 when an unhandled exception occurs during deletion attempts', async () => {
    mocks.deleteQuery
      .mockImplementationOnce(() => {
        throw new Error('Simulated crash');
      })
      .mockImplementationOnce(() => {
        throw new Error('Simulated crash');
      });

    const response = await getDefaultRequest([mockId1, mockId2]);

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: [BudgetDeleteMessages.error_all_failed],
    });
    expect(mocks.deleteQuery).toHaveBeenCalledTimes(2);
  });

  describe('Express Server', () => {
    // Note: Budget delete handler uses SparX subsystem instead of direct database operations,
    // so we only include the shared tests that are applicable to this handler architecture.

    beforeEach(() => {
      // Mock console.error for testing a bad app situation.
      vi.spyOn(console, 'error').mockImplementation(mocks.consoleError);
    });

    it('sends 500 when systemApp is not in the request', async () => {
      const { createBadApp } = await import('../../../test-utils.js');
      const badApp = createBadApp('/', 'delete', handler);
      const response = await request(badApp).delete('/').set('x-jwt-key', 'pass');

      expect(response.status).toEqual(500);
      expect(response.body).toEqual({
        result: 'error',
        message: ['Unable to get the application from request'],
      });

      // Verify that console.error was called as fallback when req.systemApp.logger doesn't exist.
      expect(mocks.consoleError).toHaveBeenCalled();

      // Check the contents of the logged error message
      const consoleErrorCall = mocks.consoleError.mock.calls[0];
      expect(consoleErrorCall).toHaveLength(1);
      expect(consoleErrorCall[0]).toBeDefined();
      expect(consoleErrorCall[0]).toBeInstanceOf(Error);
      expect(consoleErrorCall[0].message).toBe('Unable to get the application from request');
    });

    it('sends 500 when getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

      const response = await getDefaultRequest();

      expect(response.status).toEqual(500);
      expect(response.body).toEqual({
        result: 'error',
        message: ['Database Unavailable'],
      });

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);

      const error = errorLog?.[0];
      expect(error).toBeDefined();
      expect(error?.error).toBeDefined();
      expect(error.error).toBeInstanceOf(Error);
      if (error?.error instanceof Error) {
        expect(error.error.message).toBeDefined();
        expect(error.error.message).toContain('Database Unavailable');
      }
    });

    it('sends 500 when an unhandled endpoint exception occurs', async () => {
      const brokenHandler = (): void => {
        throw new Error('Unexpected error');
      };

      const brokenResourceConfig = () => ({
        path: '/',
        resourceConfig: [{
          name: 'test',
          path: '/',
          method: 'delete' as const,
          resource: brokenHandler,
        }],
      });

      const testApp = await createUnitTestApp(brokenResourceConfig);
      const response = await request(testApp).delete('/').set('x-jwt-key', 'pass').query({
        id: '{11111111-2222-3333-4444-555555555555}',
      });

      expect(response.body).toEqual({
        result: 'error',
        message: ['Internal Server Error'],
      });
      expect(response.status).toEqual(500);
    });
  });
});
