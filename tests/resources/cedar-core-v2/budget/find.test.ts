import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import findModule from '../../../../src/resources/cedar-core-v2/budget/find';
import budgetConfig from '../../../../src/resources/cedar-core-v2/budget';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import {
  oidsAPIResponse,
  allBudgetAPIResponse,
  projectIdTitleAPIResponse,
  systemIdAPIResponse,
} from './findTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  budgetFindUtil: vi.fn(),
  findGlobalVariable: vi.fn().mockResolvedValue({ globalValue: '2022' }),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    findGlobalVariable: mocks.findGlobalVariable,
  };
});

vi.mock(import('../../../../src/utils/budget'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    budgetFindUtil: mocks.budgetFindUtil,
  };
});

const app = await createApp();
app.resources.register(app, '/', budgetConfig);
app.resources.initResources(app);

describe('Find budget tests', () => {
  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', findModule);
    const response = await request(badApp)
      .get('/')
      .query({
        projectId: 'bad app id',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/')
      .query({
        projectId: 'bad app id',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 500 and an error message when onlyIds is false and findData returns an error', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce(new Error('Find Data Error'));
    const response = await request(app)
      .get('/')
      .query({
        projectId: '000004',
      })
      .set('x-jwt-key', 'pass');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Find Data Error');
    expect(response.status).toEqual(500);
    expect(response.body.error).toEqual('There was an issue fetching the budgets');
  });

  it('Should return a 200 with a result when given a project id when onlyIds is false or null ', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: 1,
      Budgets: projectIdTitleAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        projectId: '000004',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Budgets: projectIdTitleAPIResponse,
    });
  });

  it('Should return a 200 with a result when given a project title when onlyIds is false or null', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: 1,
      Budgets: projectIdTitleAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Budgets: projectIdTitleAPIResponse,
    });
  });

  it('Should return a 200 with a result when given a system id when onlyIds is false or null', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: 1,
      Budgets: systemIdAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Budgets: systemIdAPIResponse,
    });
  });

  it('Should return a 200 with a list of all budgets when onlyIds is false or null and there is no query', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: 3,
      Budgets: allBudgetAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 3,
      Budgets: allBudgetAPIResponse,
    });
  });

  it('Should return a 200 with a result when given just a project id and onlyIds is true', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: '1',
      Budgets: oidsAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        onlyIds: true,
        projectId: '000004',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: '1',
      Budgets: oidsAPIResponse,
    });
  });

  it('Should return a 200 with a result when given just a project title and onlyIds is true', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: '1',
      Budgets: oidsAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        onlyIds: true,
        projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: '1',
      Budgets: oidsAPIResponse,
    });
  });

  it('Should return a 200 with a result when given both a project title and project id and onlyIds is true', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: '1',
      Budgets: oidsAPIResponse,
    });
    const response = await request(app)
      .get('/')
      .query({
        onlyIds: true,
        projectId: '000004',
        projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: '1',
      Budgets: oidsAPIResponse,
    });
  });

  it('Should return a 200 with just a zero count when given no query and onlyIds is true', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({
      count: '0',
    });
    const response = await request(app)
      .get('/')
      .query({
        onlyIds: true,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: '0',
    });
  });

  it('Should return a 500 with an error when onlyIds is true and budgetFindUtil returns an error', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce(new Error('Find Data Ids Only Error'));
    const response = await request(app)
      .get('/')
      .query({
        onlyIds: true,
        projectId: '000004',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body.error).toEqual('There was an issue fetching the budgets');

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Find Data Ids Only Error');
  });
});
