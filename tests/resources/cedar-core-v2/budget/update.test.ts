import request from 'supertest';
import { get } from 'lodash';
import {
  afterEach, beforeEach, describe, expect, it, vi,
} from 'vitest';

import handler, { BudgetUpdateMessages } from '../../../../src/resources/cedar-core-v2/budget/update';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import { CMSApp } from '../../../../src/types';
import createFailureTests from '../../../shared/endpoints';

const mocks = vi.hoisted(() => ({
  queryStoredProceduresTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProceduresTyped: mocks.queryStoredProceduresTyped,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

let app: CMSApp;

const getValidUpdateRequest = () => ({
  Budgets: [
    {
      FiscalYear: '2024',
      FundingSource: 'Internal',
      id: '{11111111-2222-3333-4444-555555555555}',
      Name: 'Test Budget 1',
      projectId: '{66666666-7777-8888-9999-000000000000}',
      systemId: '{11111111-2222-3333-4444-555555555555}',
      projectTitle: 'Test Project 1',
      fundingId: '{12345678-ABCD-EFGH-1234-567890ABCDEF}',
      funding: '1000000',
    },
    {
      FiscalYear: '2025',
      FundingSource: 'External',
      id: '{55555555-4444-3333-2222-111111111111}',
      Name: 'Test Budget 2',
      projectId: '{77777777-8888-9999-0000-111111111111}',
      systemId: '{11111111-2222-3333-4444-555555555555}',
      projectTitle: 'Test Project 2',
      fundingId: '{FEDCBA98-7654-3210-FEDC-BA9876543210}',
      funding: '2500000',
    },
  ],
});

const defaultRequest = (override = {}) => {
  const valid = getValidUpdateRequest();
  const body = {
    ...valid,
    ...override,
  };

  return request(app)
    .put('/')
    .send(body)
    .set('x-jwt-key', 'pass');
};

describe('Census Budget Update Handler', () => {
  beforeEach(async () => {
    // Create a wrapper to convert GetResourceConfig to GetResourceConfigSub
    const resourceConfigSub = () => ({
      path: '/',
      resourceConfig: [
        {
          name: 'put-budget-update',
          path: '/',
          method: 'put' as const,
          resource: handler,
        },
      ],
    });

    app = await createUnitTestApp(resourceConfigSub);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('returns 200 with valid request body', async () => {
    mocks.queryStoredProceduresTyped.mockResolvedValueOnce([
      { queryStatus: 0 },
    ]);

    const response = await defaultRequest();

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Successfully updated 2 budget(s).'],
    });

    const expectedJsonInput = JSON.stringify({
      CurrentProfile: 'API User',
      Objects: [
        {
          RefStr: '{12345678-ABCD-EFGH-1234-567890ABCDEF}',
          ClassName: 'ProjectArch',
          Id: '1',
          Values: { cms_funding: '1000000' },
        },
        {
          RefStr: '{FEDCBA98-7654-3210-FEDC-BA9876543210}',
          ClassName: 'ProjectArch',
          Id: '2',
          Values: { cms_funding: '2500000' },
        },
      ],
    });

    expect(mocks.queryStoredProceduresTyped).toHaveBeenCalledWith(
      'SP_Update_SystemBudget_json',
      [
        { name: 'jsonInput', type: 'nvarchar', param: 'max' },
      ],
      [
        { name: 'jsonInput', value: expectedJsonInput },
      ],
      [],
    );
  });

  it('returns 400 when Budgets array is missing', async () => {
    const response = await defaultRequest({ Budgets: undefined });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [BudgetUpdateMessages.error_budgets_missing],
    });
  });

  it('returns 400 when Budgets array is empty', async () => {
    const response = await defaultRequest({ Budgets: [] });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [BudgetUpdateMessages.error_budgets_missing],
    });
  });

  it('returns 400 when fundingId is missing for a budget', async () => {
    const invalidRequest = {
      Budgets: [
        {
          ...getValidUpdateRequest().Budgets[0],
          fundingId: undefined,
        },
      ],
    };

    const response = await defaultRequest(invalidRequest);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [BudgetUpdateMessages.error_funding_id_missing],
    });
  });

  it('returns 500 when database stored procedure call fails', async () => {
    mocks.queryStoredProceduresTyped.mockRejectedValueOnce(new Error('SP call failed'));

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0].error')).toBeInstanceOf(Error);
    expect(get(errorLog, '[0].error.message')).toEqual('SP call failed');
  });

  it('returns 500 when stored procedure returns non-zero status', async () => {
    mocks.queryStoredProceduresTyped.mockResolvedValueOnce([
      { queryStatus: 1 }, // Non-zero status indicates error in WM flow
    ]);

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [BudgetUpdateMessages.error_unexpected_sp_status],
    });
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
      [
        { [Messages.db_query_stored_procedure_error]: mocks.queryStoredProceduresTyped },
      ],
    ),
  );
});
