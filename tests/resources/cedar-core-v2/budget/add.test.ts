import {
  afterEach, beforeEach, describe, expect, it, vi,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import budgetAddModule from '../../../../src/resources/cedar-core-v2/budget/add';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import { SP_INSERT_SYSTEMBUDGET_JSON } from '../../../../src/utils/db-helpers';

// Setup mocks.
const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// Create the app for testing.
// A mock resource config for budget needs to be available if `budgetConfig` is imported.
// For simplicity, directly register the handler if `budgetConfig` is just a placeholder.
const app = await createApp();
// Assuming budgetConfig provides a resourceConfig similar to note.
// If budget has its own endpoint setup (e.g., in `src/resources/census-core-v2/budget/index.ts`),
// we'd import and use it. For this example, directly registering the `budgetAdd` handler.
app.resources.register(app, '/', (() => () => [{
  path: '/',
  method: 'post',
  resource: budgetAddModule,
  name: 'budgetAdd',
}])())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

describe('Budget Add tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const getValidBudgetRequest = () => ({
    Budgets: [{
      projectId: '{11111111-2222-3333-4444-555555555555}',
      systemId: '{22222222-3333-4444-5555-666666666666}',
      FiscalYear: '2024',
      FundingSource: 'Federal',
      funding: 'Operational Budget',
      projectTitle: 'Annual System Maintenance',
    }],
  });

  it('Should return a 200 with success message when budget is added successfully', async () => {
    // Mock a successful stored procedure call (Webmethods expects RETURN_VALUE of 0 for success)
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Successfully added 1 budget(s).'],
    });

    // Verify the stored procedure was called with correct arguments
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      SP_INSERT_SYSTEMBUDGET_JSON,
      [
        { name: 'RETURN_VALUE', type: 'int' },
      ],
      [
        {
          name: 'jsonInput',
          value: JSON.stringify({
            CurrentProfile: 'API User',
            Objects: [{
              ClassName: 'ProjectArch',
              Id: '1', // The first budget in the array gets ID '1'
              Values: { cms_funding: 'Operational Budget' },
            }],
            Relations: [
              {
                FromId: '1',
                Property: 'project',
                ToRef: '{11111111-2222-3333-4444-555555555555}',
              },
              {
                FromId: '1',
                Property: 'object',
                ToRef: '{22222222-3333-4444-5555-666666666666}',
              },
            ],
          }),
        },
      ],
      [
        { resultKey: 'queryStatus', paramName: 'RETURN_VALUE', wrapAsParam: true },
      ],
    );
  });

  it('Should handle multiple budgets in a single request', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

    const multiBudgetRequest = {
      Budgets: [
        {
          projectId: '{11111111-2222-3333-4444-555555555555}',
          systemId: '{22222222-3333-4444-5555-666666666666}',
          FiscalYear: '2024',
          projectTitle: 'Project A',
          funding: 'Budget A',
        },
        {
          projectId: '{00000000-1111-2222-3333-444444444444}',
          systemId: '{55555555-6666-7777-8888-999999999999}',
          FiscalYear: '2025',
          projectTitle: 'Project B',
          funding: 'Budget B',
        },
      ],
    };

    const response = await request(app)
      .post('/')
      .send(multiBudgetRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Successfully added 2 budget(s).'],
    });

    const expectedJsonInput = JSON.stringify({
      CurrentProfile: 'API User',
      Objects: [
        { ClassName: 'ProjectArch', Id: '1', Values: { cms_funding: 'Budget A' } },
        { ClassName: 'ProjectArch', Id: '2', Values: { cms_funding: 'Budget B' } },
      ],
      Relations: [
        { FromId: '1', Property: 'project', ToRef: '{11111111-2222-3333-4444-555555555555}' },
        { FromId: '1', Property: 'object', ToRef: '{22222222-3333-4444-5555-666666666666}' },
        { FromId: '2', Property: 'project', ToRef: '{00000000-1111-2222-3333-444444444444}' },
        { FromId: '2', Property: 'object', ToRef: '{55555555-6666-7777-8888-999999999999}' },
      ],
    });

    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      SP_INSERT_SYSTEMBUDGET_JSON,
      expect.any(Array),
      [{ name: 'jsonInput', value: expectedJsonInput }],
      expect.any(Array),
    );
  });

  it('Should return a 400 when Budgets array is missing', async () => {
    const response = await request(app)
      .post('/')
      .send({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide a list of budgets.'],
    });
  });

  it('Should return a 400 when Budgets array is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({ Budgets: [] })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide a list of budgets.'],
    });
  });

  it('Should return a 400 when projectId is missing for a budget', async () => {
    const invalidRequest = {
      Budgets: [{
        FiscalYear: '2024',
        FundingSource: 'Federal',
        funding: 'Operational Budget',
        projectTitle: 'Annual System Maintenance',
        // projectId is missing
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `projectId` for a budget.'],
    });
  });

  it('Should return a 400 when projectId is not a string for a budget', async () => {
    const invalidRequest = {
      Budgets: [{
        projectId: 12345, // Invalid: should be string
        FiscalYear: '2024',
        FundingSource: 'Federal',
        funding: 'Operational Budget',
        projectTitle: 'Annual System Maintenance',
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `projectId` for a budget.'],
    });
  });

  it('Should return a 400 when one budget in multiple budgets has invalid projectId', async () => {
    const invalidMultiBudgetRequest = {
      Budgets: [
        {
          projectId: '{11111111-2222-3333-4444-555555555555}', // Valid
          systemId: '{22222222-3333-4444-5555-666666666666}',
          FiscalYear: '2024',
          projectTitle: 'Project A',
          funding: 'Budget A',
        },
        {
          // projectId is missing for this budget
          systemId: '{55555555-6666-7777-8888-999999999999}',
          FiscalYear: '2025',
          projectTitle: 'Project B',
          funding: 'Budget B',
        },
      ],
    };

    const response = await request(app)
      .post('/')
      .send(invalidMultiBudgetRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `projectId` for a budget.'],
    });

    // Ensure stored procedure was not called due to early validation
    expect(mocks.queryStoredProcedures).not.toHaveBeenCalled();
  });

  it('Should return a 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'post', budgetAddModule);
    const response = await request(badApp)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.app_invalid] });
  });

  it('Should return a 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error(Messages.db_unavailable));
    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.db_unavailable] });
  });

  it('Should return a 500 when stored procedure execution fails (rejected promise)', async () => {
    mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('SP execution failed'));

    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP execution failed');
  });

  it('Should return a 500 when stored procedure execution returns an error object', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('SP returned error'));

    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP returned error');
  });

  it('Should return a 500 when stored procedure returns non-zero status', async () => {
    mocks.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 1 }]]); // Non-zero status

    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Failed to add budget(s).'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).message).toEqual('Stored procedure returned non-zero status.');
  });

  it('Should return a 500 when an unhandled exception occurs', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => {
      throw new Error('Unexpected error');
    });

    const response = await request(app)
      .post('/')
      .send(getValidBudgetRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Internal server error'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = get(errorLog, '[0]', []);
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
    expect(get(error, 'error.message')).toEqual('Unexpected error');
  });
});
