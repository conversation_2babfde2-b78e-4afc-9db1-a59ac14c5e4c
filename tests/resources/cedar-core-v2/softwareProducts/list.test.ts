import request from 'supertest';
import { get } from 'lodash';
import { CMSApp } from 'src/types';
import errorHandler from 'src/outerceptors/error-handler';
import listConfig from 'src/resources/cedar-core-v2/softwareProducts';
import listModule from 'src/resources/cedar-core-v2/softwareProducts/list';
import { createApp, createBadApp, testLogger } from 'tests/test-utils';
import {
  successfulQuery,
} from 'tests/utils/softwareProductsTestData';

const mocks = vi.hoisted(() => ({
  consoleError: vi.fn(),
  queryViewTypedFixed: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTypedFixed: mocks.queryViewTypedFixed,
  })),
  getSoftwareProductsList: vi.fn(),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/softwareProducts'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSoftwareProductsList: mocks.getSoftwareProductsList,
  };
});

let app: CMSApp;

describe('Cedar Software Products List tests', () => {
  beforeEach(async () => {
    app = await createApp();
    await app.resources.register(app, '/', listConfig);
    app.resources.initResources(app);
    app.use(errorHandler);

    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(mocks.consoleError);
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 with an error if id param is empty', async () => {
    const response = await request(app).get('/')
      .query({
        id: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'id\'',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Please provide required parameters \'id\'');
  });

  it('Should return a 200 with an empty object if id param is not a valid UUID', async () => {
    const response = await request(app).get('/')
      .query({
        id: '42fdsfds',
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({});
    expect(response.status).toEqual(200);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).toBeUndefined();
  });

  it('Should return a 500 with an error if getSoftwareProductsList returns an error', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(new Error('Error fetching software products list'));
    const response = await request(app).get('/')
      .query({
        id: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error fetching software products list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Error fetching software products list');
  });

  it('Should return a 200 with an object consisting system, software, and category data', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(successfulQuery);
    const response = await request(app).get('/')
      .query({
        id: '{11111111-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(successfulQuery);
  });
});
