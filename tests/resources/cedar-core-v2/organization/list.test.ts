import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import {
  dbOrganizationsClassTest,
  dbFullList,
  apiFullList,
  apiIdList,
  apiNameList,
  apiAcronymList,
  childOne,
  childTwo,
  childThree,
  childFour,
} from './organizationListTestData';
import organizationConfig from '../../../../src/resources/cedar-core-v2/organization';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import listModule, {
  buildOrganizationTree,
  buildOrganizationSubtree,
  convertOrg,
  processOrganizationList,
  processFilteredOrganizationList,
  OrganizationData,
  Org,
} from '../../../../src/resources/cedar-core-v2/organization/list';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', organizationConfig);
app.resources.initResources(app);

describe('Organization Class tests', () => {
  // it('Should convert an organization data object to an Org object', () => {
  //   const orgData: Org = dbOrganizationsClassTest[0];
  //   const org = convertOrg(orgData);
  //   expect(org).not.toBeNull();
  //   expect(org.id).toEqual('{11111111-2222-3333-4444-555555555555}');
  //   expect(org.name).toEqual('Root');
  //   expect(org.acronym).toEqual('R');
  //   expect(org.description).toEqual('Root');
  //   expect(org.component).toEqual('Root');
  //   expect(org.fullPath).toEqual('Root');
  //   expect(org.parentId).toEqual('');
  // });

  it('Should have empty strings as properties when no data is provided', () => {
    const org = new Org();
    expect(org).not.toBeNull();
    expect(org.id).toEqual('');
    expect(org.name).toEqual('');
    expect(org.acronym).toEqual('');
    expect(org.description).toEqual('');
    expect(org.component).toEqual('');
    expect(org.fullPath).toEqual('');
    expect(org.parentId).toEqual('');
  });

  it('Should set the properties when data is provided', () => {
    const org = new Org({
      id: '{44444444-5555-4444-5555-666666666666}',
      name: 'Start Property',
      acronym: 'SP',
      description: 'It is a fake org',
      component: 'Property',
      fullPath: 'Root | Start Property',
      parentId: '{11111111-1111-1111-1111-111111111111}',
    });

    org.setData({
      id: '{22222222-5555-2222-5555-666666666666}',
      name: 'End Property',
      acronym: 'EP',
      description: 'It is another fake org',
      component: 'Property',
      fullPath: 'Root | End Property',
      parentId: '{22222222-2222-2222-2222-222222222222}',
    });

    expect(org).not.toBeNull();
    expect(org.id).toEqual('{22222222-5555-2222-5555-666666666666}');
    expect(org.name).toEqual('End Property');
    expect(org.acronym).toEqual('EP');
    expect(org.description).toEqual('It is another fake org');
    expect(org.component).toEqual('Property');
    expect(org.fullPath).toEqual('Root | End Property');
    expect(org.parentId).toEqual('{22222222-2222-2222-2222-222222222222}');
  });
  
  it('Should add a child to the organization', () => {
    const org = new Org({
      id: '{44444444-5555-4444-5555-666666666666}',
      name: 'Start Organization',
      acronym: 'SO',
      description: 'It is a fake org',
      component: 'Organization',
      fullPath: 'Root | Start Organization',
      parentId: '{11111111-1111-1111-1111-111111111111}',
    });

    const child = new Org({
      id: '{55555555-5555-5555-5555-666666666666}',
      name: 'Child Organization',
      acronym: 'CO',
      description: 'It is another fake org',
      component: 'Organization',
      fullPath: 'Root | Start Organization | Child Organization',
      parentId: '{44444444-5555-4444-5555-666666666666}',
    });

    org.addChild(child);
    expect(org.Organizations).toHaveLength(1);
    expect(org.Organizations).toEqual([child]);
  });

  it('Should get the children of the organization if there are any children', () => {
    const org = new Org({
      id: '{44444444-5555-4444-5555-666666666666}',
      name: 'Start Organization',
      acronym: 'SO',
      description: 'It is a fake org',
      component: 'Organization',
      fullPath: 'Root | Start Organization',
      parentId: '{11111111-1111-1111-1111-111111111111}',
    });
    org.addChild(childOne);
    org.addChild(childTwo);
    org.addChild(childThree);
    org.addChild(childFour);

    expect(org.getChildren()).toHaveLength(4);
    expect(org.getChildren()).toEqual([
      childOne,
      childTwo,
      childThree,
      childFour,
    ]);
  });

  it('Should return an empty array when calling getChildren if there are no children', () => {
    const org = new Org({
      id: '{44444444-5555-4444-5555-666666666666}',
      name: 'Start Organization',
      acronym: 'SO',
      description: 'It is a fake org',
      component: 'Organization',
      fullPath: 'Root | Start Organization',
      parentId: '{11111111-1111-1111-1111-111111111111}',
    });

    expect(org.getChildren()).toHaveLength(0);
    expect(org.getChildren()).toEqual([]);
  });

  it('Should build a tree from a list of organizations', () => {
    const orgList: OrganizationData[] = dbOrganizationsClassTest;
    const orgTree = buildOrganizationTree(orgList) as Org;
    expect(orgTree).not.toBeNull();
    expect(orgTree.id).toEqual('{11111111-2222-3333-4444-555555555555}');
    expect(orgTree.name).toEqual('Root');
    expect(orgTree.Organizations).toHaveLength(1);
  });
});

describe('buildOrganizationTree tests', () => {
  it('Should build a tree from a list of organizations', () => {
    const orgList: OrganizationData[] = dbOrganizationsClassTest;
    const orgTree = buildOrganizationTree(orgList) as Org;
    expect(orgTree).not.toBeNull();
    expect(orgTree.id).toEqual('{11111111-2222-3333-4444-555555555555}');
    expect(orgTree.name).toEqual('Root');
    expect(orgTree.Organizations).toHaveLength(1);
  });
  it('Should return null if there are no organizations', () => {
    const orgList: OrganizationData[] = [];
    const orgTree = buildOrganizationTree(orgList);
    expect(orgTree).toBeNull();
  });
  it('Should return null if there is no root organization', () => {
    const orgList: OrganizationData[] = [
      {
        id: '{22222222-3333-4444-5555-666666666666}',
        name: 'Child',
        acronym: 'C',
        description: 'Child',
        component: 'Child',
        fullPath: 'Root | Child',
        parentId: '{11111111-2222-3333-4444-555555555555}',
      },
    ];
    const orgTree = buildOrganizationTree(orgList);
    expect(orgTree).toBeNull();
  });

  it('Should create a new org and add it to the hash map if the organization is not already there', () => {
    const orgList: OrganizationData[] = [
      {
        id: '{22222222-3333-4444-5555-666666666666}',
        name: 'Child',
        acronym: 'C',
        description: 'Child',
        component: 'Child',
        fullPath: 'Root | Child',
        parentId: '{11111111-2222-3333-4444-555555555555}',
      },
    ];
    const orgTree = buildOrganizationTree(orgList) as Org;
    expect(orgTree).not.toBeNull();
    expect(orgTree.id).toEqual('{11111111-2222-3333-4444-555555555555}');
    expect(orgTree.name).toEqual('Root');
    expect(orgTree.Organizations).toHaveLength(1);
  });
});

describe('buildOrganizationSubtree tests', () => {
  it('Should build a subtree from a list of organizations', () => {
    const orgList: OrganizationData[] = dbOrganizationsClassTest;
    const orgTree = buildOrganizationSubtree(orgList, '{11111111-2222-3333-4444-555555555555}') as Org;
    expect(orgTree).not.toBeNull();
    expect(orgTree.id).toEqual('{11111111-2222-3333-4444-555555555555}');
    expect(orgTree.name).toEqual('Root');
    expect(orgTree.Organizations).toHaveLength(1);
  });
});

describe('Organization List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 500 and an error if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve organizations',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
  });

  it('Should return a 500 and an error if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve organizations',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
  });

  it('Should return a 200 with a list of all organizations given no query', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiFullList);
  });

  it('Should return a 200 with an array of an organization and its children given an id', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const response = await request(app)
      .get('/')
      .query({
        id: '{22222222-4444-4444-5555-666666666666}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiIdList);
  });

  it('Should return a 200 with an array of an organization and its children given a name', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const response = await request(app)
      .get('/')
      .query({
        name: 'Child Three',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiNameList);
  });

  it('Should return a 200 with an array of an organization and its children given an acronym', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const response = await request(app)
      .get('/')
      .query({
        acronym: 'CT',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiAcronymList);
  });
});
