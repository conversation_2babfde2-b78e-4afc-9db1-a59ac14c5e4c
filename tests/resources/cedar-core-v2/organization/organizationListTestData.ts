import { Org } from 'src/resources/cedar-core-v2/organization/list';

export const dbOrganizationsClassTest = [
  {
    id: '{11111111-2222-3333-4444-555555555555}',
    name: 'Root',
    acronym: 'R',
    description: 'Root',
    component: 'Root',
    fullPath: 'Root',
    parentId: undefined,
  },
  {
    id: '{22222222-3333-4444-5555-666666666666}',
    name: 'Child',
    acronym: 'C',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | Child',
    parentId: '{11111111-2222-3333-4444-555555555555}',
  },
];

export const dbFullList = [
  {
    id: '{11111111-2222-3333-4444-555555555555}',
    name: 'Root',
    acronym: 'R',
    description: 'Root',
    component: 'Root',
    fullPath: 'Root',
    parentId: undefined,
  }, {
    id: '{22222222-3333-4444-5555-666666666666}',
    name: '<PERSON>One',
    acronym: 'CT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildOne',
    parentId: '{11111111-2222-3333-4444-555555555555}',
  }, {
    id: '{22222222-4444-4444-5555-666666666666}',
    name: 'Child Two',
    acronym: 'CTT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildTwo',
    parentId: '{11111111-2222-3333-4444-555555555555}',
  }, {
    id: '{22222222-5555-4444-5555-666666666666}',
    name: 'Child Three',
    acronym: 'CTTT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildThree',
    parentId: '{11111111-2222-3333-4444-555555555555}',
  }, {
    id: '{33333333-3333-4444-5555-666666666666}',
    name: 'GrandChildOne',
    acronym: 'GCO',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Three | GrandChildOne',
    parentId: '{22222222-5555-4444-5555-666666666666}',
  }, {
    id: '{33333333-4444-4444-5555-666666666666}',
    name: 'GrandChild Two',
    acronym: 'CTT',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Three | GrandChildTwo',
    parentId: '{22222222-5555-4444-5555-666666666666}',
  }, {
    id: '{33333333-5555-4444-5555-666666666666}',
    name: 'GrandChild Three',
    acronym: 'CTTT',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Three | GrandChildThree',
    parentId: '{22222222-5555-4444-5555-666666666666}',
  }, {
    id: '{44444444-3333-4444-5555-666666666666}',
    name: 'Other GrandChild One',
    acronym: 'OGCO',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Two | Other GrandChild One',
    parentId: '{22222222-4444-4444-5555-666666666666}',
  }, {
    id: '{44444444-4444-4444-5555-666666666666}',
    name: 'Other GrandChild Two',
    acronym: 'CTT',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Two | Other GrandChild Two',
    parentId: '{22222222-4444-4444-5555-666666666666}',
  }, {
    id: '{44444444-5555-4444-5555-666666666666}',
    name: 'Other GrandChild Three',
    acronym: 'CTTT',
    description: 'GrandChild',
    component: 'GrandChild',
    fullPath: 'Root | Child Two | Other GrandChild Three',
    parentId: '{22222222-4444-4444-5555-666666666666}',
  },
];

export const apiFullList = {
  count: 10,
  Organizations: {
    id: '{11111111-2222-3333-4444-555555555555}',
    name: 'Root',
    acronym: 'R',
    description: 'Root',
    component: 'Root',
    fullPath: 'Root',
    parentId: '',
    Organizations: [
      {
        id: '{22222222-3333-4444-5555-666666666666}',
        name: 'ChildOne',
        acronym: 'CT',
        description: 'Child',
        component: 'Child',
        fullPath: 'Root | ChildOne',
        parentId: '{11111111-2222-3333-4444-555555555555}',
      }, {
        id: '{22222222-4444-4444-5555-666666666666}',
        name: 'Child Two',
        acronym: 'CTT',
        description: 'Child',
        component: 'Child',
        fullPath: 'Root | ChildTwo',
        parentId: '{11111111-2222-3333-4444-555555555555}',
        Organizations: [
          {
            id: '{44444444-3333-4444-5555-666666666666}',
            name: 'Other GrandChild One',
            acronym: 'OGCO',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Two | Other GrandChild One',
            parentId: '{22222222-4444-4444-5555-666666666666}',
          }, {
            id: '{44444444-4444-4444-5555-666666666666}',
            name: 'Other GrandChild Two',
            acronym: 'CTT',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Two | Other GrandChild Two',
            parentId: '{22222222-4444-4444-5555-666666666666}',
          }, {
            id: '{44444444-5555-4444-5555-666666666666}',
            name: 'Other GrandChild Three',
            acronym: 'CTTT',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Two | Other GrandChild Three',
            parentId: '{22222222-4444-4444-5555-666666666666}',
          },
        ],
      }, {
        id: '{22222222-5555-4444-5555-666666666666}',
        name: 'Child Three',
        acronym: 'CTTT',
        description: 'Child',
        component: 'Child',
        fullPath: 'Root | ChildThree',
        parentId: '{11111111-2222-3333-4444-555555555555}',
        Organizations: [
          {
            id: '{33333333-3333-4444-5555-666666666666}',
            name: 'GrandChildOne',
            acronym: 'GCO',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Three | GrandChildOne',
            parentId: '{22222222-5555-4444-5555-666666666666}',
          }, {
            id: '{33333333-4444-4444-5555-666666666666}',
            name: 'GrandChild Two',
            acronym: 'CTT',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Three | GrandChildTwo',
            parentId: '{22222222-5555-4444-5555-666666666666}',
          }, {
            id: '{33333333-5555-4444-5555-666666666666}',
            name: 'GrandChild Three',
            acronym: 'CTTT',
            description: 'GrandChild',
            component: 'GrandChild',
            fullPath: 'Root | Child Three | GrandChildThree',
            parentId: '{22222222-5555-4444-5555-666666666666}',
          },
        ],
      },
    ],
  },
};

export const apiIdList = {
  Organizations: [{
    id: '{22222222-4444-4444-5555-666666666666}',
    name: 'Child Two',
    acronym: 'CTT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildTwo',
    parentId: '{11111111-2222-3333-4444-555555555555}',
    Organizations: [
      {
        id: '{44444444-3333-4444-5555-666666666666}',
        name: 'Other GrandChild One',
        acronym: 'OGCO',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Two | Other GrandChild One',
        parentId: '{22222222-4444-4444-5555-666666666666}',
      }, {
        id: '{44444444-4444-4444-5555-666666666666}',
        name: 'Other GrandChild Two',
        acronym: 'CTT',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Two | Other GrandChild Two',
        parentId: '{22222222-4444-4444-5555-666666666666}',
      }, {
        id: '{44444444-5555-4444-5555-666666666666}',
        name: 'Other GrandChild Three',
        acronym: 'CTTT',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Two | Other GrandChild Three',
        parentId: '{22222222-4444-4444-5555-666666666666}',
      },
    ],
  }],
};

export const apiNameList = {
  Organizations: [{
    id: '{22222222-5555-4444-5555-666666666666}',
    name: 'Child Three',
    acronym: 'CTTT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildThree',
    parentId: '{11111111-2222-3333-4444-555555555555}',
    Organizations: [
      {
        id: '{33333333-3333-4444-5555-666666666666}',
        name: 'GrandChildOne',
        acronym: 'GCO',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Three | GrandChildOne',
        parentId: '{22222222-5555-4444-5555-666666666666}',
      }, {
        id: '{33333333-4444-4444-5555-666666666666}',
        name: 'GrandChild Two',
        acronym: 'CTT',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Three | GrandChildTwo',
        parentId: '{22222222-5555-4444-5555-666666666666}',
      }, {
        id: '{33333333-5555-4444-5555-666666666666}',
        name: 'GrandChild Three',
        acronym: 'CTTT',
        description: 'GrandChild',
        component: 'GrandChild',
        fullPath: 'Root | Child Three | GrandChildThree',
        parentId: '{22222222-5555-4444-5555-666666666666}',
      },
    ],
  }],
};

export const apiAcronymList = {
  Organizations: [{
    id: '{22222222-3333-4444-5555-666666666666}',
    name: 'ChildOne',
    acronym: 'CT',
    description: 'Child',
    component: 'Child',
    fullPath: 'Root | ChildOne',
    parentId: '{11111111-2222-3333-4444-555555555555}',
  }],
};

// should get the children of org test
export const childOne = new Org({
  id: '{55555555-5555-5555-5555-666666666666}',
  name: 'Child Organization One',
  acronym: 'CO',
  description: 'It is another fake org',
  component: 'Organization',
  fullPath: 'Root | Start Organization | Child Organization',
  parentId: '{44444444-5555-4444-5555-666666666666}',
});

export const childTwo = new Org({
  id: '{15555555-5555-5555-5555-666666666666}',
  name: 'Child Organization Two',
  acronym: 'COO',
  description: 'It is another fake org',
  component: 'Organization',
  fullPath: 'Root | Start Organization | Child Organization Two',
  parentId: '{44444444-5555-4444-5555-666666666666}',
});

export const childThree = new Org({
  id: '{25555555-5555-5555-5555-666666666666}',
  name: 'Child Organization Three',
  acronym: 'COOO',
  description: 'It is another fake org',
  component: 'Organization',
  fullPath: 'Root | Start Organization | Child Organization Three',
  parentId: '{44444444-5555-4444-5555-666666666666}',
});

export const childFour = new Org({
  id: '{35555555-5555-5555-5555-666666666666}',
  name: 'Child Organization Four',
  acronym: 'COOOO',
  description: 'It is another fake org',
  component: 'Organization',
  fullPath: 'Root | Start Organization | Child Organization Four',
  parentId: '{44444444-5555-4444-5555-666666666666}',
});