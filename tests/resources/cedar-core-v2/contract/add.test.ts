import {
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';

import contractAddModule from '../../../../src/resources/cedar-core-v2/contract/add';
import contractConfig from '../../../../src/resources/cedar-core-v2/contract';
import Messages from '../../../../src/utils/constants/messages';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

// Setup mocks for the utility function
const mocks = vi.hoisted(() => ({
  contractAddUtil: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: vi.fn(),
  })),
}));

vi.mock(import('../../../../src/utils/contract'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    contractAddUtil: mocks.contractAddUtil,
  };
});

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', contractConfig);
app.resources.initResources(app);

describe('Contract Add tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset all mocks to their default state
    mocks.contractAddUtil.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const getValidContractRequest = () => ({
    Contracts: [{
      id: '{11111111-**************-************}', // Existing contract ID
      systemId: '{2222**************-5555-666666666666}', // System ID
      ContractNumber: 'TEST-CONTRACT-001',
      IsDeliveryOrg: 'No',
      OrderNumber: 'ORD-TEST-001',
      ProductServiceDescription: 'Test Product/Service',
      ProjectTitle: 'Test Project',
      ServiceProvided: 'Test Service',
      parentAwardId: '{*************-5555-6666-************}',
      contractADO: 'Yes',
      awardId: 'AWARD-TEST-001',
      description: 'A test contract for unit testing.',
      POPStartDate: '2023-01-01',
      POPEndDate: '2024-12-31',
      contractName: 'Test Contract Name',
    }],
  });

  it('Should return a 200 with success message and new GUID when contract is added successfully', async () => {
    const mockReturnedGuid = '11111111-**************-************';
    mocks.contractAddUtil.mockResolvedValueOnce({
      success: true,
      guid: mockReturnedGuid,
      count: 1,
    });

    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: mockReturnedGuid,
    });
    expect(mocks.contractAddUtil).toHaveBeenCalledWith(
      expect.any(Function),
      expect.any(Object),
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          systemId: expect.any(String),
        }),
      ]),
    );
  });

  it('Should handle multiple contracts in a single request', async () => {
    const mockReturnedGuid = '11111111-**************-************';
    mocks.contractAddUtil.mockResolvedValueOnce({
      success: true,
      guid: mockReturnedGuid,
      count: 2,
    });

    const multipleContractsRequest = {
      Contracts: [
        getValidContractRequest().Contracts[0],
        {
          id: '{aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee}',
          systemId: '{ffffffff-aaaa-bbbb-cccc-dddddddddddd}',
          ContractNumber: 'TEST-CONTRACT-002',
          ProjectTitle: 'Another Project',
        },
      ],
    };

    const response = await request(app)
      .post('/')
      .send(multipleContractsRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: mockReturnedGuid,
    });

    expect(mocks.contractAddUtil).toHaveBeenCalledWith(
      expect.any(Function),
      expect.any(Object),
      expect.arrayContaining([
        expect.objectContaining({
          id: expect.any(String),
          systemId: expect.any(String),
        }),
        expect.objectContaining({
          id: expect.any(String),
          systemId: expect.any(String),
        }),
      ]),
    );
  });

  it('Should return a 400 when Contracts array is missing', async () => {
    const response = await request(app)
      .post('/')
      .send({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide a list of contracts.'],
    });
  });

  it('Should return a 400 when Contracts array is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({ Contracts: [] })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide a list of contracts.'],
    });
  });

  it('Should return a 400 when contract in array is missing `id`', async () => {
    const invalidRequest = {
      Contracts: [{
        ...getValidContractRequest().Contracts[0],
        id: undefined,
      }],
    };

    mocks.contractAddUtil.mockResolvedValueOnce(new Error('Missing or invalid \'id\' for contract at index 0.'));

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
  });

  it('Should return a 400 when contract in array is missing `systemId`', async () => {
    const invalidRequest = {
      Contracts: [{
        ...getValidContractRequest().Contracts[0],
        systemId: undefined,
      }],
    };

    mocks.contractAddUtil.mockResolvedValueOnce(new Error('Missing or invalid \'systemId\' for contract at index 0.'));

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
  });

  it('Should return a 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'post', contractAddModule);
    const response = await request(badApp)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.app_invalid] });
  });

  it('Should return a 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error(Messages.db_unavailable));
    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.db_unavailable] });
  });

  it('Should return a 500 when utility function returns database error', async () => {
    mocks.contractAddUtil.mockResolvedValueOnce(new Error('Database insert failed: SP call failed'));
    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
  });

  it('Should return a 500 when stored procedure returns non-zero status', async () => {
    mocks.contractAddUtil.mockResolvedValueOnce(new Error('Stored procedure returned non-zero status'));

    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Failed to add contract(s).'],
    });
  });

  it('Should return a 500 when an unhandled exception occurs', async () => {
    mocks.contractAddUtil.mockImplementationOnce(() => {
      throw new Error('Unexpected error during utility call');
    });

    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Internal server error'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    const error = get(errorLog, '[0]', []);
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
    expect(error.error.message).toContain('Unexpected error during utility call');
  });

  it('Should return empty string when no GUID is returned from utility', async () => {
    mocks.contractAddUtil.mockResolvedValueOnce({
      success: true,
      guid: undefined,
      count: 1,
    });

    const response = await request(app)
      .post('/')
      .send(getValidContractRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: '',
    });
  });
});
