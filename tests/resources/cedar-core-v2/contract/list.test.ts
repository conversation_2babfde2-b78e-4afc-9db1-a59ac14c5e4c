import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import {
  apiSystemIdList,
  apiFullList,
} from './contractListTestData';
import listModule from '../../../../src/resources/cedar-core-v2/contract/list';
import contractConfig from '../../../../src/resources/cedar-core-v2/contract';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  contractListUtil: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/contract'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    contractListUtil: mocks.contractListUtil,
  };
});

const app = await createApp();
app.resources.register(app, '/', contractConfig);
app.resources.initResources(app);

describe('Contract List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mocks.contractListUtil.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .query({
        systemId: '{66666666-7777-8888-9999-6306ABC0C141}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{66666666-7777-8888-9999-6306ABC0C141}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 500 and an error if contractListUtil returns an error', async () => {
    mocks.contractListUtil.mockResolvedValueOnce(new Error('Unable to retrieve the contract list'));
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{66666666-7777-8888-9999-6306ABC0C141}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an issue fetching the contracts',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list');
  });

  it('Should return a 200 with a list of all contracts', async () => {
    mocks.contractListUtil.mockResolvedValueOnce(apiFullList);
    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiFullList);
  });

  it('Should return a 200 with a count and list of all contracts given a system id', async () => {
    mocks.contractListUtil.mockResolvedValueOnce(apiSystemIdList);
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{66666666-7777-8888-9999-6306ABC0C141}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiSystemIdList);
  });
});
