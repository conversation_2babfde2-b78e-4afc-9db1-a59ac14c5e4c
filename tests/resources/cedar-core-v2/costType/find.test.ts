// Modules.
import request from 'supertest';
import { get } from 'lodash';

// Custom.
import handler, {
  CostTypeApplication,
  CostTypeFindMessages,
  convertDbCostTypeToCostType,
  DbCostTypeQueryResult,
} from '../../../../src/resources/cedar-core-v2/costType/find';
import routeConfig from '../../../../src/resources/cedar-core-v2/costType';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import createFailureTests from '../../../shared/endpoints';
import Databases from '../../../../src/utils/constants/databases';
import Messages from '../../../../src/utils/constants/messages';
import { CMSApp } from '../../../../src/types';

const mocks = vi.hoisted(() => ({
  queryViewTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTyped: mocks.queryViewTyped,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const mockDbQueryResult: DbCostTypeQueryResult = [
  [
    {
      'Sparx CostPool GUID': '11111111-**************-************',
      'CostPool Name': 'IT Operations',
    },
    {
      'Sparx CostPool GUID': '2222**************-5555-************',
      'CostPool Name': 'Admin Costs',
    },
  ],
  2,
];

const mockApiResult = {
  id: '11111111-**************-************',
  name: 'IT Operations',
  CostTypes: [
    {
      id: '11111111-**************-************',
      name: 'IT Operations',
    },
    {
      id: '2222**************-5555-************',
      name: 'Admin Costs',
    },
  ],
  count: 2,
};

let app: CMSApp;

const defaultRequest = async (
  application: CostTypeApplication = CostTypeApplication.Alfabet,
  name?: string,
) => {
  const query: Record<string, unknown> = { application };
  if (name !== undefined) {
    query.name = name;
  }

  return request(app).get('/')
    .query(query)
    .set('x-jwt-key', 'pass');
};

describe('Census Cost Type Find Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);
    vi.clearAllMocks();
  });

  it('sends 200 with a list of cost types when application is valid and name is omitted', async () => {
    mocks.queryViewTyped.mockResolvedValueOnce(mockDbQueryResult);

    const response = await defaultRequest(CostTypeApplication.All);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(mockApiResult);
    expect(mocks.queryViewTyped).toHaveBeenCalledWith(
      `${Databases.sparxSupport}.Sparx_CostPool`,
      [
        '[Sparx CostPool GUID]',
        '[CostPool Name]',
      ],
      {
        where: {
          operation: {
            column: '1',
            operator: '=',
            value: '1',
          },
        },
      },
    );
  });

  it('sends 200 with a filtered list of cost types when name is provided', async () => {
    const singleResult: DbCostTypeQueryResult = [[mockDbQueryResult[0][0]], 1];
    mocks.queryViewTyped.mockResolvedValueOnce(singleResult);

    const filteredApiResult = {
      id: '11111111-**************-************',
      name: 'IT Operations',
      CostTypes: [
        {
          id: '11111111-**************-************',
          name: 'IT Operations',
        },
      ],
      count: 1,
    };

    const response = await defaultRequest(CostTypeApplication.Alfabet, 'IT Operations');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(filteredApiResult);
    expect(mocks.queryViewTyped).toHaveBeenCalledWith(
      `${Databases.sparxSupport}.Sparx_CostPool`,
      [
        '[Sparx CostPool GUID]',
        '[CostPool Name]',
      ],
      {
        where: {
          operation: {
            column: '1',
            operator: '=',
            value: '1',
          },
        },
        and: [
          {
            operation: {
              column: '[CostPool Name]',
              operator: '=',
              value: 'IT Operations',
            },
          },
        ],
      },
    );
  });

  it('sends 200 with empty list when no cost types are found', async () => {
    mocks.queryViewTyped.mockResolvedValueOnce([[], 0]);

    const response = await defaultRequest(CostTypeApplication.All, 'NonExistentType');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      CostTypes: [],
      count: 0,
    });
  });

  it('sends 400 when application parameter is missing', async () => {
    const response = await request(app).get('/')
      .query({ name: 'IT Operations' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [CostTypeFindMessages.error_application_invalid],
    });
  });

  it('sends 400 when application parameter is invalid', async () => {
    const response = await defaultRequest('invalidApp' as CostTypeApplication);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [CostTypeFindMessages.error_application_invalid],
    });
  });

  it('sends 400 when name parameter is not a string', async () => {
    const response = await defaultRequest(CostTypeApplication.All, ['IT Operations'] as unknown as string);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [CostTypeFindMessages.error_name_missing],
    });
  });

  it('sends 500 when database query fails', async () => {
    mocks.queryViewTyped.mockRejectedValueOnce(new Error('Database query failed'));

    const response = await defaultRequest(CostTypeApplication.All);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_view_error],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0].error')).toBeInstanceOf(Error);
  });

  it('sends 500 when query result is not an array', async () => {
    mocks.queryViewTyped.mockResolvedValueOnce([null]); // Invalid result format

    const response = await defaultRequest(CostTypeApplication.All);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_result_missing],
    });
  });

  describe('Express Server', () => {
    it('handles unexpected errors gracefully', async () => {
      mocks.queryViewTyped.mockImplementationOnce(() => {
        throw new Error('Unexpected handler error');
      });

      const response = await defaultRequest(CostTypeApplication.All);

      expect(response.status).toEqual(500);
      expect(response.body).toEqual({
        message: [Messages.internal_server_error],
      });

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      const errorDetails = get(errorLog, '[0]', {});
      expect(errorDetails).toHaveProperty('package', 'census-core-v2');
      expect(errorDetails).toHaveProperty('service', 'costType');
      expect(errorDetails).toHaveProperty('action', 'find-list');
      expect(errorDetails.error).toBeInstanceOf(Error);
      expect(errorDetails.error.message).toEqual('Unexpected handler error');
    });
  });

  describe('convertDbCostTypeToCostType', () => {
    it('correctly transforms a single DB record to CostType', () => {
      const dbRecord = {
        'Sparx CostPool GUID': '11111111-**************-************',
        'CostPool Name': 'Test Cost Type',
      };
      const expected = {
        id: '11111111-**************-************',
        name: 'Test Cost Type',
      };
      expect(convertDbCostTypeToCostType(dbRecord)).toEqual(expected);
    });
  });
});