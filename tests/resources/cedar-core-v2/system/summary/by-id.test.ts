import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import byIdModule from '../../../../../src/resources/cedar-core-v2/system/summary/by-id';
import systemSummaryConfig from '../../../../../src/resources/cedar-core-v2/system/summary';
import { createApp, createBadApp, testLogger } from '../../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', systemSummaryConfig);
app.resources.initResources(app);

const dbResponse = [
  [{
    id: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
    nextVersionId: null,
    previousVersionId: null,
    acronym: 'CEDAR',
  }],
  1,
];

const apiResponse = {
  SystemSummary: [
    {
      id: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
      nextVersionId: null,
      previousVersionId: null,
      acronym: 'CEDAR',
    },
  ],
  count: 1,
};

describe('System Summary By ID tests', () => {
  it('Should return a 400 and an error if there is no ID provided', async () => {
    const badApp = createBadApp('/:bad', 'get', byIdModule);
    const response = await request(badApp).get('/bad');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'No object id provided' });
  });

  it('Should return a 400 and an error if the ID does not validate', async () => {
    const response = await request(app)
      .get('/not-a-uuid')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({ message: 'Invalid object id' });
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/:id', 'get', byIdModule);
    const response = await request(badApp)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Database Unavailable',
    });
  });

  it('Should return a 500 with an error if queryView returns a critical error', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((_, reject) => {
      reject(new Error('Bad Critical Connection'));
    }));

    const response = await request(app)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve system summary by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
  });

  it('Should return a 500 with an error if queryView returns an error', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve(new Error('Bad Connection'));
    }));

    const response = await request(app)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve system summary by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
  });

  it('Should return a 200 with a result and count', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve(dbResponse);
    }));

    const response = await request(app)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(apiResponse);
  });

  it('Should return a 200 with an empty result and count of 0', async () => {
    mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([]);
    }));

    const response = await request(app)
      .get('/{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      SystemSummary: [],
    });
  });
});
