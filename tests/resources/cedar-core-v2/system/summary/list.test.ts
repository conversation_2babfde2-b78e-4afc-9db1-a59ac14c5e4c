import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import listConfig from '../../../../../src/resources/cedar-core-v2/system/summary';
import listModule from '../../../../../src/resources/cedar-core-v2/system/summary/list';
import { createApp, createBadApp, testLogger } from '../../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
app.resources.register(app, '/', listConfig);
app.resources.initResources(app);

const fullResults = {
  ResultSet: [{
    id: '{018A3E2E-42FB-4123-AA26-53A6A077DF15}',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '{018A3E2E-42FB-4123-AA26-53A6A077DF15}',
    uuid: 'E6B0C279-3881-4E6F-A813-8B6D06980F76',
    name: 'Centralized Data Abstraction Tool',
    description: 'CMS conducts medical record reviews to validate the accuracy of risk adjustment data submitted to CMS by Medicare Advantage organizations for Part C payments. The purpose of data validation analysis is to measure the extent that inaccurate diagnosis codes impact HCC assignments and the associated payment for Medicare Advantage beneficiaries. CDAT was developed to improve and support the medical record review and risk adjustment processes. CDAT is used to automate the flow and control of the RADV activities for CMS. CDAT generates and tracks the medical record requests sent to the selected plans for the medical records needed for the abstraction process. It then tracks the receipt and abstraction of the medical records. Protected Health Information (PHI) and Personally Identifiable Information (PII) are stored in the CDAT database, but this type of data is not downloaded to the workstation.CDAT tool also provides the ability for the final abstracted data to be extracted for further analysis. The application also provides reporting capabilities for tracking medical record requests, as well as Inter Rater Reliability (IRR) to track and analyze the abstractions created by different medical record review contractors (MRRCs) and coders within those MRRCS.Core Functions:* Supports the medical record review and risk adjustment processes by automating the flow and control of the RADV activities for CMS. * Generates and tracks medical record requests sent to the selected plans for medical records needed for the abstraction process. * Tracks the receipt and abstraction of the medical records. * Stores Protected Health Information (PHI) and Personally Identifiable Information (PII) for downloading to the workstation.* Provides extraction of final abstracted data for further analysis. * Provides reports to facilitate tracking medical record requests.* Contains Inter Rater Reliability (IRR) to track and analyze the abstractions created by different medical record review contractors (MRRCs) and coders within those MRRCS.',
    version: '25',
    acronym: 'CDAT',
    status: '',
    belongsTo: '',
    businessOwnerOrg: 'Division of Plan Oversight and Accountability - RETIRED',
    systemMaintainerOrg: 'Division of Plan Oversight and Accountability - RETIRED',
    objectState: 'Retired',
    businessOwnerComp: 'CPI',
    systemMaintainerComp: 'CPI',
    'ATO Effective Date': '05/02/2016',
    'ATO Expiration Date': '05/02/2019',
  }, {
    id: '{01AB69E1-1DDE-4df3-BE38-86497DDB7AA0}',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '{01AB69E1-1DDE-4df3-BE38-86497DDB7AA0}',
    uuid: 'CENSUS2018_5A-49B9-8242-9E8C0F76183A',
    name: 'OnePI Extraction',
    description: 'Extraction Process to transmit UCM reporting data to OnePI and Acumen.',
    version: '25',
    acronym: 'OnePI',
    status: '',
    belongsTo: '{F40BE790-D8C8-42b2-A5B4-3074D994DA72}',
    businessOwnerOrg: '',
    systemMaintainerOrg: '',
    objectState: 'Active',
    businessOwnerComp: '',
    systemMaintainerComp: '',
    'ATO Effective Date': '06/03/2015',
    'ATO Expiration Date': '',
  }, {
    id: '{01BF73F7-B3E3-45e9-BD1B-33DBE7F75624}',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '{01BF73F7-B3E3-45e9-BD1B-33DBE7F75624}',
    uuid: 'DAB9D0B5-FAFB-4BD5-956A-573AEEA69407',
    name: 'Denominator System',
    description: 'Description: Provides for creation of the Denominator File, a skeleton entitlement and enrollment file that contains data on all Medicare beneficiaries with entitlement in a given year.',
    version: '25',
    acronym: 'DNMNTR',
    status: '',
    belongsTo: '{E0AD284C-6053-4cf4-9E93-71C743D52C89}',
    businessOwnerOrg: '',
    systemMaintainerOrg: '',
    objectState: 'Retired',
    businessOwnerComp: '',
    systemMaintainerComp: '',
    'ATO Effective Date': '',
    'ATO Expiration Date': '04/01/2013',
  }, {
    id: '{01FA0A4D-8B02-4add-876B-5DB887ED72A8}',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '{01FA0A4D-8B02-4add-876B-5DB887ED72A8}',
    uuid: '52897F9B-8CAD-417E-8BA4-89EA10336895',
    name: 'Health Care Financing Review Abstract Search',
    description: 'This web application allows one to search for articles that have been published in the Health Care and Financing Review journal.  Part of cms.hhs.gov.See http://www.cms.hhs.gov/HealthCareFinancingReview/PastArticles/list.asp CISS #  FISMA Family Number: 6.A.i.8',
    version: '25',
    acronym: 'HCFReview',
    status: '',
    belongsTo: '{5BB9B6F9-3247-4077-81A7-D4C676708B21}',
    businessOwnerOrg: '',
    systemMaintainerOrg: '',
    objectState: 'Retired',
    businessOwnerComp: '',
    systemMaintainerComp: '',
    'ATO Effective Date': '07/02/2016',
    'ATO Expiration Date': '07/02/2019',
  }],
};

const fullProcessedResults = [{
  id: '{018A3E2E-42FB-4123-AA26-53A6A077DF15}',
  nextVersionId: '',
  previousVersionId: '',
  ictObjectId: '{018A3E2E-42FB-4123-AA26-53A6A077DF15}',
  uuid: 'E6B0C279-3881-4E6F-A813-8B6D06980F76',
  name: 'Centralized Data Abstraction Tool',
  description: 'CMS conducts medical record reviews to validate the accuracy of risk adjustment data submitted to CMS by Medicare Advantage organizations for Part C payments. The purpose of data validation analysis is to measure the extent that inaccurate diagnosis codes impact HCC assignments and the associated payment for Medicare Advantage beneficiaries. CDAT was developed to improve and support the medical record review and risk adjustment processes. CDAT is used to automate the flow and control of the RADV activities for CMS. CDAT generates and tracks the medical record requests sent to the selected plans for the medical records needed for the abstraction process. It then tracks the receipt and abstraction of the medical records. Protected Health Information (PHI) and Personally Identifiable Information (PII) are stored in the CDAT database, but this type of data is not downloaded to the workstation.CDAT tool also provides the ability for the final abstracted data to be extracted for further analysis. The application also provides reporting capabilities for tracking medical record requests, as well as Inter Rater Reliability (IRR) to track and analyze the abstractions created by different medical record review contractors (MRRCs) and coders within those MRRCS.Core Functions:* Supports the medical record review and risk adjustment processes by automating the flow and control of the RADV activities for CMS. * Generates and tracks medical record requests sent to the selected plans for medical records needed for the abstraction process. * Tracks the receipt and abstraction of the medical records. * Stores Protected Health Information (PHI) and Personally Identifiable Information (PII) for downloading to the workstation.* Provides extraction of final abstracted data for further analysis. * Provides reports to facilitate tracking medical record requests.* Contains Inter Rater Reliability (IRR) to track and analyze the abstractions created by different medical record review contractors (MRRCs) and coders within those MRRCS.',
  version: '25',
  acronym: 'CDAT',
  status: '',
  belongsTo: '',
  businessOwnerOrg: 'Division of Plan Oversight and Accountability - RETIRED',
  systemMaintainerOrg: 'Division of Plan Oversight and Accountability - RETIRED',
  state: 'Retired',
  businessOwnerOrgComp: 'CPI',
  systemMaintainerOrgComp: 'CPI',
  atoEffectiveDate: '2016-05-02',
  atoExpirationDate: '2019-05-02',
}, {
  id: '{01AB69E1-1DDE-4df3-BE38-86497DDB7AA0}',
  nextVersionId: '',
  previousVersionId: '',
  ictObjectId: '{01AB69E1-1DDE-4df3-BE38-86497DDB7AA0}',
  uuid: 'CENSUS2018_5A-49B9-8242-9E8C0F76183A',
  name: 'OnePI Extraction',
  description: 'Extraction Process to transmit UCM reporting data to OnePI and Acumen.',
  version: '25',
  acronym: 'OnePI',
  status: '',
  belongsTo: '{F40BE790-D8C8-42b2-A5B4-3074D994DA72}',
  businessOwnerOrg: '',
  systemMaintainerOrg: '',
  state: 'Active',
  businessOwnerOrgComp: '',
  systemMaintainerOrgComp: '',
  atoEffectiveDate: '2015-06-03',
  atoExpirationDate: null,
}, {
  id: '{01BF73F7-B3E3-45e9-BD1B-33DBE7F75624}',
  nextVersionId: '',
  previousVersionId: '',
  ictObjectId: '{01BF73F7-B3E3-45e9-BD1B-33DBE7F75624}',
  uuid: 'DAB9D0B5-FAFB-4BD5-956A-573AEEA69407',
  name: 'Denominator System',
  description: 'Description: Provides for creation of the Denominator File, a skeleton entitlement and enrollment file that contains data on all Medicare beneficiaries with entitlement in a given year.',
  version: '25',
  acronym: 'DNMNTR',
  status: '',
  belongsTo: '{E0AD284C-6053-4cf4-9E93-71C743D52C89}',
  businessOwnerOrg: '',
  systemMaintainerOrg: '',
  state: 'Retired',
  businessOwnerOrgComp: '',
  systemMaintainerOrgComp: '',
  atoEffectiveDate: null,
  atoExpirationDate: '2013-04-01',
}, {
  id: '{01FA0A4D-8B02-4add-876B-5DB887ED72A8}',
  nextVersionId: '',
  previousVersionId: '',
  ictObjectId: '{01FA0A4D-8B02-4add-876B-5DB887ED72A8}',
  uuid: '52897F9B-8CAD-417E-8BA4-89EA10336895',
  name: 'Health Care Financing Review Abstract Search',
  description: 'This web application allows one to search for articles that have been published in the Health Care and Financing Review journal.  Part of cms.hhs.gov.See http://www.cms.hhs.gov/HealthCareFinancingReview/PastArticles/list.asp CISS #  FISMA Family Number: 6.A.i.8',
  version: '25',
  acronym: 'HCFReview',
  status: '',
  belongsTo: '{5BB9B6F9-3247-4077-81A7-D4C676708B21}',
  businessOwnerOrg: '',
  systemMaintainerOrg: '',
  state: 'Retired',
  businessOwnerOrgComp: '',
  systemMaintainerOrgComp: '',
  atoEffectiveDate: '2016-07-02',
  atoExpirationDate: '2019-07-02',
}];

describe('System Summary List tests', () => {
  afterEach(() => {
    mocks.getDb.mockImplementation(() => ({
      queryStoredProcedures: mocks.queryStoredProcedures,
    }));
  });

  it('Should return a 200 with a list of all systems', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: JSON.stringify(fullResults),
        queryStatus: 0,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      SystemSummary: fullProcessedResults,
      count: 4,
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 200 with a filtered list based on the state equalling active', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: JSON.stringify({
          ...fullResults,
          ResultSet: fullResults.ResultSet.filter((item) => item.objectState === 'Active'),
        }),
        queryStatus: 0,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/')
      .query({
        state: 'active',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      SystemSummary: fullProcessedResults.filter((item) => item.state === 'Active'),
      count: 1,
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 200 with a filtered list based on the EUA provided', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: JSON.stringify({
          ...fullResults,
          ResultSet: [fullResults.ResultSet[0]],
        }),
        queryStatus: 0,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/')
      .query({
        userName: 'TST1',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      SystemSummary: [fullProcessedResults[0]],
      count: 1,
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemListRole',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the stored procedure returns an error', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve(new Error('SP Error'));
    }));

    const response = await request(app).get('/')
      .query({
        state: 'active',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve the system list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP Error');
  });

  it('Should return a 500 with an error if the stored procedure returns a critical error', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((_, reject) => {
      reject(new Error('SP Critical Error'));
    }));

    const response = await request(app).get('/')
      .query({
        state: 'active',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to retrieve the system list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('SP Critical Error');
  });

  it('Should return a 500 with an error if the query returns status -1', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: null,
        queryStatus: -1,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Status of the query was invalid',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 200 with an empty object if the query returns status 1', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: null,
        queryStatus: 1,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({});
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the results are not in the query response', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        results: null,
        queryStatus: 0,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get get result from query',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if the results cannot be parsed', async () => {
    mocks.queryStoredProcedures.mockImplementationOnce(() => new Promise((resolve) => {
      resolve([[{
        result: '{"bad": "json"',
        queryStatus: 0,
      }],
      1,
      ]);
    }));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to parse database response',
    });
    expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
      'SP_Get_SystemList',
      expect.anything(),
      expect.anything(),
      expect.anything(),
    );
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/:bad', 'get', listModule);
    const response = await request(badApp).get('/bad').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Database Unavailable',
    });
  });

  it('Should return a 500 with an error if state param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        state: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if status param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        status: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if version param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        version: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if includeInSurvey param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        includeInSurvey: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if idsOnly param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        idsOnly: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if belongsTo param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        belongsTo: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if userName param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        userName: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });

  it('Should return a 500 with an error if roleType param fails validation', async () => {
    const response = await request(app).get('/')
      .query({
        userName: 'TST1',
        roleType: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: '"value" must be a string',
    });
  });
});
