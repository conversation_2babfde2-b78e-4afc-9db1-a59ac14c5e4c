import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { cloneDeep, get } from 'lodash';
import { createApp, createBadApp, testLogger } from '../../../../test-utils';
import systemDetailById from '../../../../../src/resources/cedar-core-v2/system/detail';
import systemDetailByIdModule from '../../../../../src/resources/cedar-core-v2/system/detail/by-id';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const apiResponse = {
  id: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  ictObjectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  name: 'CMS Enterprise Data Analytics Repository',
  acronym: 'CEDAR',
  state: 'Active',
  uuid: 'c85efeb4dbb43b40c029388d7c9619a1',
  nextVersionId: null,
  previousVersionId: null,
  description: "The CEDAR system allows the OIT/EADG to perform EA analysis in support of CMS' planning and critical initiatives. CEDAR provides Business Architecture analytical functions such as value stream mapping, business capability maps and models, line of sight from business strategy to technical components and resource utilization, What-If analysis, and performance measurement.\n"
  + "The tool supports agency activities such as business architecture modeling, investment analysis and strategic alignment support. Additionally, it is the main repository of the Division of Enterprise Architecture's (DEA) portfolio in evaluating the business activities and data elements required to execute and manage the CMS Strategy.",
  version: 25,
  status: null,
  belongsTo: null,
  isCmsOwned: '1',
  storesBeneficiaryAddress: 'No',
  beneficiaryAddressSource: null,
  beneficiaryAddressSourceOther: null,
  beneficiaryAddressPurpose: null,
  beneficiaryAddressPurposeOther: null,
  storesBankingData: 'No',
  costPerYear: '********',
  numberOfFederalFte: '4',
  numberOfContractorFte: '20',
  numberOfSupportedUsersPerMonth: '50',
  movingToCloud: "No, It's already moved to a cloud service provider",
  movingToCloudDate: '10/01/2019',
  systemCustomization: 'Mixed',
  frontendAccessType: 'IPv4 Only',
  netAccessibility: 'Accessible to a CMS-internal network only',
  ipEnabledAssetCount: '33',
  ip6EnabledAssetPercent: 'Less than 20%',
  hardCodedIpAddress: 'No',
  ecapParticipation: 'No',
  systemProductionDate: '01/01/2019',
  devCompletionPercent: '75% - 99%',
  devWorkDescription: 'CMS system maintainer organization changed in March 2024. Otherwise no major changes planned.',
  agileUsed: 'Yes',
  deploymentFrequency: 'Ad Hoc/As Needed',
  majorRefreshDate: null,
  plansToRetireReplace: 'No',
  yearToRetireReplace: null,
  quarterToRetireReplace: null,
  businessArtifactsOnDemand: 'Yes',
  testScriptsOnDemand: 'Yes',
  testReportsOnDemand: 'Yes',
  testPlanOnDemand: 'Yes',
  sourceCodeOnDemand: 'Yes',
  systemDesignOnDemand: 'Yes',
  omDocumentationOnDemand: 'Yes',
  systemRequirementsOnDemand: 'Yes',
  recordsManagementBucket: 'https://confluenceent.cms.gov/display/CMSEA/CEDAR',
  apisDeveloped: 'Yes',
  apiDataArea: null,
  apisAccessibility: 'Internal Access',
  apiFHIRUse: 'No',
  apiFHIRUseOther: null,
  systemHasApiGateway: 'Yes',
  aiPlan: 'Yes, within the next three years',
  systemAiType: null,
  systemAiTypeOther: null,
  businessOwnerOrg: 'Enterprise Architecture and Data Group',
  businessOwnerOrgComp: 'OIT',
  systemMaintainerOrg: 'Division of Enterprise Architecture',
  systemMaintainerOrgComp: 'OIT',
  ip6TransitionPlan: 'Yes, transition to IPv6',
  editBeneficiaryInformation: null,
  '508UserInterface': 'Yes, this system UI is accessible',
  systemDataLocation: 'This system|Another CMS system',
  systemDataLocationNotes: 'Full or custom extracts available to all CMS staff on SharePoint (https://share.cms.gov/Office/OIT/EADG/DEA/SitePages/HomeDEA.aspx) or via <NAME_EMAIL>. Several system inventory data sets are avaiable via APIs. And the EASi system is publishing some of the system inventory data as well.',
  adHocAgileDeploymentFrequency: 'Every Two Weeks',
  locallyStoredUserInformation: 'No',
  multifactorAuthenticationMethod: 'One Time Password or Push from an authenticator app, e.g. Google Authenticator, DUO, OKTA Verify|PIV/certificate',
  multifactorAuthenticationMethodOther: null,
  networkTrafficEncryptionKeyManagement: 'We have a process for managing encryption keys.',
  dataAtRestEncryptionKeyManagement: 'We have a process for managing encryption keys and it is automated.',
  noPersistentRecordsFlag: null,
  recordsManagementDisposalLocation: null,
  recordsManagementDisposalPlan: null,
  recordsUnderLegalHold: 'No',
  legalHoldCaseName: null,
  noMajorRefresh: 'No',
  noPlannedMajorRefresh: 'Yes',
  beneficiaryInformation: 'None of the Above',
  authoritativeDatasource: 'CEDAR is the authoritative source of enterprise views of CMS IT Portfolio connected with the CMS Enterprise Business Portfolio. This information supports over 100 data calls annually and planning for every major cross-cutting initiative at CMS since 2010. CEDAR contains information on the following areas of IT management and the connections/interdependencies between them: CMS Programs and Lines of Business, CMS processes and business functions, System Inventory, System Capability mappings, IT Budget, IT Contracts, Types of System Data, Hosted Environments at data centers and CSPs, Data Exchanges, Software Products. ',
  atoEffectiveDate: '10/28/2022',
  atoExpirationDate: '10/28/2025' as string | number | null,
  systemOwnership: 'CMS Owned',
};

const expectedResponse = {
  id: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  nextVersionId: null,
  previousVersionId: null,
  ictObjectId: '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}',
  uuid: 'c85efeb4dbb43b40c029388d7c9619a1',
  name: 'CMS Enterprise Data Analytics Repository',
  description: "The CEDAR system allows the OIT/EADG to perform EA analysis in support of CMS' planning and critical initiatives. CEDAR provides Business Architecture analytical functions such as value stream mapping, business capability maps and models, line of sight from business strategy to technical components and resource utilization, What-If analysis, and performance measurement.\nThe tool supports agency activities such as business architecture modeling, investment analysis and strategic alignment support. Additionally, it is the main repository of the Division of Enterprise Architecture's (DEA) portfolio in evaluating the business activities and data elements required to execute and manage the CMS Strategy.",
  acronym: 'CEDAR',
  state: 'Active',
  status: null,
  belongsTo: null,
  businessOwnerOrg: 'Enterprise Architecture and Data Group',
  businessOwnerOrgComp: 'OIT',
  systemMaintainerOrg: 'Division of Enterprise Architecture',
  systemMaintainerOrgComp: 'OIT',
  atoEffectiveDate: '2022-10-28',
  atoExpirationDate: '2025-10-28' as string | number | null,
  BusinessOwnerInformation: {
    isCmsOwned: true,
    storesBeneficiaryAddress: false,
    beneficiaryAddressPurpose: null,
    beneficiaryAddressPurposeOther: null,
    beneficiaryAddressSource: null,
    beneficiaryAddressSourceOther: null,
    storesBankingData: false,
    costPerYear: '********',
    numberOfFederalFte: '4',
    numberOfContractorFte: '20',
    numberOfSupportedUsersPerMonth: '50',
    beneficiaryInformation: [
      'None of the Above',
    ],
    editBeneficiaryInformation: null,
    '508UserInterface': 'Yes, this system UI is accessible',
    systemOwnership: 'CMS Owned',
  },
  DataCenterHosting: {
    movingToCloud: "No, It's already moved to a cloud service provider",
    movingToCloudDate: '2019-10-01',
  },
  SystemMaintainerInformation: {
    systemCustomization: 'Mixed',
    frontendAccessType: 'IPv4 Only',
    netAccessibility: 'Accessible to a CMS-internal network only',
    ipEnabledAssetCount: 33,
    ip6EnabledAssetPercent: 'Less than 20%',
    ip6TransitionPlan: 'Yes, transition to IPv6',
    hardCodedIpAddress: false,
    ecapParticipation: false,
    systemProductionDate: '2019-01-01',
    devCompletionPercent: '75% - 99%',
    devWorkDescription: 'CMS system maintainer organization changed in March 2024. Otherwise no major changes planned.',
    agileUsed: true,
    deploymentFrequency: 'Ad Hoc/As Needed',
    majorRefreshDate: null,
    plansToRetireReplace: 'No',
    yearToRetireReplace: null,
    quarterToRetireReplace: null,
    businessArtifactsOnDemand: true,
    systemRequirementsOnDemand: true,
    systemDesignOnDemand: true,
    sourceCodeOnDemand: true,
    testPlanOnDemand: true,
    testScriptsOnDemand: true,
    testReportsOnDemand: true,
    omDocumentationOnDemand: true,
    recordsManagementBucket: [
      'https://confluenceent.cms.gov/display/CMSEA/CEDAR',
    ],
    adHocAgileDeploymentFrequency: 'Every Two Weeks',
    dataAtRestEncryptionKeyManagement: 'We have a process for managing encryption keys and it is automated.',
    legalHoldCaseName: null,
    locallyStoredUserInformation: false,
    multifactorAuthenticationMethod: [
      'One Time Password or Push from an authenticator app, e.g. Google Authenticator, DUO, OKTA Verify|PIV',
      'certificate',
    ],
    multifactorAuthenticationMethodOther: null,
    networkTrafficEncryptionKeyManagement: 'We have a process for managing encryption keys.',
    noMajorRefresh: false,
    noPersistentRecordsFlag: null,
    noPlannedMajorRefresh: true,
    recordsManagementDisposalLocation: null,
    recordsManagementDisposalPlan: null,
    recordsUnderLegalHold: false,
    systemDataLocation: [
      'This system',
      'Another CMS system',
    ],
    systemDataLocationNotes: 'Full or custom extracts available to all CMS staff on SharePoint (https://share.cms.gov/Office/OIT/EADG/DEA/SitePages/HomeDEA.aspx) or via <NAME_EMAIL>. Several system inventory data sets are avaiable via APIs. And the EASi system is publishing some of the system inventory data as well.',
  },
  SoftwareProductDetails: {
    apisDeveloped: 'Yes',
    apisAccessibility: 'Internal Access',
    apiFHIRUse: 'No',
    apiFHIRUseOther: null,
    apiDataArea: null,
    systemHasApiGateway: true,
    aiPlan: 'Yes, within the next three years',
    systemAiType: null,
    systemAiTypeOther: null,
  },
};

const app = await createApp();
app.resources.register(app, '/', systemDetailById);
app.resources.initResources(app);

afterEach(() => {
  mocks.queryView.mockReset();
});

const validUuid = '123e4567-e89b-12d3-a456-************';

describe('systemDetailById', () => {
  it('Should return a detailed response about the requested system', async () => {
    mocks.queryView.mockResolvedValueOnce([[apiResponse], 1]);

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(JSON.parse(response.text)).toStrictEqual(expectedResponse);
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', systemDetailByIdModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Unable to get the application from request',
    });
  });

  it('Should return a 400 if no id is passed in params', async () => {
    const badApp = createBadApp(
      '/:badParam',
      'get',
      systemDetailByIdModule,
      {
        includeApp: true,
        includeLogger: true,
      },
    );
    const response = await request(badApp).get('/test').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: 'No object id provided',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('No id was provided');
  });

  it('Should return a 400 if an invalid id is passed', async () => {
    const response = await request(app)
      .get('/invalid-id')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Invalid object id provided',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Invalid object id provided');
  });

  it('Should return a 500 if the database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 if queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Query View Critical Error'));

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve system detail by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Query View Critical Error');
  });

  it('Should return a 500 if queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to retrieve system detail by ID',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Query View Error');
  });

  it('Should return a 500 if resultStatus is not 1', async () => {
    mocks.queryView.mockResolvedValueOnce([[apiResponse], 0]);

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'An error occurred processing the system detail',
    });
  });

  it('Should log a warning and return a null value if the date is a string and does not format correctly', async () => {
    const modifiedApiResponse = cloneDeep(apiResponse);
    modifiedApiResponse.atoExpirationDate = 'bad date';
    const modifiedExpectedResponse = cloneDeep(expectedResponse);
    modifiedExpectedResponse.atoExpirationDate = null;

    mocks.queryView.mockResolvedValueOnce([[modifiedApiResponse], 1]);

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(JSON.parse(response.text)).toStrictEqual(modifiedExpectedResponse);

    const errorLog = testLogger.warn.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).warn).toContain('The atoExpirationDate is not empty and not a valid date');
  });

  it('Should log a warning and return a null value if the date is not a string and does not format correctly', async () => {
    const modifiedApiResponse = cloneDeep(apiResponse);
    modifiedApiResponse.atoExpirationDate = 8675309;
    const modifiedExpectedResponse = cloneDeep(expectedResponse);
    modifiedExpectedResponse.atoExpirationDate = null;

    mocks.queryView.mockResolvedValueOnce([[modifiedApiResponse], 1]);

    const response = await request(app)
      .get(`/"${validUuid}"`)
      .set('x-jwt-key', 'pass');

    expect(JSON.parse(response.text)).toStrictEqual(modifiedExpectedResponse);

    const errorLog = testLogger.warn.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).warn).toContain('The atoExpirationDate is not a string, is empty, and not is null');
  });
});
