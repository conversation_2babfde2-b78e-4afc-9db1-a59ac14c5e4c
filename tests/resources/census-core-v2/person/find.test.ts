// Generation SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3git a
import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import findConfig from '../../../../src/resources/census-core-v2/person';
import { personFindList, mapLdapUserToPerson } from '../../../../src/resources/census-core-v2/person/find';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import { Person } from '../../../../src/types';

// Setup mocks for LDAP functions
const mocks = vi.hoisted(() => ({
  ldapPersonSearch: vi.fn(),
  ldapPersonSearchById: vi.fn(),
}));

vi.mock('../../../../src/subsystems/ldap', () => ({
  person: mocks.ldapPersonSearch,
  personIds: mocks.ldapPersonSearchById,
}));

// Mock LDAP User data
const mockLdapUsers = [
  {
    dn: 'uid=11111111-**************-************,ou=people,dc=example,dc=com',
    cn: 'Test User',
    mail: '<EMAIL>',
    givenName: 'Test',
    sn: 'User',
    telephoneNumber: '************',
    uid: 'TUSE',
    ismemberof: ['group1', 'group2'],
  },
  {
    dn: 'uid=JDOE,ou=people,dc=example,dc=com',
    cn: 'John Doe',
    mail: '<EMAIL>',
    givenName: 'John',
    sn: 'Doe',
    telephoneNumber: '************',
    uid: 'JDOE',
    ismemberof: ['group3'],
  },
];

const mockLdapUserSingle = mockLdapUsers[0];
const mockResponsePersons: Person[] = mockLdapUsers.map(mapLdapUserToPerson);

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', (
  () => () => findConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

describe('Person Find List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });
  it('Should return a 200 with a list of persons when searching by ID', async () => {
    mocks.ldapPersonSearchById.mockResolvedValueOnce([mockLdapUserSingle]);

    const response = await request(app).get('/')
      .query({
        id: 'TUSE',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Users: [{
        id: 'TUSE',
        userName: 'TUSE',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      }],
    });
    expect(mocks.ldapPersonSearch).not.toHaveBeenCalled();
  });

  it('Should return a 200 with a list of persons when searching by multiple criteria (excluding ID)', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        firstName: 'John',
        lastName: 'Doe',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 2,
      Users: mockResponsePersons,
    });
  });

  it('Should return an empty list when no persons are found', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce([]);

    const response = await request(app).get('/')
      .query({
        firstName: 'NonExistent',
        lastName: 'Person',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      Users: [],
    });
  });

  it('Should verify presence of id parameter', async () => {
    mocks.ldapPersonSearchById.mockResolvedValueOnce([mockLdapUserSingle]);

    const response = await request(app).get('/')
      .query({
        id: 'TUSE',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearchById).toHaveBeenCalledWith(
      expect.anything(),
      'TUSE',
    );
    expect(mocks.ldapPersonSearch).not.toHaveBeenCalled();
  });

  it('Should verify presence of firstName parameter', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        firstName: 'Test',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearch).toHaveBeenCalledWith(
      expect.anything(),
      {
        firstName: 'Test',
      },
    );
  });

  it('Should verify presence of lastName parameter', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        lastName: 'User',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearch).toHaveBeenCalledWith(
      expect.anything(),
      {
        lastName: 'User',
      },
    );
  });

  it('Should verify presence of phone parameter', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        phone: '************',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearch).toHaveBeenCalledWith(
      expect.anything(),
      {
        telephone: '************',
      },
    );
  });

  it('Should verify presence of email parameter', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        email: '<EMAIL>',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearch).toHaveBeenCalledWith(
      expect.anything(),
      {
        email: '<EMAIL>',
      },
    );
  });

  it('Should accept empty string parameters but treat them as not provided', async () => {
    const response = await request(app).get('/')
      .query({
        id: '',
        userName: '',
        firstName: '',
        lastName: '',
        phone: '',
        email: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'At least one search parameter (id, userName, firstName, lastName, phone, email) is required.',
    });
  });

  it('Should prioritize id parameter when multiple parameters are provided', async () => {
    mocks.ldapPersonSearchById.mockResolvedValueOnce([mockLdapUserSingle]);

    const response = await request(app).get('/')
      .query({
        id: 'TUSE',
        firstName: 'Test',
        lastName: 'User',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearchById).toHaveBeenCalledWith(
      expect.anything(),
      'TUSE',
    );
    expect(mocks.ldapPersonSearch).not.toHaveBeenCalled();
  });

  it('Should use all non-id parameters when id is not provided', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(mockLdapUsers);

    const response = await request(app).get('/')
      .query({
        userName: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.ldapPersonSearch).toHaveBeenCalledWith(
      expect.anything(),
      {
        firstName: 'Test',
        lastName: 'User',
        commonName: 'testuser',
        email: '<EMAIL>',
        telephone: '************',
      },
    );
  });

  it('Should return a 400 when no search parameters are provided', async () => {
    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'At least one search parameter (id, userName, firstName, lastName, phone, email) is required.',
    });
  });

  it('Should return a 500 when systemApp is not in the request', async () => {
    const badApp = createBadApp('/', 'get', personFindList);
    const response = await request(badApp).get('/')
      .query({ id: 'anyId' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 when LDAP search by ID returns an error', async () => {
    mocks.ldapPersonSearchById.mockResolvedValueOnce(new Error('LDAP connection failed'));

    const response = await request(app).get('/')
      .query({ id: 'ABCD' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Error querying LDAP: LDAP connection failed',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', {}).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', {}).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', {}).error.message).toContain('LDAP connection failed');
  });

  it('Should return a 500 when general LDAP search returns an error', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce(new Error('LDAP search error'));

    const response = await request(app).get('/')
      .query({ firstName: 'Test' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Error querying LDAP: LDAP search error',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', {}).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', {}).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', {}).error.message).toContain('LDAP search error');
  });

  it('Should return a 500 when LDAP returns unexpected non-array format', async () => {
    mocks.ldapPersonSearch.mockResolvedValueOnce('unexpected string');

    const response = await request(app).get('/')
      .query({ firstName: 'Test' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unexpected LDAP query result format',
    });
  });
});
