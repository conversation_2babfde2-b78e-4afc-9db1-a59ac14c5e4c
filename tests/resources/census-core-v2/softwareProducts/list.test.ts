// Modules.
import request from 'supertest';
import { get } from 'lodash';

// Custom: App.
import routeConfig from 'src/resources/census-core-v2/softwareProducts';
import Messages from 'src/utils/constants/messages';

// Custom: Tests.
import { createBadApp, createUnitTestApp, testLogger } from 'tests/test-utils';
import {
  successfulQuery,
} from 'tests/utils/softwareProductsTestData';
import handler, { SoftwareProductsMessages } from 'src/resources/census-core-v2/softwareProducts/list';
import { beforeEach, vi } from 'vitest';

const mocks = vi.hoisted(() => ({
  consoleError: vi.fn(),
  queryViewTypedFixed: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTypedFixed: mocks.queryViewTypedFixed,
  })),
  getSoftwareProductsList: vi.fn(),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('src/utils/softwareProducts'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSoftwareProductsList: mocks.getSoftwareProductsList,
  };
});

const app = await createUnitTestApp(routeConfig);

describe('Census Software Products Page Find tests', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    vi.spyOn(console, 'error').mockImplementation(mocks.consoleError);
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', handler);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.app_invalid],
    });
    expect(response.status).toEqual(500);
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.db_unavailable],
    });
    expect(response.status).toEqual(500);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain(Messages.db_unavailable);
  });

  it('Should return a 400 with an error if id param is empty', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(successfulQuery);
    const response = await request(app).get('/')
      .query({
        id: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      result: 'error',
      message: [SoftwareProductsMessages.invalid_parameter_system_id],
    });
    expect(response.status).toEqual(400);

    // Validation errors should not be logged as errors since they're user input issues
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).toBeUndefined();
  });

  it('Should return a 200 with an empty object if id param is not a valid UUID', async () => {
    const response = await request(app).get('/')
      .query({
        id: '42fdsfds',
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({});
    expect(response.status).toEqual(200);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).toBeUndefined();
  });

  it('Should return a 500 with an error if getSoftwareProductsList returns an error', async () => {
    mocks.getSoftwareProductsList.mockRejectedValueOnce(
      new Error('Error fetching software products list'),
    );

    const response = await request(app).get('/')
      .query({
        id: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.db_query_view_error],
    });
    expect(response.status).toEqual(500);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Error fetching software products list');
  });

  it('Should return a 200 with an object consisting system, software, and category data', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(successfulQuery);
    const response = await request(app).get('/')
      .query({
        id: '{11111111-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual(successfulQuery);
    expect(response.status).toEqual(200);
  });
});
