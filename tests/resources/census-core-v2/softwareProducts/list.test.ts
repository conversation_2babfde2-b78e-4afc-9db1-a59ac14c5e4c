import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import findConfig from '../../../../src/resources/census-core-v2/softwareProducts';
import listModule from '../../../../src/resources/census-core-v2/softwareProducts/list';
import { createBadApp, createUnitTestApp, testLogger } from '../../../test-utils';
import {
  successfulQuery,
} from '../../../utils/softwareProductsTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  getSoftwareProductsList: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/softwareProducts'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSoftwareProductsList: mocks.getSoftwareProductsList,
  };
});

const app = await createUnitTestApp(findConfig);

describe('Software Products Page Find tests', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 with an error if id param is empty', async () => {
    const response = await request(app).get('/')
      .query({
        id: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'id\'',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Please provide required parameters \'id\'');
  });

  it('Should return a 500 with an error if id param is not a valid UUID', async () => {
    const response = await request(app).get('/')
      .query({
        id: '42fdsfds',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The system ID is not valid',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error.details[0]).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.details[0].message).toContain('"value" must be a valid GUID');
  });

  it('Should return a 500 with an error if getSoftwareProductsList returns an error', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(new Error('Error fetching software products list'));
    const response = await request(app).get('/')
      .query({
        id: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error fetching software products list',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Error fetching software products list');
  });

  it('Should return a 200 with an object consisting system, software, and category data', async () => {
    mocks.getSoftwareProductsList.mockResolvedValueOnce(successfulQuery);
    const response = await request(app).get('/')
      .query({
        id: '{11111111-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(successfulQuery);
  });
});
