// generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import {
  describe,
  it,
  vi,
  expect,
  afterEach,
  beforeEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import businessOwnerFindList, {
  convertDbBusinessOwnerToBusinessOwner,
  DbBusinessOwner,
  BusinessOwner,
} from '../../../../src/resources/census-core-v2/businessOwner/list';
import findConfig from '../../../../src/resources/census-core-v2/businessOwner';
import { createBadApp, createUnitTestApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createUnitTestApp(findConfig);
const mockDbQueryResultSingle: [DbBusinessOwner[], number] = [
  [
    {
      'Sparx System ID': 123,
      'Sparx System GUID': '{11111111-**************-************}',
      'System Name': 'Test Business System',
      Description: 'A system for testing business owner info.',
      Acronym: 'TBS',
      'Object State': 'Active',
      'CMS UUID': '{11111111-**************-************}',
      'CMS Owned': 'Yes',
      'Contractor Owned': 'No',
      'System Cost Per Year': '1000000.50',
      'Number of Contractor Support FTEs': '10',
      'Number of Direct System Users': '1000',
      'Number of Federal Support FTEs': '5',
      'Beneficiary Information': 'Name|Address',
      'Edit Beneficiary Information': 'true',
      'System has UI': 'Yes',
      'Health Disparity Data': 'false',
      'System Ownership': 'Federal',
      'System UI Accessibility': 'WCAG 2.0|Section 508',
    },
  ],
  1,
];

const mockConvertedBusinessOwner: BusinessOwner = convertDbBusinessOwnerToBusinessOwner(
  mockDbQueryResultSingle[0][0],
);

describe('Business Owner Find List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Reset `getDb` mock after each test to ensure fresh mock behavior
    mocks.getDb.mockImplementation(() => ({
      queryView: mocks.queryView,
    }));
  });

  it('Should return a 200 with business owner information for a system', async () => {
    mocks.queryView.mockResolvedValueOnce(mockDbQueryResultSingle);

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(mockConvertedBusinessOwner);
    expect(mocks.queryView).toHaveBeenCalledWith(
      'Sparx_Support.dbo.Sparx_System_BusinessOwner_Census',
      [
        '"Sparx System ID"',
        '"Sparx System GUID"',
        '"System Name"',
        '"Description"',
        '"Acronym"',
        '"Object State"',
        '"CMS UUID"',
        '"CMS Owned"',
        '"Contractor Owned"',
        '"System Cost Per Year"',
        '"Number of Contractor Support FTEs"',
        '"Number of Direct System Users"',
        '"Number of Federal Support FTEs"',
        '"Beneficiary Information"',
        '"Edit Beneficiary Information"',
        '"System has UI"',
        '"Health Disparity Data"',
        '"System Ownership"',
        '"System UI Accessibility"',
      ],
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: '{11111111-**************-************}',
          },
        },
      },
    );
  });

  it('Should return a 400 when id parameter is missing', async () => {
    const response = await request(app).get('/')
      .query({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'ID must be provided',
    });
  });

  it('Should return a 400 when id is not a string', async () => {
    const response = await request(app).get('/')
      .query({ id: undefined }) // Invalid type
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'ID must be provided',
    });
  });

  it('Should return a 500 when systemApp is not in the request', async () => {
    const badApp = createBadApp('/', 'get', businessOwnerFindList);
    const response = await request(badApp).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 when getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 when query returns an error', async () => {
    mocks.queryView.mockImplementationOnce(() => Promise.reject(new Error('Query Error')));

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error querying database',
    });
  });

  it('Should return a 500 when query result is not an array', async () => {
    mocks.queryView.mockResolvedValueOnce(null); // Simulate non-array result

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get result from query',
    });
  });

  it('Should return a 400 when no records are found with the ID', async () => {
    mocks.queryView.mockResolvedValueOnce([[], 0]); // Simulate 0 results

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'No records found with that ID',
    });
  });

  it('Should return a 400 when more than one system is found with the ID', async () => {
    const mockMultiQueryResult: [DbBusinessOwner[], number] = [
      [
        mockDbQueryResultSingle[0][0],
        {
          ...mockDbQueryResultSingle[0][0],
          'Sparx System GUID': '{00000000-0000-0000-0000-000000000000}', // Different GUID for second entry
        },
      ],
      2,
    ];
    mocks.queryView.mockResolvedValueOnce(mockMultiQueryResult);

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'More than one system found with that ID',
    });
  });

  it('Should return a 500 when a query view error occurs', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error querying database',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Query View Error');
  });

  it('Should return a 500 when a critical query view error occurs', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app).get('/')
      .query({ id: '{11111111-**************-************}' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error querying database',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Critical Query View Error');
  });
});
