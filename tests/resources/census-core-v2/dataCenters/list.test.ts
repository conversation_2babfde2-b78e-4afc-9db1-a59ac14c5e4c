import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import listConfig from '../../../../src/resources/census-core-v2/dataCenters';
import listModule from '../../../../src/resources/census-core-v2/dataCenters/list';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import { formattedDeploymentSelectedQuery } from '../../../utils/deploymentListTestData';

const mocks = vi.hoisted(() => ({
  getDb: vi.fn(),
  deploymentListUtil: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/deployments'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    deploymentListUtil: mocks.deploymentListUtil,
  };
});

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', (
  () => () => listConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

const systemId = '{11111111-1111-1111-1111-111111111111}';

describe('Data Centers Page Find tests', () => {
  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 400 with an error if systemId param is empty', async () => {
    const response = await request(app).get('/')
      .query({
        systemId: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'systemId\'',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Please provide required parameters \'systemId\'');
  });

  it('Should return a 400 with an error if the state param is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        systemId,
        state: { totally: 'random' },
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: '\'state\' must be a string',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('\'state\' must be a string');
  });

  it('Should return a 400 with an error if the status param is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        systemId,
        status: { totally: 'random' },
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: '\'status\' must be a string',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('\'status\' must be a string');
  });

  it('Should return a 400 with an error if the type param is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        systemId,
        type: { totally: 'random' },
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: '\'type\' must be a string',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('\'type\' must be a string');
  });

  it('Should return a 500 with an error if deploymentListUtil returns an error', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce(new Error('There was an error fetching the data centers'));
    const response = await request(app).get('/')
      .query({
        systemId: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an error fetching the data centers',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('There was an error fetching the data centers');
  });

  it('Should return a 200 with an object consisting system, software, and category data', async () => {
    mocks.deploymentListUtil.mockResolvedValueOnce(formattedDeploymentSelectedQuery);
    const response = await request(app).get('/')
      .query({
        systemId: '{11111111-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(formattedDeploymentSelectedQuery);
  });
});
