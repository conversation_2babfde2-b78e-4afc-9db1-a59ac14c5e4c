// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import request from 'supertest';

// Custom.
import routeConfig from '../../../../src/resources/census-core-v2/systemProperty/index';
import handler, { SystemPropertyMessages, TABLE_NAME } from '../../../../src/resources/census-core-v2/systemProperty/find';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import createFailureTests from '../../../shared/endpoints';
import { CMSApp } from '../../../../src/types';

const mocks = vi.hoisted(() => ({
  consoleError: vi.fn(),
  queryViewTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTyped: mocks.queryViewTyped,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const mockSystemId = '11111111-**************-************';
const mockPropertyNameValid = 'CMS UUID';
const mockPropertyValueValid = '{*************-8888-9999-000000000000}';
const mockPropertyNameInvalid = 'AI_ENABLED';

const mockDbQueryResultValid = [
  [{
    [mockPropertyNameValid]: mockPropertyValueValid,
  }],
  1, // row count
];

let app: CMSApp;

const defaultRequest = async (override: object = {}) => {
  const query = {
    systemId: mockSystemId,
    propertyName: mockPropertyNameValid,
    ...override,
  };

  return request(app)
    .get('/')
    .query(query)
    .set('x-jwt-key', 'pass');
};

describe('Census System Property Find Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);
  });

  it('sends 200 when valid system property is found', async () => {
    mocks.queryViewTyped.mockResolvedValueOnce(mockDbQueryResultValid);

    const response = await defaultRequest();

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      propertyValue: mockPropertyValueValid,
    });
    expect(mocks.queryViewTyped).toHaveBeenCalledWith(
      TABLE_NAME,
      [`[${mockPropertyNameValid}]`],
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: mockSystemId,
          },
        },
      },
    );
  });

  it('sends 400 when systemId parameter is missing', async () => {
    const response = await defaultRequest({ systemId: undefined });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.invalid_parameter_system_id],
    });
  });

  it('sends 400 when propertyName parameter is missing', async () => {
    const response = await defaultRequest({ propertyName: undefined });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.invalid_parameter_property_name],
    });
  });

  it('sends 400 when systemId is not a string', async () => {
    const response = await defaultRequest({ systemId: ['bad', 'value'] });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.invalid_parameter_system_id],
    });
  });

  it('sends 400 when propertyName is not a string', async () => {
    const response = await defaultRequest({ propertyName: ['bad', 'value'] });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.invalid_parameter_property_name],
    });
  });

  it('sends 500 when valid system property is not found', async () => {
    mocks.queryViewTyped.mockResolvedValueOnce([[], 0]);

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.property_not_found],
    });
  });

  it('sends 500 when propertyName is invalid', async () => {
    const response = await defaultRequest({ propertyName: mockPropertyNameInvalid });

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [SystemPropertyMessages.invalid_property_name_value],
    });
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
    ),
  );
});
