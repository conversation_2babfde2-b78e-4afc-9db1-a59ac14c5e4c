import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import request from 'supertest';
import listModule from '../../../../src/resources/census-core-v2/urls/find';
import urlConfig from '../../../../src/resources/census-core-v2/urls';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  censusUrlsUtil: vi.fn(),
  getDb: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/urls'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    censusUrlsUtil: mocks.censusUrlsUtil,
  };
});

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', (
  () => () => urlConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

const systemId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';

const fullProcessedResults = [{
  urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'webmethods-apiportal.cedar.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'API Gateway/API Portal',
},
{
  urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'www.cedarimpl.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'Implementation',
}];

describe('URL List tests', () => {
  it('Should return a 200 with a list of all URLs with the systemId', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce(fullProcessedResults);
    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 2,
      pageName: 'Urls',
      Urls: fullProcessedResults,
    });
  });

  it('Should return a 200 and count: 0 if no URLs are found', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce([]);
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{113BE457-783E-413e-99A2-FB52C55A1616}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      pageName: 'Urls',
      Urls: [],
    });
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', listModule);
    const response = await request(badApp)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 400 and an error if systemId is not present', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId: '',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'systemId\'',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Please provide required parameters \'systemId\'');
  });

  it('Should return a 400 and an error if systemId is an invalid GUID', async () => {
    const response = await request(app)
      .get('/')
      .query({
        systemId: '/{1234-bad2-uuid-and3-1234}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The system ID is not valid',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
  });

  it('Should return a 500 with an error if the util was not successful', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce(new Error('Util Error'));
    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body.error).toEqual('There was an error fetching the URLs page');
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Util Error');
  });
});
