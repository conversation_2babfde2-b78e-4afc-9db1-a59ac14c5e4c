import { get } from 'lodash';
import request from 'supertest';
import handler from 'src/resources/census-core-v2/urls/find';
import routeConfig from 'src/resources/census-core-v2/urls';
import { createBadApp, createUnitTestApp, testLogger } from 'tests/test-utils';

const mocks = vi.hoisted(() => ({
  censusUrlsUtil: vi.fn(),
  getDb: vi.fn(),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('src/utils/urls'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    censusUrlsUtil: mocks.censusUrlsUtil,
  };
});

// Create the app for testing.
const app = await createUnitTestApp(routeConfig);
const systemId = '{11111111-2222-3333-4444-555555555555}';

// noinspection SpellCheckingInspection
const fullProcessedResults = [{
  urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'webmethods-apiportal.cedar.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'API Gateway/API Portal',
},
{
  urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'www.cedarimpl.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'Implementation',
}];

describe('Census URL Find Tests', () => {
  it('Should return a 200 with a list of all URLs with the systemId', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce(fullProcessedResults);
    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: '2',
      Urls: fullProcessedResults,
    });
  });

  it('Should return a 200 and count: 0 if no URLs are found', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce([]);
    const response = await request(app)
      .get('/')
      .query({
        systemId: '{113BE457-783E-413e-99A2-FB52C55A1616}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      pageName: 'Urls',
    });
  });

  it('Should return a 500 with an error if app is not in the req', async () => {
    const badApp = createBadApp('/', 'get', handler);
    const response = await request(badApp)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return a 200 and an empty list if systemId is not present', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce([]);
    const response = await request(app)
      .get('/')
      .query({
        systemId: '',
      })
      .set('x-jwt-key', 'pass');
    expect(response.body).toEqual({
      count: 0,
      pageName: 'Urls',
    });
    expect(response.status).toEqual(200);
  });

  it('Should return a 200 and an empty list if systemId is an invalid GUID', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce([]);
    const response = await request(app)
      .get('/')
      .query({
        systemId: '/{1234-bad2-uuid-and3-1234}',
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      pageName: 'Urls',
    });
  });

  it('Should return a 500 with an error if the util was not successful', async () => {
    mocks.censusUrlsUtil.mockResolvedValueOnce(new Error('Util Error'));
    const response = await request(app)
      .get('/')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');
    expect(response.status).toEqual(500);
    expect(response.body.error).toEqual('There was an error fetching the URLs page');
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Util Error');
  });
});
