// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import request from 'supertest';

// Custom.
import { afterEach, vi } from 'vitest';
import handler, { checkForUnexpectedKeys } from '../../../../src/resources/census-core-v2/systemMaintainer/find';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import createFailureTests from '../../../shared/endpoints';
import routeConfig from '../../../../src/resources/census-core-v2/systemMaintainer';
import { SparxSupportViews } from '../../../../src/utils/constants/views';
import Messages from '../../../../src/utils/constants/messages';
import { CMSApp } from '../../../../src/types';
import {
  DbSystemMaintainer,
  DbSystemRecordsManagementBucket,
  SystemMaintainerResponse,
  SystemMaintainerMessages,
} from '../../../../src/resources/census-core-v2/systemMaintainer/types/find';

const mocks = vi.hoisted(() => ({
  queryViewTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTyped: mocks.queryViewTyped,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// noinspection SpellCheckingInspection
const mockDbSystemMaintainer = {
  'System ID': 'SYSTEM001',
  'Sparx System ID': 'SSID001',
  'Sparx System GUID': '{11111111-**************-************}',
  'System Name': 'Test System',
  'System Customization': 'Yes',
  'Front End Access Type': 'Web',
  'CMS System Access': 'Public',
  'IP Enabled Asset Count': '10',
  'Percent IPV6': '50%',
  'Long Term IPV6 Plan': 'Planned for Q4 2024',
  'Start Date': '01/15/2020',
  'Development Work Still Underway': 'Feature X development',
  'Agile Methodology Use': 'true',
  'Deployment Frequency': 'Monthly',
  'Deplolyment AdHoc Frequency': 'Weekly',
  'Last Major Tech Refresh Date': '03/01/2023',
  'No Major Refresh Flag': 'false',
  'Next Major Tech Refresh Date': '03/01/2025',
  'No Planned Major Refresh Flag': 'false',
  'Retire or Replace': 'No',
  'Retire or Replace Date': null,
  'Planned Retirement Quarter': null,
  'Business Artifacts on Demand': 'Yes',
  'Business Artifact Location': 'SharePoint',
  'Requirements on Demand': 'Yes',
  'Requirements Location': 'Jira',
  'Design on Demand': 'Yes',
  'System Design Location': 'Confluence',
  'Source Code on Demand': 'Yes',
  'Souce Code Location': 'Git',
  'Test Plan on Demand': 'Yes',
  'Test Plan Location': 'TestLink',
  'Test Script on Demand': 'Yes',
  'Test Script Location': 'Selenium',
  'Test Reports on Demand': 'Yes',
  'Test Report Location': 'Confluence',
  'Ops and Maintenance Plans on Demand': 'Yes',
  'Ops and Maintenance Plan Location': 'ServiceNow',
  'No Persistent Records Flag': 'No',
  'Records Management Record Type Identification': 'Yes',
  'Metadata Glossary': 'Yes',
  'System Data Authoritative Source': 'Internal DB',
  'System Data Location': 'On-Prem|Cloud',
  'System Data Location Notes': 'Sensitive data stored encrypted',
  'Centralized Data Catalog': 'Yes',
  'EDL Plan': 'Phase 1 Complete',
  'Identity Management Solution': 'LDAP|OAuth',
  'Identity Management Solution Other': 'Custom SSO',
  'Locally Stored User Info': 'false',
  'MFA Method': 'TOTP|Smartcard',
  'MFA Other': 'Biometric',
  'Network Traffic Encryption Management': 'KMS',
  'Data At Rest Encryption Management': 'HSM',
  'Records Under Legal Hold': 'No',
  'Legal Hold Case Name': null,
  'Records Management Approved Schedule': 'Yes',
  'Records Management Disposal Plan': 'Yes',
  'Records Management Disposal Location': 'Archive',
  'Records Management Format': 'PDF\nXML',
  'CMS Owned': 'true',
  'Hard Coded IP Address': 'false',
  Version: '2.0',
} as unknown as DbSystemMaintainer;

const mockDbRecordsManagementBucket = {
  'Sparx System GUID': '{11111111-**************-************}',
  'Records Management Bucket': 'Bucket1|Bucket2',
} as unknown as DbSystemRecordsManagementBucket;

const mockQueryResultMaintainer: [DbSystemMaintainer[], number] = [
  [mockDbSystemMaintainer],
  1,
];

const mockQueryResultRecordsBucket: [DbSystemRecordsManagementBucket[], number] = [
  [mockDbRecordsManagementBucket],
  1,
];

// noinspection SpellCheckingInspection
const expectedApiResponse: SystemMaintainerResponse = {
  id: '{11111111-**************-************}',
  version: '2.0',
  pageName: 'SystemMaintainerBasicInfo',
  name: 'Test System',
  systemCustomization: 'Yes',
  frontendAccessType: 'Web',
  netAccessibility: 'Public',
  ipEnabledAssetCount: 10,
  ip6EnabledAssetPercent: '50%',
  ip6TransitionPlan: 'Planned for Q4 2024',
  systemProductionDate: '2020-01-15',
  devWorkDescription: 'Feature X development',
  agileUsed: true,
  deploymentFrequency: 'Monthly',
  adHocAgileDeploymentFrequency: 'Weekly',
  majorRefreshDate: '2023-03-01',
  noMajorRefresh: false,
  nextMajorRefreshDate: '03/01/2025',
  noPlannedMajorRefresh: false,
  plansToRetireReplace: 'No',
  yearToRetireReplace: null,
  quarterToRetireReplace: null,
  businessArtifactsOnDemand: true,
  businessArtifactsLocation: 'SharePoint',
  systemRequirementsOnDemand: true,
  systemRequirementsLocation: 'Jira',
  systemDesignOnDemand: true,
  systemDesignLocation: 'Confluence',
  sourceCodeOnDemand: true,
  sourceCodeLoction: 'Git',
  testPlanOnDemand: true,
  testPlanLocation: 'TestLink',
  testScriptsOnDemand: true,
  testScriptsLocation: 'Selenium',
  testReportsOnDemand: true,
  testReportsLocation: 'Confluence',
  omDocumentationOnDemand: true,
  omDocumentationLocation: 'ServiceNow',
  noPersistentRecordsFlag: false,
  recordsManagementBucket: ['Bucket1', 'Bucket2'],
  recordsManagementRecordTypeId: true,
  hasMetadataGlossary: true,
  authoritativeDatasource: 'Internal DB',
  systemDataLocation: ['On-Prem', 'Cloud'],
  systemDataLocationNotes: 'Sensitive data stored encrypted',
  storeInCentralDataCatalog: true,
  haveEnterpriseDataLakePlan: 'Phase 1 Complete',
  identityManagementSolution: ['LDAP', 'OAuth'],
  identityManagementSolutionOther: 'Custom SSO',
  locallyStoredUserInformation: false,
  multifactorAuthenticationMethod: ['TOTP', 'Smartcard'],
  multifactorAuthenticationMethodOther: 'Biometric',
  networkTrafficEncryptionKeyManagement: 'KMS',
  dataAtRestEncryptionKeyManagement: 'HSM',
  recordsUnderLegalHold: false,
  legalHoldCaseName: null,
  isRecordManagementScheduleApproved: true,
  anotherCMSsystem: true,
};

let app: CMSApp;

const defaultRequest = async () => request(app)
  .get('/')
  .query({
    id: '{11111111-**************-************}',
  })
  .set('x-jwt-key', 'pass');

describe('System Maintainer Find Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('sends 200 with system maintainer information', async () => {
    mocks.queryViewTyped
      .mockResolvedValueOnce(mockQueryResultMaintainer)
      .mockResolvedValueOnce(mockQueryResultRecordsBucket);

    const response = await defaultRequest();

    expect(response.body).toEqual(expectedApiResponse);
    expect(response.status).toEqual(200);
    expect(mocks.queryViewTyped).toHaveBeenCalledTimes(2);
    expect(mocks.queryViewTyped).toHaveBeenNthCalledWith(
      1,
      SparxSupportViews.Sparx_System,
      expect.any(Array),
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: '{11111111-**************-************}',
          },
        },
      },
    );
    expect(mocks.queryViewTyped).toHaveBeenNthCalledWith(
      2,
      SparxSupportViews.Sparx_System_RecordsManagementBucket_Census,
      expect.any(Array),
      {
        where: {
          operation: {
            column: 'Sparx System GUID',
            operator: '=',
            value: '{11111111-**************-************}',
          },
        },
      },
    );
  });

  it('sends 200 when null values are returned from the database', async () => {
    // noinspection SpellCheckingInspection
    const sparseDbResult = {
      'System ID': 'SYSTEM002',
      'Sparx System ID': 'SSID002',
      'Sparx System GUID': '{11111111-**************-************}',
      'System Name': 'Sparse Test System',
      'System Customization': null,
      'Front End Access Type': null,
      'CMS System Access': null,
      'IP Enabled Asset Count': null,
      'Percent IPV6': null,
      'Long Term IPV6 Plan': null,
      'Start Date': null,
      'Development Work Still Underway': null,
      'Agile Methodology Use': null,
      'Deployment Frequency': null,
      'Deplolyment AdHoc Frequency': null,
      'Last Major Tech Refresh Date': null,
      'No Major Refresh Flag': null,
      'Next Major Tech Refresh Date': null,
      'No Planned Major Refresh Flag': null,
      'Retire or Replace': null,
      'Retire or Replace Date': null,
      'Planned Retirement Quarter': null,
      'Business Artifacts on Demand': null,
      'Business Artifact Location': null,
      'Requirements on Demand': null,
      'Requirements Location': null,
      'Design on Demand': null,
      'System Design Location': null,
      'Source Code on Demand': null,
      'Souce Code Location': null,
      'Test Plan on Demand': null,
      'Test Plan Location': null,
      'Test Script on Demand': null,
      'Test Script Location': null,
      'Test Reports on Demand': null,
      'Test Report Location': null,
      'Ops and Maintenance Plans on Demand': null,
      'Ops and Maintenance Plan Location': null,
      'No Persistent Records Flag': null,
      'Records Management Record Type Identification': null,
      'Metadata Glossary': null,
      'System Data Authoritative Source': null,
      'System Data Location': null,
      'System Data Location Notes': null,
      'Centralized Data Catalog': null,
      'EDL Plan': null,
      'Identity Management Solution': null,
      'Identity Management Solution Other': null,
      'Locally Stored User Info': null,
      'MFA Method': null,
      'MFA Other': null,
      'Network Traffic Encryption Management': null,
      'Data At Rest Encryption Management': null,
      'Records Under Legal Hold': null,
      'Legal Hold Case Name': null,
      'Records Management Approved Schedule': null,
      'Records Management Disposal Plan': null,
      'Records Management Disposal Location': null,
      'Records Management Format': null,
      'CMS Owned': null,
      'Hard Coded IP Address': null,
      Version: null,
    } as unknown as DbSystemMaintainer;

    const sparseRecordsBucket = {
      'Sparx System GUID': '{11111111-**************-************}',
      'Records Management Bucket': null,
    } as unknown as DbSystemRecordsManagementBucket;

    mocks.queryViewTyped
      .mockResolvedValueOnce([[sparseDbResult], 1])
      .mockResolvedValueOnce([[sparseRecordsBucket], 1]);

    const response = await defaultRequest();

    expect(response.body.id).toEqual('{11111111-**************-************}');
    expect(response.body.name).toEqual('Sparse Test System');
    expect(response.body.version).toBeNull();
    expect(response.body.systemCustomization).toBeNull();
    expect(response.body.ipEnabledAssetCount).toBeNull();
    expect(response.body.agileUsed).toBeNull();
    expect(response.body.recordsManagementBucket).toBeNull();
    expect(response.status).toEqual(200);
  });

  it('swagger docs can be imported', async () => {
    const swaggerModule = await import('../../../../src/resources/census-core-v2/systemMaintainer/swagger/find');
    expect(swaggerModule).toBeDefined();
  });

  it('sends 400 when id parameter is missing', async () => {
    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      message: [SystemMaintainerMessages.invalid_parameter_system_id],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 400 when id is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        id: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.body).toEqual({
      message: [SystemMaintainerMessages.invalid_parameter_system_id],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 400 when system maintainer information not found', async () => {
    mocks.queryViewTyped
      .mockResolvedValueOnce([[], 0])
      .mockResolvedValueOnce([[], 0]);

    const response = await defaultRequest();

    expect(response.body).toEqual({
      message: ['System maintainer information not found'],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 400 when records management bucket not found', async () => {
    mocks.queryViewTyped
      .mockResolvedValueOnce(mockQueryResultMaintainer)
      .mockResolvedValueOnce([[], 0]);

    const response = await defaultRequest();

    expect(response.body).toEqual({
      message: ['System maintainer information not found'],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 400 when record counts do not match', async () => {
    mocks.queryViewTyped
      .mockResolvedValueOnce(mockQueryResultMaintainer)
      .mockResolvedValueOnce([[mockDbRecordsManagementBucket, mockDbRecordsManagementBucket], 2]);

    const response = await defaultRequest();

    expect(response.body).toEqual({
      message: ['Maintainer and records management bucket record counts do not match'],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 500 when query returns an error', async () => {
    mocks.queryViewTyped.mockRejectedValueOnce(new Error('Query Error'));

    const response = await defaultRequest();

    expect(response.body).toEqual({
      message: [Messages.db_query_view_error],
    });
    expect(response.status).toEqual(500);
  });

  it('sends 500 when database record contains unexpected keys', async () => {
    const mockWithUnexpectedKey = {
      ...mockDbSystemMaintainer,
      'Unexpected Key': 'This should not be here',
    } as DbSystemMaintainer;

    mocks.queryViewTyped
      .mockResolvedValueOnce([[mockWithUnexpectedKey], 1])
      .mockResolvedValueOnce(mockQueryResultRecordsBucket);

    const response = await defaultRequest();

    expect(response.body).toEqual({
      message: [Messages.internal_server_error],
    });
    expect(response.status).toEqual(500);
  });

  it('sends 500 when checkForUnexpectedKeys fails', () => {
    expect(() => {
      checkForUnexpectedKeys(
        { one: 'one' },
        { two: 'two' },
        'UnknownType',
      );
    }).toThrow('Key "one" is not a valid key for UnknownType');
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
      [
        { [Messages.db_query_view_error]: mocks.queryViewTyped },
      ],
    ),
  );
});
