// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import {
  describe,
  it,
  vi,
  expect,
  afterEach,
  beforeEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';

// Custom.
import { createUnitTestApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import routeConfig from '../../../../src/resources/census-core-v2/systems';
import createFailureTests from '../../../shared/endpoints';
import handler, { DB_PROC_NAME, SystemsListItem } from '../../../../src/resources/census-core-v2/systems/find';
import { CMSApp } from '../../../../src/types';

// Setup mocks.
const mocks = vi.hoisted(() => ({
  queryStoredProceduresTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProceduresTyped: mocks.queryStoredProceduresTyped,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// noinspection SpellCheckingInspection
const mockSystemsList : SystemsListItem[] = [
  {
    id: '11111111-**************-************',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '11111111-**************-************',
    uuid: '',
    name: 'Test System 1',
    description: 'Desc 1',
    version: '1.0',
    acronym: 'TS1',
    objectState: 'Active',
    status: 'Operational',
    belongsTo: '',
    businessOwnerOrg: 'OrgA',
    businessOwnerOrgComp: 'Org A Comp',
    systemMaintainerOrg: 'OrgM',
    systemMaintainerOrgComp: 'Org M Comp',
    qaReviewerAssignmentId: '*************-9999-AAAA-BBBBBBBBBBBB',
    qaReviewerFirstName: 'QA First',
    qaReviewerLastName: 'QA Last',
    qaReviewerUserName: 'QALAST',
    daReviewerAssignmentId: 'CCCCCCCC-DDDD-EEEE-FFFF-111111111111',
    daReviewerFirstName: 'DA First',
    daReviewerLastName: 'DA Last',
    daReviewerUserName: 'DALAST',
    censusStatus: 'Complete',
    percentComplete: '0%',
  },
  {
    id: '*************-5555-6666-************',
    nextVersionId: '',
    previousVersionId: '',
    ictObjectId: '*************-5555-6666-************',
    uuid: '',
    name: 'Test System 2',
    description: 'Desc 2',
    version: '2.0',
    acronym: 'TS2',
    objectState: 'Active',
    status: 'Development',
    belongsTo: '',
    businessOwnerOrg: 'OrgB',
    businessOwnerOrgComp: 'Org B Comp',
    systemMaintainerOrg: 'OrgN',
    systemMaintainerOrgComp: 'Org N Comp',
    qaReviewerAssignmentId: '',
    qaReviewerFirstName: '',
    qaReviewerLastName: '',
    qaReviewerUserName: '',
    daReviewerAssignmentId: '',
    daReviewerFirstName: '',
    daReviewerLastName: '',
    daReviewerUserName: '',
    censusStatus: 'In Progress',
    percentComplete: '50%',
  },
];

const mockExpectedResponse = {
  pageName: 'SystemsList',
  count: 2,
  SystemsList: mockSystemsList,
};

const mockRawQueryResult = [
  [{
    result: JSON.stringify({
      ResultSet: mockSystemsList,
    }),
  }],
];

let app: CMSApp;

const defaultRequest = () => request(app).get('/').set('x-jwt-key', 'pass');

describe('Census Systems List Find Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 200 with a list of systems when query is successful', async () => {
    mocks.queryStoredProceduresTyped.mockResolvedValueOnce(mockRawQueryResult);

    const response = await defaultRequest();

    expect(response.status).toEqual(200);
    expect(response.body).toEqual(mockExpectedResponse);

    expect(mocks.queryStoredProceduresTyped).toHaveBeenCalledWith(
      DB_PROC_NAME,
      [{ name: 'result', type: 'nvarchar', param: 'max' }],
      [{ name: 'OutputJson', value: 'result', isOutput: true }],
      [{ resultKey: 'result', paramName: 'result' }],
    );
  });

  it('Should return a 500 when stored procedure query returns an error', async () => {
    mocks.queryStoredProceduresTyped.mockRejectedValueOnce(new Error('SP Query Error'));

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_stored_procedure_error],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('SP Query Error');
  });

  it('Should return a 500 when stored procedure query result is not a string (raw JSON)', async () => {
    mocks.queryStoredProceduresTyped.mockResolvedValueOnce([[{ result: null }]]);

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_result_missing],
    });
  });

  it('Should return a 500 when parsing JSON from stored procedure result fails', async () => {
    mocks.queryStoredProceduresTyped.mockResolvedValueOnce([[{ result: 'this is not valid json' }]]);

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.sp_json_parse_error],
    });
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
    ),
  );
});
