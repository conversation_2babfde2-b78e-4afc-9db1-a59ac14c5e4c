import {
  describe,
  it,
  vi,
  expect,
  afterEach,
  beforeEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import dataExchangeFind from '../../../../src/resources/census-core-v2/dataExchange/find';
import Messages from '../../../../src/utils/constants/messages';
import Databases from '../../../../src/utils/constants/databases';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// Mock the main resource configuration for dataExchange
const testDataExchangeResourceConfig = () => ({
  resourceConfig: [{
    name: 'dataExchangeFind',
    path: '/', // The base path for this resource
    method: 'GET',
    resource: dataExchangeFind,
    public: false,
  }],
});

const app = await createApp();
app.resources.register(app, '/', (() => () => testDataExchangeResourceConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);


describe('Data Exchange Find List tests', () => {
  const SYSTEM_ID = '11111111-**************-************';
  const OTHER_SYSTEM_ID = '11111111-**************-************'; // Using same mock UUID as per guideline
  const EXCHANGE_ID = '11111111-**************-************'; // Using same mock UUID as per guideline

  const mockDbExchange = (direction: 'sender' | 'receiver' = 'sender') => ({
    'Sender ID': direction === 'sender' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    'Sender Name': direction === 'sender' ? 'System A' : 'System B',
    'Sparx Sendor GUID': direction === 'sender' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    'Sender Type': 'Application',
    'Connection Name': 'Sample Exchange',
    'Connector ID': '1',
    'Connection GUID': EXCHANGE_ID,
    'Receiver ID': direction === 'receiver' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    'Receiver Name': direction === 'receiver' ? 'System A' : 'System B',
    'Sparx Receiver GUID': direction === 'receiver' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    'Receiver Type': 'Application',
    'Exchange Description': 'Description of sample exchange',
    'Exchange Format': 'JSON',
    'Exchange Format Other': null,
    'Exchange Contains PHI': 'false',
    'Exchange Contains PII': 'true',
    'Exchange Frequency': 'Daily',
    'Exchange Includes Banking Data': 'false',
    'Exchange includes Beneficiary Address Data': 'false',
    'Exchange Supports Mailing to Beneficiaries': 'false',
    'IE Agreement': 'MOU',
    'Number of Records Exchanged': '1000',
    'Data Shared via API': 'true',
    'Connection Direction': 'Both',
    'Object State': 'Active',
    'Retire Date': null,
    'Address Data Editable': 'false',
    'Contains Health Disparity Data': 'false',
    'API Ownership': 'Internal',
    'Type of Data': 'Patient Demographics',
    'Type of Data ID': 'TD1',
    'Exchange Start Date': '2023-01-01',
    'Exchange End Date': '2023-12-31',
    'Exchange Version': '1',
    'Beneficiary Address Purpose': 'Enrollment',
    'Exchange Connection Authenticated': 'true',
    'Exchange Contains CUI': 'false',
    'Exchange CUI Description': null,
    'Exchange Network Protocol': 'HTTPS',
    'Exchange Network Protocol Other': null,
    'Exchange CUI Type': 'Controlled',
  });

  const mockDbStatus = (direction: 'sender' | 'receiver' = 'receiver', isNew = false) => ({
    SYSTEM_SURVEY_EXCHANGE_STATUS_ID: 1,
    EXCHANGE_ID: EXCHANGE_ID,
    NEW_EXCHANGE_FLAG: isNew,
    ORIGINAL_RECEIVER_ID: direction === 'receiver' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    ORIGINAL_SENDER_ID: direction === 'sender' ? SYSTEM_ID : OTHER_SYSTEM_ID,
    RECEIVER_REPORTED_DELETE_FLAG: false,
    SENDER_REPORTED_DELETE_FLAG: false,
    RECEIVER_STATUS: 'Completed',
    RECEIVER_LAST_SUBMIT_DATETIME: '2024-01-01T10:00:00Z',
    SENDER_STATUS: 'Completed',
    SENDER_LAST_SUBMIT_DATETIME: '2024-01-01T10:00:00Z',
    RECEIVER_QA_STATUS: 'Reviewed',
    SENDER_QA_STATUS: 'Reviewed',
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 200 with data exchanges and statuses when found for systemId as receiver', async () => {
    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[mockDbExchange('receiver')], 1])) // Mock getExchangeList
      .mockImplementationOnce(() => Promise.resolve([[mockDbStatus('receiver')], 1])); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.result).toEqual('success');
    expect(response.body.message).toEqual(['Successfully retrieved data exchange information']);
    expect(response.body.systemId).toEqual(SYSTEM_ID);
    expect(response.body.pageName).toEqual('DataExchange');
    expect(response.body.count).toEqual(1);
    expect(response.body.DataExchanges).toHaveLength(1);
    expect(response.body.DataExchanges[0].direction).toEqual('receiver');
    expect(response.body.DataExchanges[0].deleted).toEqual(false);
    expect(response.body.DataExchanges[0].isNewExchange).toEqual(false);
    expect(response.body.DataExchanges[0].Exchange.exchangeId).toEqual(EXCHANGE_ID);
    expect(response.body.DataExchanges[0].Status.exchangeId).toEqual(EXCHANGE_ID);
    expect(mocks.getDb).toHaveBeenCalledWith(expect.any(Object), Databases.sparxSupport);
    expect(mocks.getDb).toHaveBeenCalledWith(expect.any(Object), Databases.cedarSupport);
  });

  it('Should return a 200 with data exchanges and statuses when found for systemId as sender', async () => {
    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[mockDbExchange('sender')], 1])) // Mock getExchangeList
      .mockImplementationOnce(() => Promise.resolve([[mockDbStatus('sender')], 1])); // Mock getPageDataExchangeStatusList

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.DataExchanges[0].direction).toEqual('sender');
    expect(response.body.DataExchanges[0].Exchange.fromOwnerId).toEqual(SYSTEM_ID);
    expect(response.body.DataExchanges[0].Status.systemId).toEqual(SYSTEM_ID);
  });

  it('Should return a 200 with multiple data exchanges and statuses', async () => {
    const mockDbExchange2 = {
      ...mockDbExchange('receiver'),
      'Connection GUID': '11111111-**************-************', // Updated mock UUID
      'Connection Name': 'Another Exchange',
      'Sparx Sendor GUID': OTHER_SYSTEM_ID,
      'Sparx Receiver GUID': SYSTEM_ID,
    };
    const mockDbStatus2 = {
      ...mockDbStatus('receiver', true), // Set isNew to true
      EXCHANGE_ID: '11111111-**************-************', // Updated mock UUID
    };

    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[mockDbExchange('sender'), mockDbExchange2], 2]))
      .mockImplementationOnce(() => Promise.resolve([[mockDbStatus('sender'), mockDbStatus2], 2]));

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(2);
    expect(response.body.DataExchanges).toHaveLength(2);
    expect(response.body.DataExchanges[0].direction).toEqual('sender');
    expect(response.body.DataExchanges[1].direction).toEqual('receiver');
    expect(response.body.DataExchanges[1].isNewExchange).toEqual(true);
  });

  it('Should return an empty list when no data exchanges are found', async () => {
    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[], 0])) // No exchanges
      .mockImplementationOnce(() => Promise.resolve([[], 0])); // No statuses

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(0);
    expect(response.body.DataExchanges).toEqual([]);
  });

  it('Should return an empty list when exchanges are found but no matching statuses', async () => {
    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[mockDbExchange('sender')], 1]))
      .mockImplementationOnce(() => Promise.resolve([[], 0])); // No statuses

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body.count).toEqual(1);
    expect(response.body.DataExchanges).toHaveLength(1);
    expect(response.body.DataExchanges[0].direction).toBeNull(); // No matching status, so direction is null
    expect(response.body.DataExchanges[0].Status.exchangeId).toEqual(EXCHANGE_ID); // Exchange ID still present
    expect(response.body.DataExchanges[0].Status.systemId).toEqual(SYSTEM_ID); // System ID copied over
  });

  it('Should return a 400 when systemId is missing', async () => {
    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ["'systemId' must be provided"],
    });
  });

  it('Should return a 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'get', dataExchangeFind);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.app_invalid],
    });
  });

  it('Should return a 500 when fetching exchanges fails (DB unavailable)', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('DB connection failed'));

    const response = await request(app).get('/')
      .query({ systemId: SYSTEM_ID })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Error fetching data exchanges'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('DB connection failed');
  });

  it('Should return a 500 when fetching statuses fails (DB unavailable)', async () => {
    mocks.queryView.mockImplementationOnce(() => Promise.resolve([[mockDbExchange('sender')], 1])); // Exchange query succeeds
    mocks.getDb.mockImplementationOnce(() => new Error('DB connection failed for statuses')); // Status DB fails

    const response = await request(app).get('/')
      .query({ systemId: SYSTEM_ID })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Error fetching data exchange statuses'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('DB connection failed for statuses');
  });


  it('Should return a 500 when an unhandled exception occurs', async () => {
    mocks.queryView.mockImplementationOnce(() => {
      throw new Error('Unexpected error');
    });

    const response = await request(app).get('/')
      .query({ systemId: SYSTEM_ID })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Internal server error'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = get(errorLog, '[0]', []);
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
  });

  it('Should handle version parameter for exchange query', async () => {
    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[mockDbExchange('sender')], 1]))
      .mockImplementationOnce(() => Promise.resolve([[mockDbStatus('sender')], 1]));

    const response = await request(app).get('/')
      .query({
        systemId: SYSTEM_ID,
        version: '1',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(mocks.queryView).toHaveBeenCalledTimes(2);
    expect(mocks.queryView.mock.calls[0][2]).toHaveProperty('and');
    expect(mocks.queryView.mock.calls[0][2].and[0]).toEqual({
      operation: {
        column: '[Exchange Version]',
        operator: '=',
        value: '1',
      },
    });
  });

  it('Should correctly handle null values from DB for optional fields', async () => {
    const minimalDbExchange = {
      'Sparx Sendor GUID': SYSTEM_ID,
      'Connection GUID': EXCHANGE_ID,
      'Sparx Receiver GUID': OTHER_SYSTEM_ID,
    };
    const minimalDbStatus = {
      EXCHANGE_ID: EXCHANGE_ID,
      ORIGINAL_RECEIVER_ID: OTHER_SYSTEM_ID,
      ORIGINAL_SENDER_ID: SYSTEM_ID,
    };

    mocks.queryView
      .mockImplementationOnce(() => Promise.resolve([[minimalDbExchange], 1]))
      .mockImplementationOnce(() => Promise.resolve([[minimalDbStatus], 1]));

    const response = await request(app).get('/')
      .query({ systemId: SYSTEM_ID })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    const item = response.body.DataExchanges[0];

    // Check optional fields are null or empty array where expected
    expect(item.Exchange.exchangeName).toBeNull();
    expect(item.Exchange.connectionFrequency).toBeNull();
    expect(item.Exchange.containsBankingData).toBeNull();
    expect(item.Exchange.typeOfData).toBeNull();
  });
});
