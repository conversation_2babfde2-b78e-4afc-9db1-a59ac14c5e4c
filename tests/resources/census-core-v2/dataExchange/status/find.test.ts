import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import dataExchangeStatusFind from '../../../../../src/resources/census-core-v2/dataExchange/status/find';
import dataExchangeConfig from '../../../../../src/resources/census-core-v2/dataExchange';
import Messages from '../../../../../src/utils/constants/messages';
import { createUnitTestApp, createBadApp, testLogger } from '../../../../test-utils';
import { UnitTestForcedExceptionError } from '../../../../../src/utils/express/errors';
import { CMSApp, GetResourceConfigSub } from '../../../../../src/types';

const mocks = vi.hoisted(() => ({
  getDb: vi.fn(),
  dataExchangeStatusFindUtil: vi.fn(),
}));

vi.mock('../../../../../src/utils/db-helpers', async (importOriginal) => {
  const mod = await importOriginal() as Record<string, unknown>;
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock('../../../../../src/utils/dataExchange', async (importOriginal) => {
  const mod = await importOriginal() as Record<string, unknown>;
  return {
    ...mod,
    dataExchangeStatusFindUtil: mocks.dataExchangeStatusFindUtil,
  };
});

const app = await createUnitTestApp(dataExchangeConfig);
app.resources.initResources(app);

const defaultRequest = async () => {
  const response = await request(app).get('/')
    .query({
      systemId: '{11111111-2222-3333-4444-555555555555}',
    })
    .set('x-jwt-key', 'pass');

  return response;
};

const systemId = '{11111111-2222-3333-4444-555555555555}';
const exchangeIdOne = '{11111111-2222-3333-6666-555555555555}';
const exchangeIdTwo = '{11111111-2222-6666-6666-555555555555}';
const db = mocks.getDb();
const mockAllSuccessResponse = {
  count: 2,
  ExchangeStatus: [
    {
      exchangeId: exchangeIdOne,
      systemId,
      systemStatus: 'active',
      partnerId: 'system-456',
      partnerStatus: 'pending',
      reviewerStatus: 'approved',
      direction: 'receiver',
      deleted: false,
    },
    {
      exchangeId: exchangeIdTwo,
      systemId,
      systemStatus: 'pending',
      partnerId: 'system-789',
      partnerStatus: 'active',
      reviewerStatus: 'review',
      direction: 'sender',
      deleted: null,
    },
  ],
};

const mockSenderSuccessResponse = {
  count: 1,
  ExchangeStatus: [
    {
      exchangeId: exchangeIdTwo,
      systemId,
      systemStatus: 'pending',
      partnerId: 'system-789',
      partnerStatus: 'active',
      reviewerStatus: 'review',
      direction: 'sender',
      deleted: null,
    },
  ],
};

const mockReceiverSuccessResponse = {
  count: 1,
  ExchangeStatus: [
    {
      exchangeId: exchangeIdOne,
      systemId,
      systemStatus: 'active',
      partnerId: 'system-456',
      partnerStatus: 'pending',
      reviewerStatus: 'approved',
      direction: 'receiver',
      deleted: false,
    },
  ],
};

describe('Data Exchange Status Find tests', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return 200 with receiver direction when specified', async () => {
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce(mockReceiverSuccessResponse); // Mock getPageDataExchangeStatusList

    const response = await request(app)
      .get('/status')
      .query({
        systemId,
        direction: 'receiver',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockReceiverSuccessResponse);
    expect(mocks.dataExchangeStatusFindUtil).toHaveBeenCalledWith(app, db, systemId, 'receiver');
  });

  it('should return 200 with sender direction when specified', async () => {
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce(mockSenderSuccessResponse); // Mock getPageDataExchangeStatusList

    const response = await request(app)
      .get('/status')
      .query({
        systemId,
        direction: 'sender',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockSenderSuccessResponse);
    expect(mocks.dataExchangeStatusFindUtil).toHaveBeenCalledWith(app, db, systemId, 'sender');
  });

  it('should return 200 with both direction when specified', async () => {
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce(mockAllSuccessResponse); // Mock getPageDataExchangeStatusList

    const response = await request(app)
      .get('/status')
      .query({
        systemId,
        direction: 'both',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockAllSuccessResponse);
    expect(mocks.dataExchangeStatusFindUtil).toHaveBeenCalledWith(app, db, systemId, 'both');
  });

  it('should default to "both" direction when direction is not provided', async () => {
    mocks.dataExchangeStatusFindUtil
      .mockResolvedValueOnce(mockAllSuccessResponse); // Mock getPageDataExchangeStatusList

    const response = await request(app)
      .get('/status')
      .query({
        systemId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(mockAllSuccessResponse);
    expect(mocks.dataExchangeStatusFindUtil).toHaveBeenCalledWith(app, db, systemId, 'both');
  });

  it('should handle empty response from utility function', async () => {
    const emptyResponse = { count: 0, ExchangeStatus: [] };
    mocks.dataExchangeStatusFindUtil.mockResolvedValue(emptyResponse);

    const response = await request(app)
      .get('/status')
      .query({ systemId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(emptyResponse);
  });

  it('should return an error when the systemId is not a uuid', async () => {
    const badId = 'not-a-uuid';
    const response = await request(app)
      .get('/status')
      .query({ systemId: badId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The systemId is not a valid uuid'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a valid GUID');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 400 when systemId is missing', async () => {
    const response = await request(app)
      .get('/status')
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The systemId parameter is required and must be a string'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('The systemId parameter is required and must be a string');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 400 when systemId is empty string', async () => {
    const response = await request(app)
      .get('/status')
      .query({ systemId: '' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The systemId parameter is required and must be a string'],
    });
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('The systemId parameter is required and must be a string');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 400 when direction is invalid', async () => {
    const response = await request(app)
      .get('/status')
      .query({
        systemId,
        direction: 'invalid',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The provided direction is not valid'],
    });
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('The provided direction is not valid');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 400 when systemId is null', async () => {
    const response = await request(app)
      .get('/status')
      .query({ systemId: null })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The systemId parameter is required and must be a string'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('The systemId parameter is required and must be a string');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 400 when systemId is undefined', async () => {
    const response = await request(app)
      .get('/status')
      .query({ systemId: undefined })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The systemId parameter is required and must be a string'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('The systemId parameter is required and must be a string');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => (new Error('Database connection failed')));

    const response = await request(app)
      .get('/status')
      .query({ systemId: 'system-123' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Database Unavailable'],
    });
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('Database connection failed');
    expect(mocks.dataExchangeStatusFindUtil).not.toHaveBeenCalled();
  });

  it('should return 500 when dataExchangeStatusFindUtil returns an error', async () => {
    const utilError = new Error('Logger Error: There was an error fetching the query');
    mocks.dataExchangeStatusFindUtil.mockResolvedValue(utilError);

    const response = await request(app)
      .get('/status')
      .query({ systemId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['There was an error fetching the query'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error.message).toContain('Logger Error: There was an error fetching the query');
  });

  it('should return 500 when app is not available in request', async () => {
    const badApp = createBadApp('/status', 'get', dataExchangeStatusFind);

    const response = await request(badApp)
      .get('/status')
      .query({ systemId: 'system-123' });

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Unable to get the application from request'],
    });
  });
});

describe('Data Exchange Status Find - Catch Block Unit Tests', () => {
  it('sends 500 when an unhandled endpoint exception occurs', async () => {
    const brokenHandler = (): void => {
      throw new Error('Unexpected error');
    };

    const brokenResourceConfig: GetResourceConfigSub = () => ({
      path: '/',
      resourceConfig: [{
        name: 'test',
        path: '/',
        method: 'get',
        resource: brokenHandler,
      }],
    });

    const app = await createUnitTestApp(brokenResourceConfig);
    const response = await request(app).get('/').set('x-jwt-key', 'pass').query({
      id: '{11111111-2222-3333-4444-555555555555}',
    });

    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.internal_server_error],
    });
    expect(response.status).toEqual(500);
  });
});
