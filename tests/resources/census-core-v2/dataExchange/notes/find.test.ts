// Modules.
import request from 'supertest';

// Custom.
import findDataExchangeNotesList, {
  DbPageDataExchangeNoteQueryResult,
  convertDbDataExchangeNoteToDataExchangeNote,
} from '../../../../../src/resources/census-core-v2/dataExchange/notes/find';
// } from '../../../src/resources/census-core-v2/dataExchangeNotes/find';
import { createBadApp, createUnitTestApp, testLogger } from '../../../../test-utils';
import createFailureTests from '../../../../shared/endpoints';
import dataExchangeConfig from '../../../../../src/resources/census-core-v2/dataExchange';
import { findData } from 'utils/budget';
import { CMSApp } from 'types';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const mockDbQueryResult: DbPageDataExchangeNoteQueryResult = [
  [
    {
      SYSTEM_SURVEY_EXCHANGE_NOTES_ID: '11111111-2222-3333-4444-555555555555',
      EXCHANGE_ID: '{11111111-2222-3333-4444-555555555555}',
      NOTES_USER: 'T3ST',
      NOTES_CREATED_DATE: '2024-09-11T15:24:10.257Z',
      NOTES: 'test note for data exchange',
      NOTES_USER_ROLE: 'QA Reviewer',
    },
  ],
  1,
];

const mockApiResponseResult = convertDbDataExchangeNoteToDataExchangeNote(mockDbQueryResult[0][0]);

const app = await createUnitTestApp(dataExchangeConfig);
app.resources.initResources(app);
const db = mocks.getDb();

const exchangeId = '{11111111-2222-3333-4444-555555555555}';

const defaultRequest = async () => {
  // noinspection UnnecessaryLocalVariableJS
  const response = await request(app).get('/')
    .query({
      systemId: '{11111111-2222-3333-4444-555555555555}',
    })
    .set('x-jwt-key', 'pass');

  return response;
};

describe('Data Exchange Notes Find tests', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should return 200 with notes when specified', async () => {
    mocks.queryView.mockResolvedValueOnce(mockDbQueryResult);

    const response = await request(app)
      .get('/notes')
      .query({
        exchangeId,
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual({
      count: 1,
      ExchangeNotes: [mockApiResponseResult],
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_NOTES',
      [
        'SYSTEM_SURVEY_EXCHANGE_NOTES_ID',
        'EXCHANGE_ID',
        'NOTES_USER',
        'NOTES_CREATED_DATE',
        'NOTES',
        'NOTES_USER_ROLE',
      ],
      {
        where: {
          operation: {
            column: 'EXCHANGE_ID',
            operator: '=',
            value: exchangeId,
          },
        },
      },
    );
  });

  it('should handle an empty response', async () => {
    const emptyResponse = { count: 0, ExchangeNotes: [] };
    mocks.queryView.mockResolvedValue([[], 1]);

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(200);
    expect(response.body).toEqual(emptyResponse);
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_EXCHANGE_NOTES',
      [
        'SYSTEM_SURVEY_EXCHANGE_NOTES_ID',
        'EXCHANGE_ID',
        'NOTES_USER',
        'NOTES_CREATED_DATE',
        'NOTES',
        'NOTES_USER_ROLE',
      ],
      {
        where: {
          operation: {
            column: 'EXCHANGE_ID',
            operator: '=',
            value: exchangeId,
          },
        },
      },
    );
  });

  it('should return an error when the exchangeId is not a uuid', async () => {
    const badId = 'not-a-uuid';
    const response = await request(app)
      .get('/notes')
      .query({ exchangeId: badId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The exchangeId is not a valid uuid'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message.message).toContain('"value" must be a valid GUID');
    expect(mocks.queryView).not.toHaveBeenCalled();
  });

  it('should return 400 when exchangeId is missing', async () => {
    const response = await request(app)
      .get('/notes')
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The exchangeId parameter is required and must be a string'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toContain('The exchangeId parameter is required and must be a string');
    expect(mocks.queryView).not.toHaveBeenCalled();
  });

  it('should return 400 when exchangeId is an empty string', async () => {
    const response = await request(app)
      .get('/notes')
      .query({ exchangeId: '' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(400);
    expect(response.body).toEqual({
      message: ['The exchangeId parameter is required and must be a string'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toContain('The exchangeId parameter is required and must be a string');
    expect(mocks.queryView).not.toHaveBeenCalled();
  });

  it('should return 500 when queryView returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Error querying database'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toHaveProperty('message');
    expect(errorLogRes.message.message).toContain('Query View Error');
  });

  it('should return 500 when queryView returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Query View Critical Error'));

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Error querying database'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toHaveProperty('message');
    expect(errorLogRes.message.message).toContain('Query View Critical Error');
  });

  it('should return 500 when queryView does not return an array', async () => {
    mocks.queryView.mockResolvedValueOnce(12345);

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Unable to get result from query'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toHaveProperty('message');
    expect(errorLogRes.message.message).toContain('Unable to get result from query');
  });

  it('should return 500 when queryView returns null', async () => {
    mocks.queryView.mockResolvedValueOnce(null);

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Unable to get result from query'],
    });
    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes.message).toHaveProperty('message');
    expect(errorLogRes.message.message).toContain('Unable to get result from query');
  });

  it('should return 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => (new Error('Database connection failed')));

    const response = await request(app)
      .get('/notes')
      .query({ exchangeId: 'system-123' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Database Unavailable'],
    });

    const errorLog = testLogger.error.mock.lastCall ?? [];
    expect(errorLog).toHaveLength(1);
    const errorLogRes = errorLog[0] ?? [];
    expect(errorLogRes).toHaveProperty('message');
    expect(errorLogRes.message.message).toEqual('Database connection failed');
    expect(mocks.queryView).not.toHaveBeenCalled();
  });

  it('should return 500 when app is not available in request', async () => {
    const badApp = createBadApp('/notes', 'get', findDataExchangeNotesList);

    const response = await request(badApp)
      .get('/notes')
      .query({ exchangeId: 'system-123' });

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Unable to get the application from request'],
    });
  });

  it('should return 500 when there is an unexpected error thrown', async () => {
    const app = new Error('Unexpected Error');

    const response = await request(app as unknown as CMSApp)
      .get('/notes')
      .query({ exchangeId: 'system-123' });

    expect(response.status).toBe(500);
    expect(response.body).toEqual({
      message: ['Unable to get the application from request'],
    });
  });
});
