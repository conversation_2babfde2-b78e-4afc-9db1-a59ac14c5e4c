import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import findConfig from '../../../../src/resources/census-core-v2/budgetAndContracts';
import findModule from '../../../../src/resources/census-core-v2/budgetAndContracts/find';
import { createBadApp, createUnitTestApp, testLogger } from '../../../test-utils';
import {
  budgetSystemIdAPIResponse,
  contractSystemIdAPIList,
} from './findTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  budgetFindUtil: vi.fn(),
  contractListUtil: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/utils/budget'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    budgetFindUtil: mocks.budgetFindUtil,
  };
});

vi.mock(import('../../../../src/utils/contract'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    contractListUtil: mocks.contractListUtil,
  };
});

const app = await createUnitTestApp(findConfig);

describe('Budget and Contracts Page Find tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mocks.budgetFindUtil.mockReset();
    mocks.contractListUtil.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 500 with an error if systemApp is not in the req', async () => {
    const badApp = createBadApp('/', 'get', findModule);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 with an error if getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 with an error if systemId param is empty', async () => {
    const response = await request(app).get('/')
      .query({
        systemId: '',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Please provide required parameters \'systemId\'',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Please provide required parameters \'systemId\'');
  });

  it('Should return a 500 with an error if the systemId param is not a valid UUID', async () => {
    const response = await request(app).get('/')
      .query({
        systemId: '42fdsfds',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'The system ID is not valid',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error.details[0]).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.details[0].message).toContain('"value" must be a valid GUID');
  });

  it('Should return a 500 with an error if budgetFindUtil returns an error', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce(new Error('Error fetching budgets using system id'));
    const response = await request(app).get('/')
      .query({
        systemId: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an issue fetching the budgets',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Error fetching budgets using system id');
  });

  it('Should return a 500 with an error if budgetFindUtil returns with a count of 0', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce({ count: 0 });
    const response = await request(app).get('/')
      .query({
        systemId: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'No budgets found',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('No budgets found');
  });

  it('Should return a 500 with an error if contractListUtil returns an error', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce(budgetSystemIdAPIResponse);
    mocks.contractListUtil.mockResolvedValueOnce(new Error('Error fetching contracts using system id'));
    const response = await request(app).get('/')
      .query({
        systemId: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'There was an issue fetching the contracts',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Error fetching contracts using system id');
  });

  it('Should return a 200 with an object consisting of systemId, a list of budgets, and a list of contracts', async () => {
    mocks.budgetFindUtil.mockResolvedValueOnce(budgetSystemIdAPIResponse);
    mocks.contractListUtil.mockResolvedValueOnce(contractSystemIdAPIList);
    const response = await request(app).get('/')
      .query({
        systemId: '{11111112-1111-1111-1111-111111111111}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      systemId: '{11111112-1111-1111-1111-111111111111}',
      Budgets: budgetSystemIdAPIResponse.Budgets,
      Contracts: contractSystemIdAPIList.Contracts,
    });
  });
});
