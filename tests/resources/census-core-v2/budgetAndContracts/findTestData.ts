export const budgetSystemIdDbResponse = [
  {
    id: '{11111112-1111-1111-1111-111111111111}',
    projectId: '123456',
    systemId: '{11111112-1111-1111-1111-111111111111}',
    projectTitle: '123456-Project Management Support',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{11111112-1111-1111-1111-111111111111}',
  },
];

export const budgetSystemIdAPIResponse = {
  count: 1,
  Budgets: [{
    id: '{11111112-1111-1111-1111-111111111111}',
    projectId: '123456',
    systemId: '{11111112-1111-1111-1111-111111111111}',
    projectTitle: '123456-Project Management Support',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{11111112-1111-1111-1111-111111111111}',
    FiscalYear: '2022',
  }],
};

export const contractSystemIdDbList = [{
  id: '{66666666-7777-8888-9999-6306ABC0C141}',
  contractDeliverableId: '{66666666-7777-8888-9999-6306ABC0C14F}',
  parentAwardId: '234567',
  contractADO: 'No',
  awardId: null,
  systemId: '{11111112-1111-1111-1111-111111111111}',
  Cost: '80962817.66',
  ContractName: 'Test Contract Name A',
  POPStartDate: '2019-08-04',
  POPEndDate: '2024-08-03',
  OrderNumber: null,
  ProjectTitle: 'Test Project Title A',
  ProductServiceDescription: null,
  ServiceProvided: null,
  ContractNumber: '234567',
  IsDeliveryOrg: 'No',
}];

export const contractSystemIdAPIList = {
  count: 1,
  Contracts: [
    {
      id: '{66666666-7777-8888-9999-6306ABC0C141}',
      contractDeliverableId: '{66666666-7777-8888-9999-6306ABC0C14F}',
      parentAwardId: '234567',
      contractADO: 'No',
      awardId: null,
      systemId: '{11111112-1111-1111-1111-111111111111}',
      Cost: '80962817.66',
      ContractName: 'Test Contract Name A',
      POPStartDate: '2019-08-04',
      POPEndDate: '2024-08-03',
      OrderNumber: null,
      ProjectTitle: 'Test Project Title A',
      ProductServiceDescription: null,
      ServiceProvided: null,
      ContractNumber: '234567',
      IsDeliveryOrg: 'No',
    },
  ],
};
