// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import request from 'supertest';

// Custom.
import handler, {
  convertDbPageNoteToPageNote,
  DbPageNoteQueryResult, NoteFindMessages,
} from '../../../../src/resources/census-core-v2/note/find';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import createFailureTests from '../../../shared/endpoints';
import routeConfig from '../../../../src/resources/census-core-v2/note';
import Databases from '../../../../src/utils/constants/databases';
import { ParameterizeDirections } from '../../../../src/utils/db-helpers/parameterize';
import Messages from '../../../../src/utils/constants/messages';
import { CMSApp } from '../../../../src/types';

const mocks = vi.hoisted(() => ({
  queryViewTyped: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTyped: mocks.queryViewTyped,
  })),
  consoleError: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const mockQueryResult: DbPageNoteQueryResult = [
  [
    {
      SYSTEM_SURVEY_PAGE_NOTES_ID: '11111111-2222-3333-4444-555555555555',
      PAGE_NAME: 'TestPage',
      NOTES_USER: 'T3ST',
      NOTES_CREATED_DATE: '2024-09-11T15:24:10.257Z',
      NOTES: 'test note',
      NOTES_USER_ROLE: 'Reviewer',
      DISPLAY_PAGE_NAME: '',
      NOTES_USER_FIRST_NAME: 'Test',
      NOTES_USER_LAST_NAME: 'User',
      System_ID: '{11111111-2222-3333-4444-555555555555}',
    },
  ],
  1,
];

const mockResponseResult = convertDbPageNoteToPageNote(mockQueryResult[0][0]);

let app: CMSApp;

const defaultRequest = async () => {
  // noinspection UnnecessaryLocalVariableJS
  const response = await request(app).get('/')
    .query({
      id: '{11111111-2222-3333-4444-555555555555}',
      pageName: 'Urls',
    })
    .set('x-jwt-key', 'pass');

  return response;
};

describe('Census Note Find Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);
  });

  it('sends 200 with a list of notes for a system and page', async () => {
    mocks.queryViewTyped.mockImplementationOnce(() => Promise.resolve(mockQueryResult));

    const response = await defaultRequest();

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 1,
      Notes: [mockResponseResult],
    });
    expect(mocks.queryViewTyped).toHaveBeenCalledWith(
      `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
      [
        'SYSTEM_SURVEY_PAGE_NOTES_ID',
        'PAGE_NAME',
        'NOTES_USER',
        'NOTES_CREATED_DATE',
        'NOTES',
        'NOTES_USER_ROLE',
        'DISPLAY_PAGE_NAME',
        'NOTES_USER_FIRST_NAME',
        'NOTES_USER_LAST_NAME',
        'System_ID',
      ],
      {
        where: {
          operation: {
            column: 'System_ID',
            operator: '=',
            value: '{11111111-2222-3333-4444-555555555555}',
          },
        },
        and: [{
          operation: {
            column: 'PAGE_NAME',
            operator: '=',
            value: 'Urls',
          },
        }],
      },
      { NOTES_CREATED_DATE: ParameterizeDirections.asc },
    );
  });

  it('sends 400 when id parameter is missing', async () => {
    const response = await request(app).get('/')
      .query({
        pageName: 'Urls',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteFindMessages.error_id_missing],
    });
  });

  it('sends 400 when pageName parameter is missing', async () => {
    const response = await request(app).get('/')
      .query({
        id: '{11111111-2222-3333-4444-555555555555}',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteFindMessages.error_page_name_missing],
    });
  });

  it('sends 400 when id is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        id: ['bad', 'value'],
        pageName: 'Urls',
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteFindMessages.error_id_invalid],
    });
  });

  it('sends 400 when pageName is not a string', async () => {
    const response = await request(app).get('/')
      .query({
        id: '{11111111-2222-3333-4444-555555555555}',
        pageName: ['bad', 'value'],
      })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteFindMessages.error_page_name_invalid],
    });
  });

  it('sends 500 when query result is not an array', async () => {
    mocks.queryViewTyped.mockImplementationOnce(() => Promise.resolve([null]));

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_query_result_missing],
    });
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
    ),
  );
});
