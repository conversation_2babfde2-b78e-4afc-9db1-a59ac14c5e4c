// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import request from 'supertest';

// Custom.
import handler, { NoteAddMessages } from '../../../../src/resources/census-core-v2/note/add';
import routeConfig from '../../../../src/resources/census-core-v2/note';
import {
  createUnitTestApp,
  testLogger,
} from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import { CMSApp } from '../../../../src/types';
import createFailureTests from '../../../shared/endpoints';

// Setup mocks.
const mocks = vi.hoisted(() => ({
  consoleError: vi.fn(),
  wmInsert: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    wmInsert: mocks.wmInsert,
  })),
  queryPersons: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    queryPersons: mocks.queryPersons,
  };
});

let app: CMSApp;

const getValidNoteRequest = (removeFirstName = false) => ({
  Notes: [{
    systemId: '{11111111-2222-3333-4444-555555555555}',
    pageName: 'Urls',
    userId: 'T3ST',
    userFirst: removeFirstName ? undefined : 'Test',
    userLast: 'User',
    userRole: 'Reviewer',
    note: 'This is a test note.',
  }],
  EmailFlags: {
    notifyReviewer: false,
    notifyRespondent: false,
    includeHistory: false,
  },
});

const defaultRequest = (
  removeFirstName = false,
  override = {},
) => {
  const valid = getValidNoteRequest(removeFirstName);
  const body = {
    ...valid,
    ...override,
  };

  return request(app)
    .post('/')
    .send(body)
    .set('x-jwt-key', 'pass');
};

describe('Census Note Add Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);

    mocks.queryPersons.mockReset();
  });

  it('returns 200 with valid request body', async () => {
    const inserts = [
      1,
      2,
    ];

    await Promise.all(inserts.map(async (count: number) => {
      const insertedIds = Array.from({ length: count }, (_, i) => i + 1);
      mocks.wmInsert.mockResolvedValueOnce({
        insertedIds,
        affectedRows: count,
      });
      mocks.queryPersons.mockResolvedValueOnce([{
        givenName: 'Test',
        sn: 'User',
      }]);

      const response = await defaultRequest();

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        result: 'success',
        message: [`Inserted ${count} note(s)`],
      });
      expect(response.headers.inserted_ids).toEqual(JSON.stringify(insertedIds, null));
      expect(response.headers.affected_rows).toEqual(JSON.stringify(count, null));
    }));
  });

  it('returns 200 when LDAP lookup required', async () => {
    mocks.wmInsert.mockResolvedValueOnce({
      insertedIds: [1],
      affectedRows: 1,
    });
    mocks.queryPersons.mockResolvedValueOnce([{
      givenName: 'Test',
      sn: 'User',
    }]);

    const response = await defaultRequest(true);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Inserted 1 note(s)'],
    });
  });

  it('returns 400 when Notes array is missing', async () => {
    const response = await defaultRequest(false, { Notes: undefined });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_notes_missing_in_body],
    });
  });

  it('returns 400 when Notes array is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({ Notes: [] })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_notes_missing_in_body],
    });
  });

  it('returns 400 when noteId is present in request', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        noteId: '1234',
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_note_id_present],
    });
  });

  it('returns 400 when systemId is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        systemId: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_system_id_missing],
    });
  });

  it('returns 400 when pageName is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        pageName: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_page_name_missing],
    });
  });

  it('returns 400 when note content is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        note: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_note_content_missing],
    });
  });

  it('returns 400 when userId is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        userId: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_user_id_missing],
    });
  });

  it('returns 500 when database insert fails', async () => {
    mocks.wmInsert.mockRejectedValueOnce(new Error('Database insert failed'));
    mocks.queryPersons.mockResolvedValueOnce([{
      givenName: 'Test',
      sn: 'User',
    }]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_insert_error],
    });
  });

  it('returns 400 when LDAP query for user info fails', async () => {
    mocks.queryPersons.mockResolvedValueOnce(new Error('LDAP query error'));

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_ldap_lookup_failed],
    });
  });

  it('returns 400 when multiple users are found for userId', async () => {
    mocks.queryPersons.mockResolvedValueOnce([
      { givenName: 'MultiTest 1 First', sn: 'MultiTest 1 Last' },
      { givenName: 'MultiTest 2 First', sn: 'MultiTest 2 Last' },
    ]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_ldap_lookup_failed],
    });
  });

  it('returns 400 when no user is found for userId', async () => {
    mocks.queryPersons.mockResolvedValueOnce([]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: [NoteAddMessages.error_ldap_lookup_failed],
    });
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      defaultRequest,
      [
        { [Messages.db_insert_error]: mocks.wmInsert },
      ],
    ),
  );
});
