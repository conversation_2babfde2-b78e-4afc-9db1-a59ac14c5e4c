// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import {
  afterEach, beforeEach, describe, expect, it, vi,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import addListModule from '../../../../src/resources/census-core-v2/note/add';
import noteConfig from '../../../../src/resources/census-core-v2/note';
import { createApp, createBadApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';

// Setup mocks.
const mocks = vi.hoisted(() => ({
  wmInsert: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    wmInsert: mocks.wmInsert,
  })),
  queryPersons: vi.fn(),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    queryPersons: mocks.queryPersons,
  };
});

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', (
  () => () => noteConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

describe('Note Add List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset all mocks to their default state
    mocks.queryPersons.mockReset();
    mocks.wmInsert.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  const getValidNoteRequest = (removeFirstName = false) => ({
    Notes: [{
      systemId: '{11111111-2222-3333-4444-555555555555}',
      pageName: 'Urls',
      userId: 'T3ST',
      userFirst: removeFirstName ? undefined : 'Test',
      userLast: 'User',
      userRole: 'Reviewer',
      note: 'This is a test note.',
    }],
    EmailFlags: {
      notifyReviewer: true,
      notifyRespondent: true,
      includeHistory: true,
    },
  });

  it('Should return a 200 with success message when note is added successfully', async () => {
    mocks.wmInsert.mockResolvedValueOnce([1]); // 1 note inserted
    mocks.queryPersons.mockResolvedValueOnce([{
      givenName: 'Test',
      sn: 'User',
    }]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Inserted 1 note(s)'],
    });
  });

  it('Should return a 400 when Notes array is missing', async () => {
    const response = await request(app)
      .post('/')
      .send({})
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide required input Notes'],
    });
  });

  it('Should return a 400 when Notes array is empty', async () => {
    const response = await request(app)
      .post('/')
      .send({ Notes: [] })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Please provide required input Notes'],
    });
  });

  it('Should return a 400 when noteId is present in request', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        noteId: '1234',
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Cannot add Note when `noteId` is already present'],
    });
  });

  it('Should return a 400 when systemId is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        systemId: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `systemId` for a note'],
    });
  });

  it('Should return a 400 when pageName is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        pageName: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `pageName` for a note'],
    });
  });

  it('Should return a 400 when note content is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        note: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `note` content for a note'],
    });
  });

  it('Should return a 400 when userId is missing', async () => {
    const invalidRequest = {
      Notes: [{
        ...getValidNoteRequest().Notes[0],
        userId: undefined,
      }],
    };

    const response = await request(app)
      .post('/')
      .send(invalidRequest)
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Missing or invalid `userId` for a note'],
    });
  });

  it('Should return a 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'post', addListModule);
    const response = await request(badApp)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.app_invalid] });
  });

  it('Should return a 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error(Messages.db_unavailable));
    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ message: [Messages.db_unavailable] });
  });

  it('Should return a 500 when database insert fails', async () => {
    mocks.wmInsert.mockRejectedValueOnce(new Error('Database insert failed'));
    mocks.queryPersons.mockResolvedValueOnce([{
      givenName: 'Test',
      sn: 'User',
    }]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database insert failed: Database insert failed',
    });
  });

  it('Should return a 400 when LDAP query for user info fails', async () => {
    mocks.queryPersons.mockResolvedValueOnce(new Error('LDAP query error'));

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Error querying for T3ST in LDAP'],
    });
  });

  it('Should return a 400 when multiple users are found for userId', async () => {
    mocks.queryPersons.mockResolvedValueOnce([
      { givenName: 'MultiTest 1 First', sn: 'MultiTest 1 Last' },
      { givenName: 'MultiTest 2 First', sn: 'MultiTest 2 Last' },
    ]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['Multiple users found for T3ST'],
    });
  });

  it('Should return a 400 when no user is found for userId', async () => {
    mocks.queryPersons.mockResolvedValueOnce([]);

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest(true))
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['No user found for T3ST'],
    });
  });

  it('Should return a 500 when an unhandled exception occurs', async () => {
    mocks.wmInsert.mockImplementationOnce(() => {
      throw new Error('Unexpected error');
    });

    const response = await request(app)
      .post('/')
      .send(getValidNoteRequest())
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Internal server error'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = get(errorLog, '[0]', []);
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
    expect(error.action).toEqual('add-list');
  });
});
