// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// Modules.
import request from 'supertest';

// Custom.
import handler, { NoteDeleteMessages } from '../../../../src/resources/census-core-v2/note/delete';
import routeConfig from '../../../../src/resources/census-core-v2/note';
import { createUnitTestApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import Databases from '../../../../src/utils/constants/databases';
import { CMSApp } from '../../../../src/types';
import createFailureTests from '../../../shared/endpoints';

const mocks = vi.hoisted(() => ({
  consoleError: vi.fn(),
  wmDelete: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    wmDelete: mocks.wmDelete,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

let app: CMSApp;

const getDefaultRequest = async (
  ids: string | string [] = ['1'],
) => request(app)
  .delete('/')
  .query({ id: ids })
  .set('x-jwt-key', 'pass');

describe('Census Note Delete Handler', () => {
  beforeEach(async () => {
    app = await createUnitTestApp(routeConfig);

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('sends 200 when deleting a single note', async () => {
    const ids = [
      '11111111-2222-3333-4444-555555555555',
      ['11111111-2222-3333-4444-555555555555'],
    ];

    await Promise.all(ids.map(async (id) => {
      mocks.wmDelete.mockResolvedValueOnce(1);
      const response = await getDefaultRequest(id);

      expect(response.body).toEqual({
        result: 'success',
        message: ['Deleted 1 note(s)'],
      });
      expect(response.status).toEqual(200);
    }));
  });

  it('sends 200 when deleting multiple notes', async () => {
    mocks.wmDelete.mockResolvedValueOnce(3);
    const response = await getDefaultRequest([
      '11111111-2222-3333-4444-555555555555',
      '22222222-3333-4444-5555-666666666666',
      '33333333-4444-5555-6666-777777777777',
    ]);

    expect(response.body).toEqual({
      result: 'success',
      message: ['Deleted 3 note(s)'],
    });
    expect(response.status).toEqual(200);
  });

  it('sends 200 and uses correct query structure for deletion', async () => {
    mocks.wmDelete.mockResolvedValueOnce(2);

    const response = await getDefaultRequest([
      '11111111-2222-3333-4444-555555555555',
      '99999999-8888-7777-6666-555555555555',
    ]);

    expect(response.body).toEqual({
      result: 'success',
      message: ['Deleted 2 note(s)'],
    });
    expect(response.status).toEqual(200);

    expect(mocks.wmDelete).toHaveBeenCalledWith(
      `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
      {
        where: {
          operation: {
            column: 'SYSTEM_SURVEY_PAGE_NOTES_ID',
            operator: 'IN',
            value: [
              '11111111-2222-3333-4444-555555555555',
              '99999999-8888-7777-6666-555555555555',
            ],
          },
        },
      },
    );
  });

  it('sends 400 when no ids are provided', async () => {
    const response = await getDefaultRequest('');

    expect(response.body).toEqual({
      message: [NoteDeleteMessages.error_ids_empty],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 400 when empty array of ids is provided', async () => {
    const response = await getDefaultRequest([]);

    expect(response.body).toEqual({
      message: [NoteDeleteMessages.error_ids_empty],
    });
    expect(response.status).toEqual(400);
  });

  // @todo-unit-testing Something is happening either in the request or express where this turns
  //                    into [ '1', '2', '1', '2' ], more time needed. --hrivera
  it.skip('sends 400 when empty array ids are not strings', async () => {
    // @ts-expect-error - we want to test the error case
    const response = await getDefaultRequest([[1, 2], [1, 2]]);

    expect(response.body).toEqual({
      message: [NoteDeleteMessages.error_ids_missing],
    });
    expect(response.status).toEqual(400);
  });

  it('sends 500 when delete operation fails', async () => {
    const ids = ['11111111-2222-3333-4444-555555555555'];

    mocks.wmDelete.mockImplementationOnce(
      () => Promise.reject(new Error(Messages.db_delete_error)),
    );

    const response = await getDefaultRequest(ids);

    expect(response.body).toEqual({
      message: [Messages.db_delete_error],
    });
    expect(response.status).toEqual(500);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(errorLog?.[0].error).toBeInstanceOf(Error);
  });

  describe(
    'Express Server',
    createFailureTests(
      mocks,
      handler,
      testLogger,
      getDefaultRequest,
      [
        { [Messages.db_delete_error]: mocks.wmDelete },
      ],
    ),
  );
});
