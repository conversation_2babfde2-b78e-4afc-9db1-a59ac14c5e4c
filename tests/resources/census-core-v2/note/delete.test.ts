// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

import {
  describe,
  it,
  vi,
  expect,
  afterEach,
  beforeEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import deleteListModule from '../../../../src/resources/census-core-v2/note/delete';
import noteConfig from '../../../../src/resources/census-core-v2/note';
import { createBadApp, createUnitTestApp, testLogger } from '../../../test-utils';
import Messages from '../../../../src/utils/constants/messages';
import Databases from '../../../../src/utils/constants/databases';

const mocks = vi.hoisted(() => ({
  wmDelete: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    wmDelete: mocks.wmDelete,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createUnitTestApp(noteConfig);

const getDefaultResponse = async (ids: string | string[]) => {
  // noinspection UnnecessaryLocalVariableJS
  const response = await request(app)
    .delete('/')
    .query({ id: ids })
    .set('x-jwt-key', 'pass');
  return response;
};

describe('Note Delete List tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should successfully delete a single note', async () => {
    mocks.wmDelete.mockResolvedValueOnce([1]);
    const response = await getDefaultResponse(['11111111-2222-3333-4444-555555555555']);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Deleted 1 note(s)'],
    });
  });

  it('Should successfully delete multiple notes using array format', async () => {
    mocks.wmDelete.mockResolvedValueOnce([3]);
    const response = await getDefaultResponse([
      '11111111-2222-3333-4444-555555555555',
      '22222222-3333-4444-5555-666666666666',
      '33333333-4444-5555-6666-777777777777',
    ]);

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      result: 'success',
      message: ['Deleted 3 note(s)'],
    });
  });

  it('Should return 400 when no ids are provided', async () => {
    const response = await getDefaultResponse('');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['The id parameter is required'],
    });
  });

  it('Should return 400 when empty array of ids is provided', async () => {
    const response = await getDefaultResponse([]);

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      message: ['The id parameter is required'],
    });
  });

  it('Should return 500 when app is not in the request', async () => {
    const badApp = createBadApp('/', 'delete', deleteListModule);
    const response = await request(badApp)
      .delete('/')
      .query({ id: '11111111-2222-3333-4444-555555555555' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.app_invalid],
    });
  });

  it('Should return 500 when database is unavailable', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
    const response = await getDefaultResponse(['11111111-2222-3333-4444-555555555555']);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.db_unavailable],
    });
  });

  it('Should return 400 when delete operation fails', async () => {
    const harness = ['reject', 'return'];
    const ids = ['11111111-2222-3333-4444-555555555555'];

    await Promise.all(harness.map(async (h) => {
      mocks.wmDelete.mockImplementationOnce(() => {
        if (h === 'reject') {
          return Promise.reject(new Error('Query Error'));
        }
        return Promise.resolve(new Error('Delete operation failed'));
      });

      const response = await getDefaultResponse(ids);

      expect(response.status).toEqual(400);
      expect(response.body).toEqual({
        message: ['Error deleting notes'],
      });

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
    }));
  });

  it('Should verify correct database and table are used for deletion', async () => {
    mocks.wmDelete.mockResolvedValueOnce([null, [1, 1]]);

    await getDefaultResponse(['11111111-2222-3333-4444-555555555555', '99999999-8888-7777-6666-555555555555']);

    expect(mocks.wmDelete).toHaveBeenCalledWith(
      `${Databases.systemCensus}.SYSTEM_SURVEY_PAGE_NOTES`,
      {
        where: {
          operation: {
            column: 'SYSTEM_SURVEY_PAGE_NOTES_ID',
            operator: 'IN',
            value: [
              '11111111-2222-3333-4444-555555555555',
              '99999999-8888-7777-6666-555555555555',
            ],
          },
        },
      },
    );
  });

  it('Should return 500 when an unhandled exception occurs', async () => {
    mocks.wmDelete.mockImplementationOnce(() => {
      throw new Error('Unexpected error');
    });

    const response = await getDefaultResponse(['11111111-2222-3333-4444-555555555555']);

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: ['Internal server error'],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('package');
    expect(get(errorLog, '[0]', [])).toHaveProperty('service');
    expect(get(errorLog, '[0]', [])).toHaveProperty('action');
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).action).toBe('deleteList');
  });
});
