// Generated SHA: 91094c77b4a32f0dfcdd48c55263df78157ea8b3

import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import { get } from 'lodash';
import findConfig from '../../../../src/resources/census-core-v2/adminReviewer';
import adminReviewerFind, {
  convertDbReviewerToReviewer,
} from '../../../../src/resources/census-core-v2/adminReviewer/find';
import { DbReviewer, Reviewer, Where } from '../../../../src/types';
// import adminReviewerConfig from '../../../../src/resources/census-core-v2/adminReviewer';
import { createApp, createBadApp, testLogger } from '../../../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

// Create the app for testing.
const app = await createApp();
app.resources.register(app, '/', (
  () => () => findConfig().resourceConfig)())
  .catch((e) => {
    throw e;
  });
app.resources.initResources(app);

const mockAllReviewers: [DbReviewer[], number] = [
  [
    {
      SYSTEM_SURVEY_REVIEWER_ID: '11111111-2222-3333-4444-555555555555',
      REVIEWER_USERNAME: 'QAUSER1',
      REVIEWER_FULLNAME: 'QA Reviewer One',
      REVIEWER_TYPE: 'QA',
    },
    {
      SYSTEM_SURVEY_REVIEWER_ID: '66666666-7777-8888-9999-000000000000',
      REVIEWER_USERNAME: 'DAUSER1',
      REVIEWER_FULLNAME: 'DA Reviewer One',
      REVIEWER_TYPE: 'DA',
    },
  ],
  2,
];

const mockQaReviewers: [DbReviewer[], number] = [
  [
    {
      SYSTEM_SURVEY_REVIEWER_ID: '11111111-2222-3333-4444-555555555555',
      REVIEWER_USERNAME: 'QAUSER1',
      REVIEWER_FULLNAME: 'QA Reviewer One',
      REVIEWER_TYPE: 'QA',
    },
  ],
  1,
];

const mockDaReviewers: [DbReviewer[], number] = [
  [
    {
      SYSTEM_SURVEY_REVIEWER_ID: '66666666-7777-8888-9999-000000000000',
      REVIEWER_USERNAME: 'DAUSER1',
      REVIEWER_FULLNAME: 'DA Reviewer One',
      REVIEWER_TYPE: 'DA',
    },
  ],
  1,
];

const expectedAllReviewers = mockAllReviewers[0].map(convertDbReviewerToReviewer);
const expectedQaReviewers = mockQaReviewers[0].map(convertDbReviewerToReviewer);
const expectedDaReviewers = mockDaReviewers[0].map(convertDbReviewerToReviewer);

describe('Admin Reviewer Find List tests', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should return a 200 with all reviewers when no type is specified', async () => {
    mocks.queryView.mockResolvedValueOnce(mockAllReviewers);

    const response = await request(app)
      .get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: expectedAllReviewers.length,
      Reviewers: expectedAllReviewers,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_REVIEWERS',
      [
        'SYSTEM_SURVEY_REVIEWER_ID',
        'REVIEWER_USERNAME',
        'REVIEWER_FULLNAME',
        'REVIEWER_TYPE',
      ],
      undefined,
    );
  });

  it('Should return a 200 with QA reviewers when type is \'QA\'', async () => {
    mocks.queryView.mockResolvedValueOnce(mockQaReviewers);

    const response = await request(app).get('/')
      .query({ type: 'QA' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: expectedQaReviewers.length,
      Reviewers: expectedQaReviewers,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_REVIEWERS',
      [
        'SYSTEM_SURVEY_REVIEWER_ID',
        'REVIEWER_USERNAME',
        'REVIEWER_FULLNAME',
        'REVIEWER_TYPE',
      ],
      {
        where: {
          operation: {
            column: 'REVIEWER_TYPE',
            operator: '=',
            value: 'QA',
          },
        },
      },
    );
  });

  it('Should return a 200 with DA reviewers when type is \'DA\'', async () => {
    mocks.queryView.mockResolvedValueOnce(mockDaReviewers);

    const response = await request(app).get('/')
      .query({ type: 'DA' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: expectedDaReviewers.length,
      Reviewers: expectedDaReviewers,
    });
    expect(mocks.queryView).toHaveBeenCalledWith(
      'CEDAR_Support.System_Census.SYSTEM_SURVEY_REVIEWERS',
      [
        'SYSTEM_SURVEY_REVIEWER_ID',
        'REVIEWER_USERNAME',
        'REVIEWER_FULLNAME',
        'REVIEWER_TYPE',
      ],
      {
        where: {
          operation: {
            column: 'REVIEWER_TYPE',
            operator: '=',
            value: 'DA',
          },
        },
      },
    );
  });

  it('Should return a 400 with if the type is neither QA nor DA', async () => {
    const response = await request(app).get('/')
      .query({ type: 'PA' })
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: '\'type\' must be QA or DA',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('\'type\' must be QA or DA');
  });

  it('Should return a 200 with empty array if no reviewers are found', async () => {
    mocks.queryView.mockResolvedValueOnce([[], 0]);

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({
      count: 0,
      Reviewers: [],
    });
  });

  it('Should return a 500 when systemApp is not in the request', async () => {
    const badApp = createBadApp('/', 'get', adminReviewerFind);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get the application from request',
    });
  });

  it('Should return a 500 when getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Database Unavailable',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve DB');
  });

  it('Should return a 500 when query returns an error', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error querying database',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Query View Error');
  });

  it('Should return a 500 when it returns a critical error', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Error querying database',
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toContain('Critical Query View Error');
  });

  it('Should return a 500 when query result is not an array', async () => {
    mocks.queryView.mockResolvedValueOnce(null);

    const response = await request(app).get('/')
      .set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      error: 'Unable to get result from query',
    });
  });
});