import { describe, it, expect } from 'vitest';
import request from 'supertest';
import rootConfig from '../../../src/resources/root';
import { createApp } from '../../test-utils';

const app = await createApp();
app.resources.register(app, '/', rootConfig);
app.resources.initResources(app);

describe('Main Router tests', () => {
  describe('/', () => {
    it('Should perform a GET request', async () => {
      const response = await request(app).get('/');

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ message: 'Hello from the server!' });
    });
  });
});
