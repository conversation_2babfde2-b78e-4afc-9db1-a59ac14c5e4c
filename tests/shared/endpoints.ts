// Modules.
import {
  beforeEach, expect, it, MockedFunction, vi,
} from 'vitest';
import { RequestHand<PERSON> } from 'express';
import request from 'supertest';

// Custom.
import { createBadApp } from '../test-utils';
import { getDb } from '../../src/utils/db-helpers';
import Messages from '../../src/utils/constants/messages';

interface IEndpointMocks {
  queryView?: MockedFunction<(...args: unknown[]) => Promise<unknown>>;
  queryStoredProcedures?: MockedFunction<(...args: unknown[]) => Promise<unknown>>;
  consoleError: MockedFunction<typeof console.error>;
  getDb: MockedFunction<typeof getDb>;
}

interface IMockableLogger {
  error: MockedFunction<(...args: unknown[]) => void>;
}

interface AppErrorLog {
  error: Error;
  [key: string]: unknown;
}

/**
 * Returns a function that defines unit tests to be included in a describe function
 * in the test file for a given endpoint.
 */
const createFailureTests = (
  mocks: IEndpointMocks,
  handler: RequestHand<PERSON>,
  testLogger: IMockableLogger,
  defaultRequest: () => Promise<request.Response>,
) => () => {
  beforeEach(() => {
    // Mock console.error for testing a bad app situation.
    vi.spyOn(console, 'error').mockImplementation(mocks.consoleError);
  });

  it('Should return a 500 when systemApp is not in the request', async () => {
    const badApp = createBadApp('/', 'get', handler);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.app_invalid],
    });

    // Verify that console.error was called as fallback when req.systemApp.logger doesn't exist
    expect(mocks.consoleError).toHaveBeenCalled();

    // Check the contents of the logged error message
    const consoleErrorCall = mocks.consoleError.mock.calls[0];
    expect(consoleErrorCall).toHaveLength(1);
    expect(consoleErrorCall[0]).toBeDefined();
    expect(consoleErrorCall[0]).toBeInstanceOf(Error);
    expect(consoleErrorCall[0].message).toBe(Messages.app_invalid);
  });

  it('Should return a 500 when getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.db_unavailable],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = errorLog?.[0] as AppErrorLog;
    expect(error).toBeDefined();
    expect(error?.error).toBeDefined();
    expect(error.error).toBeInstanceOf(Error);
    if (error?.error instanceof Error) {
      expect(error.error.message).toBeDefined();
      expect(error.error.message).toContain(Messages.db_unavailable);
    }
  });

  it('Should return a 500 when an unhandled exception occurs', async () => {
    if (mocks.queryView) {
      mocks.queryView.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });
    } else if (mocks.queryStoredProcedures) {
      mocks.queryStoredProcedures.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });
    } else {
      throw new Error('Mocks are not misconfigured');
    }

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      message: [Messages.internal_server_error],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = errorLog?.[0];
    expect(error).toBeDefined();
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
  });
};

export default createFailureTests;
