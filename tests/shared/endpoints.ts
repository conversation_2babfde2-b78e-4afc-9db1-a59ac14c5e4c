// Modules.
import { beforeEach, MockedFunction } from 'vitest';
import { RequestHandler } from 'express';
import request from 'supertest';

// Custom.
import { createBadApp, createUnitTestApp } from '../test-utils';
import { getDb } from '../../src/utils/db-helpers';
import Messages from '../../src/utils/constants/messages';
import { UnitTestForcedExceptionError } from '../../src/utils/express/errors';
import { GetResourceConfigSub } from '../../src/types';

interface IEndpointMocks {
  consoleError: MockedFunction<typeof console.error>;
  getDb: MockedFunction<typeof getDb>;
  queryStoredProcedures?: WmMockedFunction;
  queryStoredProceduresTyped?: WmMockedFunction;
  queryView?: WmMockedFunction;
  queryViewTyped?: WmMockedFunction;
  wmDelete?: WmMockedFunction;
  wmInsert?: WmMockedFunction;
  wmUpdate?: WmMockedFunction;
}

interface IMockableLogger {
  error: MockedFunction<(...args: unknown[]) => void>;
}

interface AppErrorLog {
  error: Error;
  [key: string]: unknown;
}

type WmMockedFunction = MockedFunction<(...args: unknown[]) => Promise<unknown>>;
type QueryMocks = Record<string, WmMockedFunction>[];

// noinspection OverlyComplexFunctionJS
/**
 * Returns a function that defines unit tests to be included in a describe function
 * in the test file for a given endpoint.
 */
const createFailureTests = (
  mocks: IEndpointMocks,
  handler: RequestHandler,
  testLogger: IMockableLogger,
  defaultRequest: () => Promise<request.Response>,
  queryMocks: QueryMocks = [],
  useNewErrorFormat: boolean = false,
) => () => {
  beforeEach(() => {
    // Mock console.error for testing a bad app situation.
    vi.spyOn(console, 'error').mockImplementation(mocks.consoleError);
  });

  it('sends 500 when systemApp is not in the request', async () => {
    const badApp = createBadApp('/', 'get', handler);
    const response = await request(badApp).get('/').set('x-jwt-key', 'pass');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.app_invalid],
    });

    // Verify that console.error was called as fallback when req.systemApp.logger doesn't exist.
    expect(mocks.consoleError).toHaveBeenCalled();

    // Check the contents of the logged error message
    const consoleErrorCall = mocks.consoleError.mock.calls[0];
    expect(consoleErrorCall).toHaveLength(1);
    expect(consoleErrorCall[0]).toBeDefined();
    expect(consoleErrorCall[0]).toBeInstanceOf(Error);
    expect(consoleErrorCall[0].message).toBe(Messages.app_invalid);
  });

  it('sends 500 when getDb returns an error', async () => {
    mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.db_unavailable],
    });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = errorLog?.[0] as AppErrorLog;
    expect(error).toBeDefined();
    expect(error?.error).toBeDefined();
    expect(error.error).toBeInstanceOf(Error);
    if (error?.error instanceof Error) {
      expect(error.error.message).toBeDefined();
      expect(error.error.message).toContain(Messages.db_unavailable);
    }
  });

  // noinspection OverlyComplexFunctionJS
  it('sends 500 when query methods return an error', async () => {
    let expectedMessage: string;

    if (queryMocks.length > 0) {
      // Test the first query mock only
      const record = queryMocks[0];
      const [message, mock] = Object.entries(record)[0];

      // Note: Sonar gets angry with the nested functions.
      // eslint-disable-next-line no-restricted-syntax
      for (const t of ['throw', 'reject']) {
        const m = 'test error';
        if (t === 'throw') {
          mock.mockImplementationOnce(() => {
            throw new Error(m);
          });
        } else {
          mock.mockImplementationOnce(() => Promise.reject(new Error(m)));
        }

        // eslint-disable-next-line no-await-in-loop
        const response = await defaultRequest();
        expect(response.body).toEqual(
          useNewErrorFormat
            ? { result: 'error', message: [message] }
            : { message: [message] },
        );
        expect(response.status).toEqual(500);

        const errorLog = testLogger.error.mock.lastCall;
        expect(errorLog).not.toBeUndefined();
        expect(errorLog).toHaveLength(1);

        const errorMessage: AppErrorLog = errorLog?.[0] as AppErrorLog;
        expect(errorMessage.error).toBeInstanceOf(Error);
        expect(errorMessage?.error?.message).toContain('test error');
      }

      return;
    }

    // This is on the way out in favor of the queryMocks block above.
    // noinspection IfStatementWithTooManyBranchesJS
    if (mocks.queryView) {
      mocks?.queryView?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_query_view_error;
    } else if (mocks.queryViewTyped) {
      mocks?.queryViewTyped?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_query_view_error;
    } else if (mocks.queryStoredProcedures) {
      mocks?.queryStoredProcedures?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_query_stored_procedure_error;
    } else if (mocks.queryStoredProceduresTyped) {
      mocks?.queryStoredProceduresTyped?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_query_stored_procedure_error;
    } else if (mocks.wmDelete) {
      mocks?.wmDelete?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_delete_error;
    } else if (mocks.wmInsert) {
      mocks?.wmInsert?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_insert_error;
    } else if (mocks.wmUpdate) {
      mocks?.wmUpdate?.mockResolvedValueOnce(new Error('test error'));
      expectedMessage = Messages.db_update_error;
    } else {
      throw new Error('createFailureTests: mocks are not misconfigured');
    }

    const response = await defaultRequest();

    expect(response.body).toEqual(
      useNewErrorFormat
        ? { result: 'error', message: [expectedMessage] }
        : { message: [expectedMessage] },
    );
    // @todo-duplication Might be able to refactor this to reduce duplication. --hrivera
    // noinspection DuplicatedCode
    expect(response.status).toEqual(500);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const errorMessage: AppErrorLog = errorLog?.[0] as AppErrorLog;
    expect(errorMessage.error).toBeInstanceOf(Error);
    expect(errorMessage?.error?.message).toContain('test error');
  });

  it('sends 500 when query result is malformed', async () => {
    mocks.queryView?.mockResolvedValueOnce(null);
    mocks.queryViewTyped?.mockResolvedValueOnce(null);
    mocks.queryStoredProcedures?.mockResolvedValueOnce(null);
    mocks.queryStoredProceduresTyped?.mockResolvedValueOnce(null);
    mocks.wmDelete?.mockResolvedValueOnce(null);
    mocks.wmInsert?.mockResolvedValueOnce(null);
    mocks.wmUpdate?.mockResolvedValueOnce(null);

    const response = await defaultRequest();

    expect(response.status).toEqual(500);
    expect(response.body).toEqual(
      useNewErrorFormat
        ? { result: 'error', message: [Messages.db_query_result_missing] }
        : { message: [Messages.db_query_result_missing] },
    );
  });

  it('sends 500 when an unhandled exception occurs', async () => {
    // noinspection IfStatementWithTooManyBranchesJS
    if (mocks.queryView) {
      mocks.queryView.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.queryViewTyped) {
      mocks.queryViewTyped.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.queryStoredProcedures) {
      mocks.queryStoredProcedures.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.queryStoredProceduresTyped) {
      mocks.queryStoredProceduresTyped.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.wmDelete) {
      mocks.wmDelete.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.wmInsert) {
      // This is a bit hacky but provides a way to test an unhandled exception in a handler.
      mocks.wmInsert.mockImplementationOnce(() => {
        throw new UnitTestForcedExceptionError('Unexpected error');
      });
    } else if (mocks.wmUpdate) {
      mocks.wmUpdate.mockImplementationOnce(() => {
        throw new Error('Unexpected error');
      });
    } else {
      throw new Error('createFailureTests: mocks are not misconfigured');
    }

    const response = await defaultRequest();

    expect(response.body).toEqual(
      useNewErrorFormat
        ? { result: 'error', message: [Messages.internal_server_error] }
        : { message: [Messages.internal_server_error] },
    );
    expect(response.status).toEqual(500);

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);

    const error = errorLog?.[0];
    expect(error).toBeDefined();
    expect(error).toHaveProperty('package');
    expect(error).toHaveProperty('service');
    expect(error).toHaveProperty('action');
    expect(error).toHaveProperty('error');
  });

  it('sends 500 when an unhandled endpoint exception occurs', async () => {
    const brokenHandler = (): void => {
      throw new Error('Unexpected error');
    };

    const brokenResourceConfig: GetResourceConfigSub = () => ({
      path: '/',
      resourceConfig: [{
        name: 'test',
        path: '/',
        method: 'get',
        resource: brokenHandler,
      }],
    });

    const app = await createUnitTestApp(brokenResourceConfig);
    const response = await request(app).get('/').set('x-jwt-key', 'pass').query({
      id: '{11111111-2222-3333-4444-555555555555}',
    });

    expect(response.body).toEqual({
      result: 'error',
      message: [Messages.internal_server_error],
    });
    expect(response.status).toEqual(500);
  });
};

export default createFailureTests;
