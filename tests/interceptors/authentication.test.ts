import {
  describe,
  it,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import {
  Request,
  Response,
} from 'express';
import { get } from 'lodash';
import {
  mocks,
  createApp,
  createBadApp,
  testLogger,
} from '../test-utils';
import checkIfAuthorized from '../../src/interceptors/authentication';
import { HttpMethods } from '../../src/types';

describe('Interceptors - Authentication tests', () => {
  describe('checkIfAuthorized', async () => {
    const publicMock = (_: Request, res: Response) => res.status(200).send({ message: 'ok' });
    const privateMock = (_: Request, res: Response) => res.status(200).send({ message: 'ok' });
    const app = await createApp();
    const method: HttpMethods = 'get';
    const getResourceConfig = () => [{
      name: 'get-public',
      path: '/public',
      method,
      resource: publicMock,
      public: true,
    }, {
      name: 'get-private',
      path: '/private',
      method,
      resource: privateMock,
    }];
    app.resources.register(app, '/testing', getResourceConfig);
    app.resources.initResources(app);

    afterEach(() => {
      testLogger.resetMocks();
    });

    it('Should return a 500 status if the app is not in the req', async () => {
      const badApp = createBadApp('/', 'get', checkIfAuthorized, { includeLogger: true });
      const response = await request(badApp).get('/');

      expect(response.status).toEqual(500);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toEqual('Unable to retrieve system application');
    });

    it('Should return next if the route is public', async () => {
      const response = await request(app).get('/testing/public');

      expect(response.status).toEqual(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toEqual('ok');
    });

    it('Should return a 401 status if the gatewayKey and jwtKey are empty', async () => {
      const response = await request(app).get('/testing/private');

      expect(response.status).toEqual(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toEqual('Unauthorized access - keys');
    });

    it('Should return a 401 status if the gatewayKey and jwtKey are both provided', async () => {
      const response = await request(app)
        .get('/testing/private')
        .set('x-Gateway-APIKey', 'pass')
        .set('x-jwt-key', 'pass');

      expect(response.status).toEqual(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toEqual('Unauthorized access - keys');
    });

    it('Should return a 401 status if the authentication subsystem returns an error', async () => {
      mocks.authentication.mockResolvedValueOnce(new Error('Bad Connection'));
      const response = await request(app)
        .get('/testing/private')
        .set('x-jwt-key', 'pass');

      expect(response.status).toEqual(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toEqual('Unauthorized access - auth');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return next if the authentication subsystem passes', async () => {
      const response = await request(app)
        .get('/testing/private')
        .set('x-jwt-key', 'pass');

      expect(response.status).toEqual(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toEqual('ok');
    });
  });
});
