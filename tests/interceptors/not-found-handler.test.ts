import {
  describe,
  vi,
  it,
  expect,
} from 'vitest';
import request from 'supertest';
import express, {
  Request,
  Response,
} from 'express';
import notFoundHandler from '../../src/interceptors/not-found-handler';
import rootConfig, { root } from '../../src/resources/root';
import { createApp } from '../test-utils';

describe('notFoundMiddleware', async () => {
  const app = await createApp();
  app.resources.register(app, '/', rootConfig);
  app.resources.register(app, '/root/health', rootConfig);
  app.resources.initResources(app);
  app.use(notFoundHandler);

  it('Should call next with the error if the headers are already sent', () => {
    const mockReq = {} as Request;
    const mockRes = {
      headersSent: true,
    } as Response;
    const mockNext = vi.fn();
    notFoundHandler(mockReq, mockRes, mockNext);
    expect(mockNext).toHaveBeenCalledOnce();
  });

  it('Should return a 500 and an error if the app is not in the req', async () => {
    const badApp = express();
    badApp.use(notFoundHandler);
    badApp.get('/', root);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred retrieving the system application' });
  });

  it('Should return a 500 and an error if the path is not in the req', () => {
    const badReq = {
      systemApp: {},
    } as unknown as Request;
    const sendMock = vi.fn();
    const res = {
      send: sendMock,
    } as unknown as Response;
    const statusMock = vi.fn(() => res);
    res.status = statusMock;
    const next = vi.fn();

    notFoundHandler(badReq, res, next);
    expect(statusMock).toHaveBeenCalledWith(500);
    expect(sendMock).toHaveBeenCalledWith({ error: 'An error occurred retrieving the route path' });
    expect(next).not.toHaveBeenCalled();
  });

  it('Should return a 500 and an error if the method is not in the req', () => {
    const badReq = {
      path: '/',
      systemApp: {},
    } as unknown as Request;
    const sendMock = vi.fn();
    const res = {
      send: sendMock,
    } as unknown as Response;
    const statusMock = vi.fn(() => res);
    res.status = statusMock;
    const next = vi.fn();

    notFoundHandler(badReq, res, next);
    expect(statusMock).toHaveBeenCalledWith(500);
    expect(sendMock).toHaveBeenCalledWith({ error: 'An error occurred retrieving the route method' });
    expect(next).not.toHaveBeenCalled();
  });

  it('Should return a 200 when navigating to "/"', async () => {
    const response = await request(app).get('/');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({ message: 'Hello from the server!' });
  });

  it('Should return a 404 and an error when navigating to "/bimbam"', async () => {
    const response = await request(app).get('/bimbam');

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({ error: 'Not Found' });
  });

  it('Should return a 404 and an error when navigating to "/bimbam/root"', async () => {
    const response = await request(app).get('/bimbam/root');

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({ error: 'Not Found' });
  });
});
