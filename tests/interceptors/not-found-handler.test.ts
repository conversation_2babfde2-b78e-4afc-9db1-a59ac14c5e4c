import {
  describe,
  vi,
  it,
  expect,
} from 'vitest';
import request from 'supertest';
import express, { Request, Response, request as reqProto } from 'express';
import notFoundHandler, { formatDate, formatTime } from '../../src/interceptors/not-found-handler';
import rootConfig, { root } from '../../src/resources/root';
import { createApp } from '../test-utils';

describe('notFoundMiddleware', async () => {
  const app = await createApp();
  app.resources.register(app, '/', rootConfig);
  app.resources.register(app, '/root/health', rootConfig);
  app.resources.initResources(app);
  app.use(notFoundHandler);

  it('Should call next with the error if the headers are already sent', () => {
    const mockReq = {} as Request;
    const mockRes = {
      headersSent: true,
    } as Response;
    const mockNext = vi.fn();
    notFoundHandler(mockReq, mockRes, mockNext);
    expect(mockNext).toHaveBeenCalledOnce();
  });

  it('Should return a 500 and an error if the app is not in the req', async () => {
    const badApp = express();
    badApp.use(notFoundHandler);
    badApp.get('/', root);

    const response = await request(badApp).get('/');

    expect(response.status).toEqual(500);
    expect(response.body).toEqual({ error: 'An error occurred retrieving the system application' });
  });

  it('Should return a 500 and an error if the path is not in the req', () => {
    const badReq = {
      systemApp: {},
    } as unknown as Request;
    const sendMock = vi.fn();
    const res = {
      send: sendMock,
    } as unknown as Response;
    const statusMock = vi.fn(() => res);
    res.status = statusMock;
    const next = vi.fn();

    notFoundHandler(badReq, res, next);
    expect(statusMock).toHaveBeenCalledWith(500);
    expect(sendMock).toHaveBeenCalledWith({ error: 'An error occurred retrieving the route path' });
    expect(next).not.toHaveBeenCalled();
  });

  it('Should return a 500 and an error if the method is not in the req', () => {
    const badReq = {
      path: '/',
      systemApp: {},
    } as unknown as Request;
    const sendMock = vi.fn();
    const res = {
      send: sendMock,
    } as unknown as Response;
    const statusMock = vi.fn(() => res);
    res.status = statusMock;
    const next = vi.fn();

    notFoundHandler(badReq, res, next);
    expect(statusMock).toHaveBeenCalledWith(500);
    expect(sendMock).toHaveBeenCalledWith({ error: 'An error occurred retrieving the route method' });
    expect(next).not.toHaveBeenCalled();
  });

  it('Should return a 200 when navigating to "/"', async () => {
    const response = await request(app).get('/');

    expect(response.status).toEqual(200);
    expect(response.body).toEqual({ message: 'Hello from the server!' });
  });

  it('Should return a 404 and an error when navigating to CEDAR Core V2 "/"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/CEDAR%20Core%20API/2.0.0');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource / not found. ',
      'Request Details: Service - CEDAR Core API, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to CEDAR Core V2 "/nonexistent"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/CEDAR%20Core%20API/2.0.0/nonexistent');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource /nonexistent not found. ',
      'Request Details: Service - CEDAR Core API, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to Census Core V2 "/"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/System Census Core API/2.0.0/nonexistent');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource /nonexistent not found. ',
      'Request Details: Service - System Census Core API, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to Census Core V2 "/nonexistent"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/System Census Core API/2.0.0/nonexistent');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource /nonexistent not found. ',
      'Request Details: Service - System Census Core API, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to LDAP "/"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/LDAP/1.0');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource / not found. ',
      'Request Details: Service - LDAP, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to LDAP "/nonexistent"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/LDAP/1.0/nonexistent');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Resource /nonexistent not found. ',
      'Request Details: Service - LDAP, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to Gateway "/"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Service not found: /gateway. ',
      'Request Details: Service - null, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });

  it('Should return a 404 and an error when navigating to Gateway "/nonexistent"', async () => {
    const mockDate = new Date('2025-01-01T00:00:00.000Z');
    vi.spyOn(global, 'Date').mockImplementation(() => mockDate);
    vi.spyOn(reqProto, 'ip', 'get').mockReturnValue('*************');
    const timeStr = formatTime(mockDate);
    const dateStr = formatDate(mockDate);

    const response = await request(app).get('/gateway/nonexistent');

    expect(response.status).toEqual(404);
    const m = [
      'API Gateway encountered an error. ',
      'Error Message: Service not found: /gateway/nonexistent. ',
      'Request Details: Service - null, Operation - null, ',
      `Invocation Time:${timeStr}, Date:${dateStr},  `,
      'Client IP - *************, User - Default and Application:null',
    ];
    expect(response.body).toEqual({ Exception: m.join('') });

    // Restore the original Date implementation
    vi.restoreAllMocks();
  });
});
