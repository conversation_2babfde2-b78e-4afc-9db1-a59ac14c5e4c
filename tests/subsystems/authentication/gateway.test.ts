import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { Request } from 'express';
import { validate, createKey, remove<PERSON>ey } from '../../../src/subsystems/authentication/gateway';
import { CMSApp } from '../../../src/types';
import { testUser } from '../../test-utils';

const testKey = '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b';
const testResponseKey = 'a7100bc0-a6d2-4b9f-a833-b15a2e30859c';

const mocks = vi.hoisted(() => ({
  findOne: vi.fn().mockResolvedValue({
    id: 1,
    key: '1fe99ada-6b7c-4d5b-b2dc-fd36f839f61b',
    createdBy: 'RJ3R',
    createdDate: '2025-02-24T14:13:52.867Z',
  }),
  insertOne: vi.fn().mockResolvedValue(2),
  deleteOne: vi.fn().mockResolvedValue('success'),
  getDb: vi.fn().mockImplementation(() => ({
    findOne: mocks.findOne,
    insertOne: mocks.insertOne,
    deleteOne: mocks.deleteOne,
  })),
}));

vi.mock(import('../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('uuid'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    v4: vi.fn().mockImplementation(() => 'a7100bc0-a6d2-4b9f-a833-b15a2e30859c'),
  };
});

describe('Authentication Subsystem - Gateway tests', () => {
  const app = {} as unknown as CMSApp;
  describe('validate', () => {
    const req = {} as unknown as Request;
    it('Should return an error if the key is not valid', async () => {
      const test = await validate(req, app, 'bad-key') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid key');
    });

    it('Should return an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await validate(req, app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if findOne returns an error', async () => {
      mocks.findOne.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await validate(req, app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if findOne returns a critical error', async () => {
      mocks.findOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
      const test = await validate(req, app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if findOne returns nothing', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      const test = await validate(req, app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Gateway key not found');
    });

    it('Should return true and set the authMethod to gateway', async () => {
      const test = await validate(req, app, testKey);

      expect(req).toHaveProperty('authMethod');
      expect(req.authMethod).toEqual('gateway');
      expect(test).toEqual(true);
    });
  });

  describe('createKey', () => {
    it('Should return an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await createKey(app, testUser) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if insertOne returns an error', async () => {
      mocks.insertOne.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await createKey(app, testUser) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if insertOne returns a critical error', async () => {
      mocks.insertOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
      const test = await createKey(app, testUser) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Critical Connection');
    });

    it('Should return a key', async () => {
      const test = await createKey(app, testUser);

      expect(test).toEqual(testResponseKey);
    });
  });

  describe('removeKey', () => {
    it('Should return an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await removeKey(app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if deleteOne returns an error', async () => {
      mocks.deleteOne.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await removeKey(app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if deleteOne returns a critical error', async () => {
      mocks.deleteOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
      const test = await removeKey(app, testKey) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Critical Connection');
    });

    it('Should return true', async () => {
      const test = await removeKey(app, testKey);

      expect(test).toEqual(true);
    });
  });
});
