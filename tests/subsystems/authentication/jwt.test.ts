import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import { Request, Response, NextFunction } from 'express';
import {
  initAuthSecrets,
  addSessionTokensToReq,
  getJwt,
  setJwt,
  validate,
} from '../../../src/subsystems/authentication/jwt';
import { testLogger, testUser } from '../../test-utils';

const mocks = vi.hoisted(() => ({
  queryPersons: vi.fn().mockResolvedValue([{
    dn: 'uid=TST1,ou=people,dc=example,dc=com',
    cn: 'Test User',
    mail: '<EMAIL>',
    givenName: 'Test',
    sn: 'User',
    telephoneNumber: '(*************',
    uid: 'TST1',
    ismemberof: [
      'cn=Test,ou=Groups,dc=example,dc=com',
    ],
  }]),
  verify: vi.fn().mockImplementation(() => ({
    csrf: 'cKvrEh7C-qRt0bXpBzMaUKrYBwpPyCnwbzSY',
    id: 'TST1',
    iat: 1740671994,
    aud: 'session',
    sub: 'test-session-id',
  })),
}));

vi.mock(import('../../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    queryPersons: mocks.queryPersons,
  };
});

vi.mock(import('jsonwebtoken'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    default: {
      ...mod.default,
      verify: mocks.verify,
    },
  };
});

const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjc3JmIjoiY0t2ckVoN0MtcVJ0MGJYcEJ6TWFVS3JZQndwUHlDbndielNZIiwiaWQiOiJUU1QxIiwiaWF0IjoxNzQwNjcxOTk0LCJhdWQiOiJzZXNzaW9uIiwic3ViIjoidGVzdC1zZXNzaW9uLWlkIn0.yEqKGGtwnwOHMIEOYhMorp1qkhNpSvGRMfVWW1UZyfc';

describe('Authentication Subsystem - JWT tests', () => {
  describe('initAuthSecrets/addSessionTokensToReq', () => {
    it('Should set the csrf and jwt secrets', () => {
      const req = {} as unknown as Request;
      const res = {} as unknown as Response;
      const nextMock: NextFunction = vi.fn();
      initAuthSecrets();
      addSessionTokensToReq(req, res, nextMock);
      expect(req).toHaveProperty('session');
      expect(req.session).toHaveProperty('csrf');
      expect(req.session.csrf).toHaveProperty('secret');
      expect(req.session).toHaveProperty('jwt');
      expect(req.session.jwt).toHaveProperty('secret');
      expect(nextMock).toHaveBeenCalled();
    });
  });

  describe('getJwt', () => {
    const req = {
      session: {
        csrf: {
          secret: 'abcd1234',
        },
        jwt: {
          secret: '1234abcd',
        },
      },
      sessionID: 'test-session-id',
    } as unknown as Request;
    it('Should return an error if there is not jwt secret in the req', () => {
      const badReq = {
        session: {
          csrf: {
            secret: 'abcd1234',
          },
        },
      } as unknown as Request;
      const test = getJwt(badReq, 'TST1') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secrets');
    });

    it('Should return an error if there is not csrf secret in the req', () => {
      const badReq = {
        session: {
          jwt: {
            secret: '1234abcd',
          },
        },
      } as unknown as Request;
      const test = getJwt(badReq, 'TST1') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secrets');
    });

    it('Should return an error if the ID is invalid', () => {
      const test = getJwt(req, 'TST12') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('"value" length must be less than or equal to 4 characters long');
    });

    it('Should return a signed jwt', () => {
      const test = getJwt(req, 'TST1') as string;
      expect(test).not.toBeInstanceOf(Error);

      const tokenParts = test.split('.');
      expect(tokenParts).toHaveLength(3);
      expect(tokenParts[0]).toEqual(testToken.split('.')[0]);
    });
  });

  describe('setJwt', () => {
    it('Should set the x-set-jwt header on the response', () => {
      const setMock = vi.fn();
      const res = {
        set: setMock,
      } as unknown as Response;

      setJwt(res, testToken);
      expect(setMock).toHaveBeenCalledWith('X-Set-JWT', testToken);
    });
  });

  describe('validate', () => {
    const sessionDestroyMock = vi.fn().mockImplementation(() => null);
    const req = {
      systemApp: {
        logger: testLogger,
      },
      session: {
        csrf: {
          secret: 'abcd1234',
        },
        jwt: {
          secret: '1234abcd',
        },
        destroy: sessionDestroyMock,
      },
      sessionID: 'test-session-id',
    } as unknown as Request;

    it('Should return an error if the app is not in the req', async () => {
      const test = await validate({} as unknown as Request, 'bad-token') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve system application');
    });

    it('Should return an error if the verify fails and the session destroy fails', async () => {
      mocks.verify.mockImplementationOnce(() => {
        throw new Error('Bad Connection');
      });
      const test = await validate(req, 'bad-token') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Web token could not be verified');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return an error if the verify fails and the session is destroyed', async () => {
      mocks.verify.mockImplementationOnce(() => {
        throw new Error('Bad Connection');
      });
      sessionDestroyMock.mockImplementationOnce((cb) => cb(new Error('No session to destroy')));
      const test = await validate(req, 'bad-token') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('No session to destroy');
    });

    it('Should return an error if the csrf token does not verify', async () => {
      const badReq = {
        systemApp: {
          logger: testLogger,
        },
        session: {
          jwt: {
            secret: '1234abcd',
          },
          destroy: sessionDestroyMock,
        },
        sessionID: 'test-session-id',
      } as unknown as Request;

      const test = await validate(badReq, testToken) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The csrf security token is invalid');
    });

    it('Should return an error if queryPersons returns an error', async () => {
      mocks.queryPersons.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await validate(req, testToken) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if the found user is not in the queryPersons results', async () => {
      mocks.queryPersons.mockResolvedValueOnce([]);
      const test = await validate(req, testToken) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve user data');
    });

    it('Should set the authMethod to jwt, add the user, and return true', async () => {
      const test = await validate(req, testToken);

      expect(test).toEqual(true);
      expect(req).toHaveProperty('authMethod');
      expect(req.authMethod).toEqual('jwt');
      expect(req).toHaveProperty('user');
      expect(req.user).toEqual(testUser);
    });
  });
});
