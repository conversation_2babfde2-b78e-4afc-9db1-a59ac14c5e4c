import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { Request } from 'express';
import authentication from '../../../src/subsystems/authentication';
import { CMSApp } from '../../../src/types';

const mocks = vi.hoisted(() => ({
  gatewayValidate: vi.fn().mockResolvedValue(true),
  jwtValidate: vi.fn().mockResolvedValue(true),
}));

vi.mock(import('../../../src/subsystems/authentication/gateway'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    validate: mocks.gatewayValidate,
  };
});

vi.mock(import('../../../src/subsystems/authentication/jwt'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    validate: mocks.jwtValidate,
  };
});

const testToken = 'ede89b3a-35c6-4eae-b04a-ac425c70cb2d';

describe('Authentication Subsystem - Index tests', () => {
  const req = {} as unknown as Request;
  const app = {} as unknown as CMSApp;
  it('Should return an error if gatewayValidate returns an error', async () => {
    mocks.gatewayValidate.mockResolvedValueOnce(new Error('Bad Gateway Connection'));
    const test = await authentication(req, app, {
      gatewayKey: testToken,
      jwtKey: '',
    }) as Error;

    expect(test).toBeInstanceOf(Error);
    expect(test.message).toEqual('Bad Gateway Connection');
  });

  it('Should return an error if jwtValidate returns an error', async () => {
    mocks.jwtValidate.mockResolvedValueOnce(new Error('Bad JWT Connection'));
    const test = await authentication(req, app, {
      gatewayKey: '',
      jwtKey: testToken,
    }) as Error;

    expect(test).toBeInstanceOf(Error);
    expect(test.message).toEqual('Bad JWT Connection');
  });

  it('Should return an error if no gateway or jwt key provided', async () => {
    const test = await authentication(req, app, {
      gatewayKey: '',
      jwtKey: '',
    }) as Error;

    expect(test).toBeInstanceOf(Error);
    expect(test.message).toEqual('No authentication keys provided to authenticate');
  });

  it('Should return true if gatewayValidate is successful', async () => {
    const test = await authentication(req, app, {
      gatewayKey: testToken,
      jwtKey: '',
    }) as Error;

    expect(test).toEqual(true);
  });

  it('Should return true if jwtValidate is successful', async () => {
    const test = await authentication(req, app, {
      gatewayKey: '',
      jwtKey: testToken,
    }) as Error;

    expect(test).toEqual(true);
  });
});
