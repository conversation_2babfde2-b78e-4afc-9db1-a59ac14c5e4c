import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import { get } from 'lodash';
import {
  bindPerson,
  queryPersons,
  person,
  personIds,
  mapSinglePerson,
  mapPerson,
} from '../../src/subsystems/ldap';
import { testUser, testLogger } from '../test-utils';
import { CMSApp, LDAPPersonMap } from '../../src/types';

const mocks = vi.hoisted(() => ({
  ldapAdmin: 'user',
  ldapAdminPass: 'pass',
  clientOptions: {
    url: 'ldaps://example.com:11636',
  },
  bind: vi.fn().mockResolvedValue(true),
  search: vi.fn().mockResolvedValue({
    searchEntries: [{
      dn: 'uid=TST1,ou=people,dc=example,dc=com',
      cn: 'Test User',
      mail: '<EMAIL>',
      givenName: 'Test',
      sn: 'User',
      telephoneNumber: '(*************',
      uid: 'TST1',
      ismemberof: [
        'cn=Test,ou=Groups,dc=example,dc=com',
      ],
    }],
  }),
  unbind: vi.fn(),
  Client: vi.fn().mockImplementation(() => ({
    bind: mocks.bind,
    search: mocks.search,
    unbind: mocks.unbind,
  })),
  getCredentials: vi.fn().mockImplementation(() => ({
    ldapAdmin: mocks.ldapAdmin,
    ldapAdminPass: mocks.ldapAdminPass,
    clientOptions: mocks.clientOptions,
  })),
}));

vi.mock(import('ldapts'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    Client: mocks.Client,
  };
});

vi.mock(import('../../src/utils/auth'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getCredentials: mocks.getCredentials,
  };
});

describe('LDAP Subsystems tests', () => {
  afterEach(() => {
    mocks.unbind.mockReset();
    testLogger.resetMocks();
  });

  const app = {
    logger: testLogger,
  } as unknown as CMSApp;

  describe('bindPerson', () => {
    it('Should return an error if the ldap URL is undefined', async () => {
      mocks.clientOptions.url = 'undefined://undefined:undefined';
      const test = await bindPerson(app, { username: 'TST1', password: 'pa$$wor1' }) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid Admin Credentials');

      mocks.clientOptions.url = 'ldaps://example.com:11636';
    });

    it('Should return an error if client.bind throws an error', async () => {
      mocks.bind.mockImplementationOnce(() => {
        throw new Error('Bad Connection');
      });
      const test = await bindPerson(app, { username: 'TST1', password: 'pa$$wor1' }) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to bind to the user');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('User bind Error: Bad Connection');
    });

    it('Should return success', async () => {
      const test = await bindPerson(app, { username: 'TST1', password: 'pa$$wor1' });
      expect(test).toEqual('success');
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });
  });

  describe('queryPersons', () => {
    it('Should return an error if ldapAdmin is undefined', async () => {
      mocks.ldapAdmin = '';
      const test = await queryPersons(app, '', []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid Admin Credentials');

      mocks.ldapAdmin = 'user';
    });

    it('Should return an error if ldapAdminPass is undefined', async () => {
      mocks.ldapAdminPass = '';
      const test = await queryPersons(app, '', []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid Admin Credentials');

      mocks.ldapAdminPass = 'pass';
    });

    it('Should return an error if clientOptions.url has undefined in it', async () => {
      mocks.clientOptions.url = 'undefined://undefined:undefined';
      const test = await queryPersons(app, '', []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid Admin Credentials');

      mocks.clientOptions.url = 'ldaps://example.com:11636';
    });

    it('Should return an error if client.bind returns an error', async () => {
      mocks.bind.mockImplementationOnce(() => {
        throw new Error('Bad Bind Connection');
      });
      const test = await queryPersons(app, '', []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Bind Connection');
    });

    it('Should return an error if client.search returns an error', async () => {
      mocks.search.mockImplementationOnce(() => {
        throw new Error('Bad Search Connection');
      });
      const test = await queryPersons(app, '', []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Search Connection');
    });

    it('Should return an error if ismemberof is not in the person object', async () => {
      const badTestUser = {
        ...testUser,
      };
      // Disabling TS Error for testing purposes
      // @ts-expect-error: TS2790
      delete badTestUser.ismemberof;

      mocks.search.mockResolvedValueOnce({
        searchEntries: [badTestUser],
      });

      const test = await queryPersons(app, '', [], 'Test') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find job code for persons');
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });

    it('Should return an error if ismemberof is not an array or string', async () => {
      const badTestUser = {
        ...testUser,
        ismemberof: { bad: 'response' },
      };

      mocks.search.mockResolvedValueOnce({
        searchEntries: [badTestUser],
      });

      const test = await queryPersons(app, '', [], 'Test') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find job code for persons');
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });

    it('Should return an error if the string ismemberof does not contain the job code', async () => {
      const badTestUser = {
        ...testUser,
        ismemberof: 'cn=Foo,ou=Groups,dc=example,dc=com',
      };

      mocks.search.mockResolvedValueOnce({
        searchEntries: [badTestUser],
      });

      const test = await queryPersons(app, '', [], 'Test') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find job code for persons');
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });

    it('Should return an error if the array ismemberof does not contain the job code', async () => {
      const badTestUser = {
        ...testUser,
        ismemberof: [
          'cn=Foo,ou=Groups,dc=example,dc=com',
        ],
      };

      mocks.search.mockResolvedValueOnce({
        searchEntries: [badTestUser],
      });

      const test = await queryPersons(app, '', [], 'Test') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find job code for persons');
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });

    it('Should return the persons that have the job code', async () => {
      const test = await queryPersons(app, '', [], 'Test');
      expect(test).toEqual([testUser]);
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });

    it('Should return the persons', async () => {
      const test = await queryPersons(app, '', []);
      expect(test).toEqual([testUser]);
      expect(mocks.unbind).toHaveBeenCalledOnce();
    });
  });

  describe('person', () => {
    afterEach(() => {
      mocks.unbind.mockReset();
      testLogger.resetMocks();
    });
    const personData = {
      firstName: 'Test',
      lastName: 'User',
      commonName: 'Common',
      email: '<EMAIL>',
      telephone: '************',
    };

    const emptyPersonData = {
      firstName: null,
      lastName: null,
      commonName: null,
      email: null,
      telephone: null,
    } as unknown as LDAPPersonMap;

    const invalidFirstNameData = {
      firstName: 1234,
      lastName: 'User',
      commonName: 'Common',
      email: '<EMAIL>',
      telephone: '************',
    } as unknown as LDAPPersonMap;

    const invalidLastNameData = {
      firstName: 'Test',
      lastName: 1234,
      commonName: 'Common',
      email: '<EMAIL>',
      telephone: '************',
    } as unknown as LDAPPersonMap;

    const invalidCommonNameData = {
      firstName: 'Test',
      lastName: 'User',
      commonName: 1234,
      email: '<EMAIL>',
      telephone: '************',
    } as unknown as LDAPPersonMap;

    const invalidEmailData = {
      firstName: 'Test',
      lastName: 'User',
      commonName: 'Common',
      email: 1234,
      telephone: '************',
    } as unknown as LDAPPersonMap;

    it('Should return an error when the personData is empty', async () => {
      const test = await person(app, emptyPersonData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('No items provided for the filter');
    });

    it('Should return an error when the firstName is invalid', async () => {
      const test = await person(app, invalidFirstNameData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('LDAP searches using first name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
    });

    it('Should return an error when the lastName is invalid', async () => {
      const test = await person(app, invalidLastNameData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('LDAP searches using last name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
    });

    it('Should return an error when the commonName is invalid', async () => {
      const test = await person(app, invalidCommonNameData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('LDAP searches using common name must contain a minimum of 2 alpha-characters in addition to optional wildcard characters (* asterisk)');
    });

    it('Should return an error when the email is invalid', async () => {
      const test = await person(app, invalidEmailData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('LDAP search using email did not contain a valid email pattern. Please try again.');
    });

    it('Should return an error when search returns an error', async () => {
      mocks.search.mockResolvedValueOnce(new Error('Query Persons Error'));

      const test = await person(app, personData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Unable to find persons with supplied filter');
    });

    it('Should return an error when search returns a critical error', async () => {
      mocks.search.mockRejectedValueOnce(new Error('Critical Query Persons Error'));

      const test = await person(app, personData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Unable to find persons with supplied filter');
    });

    it('Should return an user when queryPersons is successful', async () => {
      const test = await person(app, personData);
      expect(test).toEqual([testUser]);
    });
  });

  describe('personIds', () => {
    afterEach(() => {
      mocks.unbind.mockReset();
      testLogger.resetMocks();
    });

    const { uid } = testUser;

    it('Should return an error when search returns an error', async () => {
      mocks.search.mockResolvedValueOnce(new Error('Query Persons Error'));

      const test = await personIds(app, uid) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Unable to find persons with supplied filter');
    });

    it('Should return an error when search returns a critical error', async () => {
      mocks.search.mockRejectedValueOnce(new Error('Query Persons Error'));

      const test = await personIds(app, uid) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to find persons with supplied filter');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Unable to find persons with supplied filter');
    });

    it('Should return an user when queryPersons is successful', async () => {
      const test = await personIds(app, uid);
      expect(test).toEqual([testUser]);
    });
  });
  describe('mapSinglePerson', () => {
    it('Should receive keys and map their values to different keys', async () => {
      const personData = {
        id: 'fakeid',
        firstName: 'Test',
        lastName: 'User',
        commonName: 'Common',
        email: '<EMAIL>',
        phone: '************',
        userName: 'TST1',
      };
      const mappedPersonData = {
        id: 'fakeid',
        userName: 'TST1',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      };
      const test = mapSinglePerson(personData);
      expect(test).toEqual(mappedPersonData);
    });
  });

  describe('mapPerson', () => {
    it('Should map just a single person given an array with one person', async () => {
      const personData = [{
        id: 'fakeid',
        firstName: 'Test',
        lastName: 'User',
        commonName: 'Common',
        email: '<EMAIL>',
        phone: '************',
        userName: 'TST1',
      }];

      const mappedPersonData = {
        id: 'fakeid',
        userName: 'TST1',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      };
      const test = mapPerson(personData);
      expect(test).toEqual([mappedPersonData]);
    });

    it('Should map just all persons given an array with multiple persons', async () => {
      const personData = [{
        id: 'fakeid',
        firstName: 'Test',
        lastName: 'User',
        commonName: 'Common',
        email: '<EMAIL>',
        phone: '************',
        userName: 'TST1',
      }, {
        id: 'fakeid2',
        firstName: 'Sett',
        lastName: 'Resu',
        commonName: 'Moncom',
        email: '<EMAIL>',
        phone: '************',
        userName: 'TST2',
      }];

      const mappedPersonData = [{
        id: 'fakeid',
        userName: 'TST1',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      }, {
        id: 'fakeid2',
        userName: 'TST2',
        firstName: 'Sett',
        lastName: 'Resu',
        phone: '************',
        email: '<EMAIL>',
      }];
      const test = mapPerson(personData);
      expect(test).toEqual(mappedPersonData);
    });
  });
});
