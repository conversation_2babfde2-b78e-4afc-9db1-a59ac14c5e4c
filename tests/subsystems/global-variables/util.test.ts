import {
  describe,
  beforeEach,
  afterEach,
  it,
  vi,
  expect,
} from 'vitest';
import {
  findGlobalVariable,
  createGlobalVariable,
  updateGlobalVariable,
  removeGlobalVariable,
} from '../../../src/subsystems/global-variables/util';
import {
  testLogger,
  testUser,
} from '../../test-utils';
import { GLOBAL_VARIABLES } from '../../../src/utils/db-helpers';

import { CMSApp, GlobalVariableBody } from '../../../src/types';

const mocks = vi.hoisted(() => ({
  findOne: vi.fn().mockResolvedValue({
    id: 1,
    globalName: 'name',
    globalValue: 'value',
    createdBy: 'Test String',
    createdDate: '2025-03-03T19:33:13.529Z',
    updatedBy: 'Test String',
    updatedDate: '2025-03-03T19:33:13.529Z',
  }),
  insertOne: vi.fn().mockResolvedValue(1),
  updateOne: vi.fn().mockResolvedValue('success'),
  deleteOne: vi.fn().mockResolvedValue('success'),
  getDb: vi.fn().mockImplementation(() => ({
    findOne: mocks.findOne,
    insertOne: mocks.insertOne,
    updateOne: mocks.updateOne,
    deleteOne: mocks.deleteOne,
  })),
}));

const singleDbValue = {
  id: 1,
  globalName: 'name',
  globalValue: 'value',
  createdBy: 'Test String',
  createdDate: '2025-03-03T19:33:13.529Z',
  updatedBy: 'Test String',
  updatedDate: '2025-03-03T19:33:13.529Z',
};

vi.mock(import('../../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

describe('Global Variable Util tests', () => {
  const app = {
    logger: testLogger,
    config: {
      systems: {
        core: {
          dataSource: 'core',
        },
      },
    },
  } as unknown as CMSApp;

  const testingDateString = '2024-10-31T13:45:12.123Z';
  const testingDate = new Date(testingDateString);

  beforeEach(() => {
    vi.useFakeTimers({ shouldAdvanceTime: true });
    vi.setSystemTime(testingDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('findGlobalVariable', () => {
    it('Should return an error if there is no application parameter', async () => {
      const global = {
        globalName: 'name',
      } as unknown as GlobalVariableBody;
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await findGlobalVariable(null, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Application cannot be blank'));
    });

    it('Should return with an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
      const global = 'name';
      const test = await findGlobalVariable(app, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Unable to retrieve DB'));
    });

    it('Should return an error if there is no global body', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await findGlobalVariable(app, null);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global name is required'));
    });

    it('Should return with an error if findOne returns an error', async () => {
      mocks.findOne.mockResolvedValueOnce(new Error('Fetching Global Variable Error'));
      const test = await findGlobalVariable(app, 'name');
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Fetching Global Variable Error'));
    });

    it('Should return null if the variable does not exist', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      const test = await findGlobalVariable(app, 'texture');
      expect(test).toEqual(null);
    });

    it('Should return a value if the variable exists', async () => {
      mocks.findOne.mockResolvedValueOnce(singleDbValue);
      const test = await findGlobalVariable(app, 'name');
      expect(test).toEqual(singleDbValue);
    });
  });

  describe('createGlobalVariable', () => {
    it('Should return an error if there is no application parameter', async () => {
      const global = {
        globalName: 'name',
      } as unknown as GlobalVariableBody;
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await createGlobalVariable(null, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Application cannot be blank'));
    });

    it('Should return with an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
      const global: GlobalVariableBody = {
        globalName: 'texture',
        globalValue: 'value2',
      } as unknown as GlobalVariableBody;
      const test = await createGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Unable to retrieve DB'));
    });

    it('Should return an error if there is no user', async () => {
      const global = {
        globalName: 'name',
      } as unknown as GlobalVariableBody;
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await createGlobalVariable(app, null, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Invalid user'));
    });

    it('Should return an error if there is no global data', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await createGlobalVariable(app, testUser, null);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global body cannot be blank'));
    });

    it('Should return with a true boolean if creating a global variable is successful', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      mocks.insertOne.mockResolvedValueOnce('success');
      const global: GlobalVariableBody = {
        globalName: 'texture',
        globalValue: 'value2',
      };
      const test = await createGlobalVariable(app, testUser, global);
      expect(test).toEqual(true);
      expect(mocks.insertOne).toHaveBeenCalledWith(
        GLOBAL_VARIABLES,
        {
          globalName: global.globalName,
          globalValue: global.globalValue,
          createdBy: 'TST1',
          createdDate: testingDateString,
          updatedBy: 'TST1',
          updatedDate: testingDateString,
        },
      );
    });

    it('Should return with an error if there is an error creating a global variable', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      mocks.insertOne.mockResolvedValueOnce(new Error('Creating Global Variable Error'));
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: 'value',
      };
      const test = await createGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Creating Global Variable Error'));
    });

    it('Should return with an error if there is an existing variable', async () => {
      mocks.findOne.mockResolvedValueOnce(singleDbValue);
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: 'value',
      };
      const test = await createGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global variable already exists'));
    });
  });

  describe('updateGlobalVariable', () => {
    it('Should return an error if there is no application parameter', async () => {
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: '',
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await updateGlobalVariable(null, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Application cannot be blank'));
    });

    it('Should return an error if there is no user', async () => {
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: '',
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await updateGlobalVariable(app, null, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Invalid user'));
    });

    it('Should return an error if there is no global data', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await updateGlobalVariable(app, testUser, null);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global body cannot be blank'));
    });

    it('Should return with an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
      const global: GlobalVariableBody = {
        globalName: 'texture',
        globalValue: 'value2',
      } as unknown as GlobalVariableBody;
      const test = await updateGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Unable to retrieve DB'));
    });

    it('Should return with an error if there is an error updating a global variable', async () => {
      mocks.updateOne.mockResolvedValueOnce(new Error('Updating Global Variable Error'));
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: 'value',
      };
      const test = await updateGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Updating Global Variable Error'));
    });

    it('Should return an error if the global variable does not exist', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: 'value2',
      };
      const test = await updateGlobalVariable(app, testUser, global);
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global variable does not exist'));
    });

    it('Should return with a true boolean updating a global variable has been successful', async () => {
      mocks.updateOne.mockResolvedValueOnce('success');
      const global: GlobalVariableBody = {
        globalName: 'name',
        globalValue: 'otherValue',
      };
      const test = await updateGlobalVariable(app, testUser, global);
      expect(test).toEqual(true);
      expect(mocks.updateOne).toHaveBeenCalledWith(
        GLOBAL_VARIABLES,
        {
          globalValue: 'otherValue',
          updatedBy: 'TST1',
          updatedDate: testingDateString,
        },
        {
          globalName: global.globalName,
        },
      );
    });
  });

  describe('removeGlobalVariable', () => {
    it('Should return an error if there is no application parameter', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await removeGlobalVariable(null, 'name');
      expect(test).toBeInstanceOf(Error);
      expect((test as unknown as Error).message).toEqual('Application cannot be blank');
    });

    it('Should return an error if there is no global body', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await removeGlobalVariable(app, null);
      expect(test).toBeInstanceOf(Error);
      expect((test as unknown as Error).message).toEqual('Global body cannot be blank');
    });

    it('Should return an error if there is an error removing a global variable', async () => {
      mocks.deleteOne.mockResolvedValueOnce(new Error('Removing Global Variable Error'));
      const test = await removeGlobalVariable(app, 'name');
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Removing Global Variable Error'));
    });

    it('Should return with an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve DB'));
      const test = await removeGlobalVariable(app, 'texture');
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Unable to retrieve DB'));
    });

    it('Should return an error if the global variable does not exist', async () => {
      mocks.findOne.mockResolvedValueOnce(null);
      const test = await removeGlobalVariable(app, 'name');
      expect(test).toBeInstanceOf(Error);
      expect(test).toEqual(Error('Global variable does not exist'));
    });

    it('Should return a boolean true if removing a global variable is successful', async () => {
      mocks.deleteOne.mockResolvedValueOnce('true');
      const test = await removeGlobalVariable(app, 'name');
      expect(test).toEqual(true);
      expect(mocks.deleteOne).toHaveBeenCalledWith(
        GLOBAL_VARIABLES,
        {
          globalName: 'name',
        },
      );
    });
  });
});
