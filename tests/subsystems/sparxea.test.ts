import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import express from 'express';
import { set, get } from 'lodash';
import {
  getToken,
  getTokenExpiration,
  getQuery,
  postQuery,
  deleteQuery,
  getPackages,
  getPackageNames,
  getSparxPackages,
  initSparx,
  getPackageParentId,
} from '../../src/subsystems/sparxea';
import { CMSApp } from '../../src/types';
import { testLogger } from '../test-utils';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  getSecret: vi.fn().mockImplementation(() => ({
    'admin-user': 'admin-pass',
  })),
  getDuration: vi.fn().mockImplementation(() => -3939363851),
  addToUTC: vi.fn().mockImplementation(() => '2024-10-31T14:00:12.123Z'),
  postRequest: vi.fn(),
  getRequest: vi.fn(),
  deleteRequest: vi.fn(),
}));

vi.mock(import('../../src/subsystems/secrets'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSecret: mocks.getSecret,
  };
});

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../src/utils/http'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    postRequest: mocks.postRequest,
    getRequest: mocks.getRequest,
    deleteRequest: mocks.deleteRequest,
  };
});

vi.mock(import('../../src/utils/time'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDuration: mocks.getDuration,
    addToUTC: mocks.addToUTC,
  };
});

const sparxApiSecrets = {
  'admin-user': 'admin-pass',
};

describe('SparxEA Create tests', () => {
  const app = express() as CMSApp;
  set(app, 'config', {
    systems: {
      sparxea: {
        apiSecretString: 'sparx-api-secret',
      },
    },
  });

  app.logger = testLogger;

  const badApp = express() as CMSApp;
  set(badApp, 'config', {
    systems: {
      core: {
        apiSecretString: 'core-api-secret',
      },
    },
  });
  badApp.logger = testLogger;

  const testingDate = new Date('2024-10-31T13:45:12.123Z');
  const authToken1 = '{a2affe4e-276a-4ff2-8a92-3241ff1716cc}';
  const authToken2 = '{94372b24-f8d6-458a-8681-a10a01118ba7}';
  const getAuthResponse = (token) => ({
    data: `<?xml version="1.0" encoding="UTF-8"?><rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#" xmlns:foaf="http://xmlns.com/foaf/0.1/"><ss:login><ss:useridentifier>${token}</ss:useridentifier></ss:login></rdf:RDF>`,
  });
  const testPackages = [{
    Package_GUID: 'first-uuid',
    Package_Name: 'first-package',
  }, {
    Package_GUID: 'second-uuid',
    Package_Name: 'second-package',
  }, {
    Package_GUID: 'third-uuid',
    Package_Name: 'third-package',
  }];

  const testPackageNames = testPackages.map((item) => item.Package_Name);

  beforeEach(() => {
    mocks.postRequest
      .mockImplementationOnce(() => getAuthResponse(authToken1))
      .mockImplementationOnce(() => getAuthResponse(authToken2));
  });

  afterEach(() => {
    mocks.postRequest.mockRestore();
  });

  describe('getToken', () => {
    beforeEach(() => {
      vi.useFakeTimers({ shouldAdvanceTime: true });
      vi.setSystemTime(testingDate);
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('Should return an error if getDuration returns an error', async () => {
      mocks.getDuration.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to verify token expiration');
    });

    it('Should return a token if the token expiration is greater than 2 minutes and the token is not empty', async () => {
      const init = await getToken(app);
      mocks.getDuration.mockImplementationOnce(() => 5);

      expect(init).toEqual(authToken1);
      vi.advanceTimersByTime(1000 * 60 * 10);
      const secondGet = await getToken(app);
      expect(secondGet).toEqual(authToken1);
    });

    it('Should return an error if the sparx secret string is undefined', async () => {
      const test = await getToken(badApp) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve Sparx API Secret String');
    });

    it('Should return an error if getSecret returns an error', async () => {
      mocks.getSecret.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Error getting SparxEA API secrets');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Error getting SparxEA API secrets');
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return an error if getSecret does not return a plain object', async () => {
      mocks.getSecret.mockImplementationOnce(() => ['not a plain object']);
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Error getting SparxEA API secrets');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).message).toEqual('Error getting SparxEA API secrets');
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual(['not a plain object']);
    });

    it('Should return an error if the sparxUser is undefined', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({}));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to connect to SparxEA the credentials are not configured correctly');
    });

    it('Should return an error if the sparxPass is undefined', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({
        'api-user': '',
      }));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to connect to SparxEA the credentials are not configured correctly');
    });

    it('Should return an error if postRequest returns an error', async () => {
      mocks.postRequest.mockRestore();
      mocks.postRequest.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred requesting a token');
    });

    it('Should return an error if the parsed response does not have a token', async () => {
      mocks.postRequest.mockRestore();
      mocks.postRequest.mockImplementationOnce(() => getAuthResponse(''));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve token from SparxEA response');
    });

    it('Should return an error if addToUTC returns an error', async () => {
      mocks.addToUTC.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getToken(app) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred updating the token expiration');
    });

    it('Should return the token', async () => {
      const test = await getToken(app);

      expect(test).toEqual(authToken1);
    });
  });

  describe('getTokenExpiration', () => {
    beforeEach(() => {
      vi.useFakeTimers({ shouldAdvanceTime: true });
      vi.setSystemTime(testingDate);
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('Should return the last set expiration', () => {
      const test = getTokenExpiration();

      expect(test).toEqual('2024-10-31T14:00:12.123Z');
    });

    it('Should return an expiration 15 minutes from now', async () => {
      const initExpiration = getTokenExpiration();

      expect(initExpiration).toEqual('2024-10-31T14:00:12.123Z');
      vi.advanceTimersByTime(1000 * 60 * 20);
      mocks.addToUTC.mockImplementationOnce(() => '2024-10-31T14:20:12.123Z');
      const token = await getToken(app);

      expect(token).toEqual(authToken1);
      const updatedToken = getTokenExpiration();

      expect(updatedToken).toEqual('2024-10-31T14:20:12.123Z');
    });
  });

  describe('getQuery', () => {
    it('Should return an error if getToken returns an error', async () => {
      const test = await getQuery(badApp, '');

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve Sparx API Secret String');
    });

    it('Should return an error if getRequest returns a critical error', async () => {
      mocks.getRequest.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Critical Connection'));
      }));

      const test = await getQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred getting the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if getRequest returns an error', async () => {
      mocks.getRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await getQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred getting the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Connection');
    });

    it('Should return a response', async () => {
      mocks.getRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          data: 'Mocked Response',
        });
      }));

      const test = await getQuery(app, '');
      expect(test).toEqual('Mocked Response');
    });
  });

  describe('postQuery', () => {
    it('Should return an error if postRequest returns a critical error', async () => {
      mocks.postRequest.mockRestore();
      mocks.postRequest.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Critical Connection'));
      }));

      const test = await postQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred creating the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if postRequest returns an error', async () => {
      mocks.postRequest.mockRestore();
      mocks.postRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await postQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred creating the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Connection');
    });

    it('Should return a response', async () => {
      mocks.postRequest.mockRestore();
      mocks.postRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          data: 'Mock Post Response',
        });
      }));

      const test = await postQuery(app, '');
      expect(test).toEqual('Mock Post Response');
    });
  });

  describe('deleteQuery', () => {
    it('Should return an error if getToken returns an error', async () => {
      const test = await deleteQuery(badApp, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve Sparx API Secret String');
    });

    it('Should return an error if deleteRequest returns a critical error', async () => {
      mocks.getSecret.mockImplementationOnce(() => sparxApiSecrets);
      mocks.getRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      mocks.deleteRequest.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Critical Connection'));
      }));

      const test = await deleteQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred deleting the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if deleteRequest returns an error', async () => {
      mocks.getSecret.mockImplementationOnce(() => sparxApiSecrets);
      mocks.getRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));
      mocks.deleteRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await deleteQuery(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred deleting the object');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).message).toEqual('Bad Connection');
    });

    it('Should return a response', async () => {
      mocks.getSecret.mockImplementationOnce(() => sparxApiSecrets);
      mocks.getRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));
      mocks.deleteRequest.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          data: 'Mock Delete Response',
        });
      }));

      const test = await deleteQuery(app, '');
      expect(test).toEqual('Mock Delete Response');
    });
  });

  describe('getPackages', () => {
    it('Should return the default packages', () => {
      const test = getPackages();

      expect(test).toEqual([]);
    });
  });

  describe('getPackageNames', () => {
    it('Should return the default package names', () => {
      const test = getPackageNames();

      expect(test).toEqual([]);
    });
  });

  describe('getSparxPackages', () => {
    it('Should return an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Unable to retrieve database'));

      const test = await getSparxPackages(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve database');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Critical Connection'));
      }));

      const test = await getSparxPackages(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await getSparxPackages(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if queryView returns an empty array', async () => {
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([]);
      }));

      const test = await getSparxPackages(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Package results empty');
    });

    it('Should return packages', async () => {
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([testPackages]);
      }));

      const test = await getSparxPackages(app);
      expect(test).toEqual(testPackages);
    });
  });

  describe('initSparx', () => {
    afterEach(() => {
      mocks.getSecret.mockClear();
    });

    it('Should return an error if sparxPackages returns an error', async () => {
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await initSparx(app).catch((error: Error) => error) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should set packages equal to the result of sparkPackages and packageNames to the package names', async () => {
      mocks.queryView.mockRestore();
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([testPackages]);
      }));
      mocks.getSecret.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          'api-url': 'https://api.example.com',
        });
      }));

      const packages = getPackages();
      expect(packages).toEqual([]);

      const packageNames = getPackageNames();
      expect(packageNames).toEqual([]);

      await initSparx(app);

      const updatedPackages = getPackages();
      expect(updatedPackages).toEqual(testPackages);

      const updatedPackageNames = getPackageNames();
      expect(updatedPackageNames).toEqual(testPackageNames);

      expect(mocks.getSecret).toHaveBeenCalledOnce();
      expect(mocks.getSecret).toHaveBeenCalledWith(app, 'sparx-api-secret');
    });

    it('Should return an error if the sparx API secret is not in the config', async () => {
      mocks.queryView.mockRestore();
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([testPackages]);
      }));

      const test = await initSparx(badApp).catch((error: Error) => error) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve Sparx API Secret value');
    });

    it('Should return an error if getSecret returns an error', async () => {
      mocks.queryView.mockRestore();
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([testPackages]);
      }));
      mocks.getSecret.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Unable to get secrets'));
      }));

      const test = await initSparx(app).catch((error: Error) => error) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to get secrets');
      expect(mocks.getSecret).toHaveBeenCalledOnce();
      expect(mocks.getSecret).toHaveBeenCalledWith(app, 'sparx-api-secret');
    });

    it('Should return an error if the sparx API secrets does not contain api-url', async () => {
      mocks.queryView.mockRestore();
      mocks.queryView.mockImplementationOnce(() => new Promise((resolve) => {
        resolve([testPackages]);
      }));
      mocks.getSecret.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          'bad-path': 'https://api.example.com',
        });
      }));

      const test = await initSparx(app).catch((error: Error) => error) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve Sparx API base URL');
      expect(mocks.getSecret).toHaveBeenCalledOnce();
      expect(mocks.getSecret).toHaveBeenCalledWith(app, 'sparx-api-secret');
    });
  });

  describe('getPackageParentId', () => {
    it('Should find a package guid that exists', () => {
      const test = getPackageParentId('first-package');

      expect(test).toEqual('first-uuid');
    });

    it('Should return undefined if the package does not exist', () => {
      const test = getPackageParentId('bad-package');

      expect(test).toEqual(undefined);
    });
  });
});
