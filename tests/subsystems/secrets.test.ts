import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
  afterEach,
} from 'vitest';
import { get } from 'lodash';
import { getSecret, getSecretStringFromEnv, initSecrets } from '../../src/subsystems/secrets';
import { testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';

const mocks = vi.hoisted(() => ({
  getSecret: vi.fn(),
  getBulkSecrets: vi.fn(),
}));

vi.mock(import('../../src/utils/aws'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSecret: mocks.getSecret,
    getBulkSecrets: mocks.getBulkSecrets,
  };
});

const app = {
  logger: testLogger,
} as unknown as CMSApp;

describe('Secrets Subsystem tests', () => {
  describe('getSecret', () => {
    const secrets = {
      foo: 'bar',
      biz: 'baz',
    };
    const secretString = 'fooSecrets';

    afterEach(() => {
      mocks.getSecret.mockRestore();
    });

    it('Should return an error if getSecret returns an error', async () => {
      mocks.getSecret.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getSecret(app, secretString);

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return a secret from AWS if it does not exist in secretStore', async () => {
      mocks.getSecret.mockImplementationOnce(() => secrets);
      const test = await getSecret(app, secretString);

      expect(test).toEqual(secrets);
      expect(mocks.getSecret).toHaveBeenCalledOnce();
    });

    it('Should return a secret from the secretStore', async () => {
      const test = await getSecret(app, secretString);

      expect(test).toEqual(secrets);
      expect(mocks.getSecret).not.toHaveBeenCalledOnce();
    });
  });

  describe('getSecretStringFromEnv', () => {
    beforeEach(() => {
      vi.stubEnv('foo', 'foo-secret-string');
      vi.stubEnv('bar', 'bar-secret-string');
    });

    it('Should return an error if environment strings are an array and not all strings', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = getSecretStringFromEnv(app, ['foo', ['bar']]) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The provided environment variables are not an array of strings');
    });

    it('Should return an error if one of the environments does not return a string', () => {
      vi.stubEnv('foo', undefined);
      const test = getSecretStringFromEnv(app, ['foo']) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to get secret string from foo environment variable');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to get secret string from foo environment variable');
    });

    it('Should return the secret string for one environment string', () => {
      const test = getSecretStringFromEnv(app, 'foo');

      expect(test).toEqual([
        'foo-secret-string',
      ]);
    });

    it('Should return the secret strings for multiple environment strings', () => {
      const test = getSecretStringFromEnv(app, ['foo', 'bar']);

      expect(test).toEqual([
        'foo-secret-string',
        'bar-secret-string',
      ]);
    });
  });

  describe('initSecrets', () => {
    it('Should return an error if secrets are empty', async () => {
      const test = await initSecrets(app, []).catch((error) => error) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Secrets cannot be empty when initializing the secret subsystem');
    });

    it('Should return an error if getBulkSecrets returns an error', async () => {
      mocks.getBulkSecrets.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await initSecrets(app, ['bad-secret']).catch((error) => error) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return nothing and getSecret should return an existing secret', async () => {
      const secrets = {
        init: 'test',
      };
      mocks.getBulkSecrets.mockResolvedValueOnce([{
        secretString: 'init-test',
        secrets,
      }]);
      const test = await initSecrets(app, ['init-test']).catch((error) => error);
      expect(test).toEqual(undefined);

      const validation = await getSecret(app, 'init-test');
      expect(validation).toEqual(secrets);
    });
  });
});
