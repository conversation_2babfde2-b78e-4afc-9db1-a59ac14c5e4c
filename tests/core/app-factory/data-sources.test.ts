/* eslint-disable max-classes-per-file */
import {
  describe,
  it,
  expect,
  afterEach,
} from 'vitest';
import request from 'supertest';
import express, { Router, Request, Response } from 'express';
import { get, set, isFunction } from 'lodash';
import { registerDataSource, registerAppDataSources } from '../../../src/core/app-factory/data-sources';
import DataSource from '../../../src/data-sources/data-source';
import { CMSApp, DataOptions } from '../../../src/types';
import { testLogger } from '../../test-utils';

describe('Core tests', () => {
  afterEach(() => {
    testLogger.resetMocks();
  });

  describe('registerDataSource', () => {
    class MyTestDb extends DataSource {
      logger = testLogger;

      isDbReady(): boolean {
        this.logger.error('DB Ready');
        return true;
      }

      initDb = async (): Promise<void | Error> => {
        this.isDbReady();
      };

      deleteOne(): Promise<'failed' | 'success'> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve('success');
        });
      }

      findOne(): Promise<unknown> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve('success');
        });
      }

      findAll(): Promise<unknown[]> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve(['failed']);
          });
        }

        return new Promise((resolve) => {
          resolve(['success']);
        });
      }

      findAllByParameter(): Promise<unknown[]> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve(['failed']);
          });
        }

        return new Promise((resolve) => {
          resolve(['success']);
        });
      }

      insertMany(): Promise<number[] | 'failed'> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve([1]);
        });
      }

      insertOne(): Promise<number | 'failed'> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve(1);
        });
      }

      updateOne(): Promise<'success' | 'failed'> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve('success');
        });
      }

      updateMany(): Promise<'success' | 'failed'> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve('success');
        });
      }

      upsert(): Promise<'success' | 'failed' | number> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve('failed');
          });
        }

        return new Promise((resolve) => {
          resolve('success');
        });
      }

      closeConnection(): Promise<void | boolean> {
        const ready = this.isDbReady();
        if (!ready) {
          return new Promise((resolve) => {
            resolve(false);
          });
        }

        return new Promise((resolve) => {
          resolve();
        });
      }
    }

    it('Should add a data source to the app', async () => {
      const app = express() as CMSApp;
      set(app, 'dataSources', {
        register: registerDataSource,
      });
      app.logger = testLogger;

      app.dataSources.register<MyTestDb, DataOptions>(app, 'test', MyTestDb);

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('DB Ready');
    });

    it('Should accept additional data source options', async () => {
      const app = express() as CMSApp;
      set(app, 'dataSources', {
        register: registerDataSource,
      });
      app.logger = testLogger;

      interface TestDataOptions extends DataOptions {
        test: string;
      }

      class MyTestDbOverride extends MyTestDb {
        testField: string;

        constructor(options: TestDataOptions) {
          super(options);

          const testField = get(options, 'test', 'Failed');
          this.testField = testField;
          this.logger.info(`Got ${testField} in the additional props`);
        }
      }

      const testFieldValue = 'working';
      app.dataSources.register<MyTestDbOverride, TestDataOptions>(app, 'test', MyTestDbOverride, {
        test: testFieldValue,
      });

      const infoLog = testLogger.info.mock.lastCall;
      expect(infoLog).not.toBeUndefined();
      expect(infoLog).toHaveLength(1);
      expect(get(infoLog, '[0]', [])).toEqual(`Got ${testFieldValue} in the additional props`);
    });

    it('Should throw an error if initDb returns an error', async () => {
      const app = express() as CMSApp;
      set(app, 'dataSources', {
        register: registerDataSource,
      });
      app.logger = testLogger;

      class MyTestDbOverride extends MyTestDb {
        initDb = async (): Promise<void | Error> => {
          this.logger.error('Using overridden initDb');
          this.logger.info(new Error('Forcing an error'));
          return new Error('Throwing an error');
        };
      }

      const test = await app.dataSources.register<MyTestDbOverride, DataOptions>(app, 'test', MyTestDbOverride)
        .catch((error) => error);
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Throwing an error');

      const errorLog = testLogger.error.mock.calls;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(2);
      expect(get(errorLog, '[0][0]', [])).toEqual('Using overridden initDb');
      expect(get(errorLog, '[1][0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[1][0].error', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[1][0].error.message', [])).toEqual('Throwing an error');

      const infoLog = testLogger.info.mock.calls;
      expect(infoLog).not.toBeUndefined();
      expect(infoLog).toHaveLength(1);
      expect(get(infoLog, '[0][0]', [])).toBeInstanceOf(Error);
      expect(get(infoLog, '[0][0].message', [])).toEqual('Forcing an error');
    });

    it('Should throw an error if initDb throws an error', async () => {
      const app = express() as CMSApp;
      set(app, 'dataSources', {
        register: registerDataSource,
      });
      app.logger = testLogger;

      class MyTestDbOverride extends MyTestDb {
        initDb = async (): Promise<void | Error> => {
          this.logger.error('Using overridden initDb');
          this.logger.info(new Error('Forcing a critical error'));
          throw new Error('Throwing a critical error');
        };
      }

      const test = await app.dataSources.register<MyTestDbOverride, DataOptions>(app, 'test', MyTestDbOverride)
        .catch((error) => error);
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Throwing a critical error');

      const errorLog = testLogger.error.mock.calls;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(2);
      expect(get(errorLog, '[0][0]', [])).toEqual('Using overridden initDb');
      expect(get(errorLog, '[1][0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[1][0].error', [])).toBeInstanceOf(Error);
      expect(get(errorLog, '[1][0].error.message', [])).toEqual('Throwing a critical error');

      const infoLog = testLogger.info.mock.calls;
      expect(infoLog).not.toBeUndefined();
      expect(infoLog).toHaveLength(1);
      expect(get(infoLog, '[0][0]', [])).toBeInstanceOf(Error);
      expect(get(infoLog, '[0][0].message', [])).toEqual('Forcing a critical error');
    });
  });

  describe('registerAppDataSources', () => {
    const app = express() as CMSApp;
    app.logger = testLogger;

    it('Should not have a router', async () => {
      const router = get(app, '_router');
      expect(router).toBeUndefined();
    });

    it('Should have 2 default and 5 custom middleware', async () => {
      const main = Router();
      registerAppDataSources(app);
      const testDS = (req: Request, res: Response) => {
        const registerDS = get(req, 'app.dataSources.register');
        if (!isFunction(registerDS)) {
          return res.status(404).send({ found: false });
        }

        return res.status(200).send({ found: true });
      };
      main.get('/', testDS);
      app.use('/', main);
      const defaultMiddleware = ['query', 'expressInit'];
      const customMiddleware = [
        'router',
      ];
      const middleware = get(app, '_router.stack', []).map((item) => item.name);
      expect(middleware).toHaveLength(3);
      expect(middleware).toEqual([...defaultMiddleware, ...customMiddleware]);

      const response = await request(app).get('/');
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ found: true });
    });
  });
});
