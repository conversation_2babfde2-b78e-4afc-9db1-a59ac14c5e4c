import {
  describe,
  it,
  expect,
} from 'vitest';
import express from 'express';
import { get } from 'lodash';
import { initOuterceptors } from '../../../src/core/app-factory/outerceptor';
import { CMSApp } from '../../../src/types';

describe('Outerceptor tests', () => {
  describe('initOuterceptors', async () => {
    const fullApp = express();

    it('Should not have a router', async () => {
      const router = get(fullApp, '_router');
      expect(router).toBeUndefined();
    });

    it('Should have 2 default and 4 custom middleware', () => {
      initOuterceptors(fullApp as CMSApp);
      const defaultMiddleware = ['query', 'expressInit'];
      const customMiddleware = [
        'errorHandler',
      ];
      const middleware = get(fullApp, '_router.stack', []).map((item) => item.name);
      expect(middleware).toHaveLength(3);
      expect(middleware).toEqual([...defaultMiddleware, ...customMiddleware]);
    });
  });
});
