import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import Logger, { DEBUG } from 'bunyan';
import {
  userSerializer,
  sessionSerializer,
  bodySerializer,
  addStandardSerializers,
  createBunyanLogger,
  loggingService,
  addLoggingToApp,
} from '../../../src/core/app-factory/logger';
import { AppConfig, CMSApp } from '../../../src/types';

describe('App Factory Logger tests', () => {
  describe('userSerializer', () => {
    it('Should return user for the response', () => {
      const test = userSerializer();

      expect(test).toEqual('[user]');
    });
  });

  describe('sessionSerializer', () => {
    it('Should return the session that does not have a nested session', () => {
      const testSession = {
        foo: 'bar',
      };
      const test = sessionSerializer(testSession);

      expect(test).toEqual(testSession);
    });

    it('Should return the session that has a nested session', () => {
      const testSession = {
        foo: 'bar',
        session: {
          biz: 'baz',
        },
      };
      const test = sessionSerializer(testSession);

      expect(test).toEqual(testSession);
    });
  });

  describe('bodySerializer', () => {
    it('Should return the body that does not have a nested session', () => {
      const testBody = {
        foo: 'bar',
      };
      const test = bodySerializer(testBody);

      expect(test).toEqual(testBody);
    });

    it('Should return the body that has a nested session', () => {
      const testBody = {
        foo: 'bar',
        session: {
          biz: 'baz',
        },
      };
      const test = bodySerializer(testBody);

      expect(test).toEqual(testBody);
    });
  });

  describe('addStandardSerializers', () => {
    it('Should override the logger serializer', () => {
      const mockAddSerializers = vi.fn();
      const logger = {
        addSerializers: mockAddSerializers,
        level: () => 'TESTING',
      } as unknown as Logger;
      addStandardSerializers(logger);

      expect(mockAddSerializers).toHaveBeenCalledOnce();
    });

    it('Should add additional serializers if logger level is greater than trace', () => {
      const mockAddSerializers = vi.fn();
      const logger = {
        addSerializers: mockAddSerializers,
        level: () => DEBUG,
      } as unknown as Logger;
      addStandardSerializers(logger);

      expect(mockAddSerializers).toHaveBeenCalledTimes(2);
    });
  });

  describe('createBunyanLogger', () => {
    it('Should replace the stdout stream with stdout', () => {
      const loggerConfig = {
        name: 'test',
        streams: [{
          name: 'stdout',
        }],
      };

      const logger = createBunyanLogger(loggerConfig);
      expect(logger).toBeInstanceOf(Logger);
    });

    it('Should replace the stderror stream with stderr', () => {
      const loggerConfig = {
        name: 'test',
        streams: [{
          name: 'stderr',
        }],
      };

      const logger = createBunyanLogger(loggerConfig);
      expect(logger).toBeInstanceOf(Logger);
    });

    it('Should return a new logger with no streams defined', () => {
      const loggerConfig = {
        name: 'test',
      };

      const logger = createBunyanLogger(loggerConfig);
      expect(logger).toBeInstanceOf(Logger);
    });
  });

  describe('loggingService', () => {
    it('Should create a logger and return the logging service', () => {
      const config = {
        logging: {
          loggers: [{
            name: 'test',
          }],
        },
      } as unknown as AppConfig;

      const logService = loggingService(config);
      expect(logService).toHaveProperty('get');
      expect(logService).toHaveProperty('create');
      expect(logService).toHaveProperty('getNames');

      const testLog = logService.get('test');
      expect(testLog).toBeInstanceOf(Logger);

      const newLog = logService.create({
        name: 'test2',
      });
      expect(newLog).toBeInstanceOf(Logger);

      const names = logService.getNames();
      expect(names).toEqual([
        'test',
        'test2',
      ]);
    });
  });

  describe('addLoggingToApp', () => {
    it('Should return an error if the app does not have a config object', () => {
      const badApp = {} as unknown as CMSApp;
      let test;
      try {
        test = addLoggingToApp(badApp);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toEqual('No API config found in the application');
      }
      expect(test).toBeUndefined();
    });

    it('Should add the logging service to the app and add the logger to the app', () => {
      const app = {
        config: {
          logging: {
            logNames: {
              server: 'test',
            },
            loggers: [{
              name: 'test',
            }],
          },
        },
      } as unknown as CMSApp;

      addLoggingToApp(app);
      expect(app).toHaveProperty('loggingService');
      expect(app).toHaveProperty('logger');
      expect(app.logger).toBeInstanceOf(Logger);
    });
  });
});
