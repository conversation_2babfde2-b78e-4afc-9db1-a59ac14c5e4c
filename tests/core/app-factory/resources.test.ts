import {
  describe,
  vi,
  it,
  expect,
  afterEach,
} from 'vitest';
import { get, isArray } from 'lodash';
import {
  mountResource,
  initResources,
  registerResources,
  addResourceRegistry,
} from '../../../src/core/app-factory/resources';
import { CMSApp, ResourceConfig, HttpMethods } from '../../../src/types';
import { testLogger, createApp } from '../../test-utils';

describe('App Factory Resources tests', () => {
  describe('mountResource', () => {
    const app = {
      logger: testLogger,
      resources: {
        publicRoutes: [],
        router: {
          get: vi.fn(),
          post: vi.fn(),
        },
      },
    } as unknown as CMSApp;

    it('Should return an error if the config is missing the method', () => {
      const resourceConfig = {
        path: '/system/',
        resource: () => {},
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = mountResource(app, resourceConfig) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Resource configuration is missing the method');
    });

    it('Should return an error if the config is missing the path', () => {
      const resourceConfig = {
        method: 'get',
        resource: () => {},
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = mountResource(app, resourceConfig) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Resource configuration is missing the path');
    });

    it('Should return an error if the config is missing the resource', () => {
      const resourceConfig = {
        method: 'get',
        path: '/system/',
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = mountResource(app, resourceConfig) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Resource configuration is missing the resource');
    });

    it('Should mount the route and not add it to the publicRoutes array', () => {
      const method: HttpMethods = 'get';
      const resourceConfig = {
        name: 'get-system',
        method,
        path: '/system/',
        resource: () => {},
      };

      const test = mountResource(app, resourceConfig);
      expect(test).toEqual(true);
      expect(app.resources.publicRoutes).toHaveLength(0);
      expect(app.resources.router.get).toHaveBeenCalledWith(
        resourceConfig.path,
        resourceConfig.resource,
      );
    });

    it('Should add a public route to the publicRoutes array and mount the route', () => {
      const method: HttpMethods = 'post';
      const resourceConfig = {
        name: 'post-system',
        method,
        path: '/system/',
        resource: () => {},
        public: true,
      };

      const test = mountResource(app, resourceConfig);
      expect(test).toEqual(true);
      expect(app.resources.publicRoutes).toHaveLength(1);
      expect(app.resources.publicRoutes).toEqual([{
        path: resourceConfig.path,
        method: resourceConfig.method,
      }]);
      expect(app.resources.router.post).toHaveBeenCalledWith(
        resourceConfig.path,
        resourceConfig.resource,
      );
    });
  });

  describe('initResources', () => {
    afterEach(() => {
      testLogger.resetMocks();
    });

    it('Should throw an error if the resource registry is empty', () => {
      const badApp = {
        resources: {
          registry: [],
        },
      } as unknown as CMSApp;
      let shouldNotThrow = false;

      try {
        initResources(badApp);
        shouldNotThrow = true;
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toEqual('No resources found to initialize');
      }

      expect(shouldNotThrow).toEqual(false);
    });

    it('Should throw an error if mountResource returns an error', () => {
      const badApp = {
        logger: testLogger,
        resources: {
          registry: [{
            name: 'bad-resource',
            path: '/system',
            method: '',
            resource: () => {},
          }],
        },
      } as unknown as CMSApp;
      let shouldNotThrow = false;

      try {
        initResources(badApp);
        shouldNotThrow = true;
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toEqual('One or more resources failed to mount');

        const errorLog = testLogger.error.mock.calls;
        expect(errorLog).not.toBeUndefined();
        expect(errorLog).toHaveLength(2);
        const mockLastCall = get(errorLog, '[1]', []);
        expect(isArray(mockLastCall)).toEqual(true);
        expect(mockLastCall[0]).toHaveProperty('error');
        expect(mockLastCall[0].error).toHaveLength(1);
        expect(mockLastCall[0].error[0].message).toEqual('Resource configuration is missing the method');
      }

      expect(shouldNotThrow).toEqual(false);
    });

    it('Should initialize all resources successfully', () => {
      const app = {
        logger: testLogger,
        resources: {
          registry: [{
            name: 'get-resource',
            path: '/system',
            method: 'get',
            resource: () => {},
          }],
          router: {
            get: vi.fn(),
          },
        },
        use: vi.fn(),
      } as unknown as CMSApp;

      initResources(app);

      const infoLog = testLogger.info.mock.calls;
      expect(infoLog).not.toBeUndefined();
      expect(infoLog).toHaveLength(2);
      expect(get(infoLog, '[0][0]', [])).toEqual('Mounting /system to method get');
      expect(get(infoLog, '[1][0]', [])).toEqual('Resource initialization complete');
    });
  });

  describe('registerResources', () => {
    const app = {
      logger: testLogger,
      resources: {
        registry: [],
      },
    } as unknown as CMSApp;

    it('Should return an error if the path is empty', async () => {
      const test = await registerResources(app, '', '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The path cannot be empty');
    });

    it('Should return an error if importing getResourceConfig returns an error', async () => {
      const test = await registerResources(app, '/system', '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred while importing the resource router');
    });

    it('Should return an error if getResourceConfig is not a function', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await registerResources(app, '/system', ['not a function']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The resource module does not have the getResourceConfig function');
    });

    it('Should return an error if getResourceConfig is undefined', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await registerResources(app, '/system', undefined) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to load resource module');
    });

    it('Should return an error if prependPathToResources returns an error', async () => {
      const getResourceConfig = () => ({ bad: 'response' });

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await registerResources(app, '/system', getResourceConfig) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Resources must be an array of resource configs');
    });

    it('Should return an error if any resource config is not valid', async () => {
      const getResourceConfig = (): ResourceConfig[] => [{
        name: 'get-first',
        path: '/',
        method: 'get',
        resource: () => {},
      }, {
        name: 'get-first',
        path: '/',
        // @ts-expect-error: TS2820
        method: 'postt',
        resource: () => {},
      }];

      const test = await registerResources(app, '/system', getResourceConfig) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('One or more resources failed to validate their config');

      const errorLog = testLogger.error.mock.lastCall as [{ error: Error[] }];
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(isArray(errorLog)).toEqual(true);
      expect(errorLog[0]).toHaveProperty('error');
      expect(errorLog[0].error).toHaveLength(1);
      expect(errorLog[0].error[0].message).toEqual('"method" must be one of [delete, get, options, patch, post, put]');

      expect(app.resources.registry).toHaveLength(1);
      expect(app.resources.registry[0].name).toEqual('get-first');
      app.resources.registry.splice(0, app.resources.registry.length);
      expect(app.resources.registry).toHaveLength(0);
    });

    it('Should add resources to the registry if the resource is a string', async () => {
      const test = await registerResources(app, '/authenticate', '../../../src/resources/authentication');
      expect(test).toEqual(true);
      expect(app.resources.registry).toHaveLength(1);
      expect(app.resources.registry[0].name).toEqual('post-authentication');
      app.resources.registry.splice(0, app.resources.registry.length);
      expect(app.resources.registry).toHaveLength(0);
    });

    it('Should add resources to the registry if the resource is a function', async () => {
      const getResourceConfig = (): ResourceConfig[] => [{
        name: 'get-first',
        path: '/',
        method: 'get',
        resource: () => {},
      }, {
        name: 'post-first',
        path: '/',
        method: 'post',
        resource: () => {},
      }];

      const test = await registerResources(app, '/system', getResourceConfig);
      expect(test).toEqual(true);
      expect(app.resources.registry).toHaveLength(2);
      expect(app.resources.registry[0].name).toEqual('get-first');
      expect(app.resources.registry[0].path).toEqual('/system');
      expect(app.resources.registry[1].name).toEqual('post-first');
      expect(app.resources.registry[1].path).toEqual('/system');
      app.resources.registry.splice(0, app.resources.registry.length);
      expect(app.resources.registry).toHaveLength(0);
    });

    it('Should replace duplicate entry in the registry', async () => {
      const getResourceConfig = (): ResourceConfig[] => [{
        name: 'get-first',
        path: '/',
        method: 'get',
        resource: () => {},
      }, {
        name: 'post-first',
        path: '/',
        method: 'post',
        resource: () => {},
      }, {
        name: 'override-get-first',
        path: '/',
        method: 'get',
        resource: () => {},
      }];

      const test = await registerResources(app, '/system', getResourceConfig);
      expect(test).toEqual(true);
      expect(app.resources.registry).toHaveLength(2);
      expect(app.resources.registry[0].name).toEqual('post-first');
      expect(app.resources.registry[0].path).toEqual('/system');
      expect(app.resources.registry[1].name).toEqual('override-get-first');
      expect(app.resources.registry[1].path).toEqual('/system');
      app.resources.registry.splice(0, app.resources.registry.length);
      expect(app.resources.registry).toHaveLength(0);
    });
  });

  describe('addResourceRegistry', () => {
    it('Should add the required registry items to the app', async () => {
      const app = await createApp();
      addResourceRegistry(app);

      expect(app).toHaveProperty('resources');
      expect(app.resources).toHaveProperty('router');
      expect(app.resources.router).toBeTypeOf('function');
      expect(app.resources).toHaveProperty('initResources');
      expect(app.resources.initResources).toBeTypeOf('function');
      expect(app.resources).toHaveProperty('register');
      expect(app.resources.register).toBeTypeOf('function');
      expect(app.resources).toHaveProperty('registry');
      expect(isArray(app.resources.registry)).toEqual(true);
      expect(app.resources).toHaveProperty('publicRoutes');
      expect(isArray(app.resources.publicRoutes)).toEqual(true);
    });
  });
});
