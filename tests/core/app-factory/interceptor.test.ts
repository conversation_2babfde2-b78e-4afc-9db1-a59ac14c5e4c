import {
  describe,
  it,
  expect,
} from 'vitest';
import { get } from 'lodash';
import express from 'express';
import { initInterceptors } from '../../../src/core/app-factory/interceptor';
import { CMSApp } from '../../../src/types';

describe('App Factory Interceptor tests', () => {
  const app = express();

  describe('initInterceptors', () => {
    it('Should not have a router', async () => {
      const router = get(app, '_router');
      expect(router).toBeUndefined();
    });

    it('Should have 2 default and 3 custom middleware', () => {
      initInterceptors(app as CMSApp);
      const defaultMiddleware = ['query', 'expressInit'];
      const customMiddleware = [
        'addSessionTokensToReq',
        'notFoundHandler',
        'checkIfAuthorized',
      ];
      const middleware = get(app, '_router.stack', []).map((item) => item.name);
      expect(middleware).toHaveLength(5);
      expect(middleware).toEqual([...defaultMiddleware, ...customMiddleware]);
    });
  });
});
