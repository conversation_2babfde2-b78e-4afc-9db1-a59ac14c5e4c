import { describe, it, expect } from 'vitest';
import request from 'supertest';
import express, { Router, Request, Response } from 'express';
import { get } from 'lodash';
import initMiddleware, { addAppToReq } from '../../../src/core/app-factory/middleware';
import { CMSApp } from '../../../src/types';

describe('Middleware tests', () => {
  describe('addAppToRequest', () => {
    const testAppExists = (req: Request, res: Response) => {
      const reqApp = get(req, 'systemApp');
      if (!reqApp) {
        return res.status(404).send({ found: false });
      }

      return res.status(200).send({ found: true });
    };

    it('Should return a 404 because the app was not found', async () => {
      const app = express();
      const main = Router();
      main.get('/', testAppExists);
      app.use('/', main);
      const response = await request(app).get('/');

      expect(response.status).toEqual(404);
      expect(response.body).toEqual({ found: false });
    });

    it('Should return a 200 because the app was found', async () => {
      const app = express() as CMSApp;
      const main = Router();
      addAppToReq(app);
      main.get('/', testAppExists);
      app.use('/', main);
      const response = await request(app).get('/');

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ found: true });
    });
  });

  describe('initMiddleware', () => {
    const app = express() as CMSApp;

    it('Should not have a router', async () => {
      const router = get(app, '_router');
      expect(router).toBeUndefined();
    });

    it('Should have 2 default and 4 custom middleware', () => {
      initMiddleware(app);
      const defaultMiddleware = ['query', 'expressInit'];
      const customMiddleware = [
        'corsMiddleware',
        'jsonParser',
        'urlencodedParser',
        'addAppToRequest',
      ];
      const middleware = get(app, '_router.stack', []).map((item) => item.name);
      expect(middleware).toHaveLength(6);
      expect(middleware).toEqual([...defaultMiddleware, ...customMiddleware]);
    });
  });
});
