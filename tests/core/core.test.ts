import {
  describe,
  it,
  expect,
} from 'vitest';
import request from 'supertest';
import express, { Request, Response } from 'express';
import { get, isFunction } from 'lodash';
import { createApp } from '../test-utils';
import initApp from '../../src/core';
import { HttpMethods } from '../../src/types';

describe('Core tests', () => {
  describe('initApp', async () => {
    it('Should not have a router', async () => {
      const app = express();
      const router = get(app, '_router');
      expect(router).toBeUndefined();
    });

    it('Should throw an error if no dsSetup is passed', async () => {
      const app = await createApp();
      try {
        // Disable type check to verify error handling logic
        // @ts-expect-error: TS2345
        await initApp(app, [], {}, {});
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toEqual('There must be at least one database configured');
      }
    });

    it('Should have 2 default and 9 custom middleware', async () => {
      const app = await createApp();
      const testDS = (req: Request, res: Response) => {
        const registerDS = get(req, 'systemApp.dataSources.register');
        if (!isFunction(registerDS)) {
          return res.status(404).send({ found: false });
        }

        return res.status(200).send({ found: true });
      };
      const method: HttpMethods = 'get';
      const getResourceConfig = () => [{
        name: 'get-test',
        path: '/',
        method,
        resource: testDS,
        public: true,
      }];
      app.resources.register(app, '/test', getResourceConfig);
      app.resources.initResources(app);
      const defaultMiddleware = ['query', 'expressInit'];
      const customMiddleware = [
        'corsMiddleware',
        'jsonParser',
        'urlencodedParser',
        'addAppToRequest',
        'addSessionTokensToReq',
        'notFoundHandler',
        'checkIfAuthorized',
        'errorHandler',
        'router',
      ];
      const middleware = get(app, '_router.stack', []).map((item) => item.name);
      expect(middleware).toHaveLength(11);
      expect(middleware).toEqual([...defaultMiddleware, ...customMiddleware]);

      const response = await request(app).get('/test');
      expect(response.status).toEqual(200);
      expect(response.body).toEqual({ found: true });
    });
  });
});
