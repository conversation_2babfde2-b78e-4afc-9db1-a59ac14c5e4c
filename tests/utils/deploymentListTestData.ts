const rawDeploymentAllQuery = [{
  id: '{11111111-1111-1111-1111-11111111111}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111111}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111111}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111111}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111112}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111112}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111112}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111112}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111113}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111113}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111113}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111113}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111114}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111114}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111114}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111114}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111115}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111115}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111115}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111115}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}];

const formattedDeploymentAllQuery = [{
  id: '{11111111-1111-1111-1111-11111111111}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111111}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111111}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111111}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111112}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111112}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111112}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111112}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111113}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111113}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111113}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111113}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111114}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111114}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111114}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111114}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111115}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111115}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111115}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111115}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}];

const rawDeploymentSelectedQuery = [{
  id: '{11111111-1111-1111-1111-11111111111}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111111}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111111}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111111}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111112}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111112}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111112}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111112}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}, {
  id: '{11111111-1111-1111-1111-11111111113}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111113}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111113}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenterId: '{{11111111-1111-1111-1111-11111111113}',
  DataCenterName: 'Data Center Name',
  DataCenterVersion: null,
  DataCenterDescription: 'Location',
  DataCenterStatus: null,
  DataCenterState: null,
  DataCenterStartDate: null,
  DataCenterEndDate: null,
  DataCenterAddress1: '12345 Real Ave',
  DataCenterAddress2: null,
  DataCenterCity: 'New York Citay',
  DataCenterAddressState: 'MARYLAND (MD)',
  DataCenterZip: '12345',
}];

const formattedDeploymentSelectedQuery = [{
  id: '{11111111-1111-1111-1111-11111111111}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111111}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111111}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111111}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111112}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111112}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111112}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111112}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}, {
  id: '{11111111-1111-1111-1111-11111111113}',
  name: 'Data Center Name',
  description: null,
  deploymentType: 'Implementation',
  systemId: '{11111111-1111-1111-1111-11111111113}',
  systemName: 'System Name',
  systemVersion: null,
  status: null,
  state: null,
  startDate: null,
  endDate: null,
  deploymentElementId: '{11111111-1111-1111-1111-11111111113}',
  contractorName: 'Contractor Name',
  hasProductionData: 'No',
  isHotSite: null,
  replicatedSystemElements: null,
  wanType: 'Real WAN',
  wanTypeOther: null,
  movingToCloud: 'Yes',
  movingToCloudDate: '12/25/2010',
  usersRequiringMFA: 'End-users|Developers|System Administrators',
  otherSpecialUsers: null,
  networkEncryption: 'Trust the process',
  awsEnclave: 'AWS US East/West',
  awsEnclaveOther: null,
  DataCenter: {
    id: '{{11111111-1111-1111-1111-11111111113}',
    name: 'Data Center Name',
    version: null,
    description: 'Location',
    status: null,
    state: null,
    startDate: null,
    endDate: null,
    address1: '12345 Real Ave',
    address2: null,
    city: 'New York Citay',
    addressState: 'MARYLAND (MD)',
    zip: '12345',
  },
}];

const SparxTable = 'Sparx_System_DataCenter_Full';

const SQLKeys = [
  '"Connection GUID" as id',
  '"Connection Name" as name',
  '"Description" as description',
  '"Environment" as deploymentType',
  '"Sparx System GUID" as systemId',
  '"System Name" as systemName',
  '"Version" as systemVersion',
  '"DisplaySystemStatus" as status',
  '"DisplaySystemState" as state',
  '"StartDate" as startDate',
  '"EndDate" as endDate',
  '"Connection GUID" as deploymentElementId',
  '"Contractor Name" as contractorName',
  '"Production Data Use Flag" as hasProductionData',
  '"Hot Site" as isHotSite',
  '"System Server Software Replicated" as replicatedSystemElements',
  '"WAN Type" as wanType',
  '"WAN Type - Other" as wanTypeOther',
  '"Hosted on Cloud" as movingToCloud',
  '"Cloud Migrated Date" as movingToCloudDate',
  '"Users Requiring Multifactor Authentication" as usersRequiringMFA',
  '"Other Special Users" as otherSpecialUsers',
  '"Network Encryption" as networkEncryption',
  '"AWS Enclave" as awsEnclave',
  '"AWS Enclave Other" as awsEnclaveOther',
  '"Sparx DataCenter GUID" as DataCenterId',
  '"DataCenter Name" as DataCenterName',
  '"Version" as DataCenterVersion',
  '"Data Center Type" as DataCenterDescription',
  '"DisplayDataCenterStatus" as DataCenterStatus',
  '"DisplayDataCenterState" as DataCenterState',
  '"StartDate" as DataCenterStartDate',
  '"EndDate" as DataCenterEndDate',
  '"Address Line 1" as DataCenterAddress1',
  '"Address Line 2" as DataCenterAddress2',
  '"City" as DataCenterCity',
  '"State" as DataCenterAddressState',
  '"Zip Code" as DataCenterZip',
];

export {
  rawDeploymentAllQuery,
  formattedDeploymentAllQuery,
  rawDeploymentSelectedQuery,
  formattedDeploymentSelectedQuery,
  SparxTable,
  SQLKeys,
};
