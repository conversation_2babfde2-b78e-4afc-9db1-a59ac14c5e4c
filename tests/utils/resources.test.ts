import {
  describe,
  it,
  expect,
} from 'vitest';
import {
  prependPathToResources,
  validateResourceConfig,
  checkIfRouteIsPublic,
} from '../../src/utils/resources';
import { CMSApp, HttpMethods } from '../../src/types';

describe('Resources Utilities tests', () => {
  const method: HttpMethods = 'get';
  const testResources = [{
    name: 'get-test',
    path: '/',
    method,
    resource: () => {},
  }, {
    name: 'get-test-sub',
    path: '/sub',
    method,
    resource: () => {},
  }, {
    name: 'get-test-by-id',
    path: '/:id',
    method,
    resource: () => {},
  }];
  describe('prependPathToResources', () => {
    it('Should return an error if resources is not an array', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = prependPathToResources('', { not: 'an array' }) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Resources must be an array of resource configs');
    });

    it('Should return resources if the path is empty', () => {
      const test = prependPathToResources('', testResources) as Error;
      expect(test).toEqual(testResources);
    });

    it('Should return resources if resources is empty', () => {
      const test = prependPathToResources('/system', []) as Error;
      expect(test).toEqual([]);
    });

    it('Should return an updated array with the modified path', () => {
      const test = prependPathToResources('/system', testResources) as Error;
      expect(test).toEqual([{
        ...testResources[0],
        path: '/system',
      }, {
        ...testResources[1],
        path: '/system/sub',
      }, {
        ...testResources[2],
        path: '/system/:id',
      }]);
    });
  });

  describe('validateResourceConfig', () => {
    it('Should return an error if name is not provided', () => {
      const resourceConfig = {
        path: '/',
        method,
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"name" is required');
    });

    it('Should return an error if name is not a string', () => {
      const resourceConfig = {
        name: ['not a string'],
        path: '/',
        method,
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"name" must be a string');
    });

    it('Should return an error if name is empty', () => {
      const resourceConfig = {
        name: '',
        path: '/',
        method,
        resource: () => {},
      };
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"name" is not allowed to be empty');
    });

    it('Should return an error if path is not provided', () => {
      const resourceConfig = {
        name: 'get-test',
        method,
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"path" is required');
    });

    it('Should return an error if path is not a string', () => {
      const resourceConfig = {
        name: 'get-test',
        path: ['not a string'],
        method,
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"path" must be a string');
    });

    it('Should return an error if method is not provided', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"method" is required');
    });

    it('Should return an error if method is not a http status', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method: 'bad',
        resource: () => {},
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"method" must be one of [delete, get, options, patch, post, put]');
    });

    it('Should return an error if resource is not provided', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method,
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"resource" is required');
    });

    it('Should return an error if resource is not a function', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method,
        resource: 'not a function',
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"resource" must be of type function');
    });

    it('Should return an error if public is not a boolean', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method,
        resource: () => {},
        public: 'not a boolean',
      };
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = validateResourceConfig(resourceConfig);
      expect(test).toHaveProperty('error');
      const error = test.error as Error;
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toEqual('"public" must be a boolean');
    });

    it('Should return no error if all required fields are valid', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method,
        resource: () => {},
      };
      const test = validateResourceConfig(resourceConfig);
      expect(test).not.toHaveProperty('error');
      expect(test.value).toEqual(resourceConfig);
    });

    it('Should return no error if all required and optional fields are valid', () => {
      const resourceConfig = {
        name: 'get-test',
        path: '/',
        method,
        resource: () => {},
        public: true,
      };
      const test = validateResourceConfig(resourceConfig);
      expect(test).not.toHaveProperty('error');
      expect(test.value).toEqual(resourceConfig);
    });
  });

  describe('checkIfRouteIsPublic', () => {
    it('Should return false if a path is not in the public routes', () => {
      const app = {
        resources: {
          publicRoutes: [{
            path: '/system/',
            method,
          }],
        },
      } as unknown as CMSApp; // Forcing typing for easier testing
      const test = checkIfRouteIsPublic(app, '/login', 'post');
      expect(test).toEqual(false);
    });

    it('Should return false if a path is in the public routes but has a different method', () => {
      const app = {
        resources: {
          publicRoutes: [{
            path: '/system/',
            method,
          }],
        },
      } as unknown as CMSApp; // Forcing typing for easier testing
      const test = checkIfRouteIsPublic(app, '/system/', 'put');
      expect(test).toEqual(false);
    });

    it('Should return true if the path and method are in the public routes', () => {
      const app = {
        resources: {
          publicRoutes: [{
            path: '/system/',
            method,
          }],
        },
      } as unknown as CMSApp; // Forcing typing for easier testing
      const test = checkIfRouteIsPublic(app, '/system/', 'get');
      expect(test).toEqual(true);
    });
  });
});
