import {
  expect,
  describe,
  vi,
  it,
  afterEach,
} from 'vitest';
import {
  get,
} from 'lodash';
import {
  createApp,
  testLogger,
} from '../test-utils';
import {
  createRelation,
  roleValid,
  createPromise,
  createUpdateObj,
  convertRDFToGUID,
} from '../../src/utils/role';

const mocks = vi.hoisted(() => ({
  getSecret: vi.fn().mockImplementation(() => ({
    'admin-user': 'admin-pass',
  })),
  getDuration: vi.fn().mockImplementation(() => -3939363851),
  addToUTC: vi.fn().mockImplementation(() => '2024-10-31T14:00:12.123Z'),
  queryStoredProcedures: vi.fn(),
  personIds: vi.fn(),
  person: vi.fn(),
  createPostXMLPerson: vi.fn(),
  postQuery: vi.fn(),
  findSparxUser: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../src/subsystems/secrets'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSecret: mocks.getSecret,
  };
});

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../src/utils/users'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    findSparxUser: mocks.findSparxUser,
  };
});

vi.mock(import('../../src/subsystems/ldap'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    person: mocks.person,
    personIds: mocks.personIds,
  };
});

vi.mock(import('../../src/utils/xml'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    createPostXMLPerson: mocks.createPostXMLPerson,
  };
});

vi.mock(import('../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    postQuery: mocks.postQuery,
  };
});

const fakeId = '{11111111-1111-1111-1111-111111111111}';
const app = await createApp();
const db = mocks.getDb();

describe('Role util tests', () => {
  describe('convertRDFToGUID', () => {
    it('Should return an error if not given a string', async () => {
      const test = convertRDFToGUID(app, 12345 as unknown as string) as Error;
      expect(test).toEqual(new Error('The post query result did not yield in a string'));
    });

    it('Should return a guid if given a valid string', async () => {
      const fakeString = '<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:oslc_am="http://open-services.net/ns/am#"><oslc_am:Resource rdf:about="https://sparxea.cedardev.cms.gov/CMS_Dev_Model/oslc/am/resource/el_{11111111-1111-1111-1111-111111111111}/"/></rdf:RDF>\r\n';
      const test = convertRDFToGUID(app, fakeString);
      expect(test).toEqual(fakeId);
    });
  });

  describe('createRelation', () => {
    it('Should return an object given three params', () => {
      const test = createRelation(fakeId, 'property', '1');
      expect(test).toEqual({
        fromid: fakeId,
        property: 'property',
        toref: '1',
      });
    });
  });

  describe('roleValid', () => {
    it('Should return an error if there is no object id', () => {
      const noObjectIdRoleObject = {
        roleTypeId: fakeId,
        assigneeId: fakeId,
        assigneeUserName: 'ABCD',
      };
      const test = roleValid(noObjectIdRoleObject);
      expect(test).toEqual(false);
    });

    it('Should return an error if there is no role type id', () => {
      const noObjectIdRoleObject = {
        objectId: fakeId,
        assigneeId: fakeId,
        assigneeUserName: 'ABCD',
      };
      const test = roleValid(noObjectIdRoleObject);
      expect(test).toEqual(false);
    });

    it('Should return an error if there is neither assigneeId or assigneeUserName', () => {
      const noObjectIdRoleObject = {
        objectId: fakeId,
        roleTypeId: fakeId,
      };
      const test = roleValid(noObjectIdRoleObject);
      expect(test).toEqual(false);
    });

    it('Should return a response if there are no issues', () => {
      const noObjectIdRoleObject = {
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
        assigneeUserName: 'ABCD',
      };
      const test = roleValid(noObjectIdRoleObject);
      expect(test).toEqual(true);
    });
  });

  describe('createPromise', () => {
    const goodRole = {
      objectId: fakeId,
      roleTypeId: fakeId,
      assigneeId: fakeId,
      assigneeUserName: 'ABCD',
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneePhone: '************',
      assigneeEmail: '<EMAIL>',
    };

    const RoleJustEssential = {
      objectId: fakeId,
      roleTypeId: fakeId,
      assigneeUserName: 'ABCD',
    };

    const goodRoleNoAssigneeID = {
      objectId: fakeId,
      roleTypeId: fakeId,
      assigneeUserName: 'ABCD',
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneePhone: '************',
      assigneeEmail: '<EMAIL>',
    };

    const noObjectIdBadRole = {
      objectId: null,
      roleTypeId: fakeId,
      assigneeId: fakeId,
      assigneeUserName: 'ABCD',
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneePhone: '************',
      assigneeEmail: '<EMAIL>',
    };

    const noRoleTypeIdBadRole = {
      objectId: fakeId,
      roleTypeId: null,
      assigneeId: fakeId,
      assigneeUserName: 'ABCD',
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneePhone: '************',
      assigneeEmail: '<EMAIL>',
    };

    const noAssigneeIdUserNameBadRole = {
      objectId: fakeId,
      roleTypeId: fakeId,
      assigneeId: null,
      assigneeUserName: null,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneePhone: '************',
      assigneeEmail: '<EMAIL>',
    };

    const personData = {
      id: fakeId,
      userName: 'ABCD',
      firstName: 'Test',
      lastName: 'User',
      phone: '************',
      email: '<EMAIL>',
    };

    const emptyFindSparxUserResult = {
      count: 0,
      users: [],
    };

    const sparxUserResult = {
      count: 1,
      users: [personData],
    };

    const LDAPUser = {
      dn: 'uid=ABCD,ou=people,dc=cms,dc=hhs,dc=gov',
      cn: 'Test User',
      mail: '<EMAIL>',
      givenName: 'Test',
      sn: 'User',
      telephoneNumber: '1234567890',
      uid: 'ABCD',
      ismemberof: [
        'cn=CEDAR_WMAPIGW_DEV_ADMIN,ou=Groups,dc=cms,dc=hhs,dc=gov',
        'cn=CEDAR_WMAPIGW_DEV_VIEWERS,ou=Groups,dc=cms,dc=hhs,dc=gov',
        'cn=CEDAR_WMAPIIS_DEV_ADMIN,ou=Groups,dc=cms,dc=hhs,dc=gov',
        'cn=CEDAR_WMAPIPORTAL_DEV_VIE,ou=Groups,dc=cms,dc=hhs,dc=gov',
        'cn=PROLAB_RO_DEV,ou=Groups,dc=cms,dc=hhs,dc=gov',
      ],
    };

    const goodXmlResponse = '<?xml version="1.0"?><rdf:RDF xmlns:oslc_am="http://open-services.net/ns/am#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"><oslc_am:Resource><dcterms:title>title</dcterms:title><dcterms:type>type</dcterms:type><ss:resourcetype></ss:resourcetype><ss:parentresourceidentifier>pk_undefined</ss:parentresourceidentifier><ss:useridentifier>Token</ss:useridentifier><ss:stereotype><ss:stereotypename><ss:name>Person</ss:name></ss:stereotypename></ss:stereotype></oslc_am:Resource></rdf:RDF>';
    const goodPostQueryResponse = '<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:oslc_am="http://open-services.net/ns/am#"><oslc_am:Resource rdf:about="https://sparxea.cedardev.cms.gov/CMS_Dev_Model/oslc/am/resource/el_{11111111-1111-1111-1111-111111111111}/"/></rdf:RDF>\r\n';

    afterEach(() => {
      // FIXME: Mocks don't appear to be self-cleaning. Reset before each use.
      mocks.findSparxUser.mockReset();
      mocks.person.mockReset();
      mocks.personIds.mockReset();
      mocks.createPostXMLPerson.mockReset();
      mocks.postQuery.mockReset();
      testLogger.resetMocks();
    });

    // error if there's no object id
    it('Should return an error if there is no object id', async () => {
      const test = await createPromise(app, db, noObjectIdBadRole) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Must have objectId');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Must have objectId');
    });

    // error if there's no role type id
    it('Should return an error if there is no role type id', async () => {
      const test = await createPromise(app, db, noRoleTypeIdBadRole) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Must have roleTypeId');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Must have roleTypeId');
    });

    it('Should return the role if there is an assigneeId', async () => {
      const test = await createPromise(app, db, goodRole);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
        assigneeUserName: 'ABCD',
        assigneeFirstName: 'Test',
        assigneeLastName: 'User',
        assigneePhone: '************',
        assigneeEmail: '<EMAIL>',
      }]);
    });

    it('Should return an error if there are neither assigneeId nor assigneeUserName', async () => {
      const test = await createPromise(app, db, noAssigneeIdUserNameBadRole) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No semi-required parameters provided');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('No semi-required parameters provided');
    });

    it('Should return a sparx user if available', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(sparxUserResult);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return a user if no sparx user is found and personById finds a user', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return a user if sparx returns a critical error and personById finds a user', async () => {
      mocks.findSparxUser.mockRejectedValueOnce(new Error('Sparx Critical Error'));
      mocks.personIds.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return a user if sparx returns an error and personById finds a user', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(new Error('Sparx Error'));
      mocks.personIds.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return a user if sparx returns a response with no users and personById finds a user', async () => {
      mocks.findSparxUser.mockResolvedValueOnce({ count: 0 });
      mocks.personIds.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return an error if there no other attributes to search and personById returns a critical error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockRejectedValueOnce(new Error('Critical personById Error'));
      const test = await createPromise(app, db, RoleJustEssential) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No properties given to search the user');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical personById Error');
    });

    it('Should return an error if there no other attributes to search and personById returns an error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce(new Error('personById Error'));
      const test = await createPromise(app, db, RoleJustEssential) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No properties given to search the user');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('personById Error');
    });

    it('Should return an error if there no other attributes to search and personById returns a malformed response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce('bad response');
      const test = await createPromise(app, db, RoleJustEssential) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No properties given to search the user');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toContain('bad response');
    });

    it('Should return an error if there no other attributes to search and personById returns an empty response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      const test = await createPromise(app, db, RoleJustEssential) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No properties given to search the user');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual([]);
    });

    it('Should return a user if both sparx and personById returns no result and there are other attributes to search', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      // Add good postQuery mock here
      mocks.postQuery.mockResolvedValueOnce(goodPostQueryResponse);
      const test = await createPromise(app, db, goodRoleNoAssigneeID);

      expect(test).toEqual([{
        objectId: fakeId,
        roleTypeId: fakeId,
        assigneeId: fakeId,
      }]);
    });

    it('Should return an error if both sparx, personById returns no result and person returns a critical error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockRejectedValueOnce(new Error('Critical person error'));
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No user found with that search criteria');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical person error');
    });

    it('Should return an error if both sparx, personById returns no result and person returns an error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce(new Error('Person error'));
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No user found with that search criteria');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Person error');
    });

    it('Should return an error if both sparx, personById returns no result and person returned a malformed response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce({});
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No user found with that search criteria');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual({});
    });

    it('Should return an error if both sparx, personById returns no result and person returned an empty response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([]);
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No user found with that search criteria');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual([]);
    });

    it('Should return an error in createPostXMLPerson returns a critical error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockRejectedValueOnce(new Error('Critical XML Error'));
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('The xmlBody sent is undefined');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical XML Error');
    });

    it('Should return an error in createPostXMLPerson returns an error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(new Error('Unable to retrieve system application'));
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('The xmlBody sent is undefined');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve system application');
    });

    it('Should return an error in createPostXMLPerson returns a malformed response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce({ xml: 'bad response' });
      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('The xmlBody sent is undefined');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual({ xml: 'bad response' });
    });

    it('Should return an error if postQuery returns a critical error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockRejectedValueOnce(new Error('Critical Query Error'));

      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Post query failed');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Query Error');
    });

    it('Should return an error if postQuery returns an error', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce(new Error('Query Error'));

      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Post query failed');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error.message).toContain('Query Error');
    });

    it('Should return an error if postQuery returns a malformed response', async () => {
      mocks.findSparxUser.mockResolvedValueOnce(emptyFindSparxUserResult);
      mocks.personIds.mockResolvedValueOnce([]);
      mocks.person.mockResolvedValueOnce([LDAPUser]);
      mocks.createPostXMLPerson.mockResolvedValueOnce(goodXmlResponse);
      mocks.postQuery.mockResolvedValueOnce({ xml: 'bad response' });

      const test = await createPromise(app, db, goodRoleNoAssigneeID) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Post query failed');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toEqual({ xml: 'bad response' });
    });
  });

  describe('createUpdateObj', () => {
    const iterationCount = '1';
    const assigneeIdUpdateObject = {
      assigneeId: fakeId,
      roleTypeId: fakeId,
      objectId: fakeId,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneeEmail: '<EMAIL>',
    };

    const assigneeUserNameUpdateObject = {
      roleTypeId: fakeId,
      objectId: fakeId,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneeUserName: 'ABCD',
      assigneeEmail: '<EMAIL>',
    };

    const uidUpdateObject = {
      roleTypeId: fakeId,
      objectId: fakeId,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      uid: 'ABCD',
      assigneeEmail: '<EMAIL>',
    };

    const errorUpdateObject = {
      roleTypeId: fakeId,
      objectId: fakeId,
      assigneeFirstName: 'Test',
      assigneeLastName: 'User',
      assigneeEmail: '<EMAIL>',
    };

    it('Should give proper relations with object, responsible, and roleType if assigneeId is given', async () => {
      const test = createUpdateObj(app, iterationCount, assigneeIdUpdateObject);
      expect(test).toEqual({
        Object: {
          ClassName: 'Role',
          Id: iterationCount,
          Values: {
            name: '',
          },
        },
        Relations: {
          relationOne: {
            fromid: '1',
            property: 'object',
            toref: fakeId,
          },
          relationTwo: {
            fromid: '1',
            property: 'responsible',
            toref: fakeId,
          },
          relationThree: {
            fromid: '1',
            property: 'roletype',
            toref: fakeId,
          },
        },
      });
    });

    it('Should give proper relations with object, responsible, and roleType if assigneeUserName is given', async () => {
      const test = createUpdateObj(app, iterationCount, assigneeUserNameUpdateObject);
      expect(test).toEqual({
        Object: {
          ClassName: 'Role',
          Id: iterationCount,
          Values: {
            name: '',
          },
        },
        Relations: {
          relationOne: {
            fromid: '1',
            property: 'object',
            toref: fakeId,
          },
          relationTwo: {
            fromid: '1',
            property: 'responsible',
            toref: 'ABCD',
          },
          relationThree: {
            fromid: '1',
            property: 'roletype',
            toref: fakeId,
          },
        },
      });
    });

    it('Should give proper relations with object, responsible, and uid if assigneeId is given', async () => {
      const test = createUpdateObj(app, iterationCount, uidUpdateObject);
      expect(test).toEqual({
        Object: {
          ClassName: 'Role',
          Id: iterationCount,
          Values: {
            name: '',
          },
        },
        Relations: {
          relationOne: {
            fromid: '1',
            property: 'object',
            toref: fakeId,
          },
          relationTwo: {
            fromid: '1',
            property: 'responsible',
            toref: 'ABCD',
          },
          relationThree: {
            fromid: '1',
            property: 'roletype',
            toref: fakeId,
          },
        },
      });
    });

    it('Should return an error if there were neither an assigneeId or username', async () => {
      const test = createUpdateObj(app, iterationCount, errorUpdateObject) as Error;
      expect(test).toEqual(new Error('There was no assigneeId or username provided'));
    });
  });
});
