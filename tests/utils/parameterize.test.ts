import { describe, expect, it } from 'vitest';
import Parameterize, {
  ParameterizedFunctions,
  ParameterizeDirections,
  ParameterizeLiterals,
  ParameterizeOrderByConfig,
} from '../../src/utils/db-helpers/parameterize';

// Invalid column names.
// noinspection SpellCheckingInspection
const getInvalidColumns = () => [
  'column_name[0]', // misplaced brackets
  'column-name', // hyphen
  'column.name', // period
  'column;name', // semicolon
  'column\'name', // single quote
  'column"name', // double quote
  'column`name', // backtick
  'column/name', // forward slash
  'column\\name', // backslash
  'column*name', // asterisk
  'column+name', // plus sign
  'column=name', // equals sign
  'column<name', // less than
  'column>name', // greater than
  'column&name', // ampersand
  'column|name', // pipe
  'column!name', // exclamation mark
  'column@name', // at symbol
  'column#name', // hash
  'column$name', // dollar sign
  'column%name', // percent sign
  'column^name', // caret
  'column~name', // tilde
  'column{name', // opening brace
  'column}name', // closing brace
  'column(name', // opening parenthesis
  'column)name', // closing parenthesis
  'column:name', // colon
  'column?name', // question mark
];

// Valid column names.
const getValidColumns = () => [
  'column_name',
  'ColumnName',
  'column123',
  'column_name_123',
  'column name',
  'column_name_with_underscores',
  'ColumnNameWithCamelCase',
  'column123name',
  'column name with spaces',
  '[column name with spaces]',
];

describe('Parameterize', () => {
  it('processes INSERT parameter placeholders correctly by default', () => {
    const harness = [
      {
        columns: ['c1'],
        values: [['v1']],
        literals: {},
        expected: ['(:p1)', { p1: 'v1' }],
      },
      {
        columns: ['c1', 'c2'],
        values: [['v1', 'v2']],
        literals: {},
        expected: ['(:p1, :p2)', { p1: 'v1', p2: 'v2' }],
      },
      {
        columns: ['c1', 'c2'],
        values: [['v1', 'v2'], ['v3', 'v4']],
        expected: ['(:p1, :p2), (:p3, :p4)', {
          p1: 'v1', p2: 'v2', p3: 'v3', p4: 'v4',
        }],
      },
      {
        columns: ['c1', 'c2'],
        values: [['v1', 'v2']],
        literals: <ParameterizeLiterals>{
          c1: ParameterizedFunctions.GETUTCDATE,
        },
        expected: ['(GETUTCDATE(), :p1)', { p1: 'v2' }],
      },
      {
        columns: ['c1', 'c2'],
        values: [['v1', 'v2'], ['v3', 'v4']],
        literals: <ParameterizeLiterals>{
          c2: ParameterizedFunctions.GETUTCDATE,
        },
        expected: [
          '(:p1, GETUTCDATE()), (:p2, GETUTCDATE())',
          {
            p1: 'v1',
            p2: 'v3',
          },
        ],
      },
    ];

    harness.forEach((
      {
        columns,
        values,
        literals,
        expected,
      },
    ) => {
      const v = Parameterize.insert(columns, values, literals);
      expect(v).toEqual(expected);
    });
  });

  it('processes ORDER BY parameter placeholders correctly', () => {
    const harness = [
      {
        config: <ParameterizeOrderByConfig>{
          c1: ParameterizeDirections.asc,
        },
        expected: {
          query: 'ORDER BY :o1 ASC',
          params: {
            o1: 'c1',
          },
        },
      },
      {
        config: <ParameterizeOrderByConfig>{
          c1: ParameterizeDirections.desc,
        },
        expected: {
          query: 'ORDER BY :o1 DESC',
          params: {
            o1: 'c1',
          },
        },
      },
      {
        config: <ParameterizeOrderByConfig>{
          c1: ParameterizeDirections.asc,
          c2: ParameterizeDirections.desc,
        },
        expected: {
          query: 'ORDER BY :o1 ASC, :o2 DESC',
          params: {
            o1: 'c1',
            o2: 'c2',
          },
        },
      },
      {
        config: <ParameterizeOrderByConfig>{
          c1: ParameterizeDirections.desc,
          c2: ParameterizeDirections.asc,
          c3: ParameterizeDirections.asc,
        },
        expected: {
          query: 'ORDER BY :o1 DESC, :o2 ASC, :o3 ASC',
          params: {
            o1: 'c1',
            o2: 'c2',
            o3: 'c3',
          },
        },
      },
    ];

    harness.forEach(({ config, expected }) => {
      const result = Parameterize.orderBy(config);
      expect(result).toEqual(expected);
    });
  });

  it('valid column names in INSERT do not cause exceptions when tested invalid characters', () => {
    getValidColumns().forEach((column) => {
      const result = Parameterize.insert([column], [['v1']]);
      expect(result).toBeDefined();
    });
  });

  it('invalid column names in INSERT cause exceptions when tested with invalid characters', () => {
    getInvalidColumns().forEach((column) => {
      const test = ((): Error | null => {
        try {
          Parameterize.insert([column], [['v1']]);
          return null;
        } catch (error) {
          if (error instanceof Error) {
            return error;
          }

          throw new Error('did not throw an error');
        }
      })();

      expect(test).toBeInstanceOf(Error);
      expect(test?.message).toEqual(`Parameterize::insert: invalid or unsafe column name: ${column}`);
    });
  });

  it('valid column names in ORDER BY do not cause exceptions when tested invalid characters', () => {
    getValidColumns().forEach((column) => {
      const result = Parameterize.orderBy({ [column]: ParameterizeDirections.asc });
      expect(result).toBeDefined();
    });
  });

  it('invalid column names in ORDER BY cause exceptions when tested with invalid characters', () => {
    getInvalidColumns().forEach((column) => {
      const test = ((): Error | null => {
        try {
          Parameterize.orderBy({ [column]: ParameterizeDirections.asc });
          return null;
        } catch (error) {
          if (error instanceof Error) {
            return error;
          }

          throw new Error('did not throw an error');
        }
      })();

      expect(test).toBeInstanceOf(Error);
      expect(test?.message).toEqual(`Parameterize::orderBy: invalid or unsafe column name: ${column}`);
    });
  });
});
