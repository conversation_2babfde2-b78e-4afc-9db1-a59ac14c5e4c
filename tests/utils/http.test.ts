import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import {
  getRequest,
  postRequest,
  putRequest,
  patchRequest,
  deleteRequest,
} from '../../src/utils/http';
import { testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';

const mocks = vi.hoisted(() => ({
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn(),
}));

vi.mock('axios', async () => ({
  default: {
    get: mocks.get,
    post: mocks.post,
    put: mocks.put,
    patch: mocks.patch,
    delete: mocks.delete,
  },
}));

const app = {
  logger: testLogger,
} as unknown as CMSApp;

describe('HTTP Utilities tests', () => {
  const testResponse = { test: 'data' };
  describe('getRequest', () => {
    it('Should return the response', async () => {
      mocks.get.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(testResponse);
      }));

      const test = await getRequest(app, '');
      expect(test).toEqual(testResponse);
    });

    it('Should return the error', async () => {
      mocks.get.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Get Connection'));
      }));

      const test = await getRequest(app, '');
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Get Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('Bad Get Connection');
    });
  });

  describe('postRequest', () => {
    it('Should return the response', async () => {
      mocks.post.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(testResponse);
      }));

      const test = await postRequest(app, '', {});
      expect(test).toEqual(testResponse);
    });

    it('Should return the error', async () => {
      mocks.post.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Post Connection'));
      }));

      const test = await postRequest(app, '', {});
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Post Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('Bad Post Connection');
    });
  });

  describe('putRequest', () => {
    it('Should return the response', async () => {
      mocks.put.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(testResponse);
      }));

      const test = await putRequest(app, '', {});
      expect(test).toEqual(testResponse);
    });

    it('Should return the error', async () => {
      mocks.put.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Put Connection'));
      }));

      const test = await putRequest(app, '', {});
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Put Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('Bad Put Connection');
    });
  });

  describe('patchRequest', () => {
    it('Should return the response', async () => {
      mocks.patch.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(testResponse);
      }));

      const test = await patchRequest(app, '', {});
      expect(test).toEqual(testResponse);
    });

    it('Should return the error', async () => {
      mocks.patch.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Patch Connection'));
      }));

      const test = await patchRequest(app, '', {});
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Patch Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('Bad Patch Connection');
    });
  });

  describe('deleteRequest', () => {
    it('Should return the response', async () => {
      mocks.delete.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(testResponse);
      }));

      const test = await deleteRequest(app, '');
      expect(test).toEqual(testResponse);
    });

    it('Should return the error', async () => {
      mocks.delete.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Delete Connection'));
      }));

      const test = await deleteRequest(app, '');
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Delete Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toEqual('Bad Delete Connection');
    });
  });
});
