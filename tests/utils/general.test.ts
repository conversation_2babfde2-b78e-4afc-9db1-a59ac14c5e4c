import { expect, describe, it } from 'vitest';
import {
  tryAsync,
  tryCatch,
  strToBool,
  strToBoolOrNull,
  boolToStr,
  renameKey,
  renameKeys,
  iterableToArray,
  strToNumber,
  strToNumberOrNull,
  splitOrNull,
} from '../../src/utils/general';

describe('General util tests', () => {
  describe('strToBool', () => {
    it('Should return false if a string that is not true is passed', () => {
      const test = strToBool('false');
      expect(test).toBe(false);
    });

    it('Should return false if anything other than string or boolean is passed', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = strToBool(789);
      expect(test).toBe(false);
    });

    it('Should return false if the string "No" is passed', () => {
      const test = strToBool('No');
      expect(test).toBe(false);
    });

    it('Should return true if the string "Yes" is passed', () => {
      const test = strToBool('Yes');
      expect(test).toBe(true);
    });

    it('Should return false if the string "0" is passed', () => {
      const test = strToBool('0');
      expect(test).toBe(false);
    });

    it('Should return true if the string "1" is passed', () => {
      const test = strToBool('1');
      expect(test).toBe(true);
    });

    it('Should return true if the string "true" is passed', () => {
      const test = strToBool('true');
      expect(test).toBe(true);
    });

    it('Should return the boolean passed if a boolean is passed', () => {
      const test = strToBool(true);
      expect(test).toBe(true);

      const test2 = strToBool(false);
      expect(test2).toBe(false);
    });
  });

  describe('strToBoolOrNull', () => {
    it('Should return null if passed null', () => {
      const test = strToBoolOrNull(null);
      expect(test).toEqual(null);
    });

    it('Should return false if passed "null"', () => {
      const test = strToBoolOrNull('null');
      expect(test).toEqual(false);
    });

    it('Should return false if passed "false"', () => {
      const test = strToBoolOrNull('false');
      expect(test).toEqual(false);
    });

    it('Should return false if passed "No"', () => {
      const test = strToBoolOrNull('No');
      expect(test).toEqual(false);
    });

    it('Should return false if passed false', () => {
      const test = strToBoolOrNull(false);
      expect(test).toEqual(false);
    });

    it('Should return true if passed "true"', () => {
      const test = strToBoolOrNull('true');
      expect(test).toEqual(true);
    });

    it('Should return true if passed "Yes"', () => {
      const test = strToBoolOrNull('Yes');
      expect(test).toEqual(true);
    });

    it('Should return true if passed true', () => {
      const test = strToBoolOrNull(true);
      expect(test).toEqual(true);
    });
  });

  describe('strToNumber', () => {
    it('Should return a number when a string is passed', () => {
      const test = strToNumber('17');
      expect(test).toEqual(17);
    });

    it('Should return 0 when a string is not passed', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = strToNumber();
      expect(test).toEqual(0);
    });

    it('Should return 0 when a string can not be converted', () => {
      const test = strToNumber('bad');
      expect(test).toEqual(0);
    });
  });

  describe('strToNumberOrNull', () => {
    it('Should return null if a null value is passed', () => {
      const test = strToNumberOrNull(null);
      expect(test).toBeNull();
    });

    it('Should return a number when a string is passed', () => {
      const test = strToNumberOrNull('17');
      expect(test).toEqual(17);
    });

    it('Should return 0 when a string can not be converted', () => {
      const test = strToNumberOrNull('bad');
      expect(test).toEqual(0);
    });
  });

  describe('splitOrNull', () => {
    it('Should split a string based on a passed delimiter', () => {
      const test = splitOrNull('this | is | a | test', '|');
      expect(test).toStrictEqual(['this ', ' is ', ' a ', ' test']);
    });

    it('Should return the entire string if no delimiter is passed', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = splitOrNull('this | is | a | test');
      expect(test).toStrictEqual(['this | is | a | test']);
    });

    it('Should return null if null is passed', () => {
      const test = splitOrNull(null, '|');
      expect(test).toBeNull();
    });

    it('Should return null if something other than a string is passed', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = splitOrNull(42, '|');
      expect(test).toBeNull();
    });
  });

  describe('boolToStr', () => {
    it('Should return "Yes" if the boolean is true and isYesNo is true', () => {
      const test = boolToStr(true, true);
      expect(test).toEqual('Yes');
    });

    it('Should return "true" if the boolean is true and isYesNo is false', () => {
      const test = boolToStr(true);
      expect(test).toEqual('true');
    });

    it('Should return "No" if the boolean is false and isYesNo is true', () => {
      const test = boolToStr(false, true);
      expect(test).toEqual('No');
    });

    it('Should return "false" if the boolean is false and isYesNo is false', () => {
      const test = boolToStr(false);
      expect(test).toEqual('false');
    });
  });

  describe('tryAsync', () => {
    const testFunc = () => new Promise((resolve) => {
      resolve({ test: 'success' });
    });
    const failFunc = () => new Promise((resolve, reject) => {
      reject(new Error('failed'));
    });

    it('Should return a promise', async () => {
      const [error, success] = await tryAsync(testFunc());
      expect(error).toBeNull();
      expect(success).toEqual({ test: 'success' });
    });

    it('Should return an error', async () => {
      const [error, success] = await tryAsync(failFunc());
      expect(success).toBeUndefined();
      if (error instanceof Error) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toEqual('failed');
      }
    });
  });

  describe('tryCatch', () => {
    it('Should return the thrown error without parameters', () => {
      const testFunc = () => {
        throw new Error('Bad Connection');
      };

      const test = tryCatch(testFunc);
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return the thrown error with parameters', () => {
      const testFunc = (input: string) => {
        throw new Error(`Bad ${input} Connection`);
      };

      const test = tryCatch(testFunc, 'Testing');
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Testing Connection');
    });

    it('Should return the function result without parameters', () => {
      const testFunc = () => 'success';

      const test = tryCatch(testFunc);
      expect(test).toEqual('success');
    });

    it('Should return the function result with parameters', () => {
      const testFunc = (input: string) => `success ${input}`;

      const test = tryCatch(testFunc, 'testing');
      expect(test).toEqual('success testing');
    });
  });

  describe('renameKey', () => {
    const testData = {
      foo: 'bar1',
      bad: 'biz1',
      badTime: '05-22-2019',
      badValue: '',
    };

    it('Should not change any keys if the old Key equals the new key', () => {
      renameKey(testData, 'bad', 'bad');

      expect(testData).toEqual(testData);
    });

    it('Should rename the old key to the new key without changing the value', () => {
      const testResult = {
        foo: 'bar1',
        baz: 'biz1',
        badTime: '05-22-2019',
        badValue: '',
      };
      renameKey(testData, 'bad', 'baz');

      expect(testData).toEqual(testResult);
    });

    it('Should rename the old key to the new key and format the date value', () => {
      const testResult = {
        foo: 'bar1',
        baz: 'biz1',
        goodTime: '22May2019',
        badValue: '',
      };
      renameKey(testData, 'badTime', 'goodTime', 'DDMMMYYYY');

      expect(testData).toEqual(testResult);
    });

    it('Should rename the old key to the new key and change the value to null', () => {
      const testResult = {
        foo: 'bar1',
        baz: 'biz1',
        goodTime: '22May2019',
        goodValue: null,
      };
      renameKey(testData, 'badValue', 'goodValue', undefined, true);

      expect(testData).toEqual(testResult);
    });
  });

  describe('renameKeys', () => {
    it('Should rename the requested key', () => {
      const testData = [{
        foo: 'bar1',
        bad: 'biz1',
      }, {
        foo: 'bar2',
        bad: 'biz2',
      }];

      const testResult = [{
        foo: 'bar1',
        baz: 'biz1',
      }, {
        foo: 'bar2',
        baz: 'biz2',
      }];

      const test = renameKeys(testData, [{
        oldKey: 'bad',
        newKey: 'baz',
      }]);

      expect(test).toEqual(testResult);
    });
  });

  describe('iterableToArray', () => {
    it('Should convert an iterable into an array', () => {
      const iterable = new Set(['a', 'b', 'c']);
      expect(iterableToArray(iterable)).toEqual(['a', 'b', 'c']);
    });

    it('Should work with an empty iterable', () => {
      const iterable = new Set([]);
      expect(iterableToArray(iterable)).toEqual([]);
    });

    it('Should work with an iterable that has repeated values like an array', () => {
      const iterable = ['x', 'y', 'x'];
      expect(iterableToArray(iterable)).toEqual(['x', 'y', 'x']);
    });

    it('should work with a generator function', () => {
      function* generator() {
        yield 'first';
        yield 'second';
        yield 'third';
      }
      expect(iterableToArray(generator())).toEqual(['first', 'second', 'third']);
    });
  });
});
