import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import { cedarUrlsUtil, censusUrlsUtil } from '../../src/utils/urls';
import { CMSApp } from '../../src/types';
import {
  createApp,
  testLogger,
} from '../test-utils';
import MssqlData from '../../src/data-sources/mssql';

const systemId = '{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}';

const emptyResults = [
];

const cedarFullResults = [
  {
    urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
    address: 'webmethods-apiportal.cedar.cms.gov',
    isApiEndpoint: 'No',
    isBehindWebApplicationFirewall: 'Yes',
    isVersionCodeRepository: 'No',
    urlHostingEnv: 'API Gateway/API Portal',
  },
  {
    urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
    address: 'www.cedarimpl.cms.gov',
    isApiEndpoint: 'No',
    isBehindWebApplicationFirewall: 'Yes',
    isVersionCodeRepository: 'No',
    urlHostingEnv: 'Implementation',
  },
];

const cedarFullProcessedResults = [{
  urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'webmethods-apiportal.cedar.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'API Gateway/API Portal',
},
{
  urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
  address: 'www.cedarimpl.cms.gov',
  isApiEndpoint: false,
  isBehindWebApplicationFirewall: true,
  isVersionCodeRepository: false,
  urlHostingEnv: 'Implementation',
}];

const censusFullResults = [
  {
    urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
    link: 'webmethods-apiportal.cedar.cms.gov',
    urlApiEndpoint: 'No',
    urlApiWaf: 'Yes',
    providesVerCodeAccess: 'No',
    urlHostingEnv: 'API Gateway/API Portal',
  },
  {
    urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
    link: 'www.cedarimpl.cms.gov',
    urlApiEndpoint: 'No',
    urlApiWaf: 'Yes',
    providesVerCodeAccess: 'No',
    urlHostingEnv: 'Implementation',
  },
];

const censusFullProcessedResults = [{
  urlId: '{12345678-1234-2345-3456-1B7E43AB8EC6}',
  link: 'webmethods-apiportal.cedar.cms.gov',
  urlApiEndpoint: false,
  urlApiWaf: true,
  providesVerCodeAccess: false,
  urlHostingEnv: 'API Gateway/API Portal',
},
{
  urlId: '{22345678-1234-2345-3456-1B7E43AB8EC6}',
  link: 'www.cedarimpl.cms.gov',
  urlApiEndpoint: false,
  urlApiWaf: true,
  providesVerCodeAccess: false,
  urlHostingEnv: 'Implementation',
}];

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  getDb: vi.fn().mockReturnValue({}),
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
const db = mocks.db();

describe('URL util tests', () => {
  describe('cedarUrlsUtil', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await cedarUrlsUtil(null as unknown as CMSApp, db, systemId);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await cedarUrlsUtil(app, null as unknown as MssqlData, systemId);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await cedarUrlsUtil(app, new Error('Database Error') as unknown as MssqlData, systemId);

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if the systemId is empty', async () => {
      const test = await cedarUrlsUtil(app, db, '') as Error;
      expect(test.message).toEqual('Please provide required parameters \'systemId\'');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Please provide required parameters \'systemId\'');
    });

    it('Should return an error if the systemId is invalid', async () => {
      const test = await cedarUrlsUtil(app, db, '/{1234-bad2-uuid-and3-1234}') as Error;
      expect(test.message).toEqual('The system ID is not valid');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await cedarUrlsUtil(app, db, systemId);

      expect(test).toEqual(new Error('There was an error fetching the URLs'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await cedarUrlsUtil(app, db, systemId);

      expect(test).toEqual(new Error('There was an error fetching the URLs'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return with a list of all URLs with the systemId', async () => {
      mocks.queryView.mockResolvedValueOnce([cedarFullResults]);
      const test = await cedarUrlsUtil(app, db, systemId);
      expect(test).toEqual(cedarFullProcessedResults);
    });

    it('Should return an empty arrayif no URLs are found', async () => {
      mocks.queryView.mockResolvedValueOnce([emptyResults]);
      const test = await cedarUrlsUtil(app, db, '{113BE457-783E-413e-99A2-FB52C55A1616}');
      expect(test).toEqual([]);
    });
  });

  describe('censusUrlsUtil', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await censusUrlsUtil(null as unknown as CMSApp, db, systemId);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await censusUrlsUtil(app, null as unknown as MssqlData, systemId);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await censusUrlsUtil(app, new Error('Database Error') as unknown as MssqlData, systemId);
      expect(test).toEqual(new Error('Database Unavailable'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if the systemId is empty', async () => {
      const test = await censusUrlsUtil(app, db, '') as Error;
      expect(test.message).toEqual('Please provide required parameters \'systemId\'');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Please provide required parameters \'systemId\'');
    });

    it('Should return an error if the systemId is invalid', async () => {
      const test = await censusUrlsUtil(app, db, '/{1234-bad2-uuid-and3-1234}') as Error;
      expect(test.message).toEqual('The system ID is not valid');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await censusUrlsUtil(app, db, systemId);

      expect(test).toEqual(new Error('There was an error fetching the URLs'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await censusUrlsUtil(app, db, systemId);

      expect(test).toEqual(new Error('There was an error fetching the URLs'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return with a list of all URLs with the systemId', async () => {
      mocks.queryView.mockResolvedValueOnce([censusFullResults]);
      const test = await censusUrlsUtil(app, db, systemId);
      expect(test).toEqual(censusFullProcessedResults);
    });

    it('Should return an empty arrayif no URLs are found', async () => {
      mocks.queryView.mockResolvedValueOnce([emptyResults]);
      const test = await censusUrlsUtil(app, db, '{113BE457-783E-413e-99A2-FB52C55A1616}');
      expect(test).toEqual([]);
    });
  });
});
