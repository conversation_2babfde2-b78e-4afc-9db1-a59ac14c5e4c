import { InsertPersonSubDataSP } from '../../src/types';

const faultyParam = 123498;

const goodQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const goodMultipleQueryData: InsertPersonSubDataSP[] = [{
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
}, {
  externalSource: 'exSource',
  firstName: 'Test2',
  lastName: 'Data2',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCE',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111112}',
}, {
  externalSource: 'exSource',
  firstName: 'Test3',
  lastName: 'Data3',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCE',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111113}',
}];

const formattedGoodQueryData = [
  {
    name: 'Email',
    value: '<EMAIL>',
  }, {
    name: 'ExternalSource',
    value: 'exSource',
  }, {
    name: 'FirstName',
    value: 'Test',
  }, {
    name: 'LastName',
    value: 'Data',
  }, {
    name: 'Phone',
    value: '************',
  }, {
    name: 'TechName',
    value: 'Test Data',
  }, {
    name: 'UserName',
    value: 'ABCD',
  }, {
    name: 'GUID',
    value: '{11111111-1111-1111-1111-111111111111}',
  }];

const badExternalSourceQueryData: InsertPersonSubDataSP = {
  externalSource: faultyParam as unknown as string,
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badFirstNameQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: faultyParam as unknown as string,
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badLastNameQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: faultyParam as unknown as string,
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badPhoneQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: faultyParam as unknown as string,
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badTechNameQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: faultyParam as unknown as string,
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badUserNameQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: faultyParam as unknown as string,
  email: '<EMAIL>',
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badEmailQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: faultyParam as unknown as string,
  GUID: '{11111111-1111-1111-1111-111111111111}',
};

const badGUIDQueryData: InsertPersonSubDataSP = {
  externalSource: 'exSource',
  firstName: 'Test',
  lastName: 'Data',
  phone: '************',
  techName: 'Test Data',
  userName: 'ABCD',
  email: '<EMAIL>',
  GUID: faultyParam as unknown as string,
};

const mockFailedInsertPersonResults = [
  [
    {
      PropertyID: 12345678,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: null,
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      PropertyID: 23456789,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: 'John Smith',
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      queryStatus: 1,
      result: null,
    },
  ], 2,
];

const mockInsertPersonResults = [
  [
    {
      PropertyID: 12345678,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: null,
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      PropertyID: 23456789,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: 'John Smith',
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      queryStatus: 0,
      result: null,
    },
  ], 2,
];

const mockMultipleFailedInsertPersonResults = [[
  [
    {
      PropertyID: 12345678,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: null,
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      PropertyID: 23456789,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: 'John Smith',
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      queryStatus: 1,
      result: null,
    },
  ], 2,
], [
  [
    {
      PropertyID: 12345678,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: null,
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      PropertyID: 23456789,
      Object_ID: 0,
      Property: 'Fake Prop',
      Value: 'John Smith',
      Notes: '',
      ea_guid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      queryStatus: 1,
      result: null,
    },
  ], 2,
]];

export {
  goodQueryData,
  formattedGoodQueryData,
  goodMultipleQueryData,
  badExternalSourceQueryData,
  badFirstNameQueryData,
  badLastNameQueryData,
  badPhoneQueryData,
  badTechNameQueryData,
  badUserNameQueryData,
  badEmailQueryData,
  badGUIDQueryData,
  mockFailedInsertPersonResults,
  mockInsertPersonResults,
  mockMultipleFailedInsertPersonResults,
};
