import {
  expect,
  describe,
  vi,
  it,
} from 'vitest';
import { get } from 'lodash';
import {
  createApp,
  testLogger,
} from '../test-utils';
import { CMSApp, XmlParamsObject } from '../../src/types';
import { createPostXMLPerson } from '../../src/utils/xml';

const mocks = vi.hoisted(() => ({
  getToken: vi.fn(),
  getPackageParentId: vi.fn(),
  getSecret: vi.fn().mockImplementation(() => ({
    'admin-user': 'admin-pass',
  })),
  getDuration: vi.fn().mockImplementation(() => -3939363851),
  addToUTC: vi.fn().mockImplementation(() => '2024-10-31T14:00:12.123Z'),
}));

vi.mock(import('../../src/subsystems/secrets'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSecret: mocks.getSecret,
  };
});

vi.mock(import('../../src/subsystems/sparxea'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getToken: mocks.getToken,
    getPackageParentId: mocks.getPackageParentId,
  };
});
const app = await createApp();
const testParamsObject: XmlParamsObject = {
  title: 'title',
  type: 'type',
  stereotype: 'Person',
};

describe('XML util tests', () => {
  describe('createPostXML', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await createPostXMLPerson(null as unknown as CMSApp, testParamsObject) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve system application');
    });

    it('Should return an error if there is an issue with getToken', async () => {
      mocks.getToken.mockResolvedValueOnce(new Error('Could not get token'));
      const test = await createPostXMLPerson(app, testParamsObject) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Could not get token');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Could not get token');
    });

    it('Should return an error if there is an issue with getPackageParentId', async () => {
      mocks.getToken.mockResolvedValueOnce('Token');
      mocks.getPackageParentId.mockResolvedValueOnce(new Error('Could not get package parent id'));
      const test = await createPostXMLPerson(app, testParamsObject) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Could not get package parent id');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Could not get package parent id');
    });

    it('Should return a string if there are no issues', async () => {
      mocks.getToken.mockResolvedValueOnce('Token');
      const test = await createPostXMLPerson(app, testParamsObject);
      expect(test).toEqual('<?xml version="1.0"?><rdf:RDF xmlns:oslc_am="http://open-services.net/ns/am#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:dcterms="http://purl.org/dc/terms/" xmlns:ss="http://www.sparxsystems.com.au/oslc_am#"><oslc_am:Resource><dcterms:title>title</dcterms:title><dcterms:type>type</dcterms:type><ss:resourcetype></ss:resourcetype><ss:parentresourceidentifier>pk_undefined</ss:parentresourceidentifier><ss:useridentifier>Token</ss:useridentifier><ss:stereotype><ss:stereotypename><ss:name>Person</ss:name></ss:stereotypename></ss:stereotype></oslc_am:Resource></rdf:RDF>');
    });
  });
});
