import {
  describe,
  it,
  vi,
  expect,
  afterEach,
} from 'vitest';
import express from 'express';
import { set } from 'lodash';
import { registerAppDataSources } from '../../src/core/app-factory/data-sources';
import {
  getDb,
  checkEmpty,
  handleOperation,
  handleBetween,
  handleLike,
  processWhereObject,
  handleAnd,
  handleOr,
  handleGrouped,
  processWhere,
  seedDb,
} from '../../src/utils/db-helpers';
import { CMSApp, DataOptions, WhereResult } from '../../src/types';
import { createApp, TestDataSource, testLogger } from '../test-utils';

describe('DB Helpers Utilities tests', () => {
  const app = express() as CMSApp;
  registerAppDataSources(app);
  set(app, 'config', {
    systems: {
      core: {
        dataSource: 'core',
      },
      badSource: {
        dataSource: 'badSource',
      },
    },
  });

  describe('getDb', () => {
    it('Should return an error if the name is not found in the config', () => {
      const db = getDb(app, 'badSystem') as Error;

      expect(db).toBeInstanceOf(Error);
      expect(db.message).toEqual('Unable to find system in configuration for badSystem');
    });

    it('Should return an error if the data source is not found in the app.dataSources', () => {
      const db = getDb(app, 'core') as Error;

      expect(db).toBeInstanceOf(Error);
      expect(db.message).toEqual('Unable to find data source in configuration for core');
    });

    it('Should return an error if the DB is not an instance of DataSource', () => {
      set(app, 'dataSources.badSource', {});
      const db = getDb(app, 'badSource') as Error;
      expect(db).toBeInstanceOf(Error);
      expect(db.message).toEqual('Data source badSource is not a valid DataSource instance');
      testLogger.resetMocks();
    });

    it('Should return a data source', () => {
      app.dataSources.register<TestDataSource, DataOptions>(app, 'core', TestDataSource);

      const db = getDb(app, 'core');
      expect(db).toBeInstanceOf(TestDataSource);
      const firstCallArgs = testLogger.debug.mock.calls[0];
      expect(firstCallArgs).toHaveLength(1);
      expect(firstCallArgs[0]).toEqual('Testing');
      testLogger.resetMocks();
    });
  });

  describe('checkEmpty', () => {
    it('Should return true if the item is an empty string', () => {
      const test = checkEmpty('');
      expect(test).toEqual(true);
    });

    it('Should return true if the item is a number and not 0', () => {
      const test = checkEmpty(10);
      expect(test).toEqual(false);
    });

    it('Should return true if the item is a number and 0', () => {
      const test = checkEmpty(0);
      expect(test).toEqual(false);
    });

    it('Should return false if the item is a string with content', () => {
      const test = checkEmpty('test');
      expect(test).toEqual(false);
    });

    it('Should return false if the item is not a string or number', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test1 = checkEmpty(['test']);
      expect(test1).toEqual(false);

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test2 = checkEmpty({ foo: 'test' });
      expect(test2).toEqual(false);
    });
  });

  describe('handleOperation', () => {
    it('Should return an error if the column is empty', () => {
      const operation = {
        column: '',
        operator: '',
        value: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleOperation(operation, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should return an error if the operator is empty', () => {
      const operation = {
        column: 'foo',
        operator: '',
        value: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleOperation(operation, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should return an error if the value is empty', () => {
      const operation = {
        column: 'foo',
        operator: '=',
        value: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleOperation(operation, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should update provided object with query and params with prepend', () => {
      const operation = {
        column: 'foo',
        operator: '=',
        value: 'bar',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleOperation(operation, result, ' AND ');
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND "foo" = :opParams1');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should update provided object with query and params without prepend', () => {
      const operation = {
        column: 'foo',
        operator: '=',
        value: 'bar',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleOperation(operation, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual('"foo" = :opParams1');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });
  });

  describe('handleBetween', () => {
    it('Should return an error if the column is empty', () => {
      const between = {
        column: '',
        start: '',
        stop: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleBetween(between, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "between" object is missing a required field');
    });

    it('Should return an error if the start is empty', () => {
      const between = {
        column: 'foo',
        start: '',
        stop: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleBetween(between, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "between" object is missing a required field');
    });

    it('Should return an error if the stop is empty', () => {
      const between = {
        column: 'foo',
        start: 12345,
        stop: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleBetween(between, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "between" object is missing a required field');
    });

    it('Should update provided object with query with prepend', () => {
      const between = {
        column: 'foo',
        start: 12345,
        stop: 54321,
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleBetween(between, result, ' AND ');
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND ("foo" BETWEEN 12345 AND 54321)');
    });

    it('Should update provided object with query without prepend', () => {
      const between = {
        column: 'foo',
        start: 12345,
        stop: 54321,
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleBetween(between, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual('("foo" BETWEEN 12345 AND 54321)');
    });
  });

  describe('handleLike', () => {
    it('Should return an error if the column is empty', () => {
      const like = {
        column: '',
        value: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleLike(like, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "like" object is missing a required field');
    });

    it('Should return an error if the value is empty', () => {
      const like = {
        column: 'foo',
        value: '',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleLike(like, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "like" object is missing a required field');
    });

    it('Should update provided object with query and params with prepend', () => {
      const like = {
        column: 'foo',
        value: 'bar',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleLike(like, result, ' AND ');
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND ("foo" LIKE :opParams1)');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should update provided object with query and params without prepend', () => {
      const like = {
        column: 'foo',
        value: 'bar',
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleLike(like, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual('("foo" LIKE :opParams1)');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });
  });

  describe('processWhereObject', () => {
    it('Should return an error if no actions are provided', () => {
      const obj = {};
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object must have only have one option');
    });

    it('Should return an error if more than one action is provided', () => {
      const obj = {
        operation: {
          column: '',
          operator: '',
          value: '',
        },
        between: {
          column: '',
          start: '',
          stop: '',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object must have only have one option');
    });

    it('Should return an error if handleOperation returns an error', () => {
      const obj = {
        operation: {
          column: '',
          operator: '',
          value: '',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should update thee result object if handleOperation is a success', () => {
      const obj = {
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '');
      expect(test).toEqual(true);
      expect(result.query).toEqual('"foo" = :opParams1');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should return an error if handleBetween returns an error', () => {
      const obj = {
        between: {
          column: '',
          start: '',
          stop: '',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "between" object is missing a required field');
    });

    it('Should update the result object if handleBetween is a success', () => {
      const obj = {
        between: {
          column: 'foo',
          start: 12345,
          stop: 54321,
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '');
      expect(test).toEqual(true);
      expect(result.query).toEqual('("foo" BETWEEN 12345 AND 54321)');
    });

    it('Should return an error if handleLike returns an error', () => {
      const obj = {
        like: {
          column: '',
          value: '',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "like" object is missing a required field');
    });

    it('Should update thee result object if handleLike is a success', () => {
      const obj = {
        like: {
          column: 'foo',
          value: 'bar',
        },
      };
      const result = {
        query: '',
        params: {},
      };

      const test = processWhereObject(obj, result, '');
      expect(test).toEqual(true);
      expect(result.query).toEqual('("foo" LIKE :opParams1)');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });
  });

  describe('handleAnd', () => {
    it('Should return an error if the and array is not an array', () => {
      const and = { not: 'an array' };
      const result = {
        query: '',
        params: {},
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = handleAnd(and, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "and" array is empty or invalid');
    });

    it('Should return an error if the and array is empty', () => {
      const and = [];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "and" array is empty or invalid');
    });

    it('Should return an error if any and object has more than 1 process', () => {
      const and = [{
        operation: {
          column: '',
          operator: '',
          value: '',
        },
        between: {
          column: '',
          start: '',
          stop: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "and" array objects can only have 1 option per object');
    });

    it('Should return an error if the only process has an error', () => {
      const and = [{
        operation: {
          column: '',
          operator: '',
          value: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should return an error if one of the processes has an error', () => {
      const and = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }, {
        operation: {
          column: '',
          operator: '',
          value: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should update the result object with one process', () => {
      const and = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND "foo" = :opParams1');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should update the result object with multiple processes', () => {
      const and = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }, {
        operation: {
          column: 'biz',
          operator: '=',
          value: 'baz',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleAnd(and, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND "foo" = :opParams1 AND "biz" = :opParams2');
      expect(result.params).toEqual({
        opParams1: 'bar',
        opParams2: 'baz',
      });
    });
  });

  describe('handleOr', () => {
    it('Should return an error if the or array is not an array', () => {
      const or = { not: 'an array' };
      const result = {
        query: '',
        params: {},
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = handleOr(or, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "or" array is empty or invalid');
    });

    it('Should return an error if the or array is empty', () => {
      const or = [];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "or" array is empty or invalid');
    });

    it('Should return an error if any or object has more than 1 process', () => {
      const or = [{
        operation: {
          column: '',
          operator: '',
          value: '',
        },
        between: {
          column: '',
          start: '',
          stop: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "or" array objects can only have 1 option per object');
    });

    it('Should return an error if the only process has an error', () => {
      const or = [{
        operation: {
          column: '',
          operator: '',
          value: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should return an error if one of the processes has an error', () => {
      const or = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }, {
        operation: {
          column: '',
          operator: '',
          value: '',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should update the result object with one process', () => {
      const or = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' OR "foo" = :opParams1');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should update the result object with multiple processes', () => {
      const or = [{
        operation: {
          column: 'foo',
          operator: '=',
          value: 'bar',
        },
      }, {
        operation: {
          column: 'biz',
          operator: '=',
          value: 'baz',
        },
      }];
      const result = {
        query: '',
        params: {},
      };

      const test = handleOr(or, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' OR "foo" = :opParams1 OR "biz" = :opParams2');
      expect(result.params).toEqual({
        opParams1: 'bar',
        opParams2: 'baz',
      });
    });
  });

  describe('handleGrouped', () => {
    it('Should return an error if there are no processes in the object', () => {
      const grouped = {};
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "grouped" object must have only have one option');
    });

    it('Should return an error if there are more than one process in the object', () => {
      const grouped = {
        and: [],
        or: [],
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "grouped" object must have only have one option');
    });

    it('Should return an error if the handleAnd process returns an error', () => {
      const grouped = {
        and: [{
          operation: {
            column: '',
            operator: '',
            value: '',
          },
        }],
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "operator" object is missing a required field');
    });

    it('Should return an error if the handleOr process returns an error', () => {
      const grouped = {
        or: [{
          between: {
            column: '',
            start: '',
            stop: '',
          },
        }],
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "between" object is missing a required field');
    });

    it('Should update the result object with the grouped AND', () => {
      const grouped = {
        and: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        }, {
          between: {
            column: 'bin',
            start: 12345,
            stop: 54321,
          },
        }],
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' AND ("foo" = :opParams1 AND ("bin" BETWEEN 12345 AND 54321))');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });

    it('Should update the result object with the grouped OR', () => {
      const grouped = {
        or: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        }, {
          between: {
            column: 'bin',
            start: 12345,
            stop: 54321,
          },
        }],
      };
      const result = {
        query: '',
        params: {},
      };

      const test = handleGrouped(grouped, result);
      expect(test).toEqual(true);
      expect(result.query).toEqual(' OR ("foo" = :opParams1 OR ("bin" BETWEEN 12345 AND 54321))');
      expect(result.params).toEqual({
        opParams1: 'bar',
      });
    });
  });

  describe('processWhere', () => {
    it('Should return an error if where is not in the whereObj', () => {
      const whereObj = {};

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object is required and must be an object');
    });

    it('Should return an error if where is not a plain object', () => {
      const whereObj = {
        where: ['not an object'],
      };

      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object is required and must be an object');
    });

    it('Should return an error if where is empty', () => {
      const whereObj = {
        where: {},
      };

      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object is required and must be an object');
    });

    it('Should return an error if processWhereObject returns an error', () => {
      const whereObj = {
        where: {
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
          between: {
            column: 'foo',
            start: 12345,
            stop: 54321,
          },
        },
      };

      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "where" object must have only have one option');
    });

    it('Should return an error if handleAnd returns an error', () => {
      const whereObj = {
        where: {
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        },
        and: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
          between: {
            column: 'foo',
            start: 12345,
            stop: 54321,
          },
        }],
      };

      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "and" array objects can only have 1 option per object');
    });

    it('Should return an error if handleOr returns an error', () => {
      const whereObj = {
        where: {
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        },
        or: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
          between: {
            column: 'foo',
            start: 12345,
            stop: 54321,
          },
        }],
      };

      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "or" array objects can only have 1 option per object');
    });

    it('Should return an error if handleGrouped returns an error', () => {
      const whereObj = {
        where: {
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        },
        grouped: {
          and: [{
            operation: {
              column: 'foo',
              operator: '=',
              value: 'bar',
            },
          }],
          or: [{
            between: {
              column: 'foo',
              start: 12345,
              stop: 54321,
            },
          }],
        },
      };

      const test = processWhere(whereObj) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The "grouped" object must have only have one option');
    });

    it('Should update the query and params for the where, and, or, and grouped processes', () => {
      const whereObj = {
        where: {
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        },
        and: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        }, {
          between: {
            column: 'foo',
            start: 12345,
            stop: 54321,
          },
        }],
        or: [{
          operation: {
            column: 'foo',
            operator: '=',
            value: 'bar',
          },
        }, {
          between: {
            column: 'foo',
            start: 12345,
            stop: 54321,
          },
        }],
        grouped: {
          and: [{
            operation: {
              column: 'foo',
              operator: '=',
              value: 'bar',
            },
          }, {
            between: {
              column: 'foo',
              start: 12345,
              stop: 54321,
            },
          }],
        },
      };

      const test = processWhere(whereObj) as WhereResult;
      expect(test.query).toEqual(' WHERE "foo" = :opParams1 AND "foo" = :opParams2 AND ("foo" BETWEEN 12345 AND 54321) OR "foo" = :opParams3 OR ("foo" BETWEEN 12345 AND 54321) AND ("foo" = :opParams4 AND ("foo" BETWEEN 12345 AND 54321))');
      expect(test.params).toEqual({
        opParams1: 'bar',
        opParams2: 'bar',
        opParams3: 'bar',
        opParams4: 'bar',
      });
    });
  });

  describe('seedDb', async () => {
    const truncate = vi.fn();
    const mockSeedDb = vi.fn();
    truncate.mockResolvedValue(0);
    mockSeedDb.mockResolvedValue([1, 2, 3, 4]);
    class SeedTestDataSource extends TestDataSource {
      truncate = async (key: string): Promise<void | Error> => {
        this.logger.debug(`Truncate running for ${key}`);
        return truncate(key);
      };

      seedDb = async (key: string): Promise<true | Error> => {
        this.logger.debug(`Seeding running for ${key}`);
        return mockSeedDb(key);
      };
    }

    const testApp = await createApp();
    registerAppDataSources(testApp);
    set(testApp, 'config', {
      systems: {
        core: {
          dataSource: 'core',
        },
      },
    });
    testApp.dataSources.register<SeedTestDataSource, DataOptions>(testApp, 'core', SeedTestDataSource);

    afterEach(() => {
      testLogger.resetMocks();
    });

    it('Should resolve true if seeding was completed', async () => {
      const test = await seedDb(testApp);
      expect(test).toEqual(true);

      const infoLog = testLogger.info.mock.calls;
      expect(infoLog).not.toBeUndefined();
      expect(infoLog).toHaveLength(12);
      expect(infoLog[7][0].info).toEqual('Seeding complete for globalVariables: seeded 4 item(s)');
      expect(infoLog[9][0].info).toEqual('Seeding complete for gateway: seeded 4 item(s)');
      expect(infoLog[11][0].info).toEqual('Seeding complete for intakeSchemas: seeded 4 item(s)');
    });

    it('Should return an error if a database is not present', async () => {
      const noDbApp = await createApp();

      const test = await seedDb(noDbApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find data source in configuration for core');
    });

    it('Should throw an error if truncate returns a critical error', async () => {
      truncate.mockResolvedValueOnce(new Error('Critical Error'));
      truncate.mockResolvedValueOnce(new Error('Second Critical Error'));
      truncate.mockResolvedValueOnce(new Error('Third New Error'));

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toContain('There was an error while seeding:');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to truncate globalVariables');
      expect(firstErrorLog.result).toBeInstanceOf(Error);
      expect(firstErrorLog.result.message).toEqual('Critical Error');
      expect(secondErrorLog.error).toEqual('Unable to truncate gateway');
      expect(secondErrorLog.result).toBeInstanceOf(Error);
      expect(secondErrorLog.result.message).toEqual('Second Critical Error');
      expect(thirdErrorLog.result).toBeInstanceOf(Error);
      expect(thirdErrorLog.result.message).toEqual('Third New Error');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to truncate globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to truncate gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to truncate intakeSchemas');
    });

    it('Should throw an error if truncate returns an error', async () => {
      truncate.mockResolvedValueOnce(new Error('New Error'));
      truncate.mockResolvedValueOnce(new Error('Second New Error'));
      truncate.mockResolvedValueOnce(new Error('Third New Error'));

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to truncate globalVariables,Error: Unable to truncate gateway,Error: Unable to truncate intakeSchemas');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to truncate globalVariables');
      expect(firstErrorLog.result).toBeInstanceOf(Error);
      expect(firstErrorLog.result.message).toEqual('New Error');
      expect(secondErrorLog.error).toEqual('Unable to truncate gateway');
      expect(secondErrorLog.result).toBeInstanceOf(Error);
      expect(secondErrorLog.result.message).toEqual('Second New Error');
      expect(thirdErrorLog.result).toBeInstanceOf(Error);
      expect(thirdErrorLog.result.message).toEqual('Third New Error');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to truncate globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to truncate gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to truncate intakeSchemas');
    });

    it('Should throw an error if truncate does not return 0', async () => {
      truncate.mockResolvedValueOnce(1);
      truncate.mockResolvedValueOnce(2);
      truncate.mockResolvedValueOnce(3);

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to truncate globalVariables,Error: Unable to truncate gateway,Error: Unable to truncate intakeSchemas');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to truncate globalVariables');
      expect(firstErrorLog.result).toEqual(1);
      expect(secondErrorLog.error).toEqual('Unable to truncate gateway');
      expect(secondErrorLog.result).toEqual(2);
      expect(thirdErrorLog.error).toEqual('Unable to truncate intakeSchemas');
      expect(thirdErrorLog.result).toEqual(3);
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to truncate globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to truncate gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to truncate intakeSchemas');
    });

    it('Should throw an error if database seedDb returns a critical error', async () => {
      mockSeedDb.mockRejectedValueOnce(new Error('Critical Error'));
      mockSeedDb.mockRejectedValueOnce(new Error('Second Critical Error'));
      mockSeedDb.mockRejectedValueOnce(new Error('Third Critical Error'));

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to seed globalVariables,Error: Unable to seed gateway,Error: Unable to seed intakeSchemas');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to seed globalVariables');
      expect(firstErrorLog.result).toBeInstanceOf(Error);
      expect(firstErrorLog.result.message).toEqual('Critical Error');
      expect(secondErrorLog.error).toEqual('Unable to seed gateway');
      expect(secondErrorLog.result).toBeInstanceOf(Error);
      expect(secondErrorLog.result.message).toEqual('Second Critical Error');
      expect(thirdErrorLog.error).toEqual('Unable to seed intakeSchemas');
      expect(thirdErrorLog.result).toBeInstanceOf(Error);
      expect(thirdErrorLog.result.message).toEqual('Third Critical Error');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to seed globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to seed gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to seed intakeSchemas');
    });

    it('Should throw an error if database seedDb returns an error', async () => {
      mockSeedDb.mockResolvedValueOnce(new Error('New Error'));
      mockSeedDb.mockResolvedValueOnce(new Error('Second New Error'));
      mockSeedDb.mockResolvedValueOnce(new Error('Third New Error'));

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to seed globalVariables,Error: Unable to seed gateway,Error: Unable to seed intakeSchemas');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to seed globalVariables');
      expect(firstErrorLog.result).toBeInstanceOf(Error);
      expect(firstErrorLog.result.message).toEqual('New Error');
      expect(secondErrorLog.error).toEqual('Unable to seed gateway');
      expect(secondErrorLog.result).toBeInstanceOf(Error);
      expect(secondErrorLog.result.message).toEqual('Second New Error');
      expect(thirdErrorLog.error).toEqual('Unable to seed intakeSchemas');
      expect(thirdErrorLog.result).toBeInstanceOf(Error);
      expect(thirdErrorLog.result.message).toEqual('Third New Error');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to seed globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to seed gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to seed intakeSchemas');
    });

    it('Should throw an error if database seedDb returns "failed"', async () => {
      mockSeedDb.mockResolvedValueOnce('failed');
      mockSeedDb.mockResolvedValueOnce('failed');
      mockSeedDb.mockResolvedValueOnce('failed');

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to seed globalVariables,Error: Unable to seed gateway,Error: Unable to seed intakeSchemas');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const secondErrorLog = testLogger.error.mock.calls[1][0];
      const thirdErrorLog = testLogger.error.mock.calls[2][0];
      const lastErrorLog = testLogger.error.mock.calls[3][0];
      expect(firstErrorLog.error).toEqual('Unable to seed globalVariables');
      expect(firstErrorLog.result).toEqual('failed');
      expect(secondErrorLog.error).toEqual('Unable to seed gateway');
      expect(secondErrorLog.result).toEqual('failed');
      expect(thirdErrorLog.error).toEqual('Unable to seed intakeSchemas');
      expect(thirdErrorLog.result).toEqual('failed');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to seed globalVariables');
      expect(lastErrorLog.error[1]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[1].message).toEqual('Unable to seed gateway');
      expect(lastErrorLog.error[2]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[2].message).toEqual('Unable to seed intakeSchemas');
    });

    it('Should return an error if only one of the three keys get an error', async () => {
      mockSeedDb.mockResolvedValueOnce('failed');
      mockSeedDb.mockResolvedValueOnce([1, 2, 3, 4]);
      mockSeedDb.mockResolvedValueOnce([1, 2, 3, 4]);

      const test = await seedDb(testApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('There was an error while seeding: Error: Unable to seed globalVariables,true,true');

      const firstErrorLog = testLogger.error.mock.calls[0][0];
      const lastErrorLog = testLogger.error.mock.calls[1][0];
      expect(firstErrorLog.error).toEqual('Unable to seed globalVariables');
      expect(firstErrorLog.result).toEqual('failed');
      expect(lastErrorLog.error[0]).toBeInstanceOf(Error);
      expect(lastErrorLog.error[0].message).toEqual('Unable to seed globalVariables');
      expect(lastErrorLog.error[1]).toEqual(true);
    });
  });
});
