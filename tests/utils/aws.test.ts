import {
  describe,
  it,
  vi,
  expect,
  beforeEach,
} from 'vitest';
import { get } from 'lodash';
import { getAccessKeys, getSecret, getBulkSecrets } from '../../src/utils/aws';
import { testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';

const mocks = vi.hoisted(() => ({
  send: vi.fn(),
  SecretsManagerClient: vi.fn().mockImplementation(() => ({
    send: mocks.send,
  })),
  DescribeSecretCommand: vi.fn(),
  GetSecretValueCommand: vi.fn(),
  BatchGetSecretValueCommand: vi.fn(),
}));

vi.mock('@aws-sdk/client-secrets-manager', async () => ({
  SecretsManagerClient: mocks.SecretsManagerClient,
  DescribeSecretCommand: mocks.DescribeSecretCommand,
  GetSecretValueCommand: mocks.GetSecretValueCommand,
  BatchGetSecretValueCommand: mocks.BatchGetSecretValueCommand,
}));

const app = {
  logger: testLogger,
} as unknown as CMSApp;

describe('AWS Utilities tests', () => {
  beforeEach(() => {
    vi.stubEnv('AWS_ACCESS_KEY_ID', 'key');
    vi.stubEnv('AWS_SECRET_ACCESS_KEY', 'secret-key');
    vi.stubEnv('AWS_SESSION_TOKEN', '1234abcd');
  });

  describe('getAccessKeys', () => {
    it('Should return an error if the AWS access key env variable is undefined', () => {
      vi.stubEnv('AWS_ACCESS_KEY_ID', undefined);
      const test = getAccessKeys() as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find all required AWS credentials');
    });

    it('Should return an error if the AWS secret access key env variable is undefined', () => {
      vi.stubEnv('AWS_SECRET_ACCESS_KEY', undefined);
      const test = getAccessKeys() as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find all required AWS credentials');
    });

    it('Should return an error if the AWS session token env variable is undefined', () => {
      vi.stubEnv('AWS_SESSION_TOKEN', undefined);
      const test = getAccessKeys() as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find all required AWS credentials');
    });

    it('Should return the access keys', () => {
      const test = getAccessKeys();
      expect(test).toEqual({
        accessKeyId: 'key',
        secretAccessKey: 'secret-key',
        sessionToken: '1234abcd',
      });
    });
  });

  describe('getSecret', () => {
    it('Should return an error if getAccessKeys returns an error', async () => {
      vi.stubEnv('AWS_SESSION_TOKEN', undefined);
      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find all required AWS credentials');
    });

    it('Should return an error if the secretPath is not a string', async () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const test = await getSecret(app, ['test-secret']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The secret should be a string and not empty');
    });

    it('Should return an error if the secretPath is empty', async () => {
      const test = await getSecret(app, '') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('The secret should be a string and not empty');
    });

    it('Should return an error if client.send returns a critical error', async () => {
      mocks.send.mockImplementationOnce(() => new Promise((_, reject) => {
        reject(new Error('Bad Critical Connection'));
      }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret details');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if client.send returns an error', async () => {
      mocks.send.mockImplementationOnce(() => new Promise((resolve) => {
        resolve(new Error('Bad Connection'));
      }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret details');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return an error if versions is not returned', async () => {
      mocks.send.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          UnusedKey: {},
        });
      }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret versions');
    });

    it('Should return an error if versions is empty', async () => {
      mocks.send.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          VersionIdsToStages: {},
        });
      }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret versions');
    });

    it('Should return an error if the current version cannot be found in the versions', async () => {
      mocks.send.mockImplementationOnce(() => new Promise((resolve) => {
        resolve({
          VersionIdsToStages: {
            'fake-uuid': 'AWSPREVIOUS',
          },
        });
      }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find current AWS version');
    });

    it('Should return an error if SecretString is not a string', async () => {
      mocks.send
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            VersionIdsToStages: {
              'fake-uuid': ['AWSCURRENT'],
            },
          });
        }))
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            SecretString: ['not-a-string'],
          });
        }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret string');
    });

    it('Should return an error if SecretString is empty', async () => {
      mocks.send
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            VersionIdsToStages: {
              'fake-uuid': ['AWSCURRENT'],
            },
          });
        }))
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            SecretString: '',
          });
        }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret string');
    });

    it('Should return an error if SecretString does not parse', async () => {
      mocks.send
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            VersionIdsToStages: {
              'fake-uuid': ['AWSCURRENT'],
            },
          });
        }))
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            SecretString: '{ "bad": "json"',
          });
        }));

      const test = await getSecret(app, 'test-secret') as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to parse secret string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Expected \',\' or \'}\' after property value in JSON at position 15');
    });

    it('Should return a result', async () => {
      const testResponse = {
        test1: 'foo',
        test2: 'bar',
      };
      mocks.send
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            VersionIdsToStages: {
              'fake-uuid': ['AWSCURRENT'],
            },
          });
        }))
        .mockImplementationOnce(() => new Promise((resolve) => {
          resolve({
            SecretString: JSON.stringify(testResponse),
          });
        }));

      const test = await getSecret(app, 'test-secret');
      expect(test).toEqual(testResponse);
    });
  });

  describe('getBulkSecrets', () => {
    it('Should return an error if secrets are an empty array', async () => {
      const test = await getBulkSecrets(app, []) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('No secrets provided');
    });

    it('Should return an error if getAccessKeys returns an error', async () => {
      vi.stubEnv('AWS_SESSION_TOKEN', undefined);
      const test = await getBulkSecrets(app, ['test-secret']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to find all required AWS credentials');
    });

    it('Should return an error if BatchGetSecretValueCommand returns errors in the Errors array', async () => {
      const errors = [{
        SecretId: 'test-secret',
        ErrorCode: 'abc-123',
        Message: 'Not Found',
      }];
      mocks.send.mockResolvedValueOnce({
        Errors: errors,
        SecretValues: [],
      });

      const test = await getBulkSecrets(app, ['test-secret']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('An error occurred while retrieving the secrets');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('errors');
      expect(get(errorLog, '[0]', []).errors).toHaveLength(1);
      expect(get(errorLog, '[0]', []).errors).toEqual(errors);
    });

    it('Should return an error if any of the secrets do not parse', async () => {
      mocks.send.mockResolvedValueOnce({
        Errors: [],
        SecretValues: [{
          SecretString: '{ "bad": "json"',
          Name: 'test-secret',
        }],
      });

      const test = await getBulkSecrets(app, ['test-secret']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret from AWS');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Expected \',\' or \'}\' after property value in JSON at position 15');
    });

    it('Should return an error if any of the secrets do not have a Name property', async () => {
      mocks.send.mockResolvedValueOnce({
        Errors: [],
        SecretValues: [{
          SecretString: JSON.stringify({
            foo: 'bar',
          }),
        }],
      });

      const test = await getBulkSecrets(app, ['test-secret']) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve secret from AWS');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve secret name');
    });

    it('Should return an array of StoredStoreItems', async () => {
      mocks.send.mockResolvedValueOnce({
        Errors: [],
        SecretValues: [{
          SecretString: JSON.stringify({
            foo: 'bar',
          }),
          Name: 'test-secret',
        }],
      });

      const test = await getBulkSecrets(app, ['test-secret']);
      expect(test).toEqual([{
        secretString: 'test-secret',
        secrets: {
          foo: 'bar',
        },
      }]);
    });
  });
});
