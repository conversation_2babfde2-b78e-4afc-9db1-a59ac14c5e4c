const id = '{11111111-1111-1111-1111-111111111111}';

const successfulQuery = {
  apisDeveloped: 'Yes',
  apiDescPublished: 'No, published elsewhere',
  apiDescPubLocation: 'https://github.com/API/THING',
  apiDataArea: [
    'Supporting Resource',
    'Organization',
  ],
  apisAccessibility: 'Internal Access',
  apiFHIRUse: 'No',
  apiFHIRUseOther: null,
  systemHasApiGateway: false,
  apiHasPortal: null,
  usesAiTech: 'No',
  developmentStage: null,
  aiSolnCatgOther: null,
  softwareProducts: [
    {
      software_name: 'Name 1',
      softwareProductId: '{11111111-1111-1111-1111-111111111111}',
      technopedia_id: '**********',
      vendor_name: 'Vendor 1',
      technopedia_category: 'Operating Systems',
      api_gateway_use: false,
      provides_ai_capability: false,
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
      softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      software_name: 'Name 2',
      softwareProductId: '{11111111-1111-1111-1111-111111111112}',
      technopedia_id: '**********',
      vendor_name: 'Vendor 2',
      technopedia_category: 'Configuration Management',
      api_gateway_use: false,
      provides_ai_capability: false,
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
      softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      software_name: 'Name 3',
      softwareProductId: '{11111111-1111-1111-1111-111111111113}',
      technopedia_id: '**********',
      vendor_name: 'Vendor 3',
      technopedia_category: 'Software Development',
      api_gateway_use: false,
      provides_ai_capability: false,
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
      softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    },
  ],
};

const successfulSoftwareProductsQuery = [[
  {
    'Software Name': 'Name 1',
    'Sparx Software GUID': '{11111111-1111-1111-1111-111111111111}',
    'Technopedia Release ID': '**********',
    'Vendor Name': 'Vendor 1',
    Category: 'Operating Systems',
    'Used as API Gateway': 'No',
    'Used for AI Capabilities': 'No',
    'Purchased Under Enterprise License Agreement': null,
    'ELA Vendor ID': null,
    'Software ELA Organization': null,
    'System Software Connection GUID': '{11111111-1111-1111-1111-111111111111}',
    'Software SoftwareCategory Connection GUID': '{11111111-1111-1111-1111-111111111111}',
    'Software Vendor Connection GUID': '{11111111-1111-1111-1111-111111111111}',
  },
  {
    'Software Name': 'Name 2',
    'Sparx Software GUID': '{11111111-1111-1111-1111-111111111112}',
    'Technopedia Release ID': '**********',
    'Vendor Name': 'Vendor 2',
    Category: 'Configuration Management',
    'Used as API Gateway': 'No',
    'Used for AI Capabilities': 'No',
    'Purchased Under Enterprise License Agreement': null,
    'ELA Vendor ID': null,
    'Software ELA Organization': null,
    'System Software Connection GUID': '{11111111-1111-1111-1111-111111111112}',
    'Software SoftwareCategory Connection GUID': '{11111111-1111-1111-1111-111111111112}',
    'Software Vendor Connection GUID': '{11111111-1111-1111-1111-111111111112}',
  },
  {
    'Software Name': 'Name 3',
    'Sparx Software GUID': '{11111111-1111-1111-1111-111111111113}',
    'Technopedia Release ID': '**********',
    'Vendor Name': 'Vendor 3',
    Category: 'Software Development',
    'Used as API Gateway': 'No',
    'Used for AI Capabilities': 'No',
    'Purchased Under Enterprise License Agreement': null,
    'ELA Vendor ID': null,
    'Software ELA Organization': null,
    'System Software Connection GUID': '{11111111-1111-1111-1111-111111111113}',
    'Software SoftwareCategory Connection GUID': '{11111111-1111-1111-1111-111111111113}',
    'Software Vendor Connection GUID': '{11111111-1111-1111-1111-111111111113}',
  },
], 3];

const successfulSoftwareProductsResult = [
  {
    software_name: 'Name 1',
    softwareProductId: '{11111111-1111-1111-1111-111111111111}',
    technopedia_id: '**********',
    vendor_name: 'Vendor 1',
    technopedia_category: 'Operating Systems',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
  },
  {
    software_name: 'Name 2',
    softwareProductId: '{11111111-1111-1111-1111-111111111112}',
    technopedia_id: '**********',
    vendor_name: 'Vendor 2',
    technopedia_category: 'Configuration Management',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
  },
  {
    software_name: 'Name 3',
    softwareProductId: '{11111111-1111-1111-1111-111111111113}',
    technopedia_id: '**********',
    vendor_name: 'Vendor 3',
    technopedia_category: 'Software Development',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    softwareCatagoryConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
  },
];

const successfulCategoriesQuery = [
  [
    {
      'API Cateogry': 'Supporting Resource',
    },
    {
      'API Cateogry': 'Organization',
    },
  ],
];

const successfulCategoriesResult = [
  'Supporting Resource',
  'Organization',
];

const successfulSystemsQuery = [
  [
    {
      'API Developed': 'Yes',
      'API Description Published': 'No, published elsewhere',
      'API Description Location': 'https://github.com/API/THING',
      'API Data Area': null,
      'API Accessibility': 'Internal Access',
      'Does the API use FHIR': 'No',
      'Does the API use FHIR Other': null,
      'System has API Gateway': 'No',
      'API Has Portal': null,
      'Uses AI Technology': 'No',
      'AI Project Life Cycle Stage': null,
      'AI Solution Category Other': null,
    },
  ],
];

const successfulSystemsResult = {
  apisDeveloped: 'Yes',
  apiDescPublished: 'No, published elsewhere',
  apiDescPubLocation: 'https://github.com/API/THING',
  apiDataArea: null,
  apisAccessibility: 'Internal Access',
  apiFHIRUse: 'No',
  apiFHIRUseOther: null,
  systemHasApiGateway: 'No',
  apiHasPortal: null,
  usesAiTech: 'No',
  developmentStage: null,
  aiSolnCatgOther: null,
};

export {
  id,
  successfulSoftwareProductsQuery,
  successfulSoftwareProductsResult,
  successfulCategoriesQuery,
  successfulCategoriesResult,
  successfulSystemsQuery,
  successfulSystemsResult,
  successfulQuery,
};
