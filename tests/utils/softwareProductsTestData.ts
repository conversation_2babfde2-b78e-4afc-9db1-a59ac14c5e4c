const id = '{11111111-1111-1111-1111-111111111111}';

const successfulQuery = {
  apisDeveloped: 'Yes',
  apiDescPublished: 'No, published elsewhere',
  apiDescPubLocation: 'https://github.com/API/THING',
  apiDataArea: [
    'Supporting Resource',
    'Organization',
  ],
  apisAccessibility: 'Internal Access',
  apiFHIRUse: 'No',
  apiFHIRUseOther: null,
  systemHasApiGateway: false,
  apiHasPortal: null,
  usesAiTech: 'No',
  developmentStage: null,
  aiSolnCatgOther: null,
  softwareProducts: [
    {
      software_name: 'Name 1',
      softwareProductId: '{11111111-1111-1111-1111-111111111111}',
      technopedia_id: '1234567890',
      vendor_name: 'Vendor 1',
      category: 'Operating Systems',
      api_gateway_use: 'No',
      provides_ai_capability: 'No',
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
      softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111111}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    },
    {
      software_name: 'Name 2',
      softwareProductId: '{11111111-1111-1111-1111-111111111112}',
      technopedia_id: '1234567891',
      vendor_name: 'Vendor 2',
      category: 'Configuration Management',
      api_gateway_use: 'No',
      provides_ai_capability: 'No',
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
      softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111112}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    },
    {
      software_name: 'Name 3',
      softwareProductId: '{11111111-1111-1111-1111-111111111113}',
      technopedia_id: '1234567892',
      vendor_name: 'Vendor 3',
      category: 'Software Development',
      api_gateway_use: 'No',
      provides_ai_capability: 'No',
      ela_purchase: null,
      ela_vendor_id: null,
      ela_organization: null,
      systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
      softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111113}',
      softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    },
  ],
};

const successfulSoftwareProductsQuery = [[
  {
    software_name: 'Name 1',
    softwareProductId: '{11111111-1111-1111-1111-111111111111}',
    technopedia_id: '1234567890',
    vendor_name: 'Vendor 1',
    category: 'Operating Systems',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111111}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
  },
  {
    software_name: 'Name 2',
    softwareProductId: '{11111111-1111-1111-1111-111111111112}',
    technopedia_id: '1234567891',
    vendor_name: 'Vendor 2',
    category: 'Configuration Management',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111112}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
  },
  {
    software_name: 'Name 3',
    softwareProductId: '{11111111-1111-1111-1111-111111111113}',
    technopedia_id: '1234567892',
    vendor_name: 'Vendor 3',
    category: 'Software Development',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111113}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
  },
], 3];

const successfulSoftwareProductsResult = [
  {
    software_name: 'Name 1',
    softwareProductId: '{11111111-1111-1111-1111-111111111111}',
    technopedia_id: '1234567890',
    vendor_name: 'Vendor 1',
    category: 'Operating Systems',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111111}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111111}',
  },
  {
    software_name: 'Name 2',
    softwareProductId: '{11111111-1111-1111-1111-111111111112}',
    technopedia_id: '1234567891',
    vendor_name: 'Vendor 2',
    category: 'Configuration Management',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111112}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111112}',
  },
  {
    software_name: 'Name 3',
    softwareProductId: '{11111111-1111-1111-1111-111111111113}',
    technopedia_id: '1234567892',
    vendor_name: 'Vendor 3',
    category: 'Software Development',
    api_gateway_use: 'No',
    provides_ai_capability: 'No',
    ela_purchase: null,
    ela_vendor_id: null,
    ela_organization: null,
    systemSoftwareConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
    softwareCatagoryConnectionGUID: '{11111111-1111-1111-1111-111111111113}',
    softwareVendorConnectionGuid: '{11111111-1111-1111-1111-111111111113}',
  },
];

const successfulCategoriesQuery = [
  [{
    category: 'Supporting Resource',
  }, {
    category: 'Organization',
  }],
  2,
];

const successfulCategoriesResult = [
  'Supporting Resource',
  'Organization',
];

const successfulSystemsQuery = [
  [
    {
      apisDeveloped: 'Yes',
      apiDescPublished: 'No, published elsewhere',
      apiDescPubLocation: 'https://github.com/API/THING',
      apiDataArea: null,
      apisAccessibility: 'Internal Access',
      apiFHIRUse: 'No',
      apiFHIRUseOther: null,
      systemHasApiGateway: 'No',
      apiHasPortal: null,
      usesAiTech: 'No',
      developmentStage: null,
      aiSolnCatgOther: null,
    },
  ], 1,
];

const successfulSystemsResult = [{
  apisDeveloped: 'Yes',
  apiDescPublished: 'No, published elsewhere',
  apiDescPubLocation: 'https://github.com/API/THING',
  apiDataArea: null,
  apisAccessibility: 'Internal Access',
  apiFHIRUse: 'No',
  apiFHIRUseOther: null,
  systemHasApiGateway: 'No',
  apiHasPortal: null,
  usesAiTech: 'No',
  developmentStage: null,
  aiSolnCatgOther: null,
}];

export {
  id,
  successfulSoftwareProductsQuery,
  successfulSoftwareProductsResult,
  successfulCategoriesQuery,
  successfulCategoriesResult,
  successfulSystemsQuery,
  successfulSystemsResult,
  successfulQuery,
};
