import {
  expect,
  describe,
  vi,
  it,
} from 'vitest';
import { get } from 'lodash';
import {
  getSoftwareProductsList,
  getSystemSubList,
  getSoftwareProductsSubList,
  getCategoriesSubList,
} from '../../src/utils/softwareProducts';
import {
  createApp,
  testLogger,
} from '../test-utils';
import { CMSApp } from '../../src/types';
import MssqlData from '../../src/data-sources/mssql';
import {
  successfulQuery,
  id,
  successfulSoftwareProductsQuery,
  successfulSoftwareProductsResult,
  successfulCategoriesQuery,
  successfulCategoriesResult,
  successfulSystemsQuery,
  successfulSystemsResult,
} from './softwareProductsTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    queryView: mocks.queryView,
  };
});

const app = await createApp();
const db = mocks.getDb();

describe('Software Products util tests', () => {
  describe('getSoftwareProductsSubList', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getSoftwareProductsSubList(null as unknown as CMSApp, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if there is no db in the params', async () => {
      const test = await getSoftwareProductsSubList(app, null as unknown as MssqlData, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No Database given in the params');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('No Database given in the params');
    });

    it('Should return an error if the queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Softwares'));
      const test = await getSoftwareProductsSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Softwares');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Softwares');
    });

    it('Should return an error if the queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Unable to retrieve Softwares'));
      const test = await getSoftwareProductsSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Softwares');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Softwares');
    });

    it('Should return an object with an array of softwares', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulSoftwareProductsQuery);
      const test = await getSoftwareProductsSubList(app, db, id);
      expect(test).toEqual(successfulSoftwareProductsResult);
    });
  });

  describe('getCategoriesSubList', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getCategoriesSubList(null as unknown as CMSApp, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if there is no db in the params', async () => {
      const test = await getCategoriesSubList(app, null as unknown as MssqlData, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No Database given in the params');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('No Database given in the params');
    });

    it('Should return an error if the queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Categories'));
      const test = await getCategoriesSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Categories');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Categories');
    });

    it('Should return an error if the queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Unable to retrieve Categories'));
      const test = await getCategoriesSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Categories');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Categories');
    });

    it('Should return an object with an array of category names', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      const test = await getCategoriesSubList(app, db, id);
      expect(test).toEqual(successfulCategoriesResult);
    });
  });

  describe('getSystemSubList', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getSystemSubList(null as unknown as CMSApp, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if there is no db in the params', async () => {
      const test = await getSystemSubList(app, null as unknown as MssqlData, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No Database given in the params');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('No Database given in the params');
    });

    it('Should return an error if the queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Systems data'));
      const test = await getSystemSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Systems data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Systems data');
    });

    it('Should return an error if the queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Unable to retrieve Systems data'));
      const test = await getSystemSubList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Systems data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Systems data');
    });

    it('Should return an object with an array of systems', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulSystemsQuery);
      const test = await getSystemSubList(app, db, id);
      expect(test).toEqual(successfulSystemsResult);
    });
  });

  describe('getSoftwareProductsList', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getSoftwareProductsList(null as unknown as CMSApp, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if there is no db in the params', async () => {
      const test = await getSoftwareProductsList(app, null as unknown as MssqlData, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No Database given in the params');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('No Database given in the params');
    });

    it('Should return an error if the categoryResults returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Categories data'));

      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Categories data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Categories data');
    });

    it('Should return an error if the categoryResults returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Error: Unable to retrieve Categories data'));

      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Critical Error: Unable to retrieve Categories data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Error: Unable to retrieve Categories data');
    });

    it('Should return an error if the softwareProductResults returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Software Product data'));

      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Software Product data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Software Product data');
    });

    it('Should return an error if the softwareProducts returns a critical error', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      mocks.queryView.mockResolvedValueOnce(new Error('Critical Error: Unable to retrieve Software Product data'));

      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Critical Error: Unable to retrieve Software Product data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Error: Unable to retrieve Software Product data');
    });

    it('Should return an error if the systemResults returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      mocks.queryView.mockResolvedValueOnce(successfulSoftwareProductsQuery);
      mocks.queryView.mockResolvedValueOnce(new Error('Unable to retrieve Systems data'));

      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Systems data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Systems data');
    });

    it('Should return an error if the systemResults returns a critical error', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      mocks.queryView.mockResolvedValueOnce(successfulSoftwareProductsQuery);
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Error: Unable to retrieve Systems data'));
      const test = await getSoftwareProductsList(app, db, id) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Critical Error: Unable to retrieve Systems data');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Error: Unable to retrieve Systems data');
    });

    it('Should return an object with an array of software products', async () => {
      mocks.queryView.mockResolvedValueOnce(successfulCategoriesQuery);
      mocks.queryView.mockResolvedValueOnce(successfulSoftwareProductsQuery);
      mocks.queryView.mockResolvedValueOnce(successfulSystemsQuery);

      const test = await getSoftwareProductsList(app, db, id);
      expect(test).toEqual(successfulQuery);
    });
  });
});
