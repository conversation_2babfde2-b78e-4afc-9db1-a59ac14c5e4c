// Custom: App.
import {
  getSoftwareProductsList,
  getSystemSubList,
  getSoftwareProductsSubList,
  getCategoriesById,
} from 'src/utils/softwareProducts';
import MssqlData from 'src/data-sources/mssql';

// Custom: Local.
import {
  successfulQuery,
  id,
  successfulSoftwareProductsQuery,
  successfulSoftwareProductsResult,
  successfulCategoriesQuery,
  successfulCategoriesResult,
  successfulSystemsQuery,
  successfulSystemsResult,
} from './softwareProductsTestData';

const mocks = vi.hoisted(() => ({
  queryViewTypedFixed: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryViewTypedFixed: mocks.queryViewTypedFixed,
  })),
}));

vi.mock(import('src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    queryViewTypedFixed: mocks.queryViewTypedFixed,
  };
});

const db = mocks.getDb();

describe('Software Products util tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getSoftwareProductsSubList', () => {
    it('Should throw an error if there is no db in the params', async () => {
      await expect(getSoftwareProductsSubList(null as unknown as MssqlData, id))
        .rejects.toThrow('Cannot read properties of null (reading \'queryViewTypedFixed\')');
    });

    it('Should throw an error if the queryViewTypedFixed returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Software'));
      await expect(getSoftwareProductsSubList(db, id))
        .rejects.toThrow('record.forEach is not a function');
    });

    it('Should throw an error if the queryViewTypedFixed returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockRejectedValueOnce(new Error('Unable to retrieve Software'));
      await expect(getSoftwareProductsSubList(db, id))
        .rejects.toThrow('Unable to retrieve Software');
    });

    it('Should return an object with an array of Software', async () => {
      // Note: api_gateway_use is No here because the final
      //       mapping happens in getSoftwareProductsList.
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSoftwareProductsQuery[0]);
      const test = await getSoftwareProductsSubList(db, id);
      expect(test).toEqual(successfulSoftwareProductsResult);
    });
  });

  describe('getCategoriesSubList', () => {
    it('Should throw an error if there is no db in the params', async () => {
      await expect(getCategoriesById(null as unknown as MssqlData, id))
        .rejects.toThrow('Cannot read properties of null (reading \'queryViewTypedFixed\')');
    });

    it('Should throw an error if the queryViewTypedFixed returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Categories'));
      await expect(getCategoriesById(db, id))
        .rejects.toThrow('result.map is not a function');
    });

    it('Should throw an error if the queryViewTypedFixed returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockRejectedValueOnce(new Error('Unable to retrieve Categories'));
      await expect(getCategoriesById(db, id))
        .rejects.toThrow('Unable to retrieve Categories');
    });

    it('Should return an object with an array of category names', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      const test = await getCategoriesById(db, id);
      expect(test).toEqual(successfulCategoriesResult);
    });
  });

  describe('getSystemSubList', () => {
    it('Should throw an error if there is no db in the params', async () => {
      await expect(getSystemSubList(null as unknown as MssqlData, id))
        .rejects.toThrow('Cannot read properties of null (reading \'queryViewTypedFixed\')');
    });

    it('Should throw an error if the queryViewTypedFixed returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Systems data'));
      await expect(getSystemSubList(db, id))
        .rejects.toThrow('result.map is not a function');
    });

    it('Should throw an error if the queryViewTypedFixed returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockRejectedValueOnce(new Error('Unable to retrieve Systems data'));
      await expect(getSystemSubList(db, id))
        .rejects.toThrow('Unable to retrieve Systems data');
    });

    it('Should return an object with an array of systems', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSystemsQuery[0]);
      const test = await getSystemSubList(db, id);
      expect(test).toEqual(successfulSystemsResult);
    });
  });

  describe('getSoftwareProductsList', () => {
    it('Should throw an error if there is no db in the params', async () => {
      await expect(getSoftwareProductsList(null as unknown as MssqlData, id))
        .rejects.toThrow('Cannot read properties of null (reading \'queryViewTypedFixed\')');
    });

    it('Should throw an error if the categoryResults returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Categories data'));

      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('result.map is not a function');
    });

    it('Should throw an error if the categoryResults returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockRejectedValueOnce(new Error('Critical Error: Unable to retrieve Categories data'));

      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('Critical Error: Unable to retrieve Categories data');
    });

    it('Should throw an error if the softwareProductResults returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Software Product data'));

      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('result.map is not a function');
    });

    it('Should throw an error if the softwareProducts returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Critical Error: Unable to retrieve Software Product data'));

      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('result.map is not a function');
    });

    it('Should throw an error if the systemResults returns an error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSoftwareProductsQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(new Error('Unable to retrieve Systems data'));

      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('record.forEach is not a function');
    });

    it('Should throw an error if the systemResults returns a critical error', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSoftwareProductsQuery[0]);
      mocks.queryViewTypedFixed.mockRejectedValueOnce(new Error('Critical Error: Unable to retrieve Systems data'));
      await expect(getSoftwareProductsList(db, id))
        .rejects.toThrow('Critical Error: Unable to retrieve Systems data');
    });

    it('Should return an object with an array of software products', async () => {
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSystemsQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulCategoriesQuery[0]);
      mocks.queryViewTypedFixed.mockResolvedValueOnce(successfulSoftwareProductsQuery[0]);

      const test = await getSoftwareProductsList(db, id);

      expect(test).toEqual(successfulQuery);
    });
  });
});
