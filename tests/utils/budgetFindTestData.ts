export const oidsDbResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
  },
];

export const oidsAPIResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
    FiscalYear: '2022',
  },
];

export const allBudgetDbResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    systemId: null,
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{9B158E5C-0AE8-49bb-8205-85BF7DA8EF25}',
  },
  {
    id: '{B3422406-8682-46fc-BC7F-310D3317A1B9}',
    projectId: '001624',
    systemId: null,
    projectTitle: '001624-Policy Analysis and Development',
    funding: 'Only part of this funding is directly for this system (less than 40%)',
    fundingId: '{5833BEA9-6319-4d21-9D15-B96B026E1CAD}',
  },
  {
    id: '{5B061927-331D-4fd7-A025-40142148A321}',
    projectId: '001627',
    systemId: null,
    projectTitle: '001627-Capacity-Building for Health Professionals in Underserved Areas-Award',
    funding: 'Only part of this funding is directly for this system (less than 40%)',
    fundingId: '{82022074-27C8-468d-AEDA-588AAB569B10}',
  },
];

export const allBudgetAPIResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    systemId: null,
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{9B158E5C-0AE8-49bb-8205-85BF7DA8EF25}',
    FiscalYear: '2022',
  },
  {
    id: '{B3422406-8682-46fc-BC7F-310D3317A1B9}',
    projectId: '001624',
    systemId: null,
    projectTitle: '001624-Policy Analysis and Development',
    funding: 'Only part of this funding is directly for this system (less than 40%)',
    fundingId: '{5833BEA9-6319-4d21-9D15-B96B026E1CAD}',
    FiscalYear: '2022',
  },
  {
    id: '{5B061927-331D-4fd7-A025-40142148A321}',
    projectId: '001627',
    systemId: null,
    projectTitle: '001627-Capacity-Building for Health Professionals in Underserved Areas-Award',
    funding: 'Only part of this funding is directly for this system (less than 40%)',
    fundingId: '{82022074-27C8-468d-AEDA-588AAB569B10}',
    FiscalYear: '2022',
  },
];

export const projectIdTitleDbResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    systemId: null,
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
    funding: null,
    fundingId: null,
  },
];

export const projectIdTitleAPIResponse = [
  {
    id: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
    projectId: '000004',
    systemId: null,
    projectTitle: '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments',
    funding: null,
    fundingId: null,
    FiscalYear: '2022',
  },
];

export const systemIdDbResponse = [
  {
    id: '{B846C8A2-FF04-48d0-8829-D4CD1D4EB8F9}',
    projectId: '001064',
    systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    projectTitle: '001064-Project Management Support',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{F788436E-7A10-49af-927A-CF31D1A3330B}',
  },
];

export const systemIdAPIResponse = [
  {
    id: '{B846C8A2-FF04-48d0-8829-D4CD1D4EB8F9}',
    projectId: '001064',
    systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}',
    projectTitle: '001064-Project Management Support',
    funding: 'Most of this funding is directly and only for this system (over 80%)',
    fundingId: '{F788436E-7A10-49af-927A-CF31D1A3330B}',
    FiscalYear: '2022',
  },
];
