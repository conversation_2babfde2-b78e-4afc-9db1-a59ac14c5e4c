import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import {
  getData,
  findData,
  getDataIdsOnly,
  findDataIdsOnly,
  budgetFindUtil,
} from '../../src/utils/budget';
import {
  allBudgetDbResponse,
  oidsDbResponse,
  oidsAPIResponse,
  systemIdDbResponse,
  systemIdAPIResponse,
  projectIdTitleDbResponse,
  projectIdTitleAPIResponse,
  allBudgetAPIResponse,
} from './budgetFindTestData';
import { Where, OrObject, CMSApp } from '../../src/types';
import {
  createApp,
  testLogger,
} from '../test-utils';
import MssqlData from '../../src/data-sources/mssql';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  findDataIdsOnly: vi.fn(),
  findData: vi.fn(),
  getData: vi.fn(),
  getDataIdsOnly: vi.fn(),
  findGlobalVariable: vi.fn().mockResolvedValue({ globalValue: '2022' }),
  getDb: vi.fn().mockReturnValue({}),
}));

vi.mock('../../../src/subsystems/global-variables/util', () => ({
  findGlobalVariable: mocks.findGlobalVariable,
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    findGlobalVariable: mocks.findGlobalVariable,
  };
});

const app = await createApp();
const db = mocks.db();

describe('Budget util tests', () => {
  const yearGlobal: string = '2022';
  describe('getData', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getData(null as unknown as CMSApp, db, yearGlobal);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await getData(app, null as unknown as MssqlData, yearGlobal);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await getData(app, new Error('Database Error') as unknown as MssqlData, yearGlobal);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await getData(app, db, yearGlobal);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await getData(app, db, yearGlobal);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return an object with count and Budgets', async () => {
      const queryData = { projectId: '000004', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await getData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });
  });

  describe('findData', () => {
    it('Should return an error if there is no app in the params', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(null as unknown as CMSApp, db, yearGlobal, queryData);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(app, null as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    // projectId flow
    it('Should return an error if getData returns an error with a projectId param', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectId
    it('Should return budget data filtered by projectId', async () => {
      const queryData = { projectId: '000004', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && projectTitle
    it('Should return budget data filtered by projectId and projectTitle', async () => {
      const queryData = { projectId: '000004', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && systemId
    it('Should return budget data filtered by projectId and systemId', async () => {
      const queryData = { projectId: '001064', projectTitle: '', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && projectTitle && systemId
    it('Should return budget data filtered by projectId, projectTitle, and systemId', async () => {
      const queryData = { projectId: '001064', projectTitle: '001064-Project Management Support', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([systemIdDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: systemIdAPIResponse,
      });
    });

    // projectTitle flow
    it('Should return an error if getData returns an error with a projectTitle param', async () => {
      const queryData = { projectTitle: '001064-Project Management Support', projectId: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectTitle
    it('Should return budget data filtered by projectTitle', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectTitle && systemId
    it('Should return budget data filtered by projectTitle and systemId', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // systemId flow
    it('Should return an error if getData returns an error with a systemId param', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    it('Should return budget data filtered by systemId', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([systemIdDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: systemIdAPIResponse,
      });
    });

    it('Should return budget data with no filter', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([allBudgetDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 3,
        Budgets: allBudgetAPIResponse,
      });
    });
  });

  describe('getDataIdsOnly', () => {
    const projectId = '000004';
    const projectTitle = '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments';
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: projectId as string,
        },
      },
      or: [
        {
          column: 'Budget ProjectName',
          operator: 'LIKE',
          value: projectTitle as string,
        } as OrObject,
      ],
    };

    it('Should return an error if there is no app in the params', async () => {
      const test = await getDataIdsOnly(null as unknown as CMSApp, db, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await getDataIdsOnly(app, null as unknown as MssqlData, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await getDataIdsOnly(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return an object with count and Budgets', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });
  });

  describe('findDataOnlyIds', () => {
    const projectId = '000004';
    const projectTitle = '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments';
    const queryData = {
      projectId,
      projectTitle,
      systemId: '',
    };

    it('Should return an error if there is no app in the params', async () => {
      const test = await findDataIdsOnly(null as unknown as CMSApp, db, yearGlobal, queryData);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await findDataIdsOnly(app, null as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await findDataIdsOnly(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    // projectId flow
    it('Should return an error if getData returns an error with a projectId param', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findDataIdsOnly(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectId
    it('Should return budget data filtered by projectId', async () => {
      const query = { projectId, projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    // projectId && projectTitle
    it('Should return budget data filtered by projectId and projectTitle', async () => {
      const query = { projectId, projectTitle, systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    // projectTitle flow
    it('Should return an error if getData returns an error with a projectTitle param', async () => {
      const query = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findDataIdsOnly(app, db, yearGlobal, query);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectTitle
    it('Should return budget data filtered by projectTitle', async () => {
      const query = { projectId: '', projectTitle, systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    it('Should return a count of 0 with no filter', async () => {
      const query = { projectId: '', projectTitle: '', systemId: '' };
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: 0,
      });
    });
  });

  describe('budgetFindUtil', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await budgetFindUtil(null as unknown as CMSApp, db, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await budgetFindUtil(app, null as unknown as MssqlData, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await budgetFindUtil(app, new Error('Database Error') as unknown as MssqlData, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if there is an issue with finding the global variable', async () => {
      mocks.findGlobalVariable.mockResolvedValueOnce(new Error('An error occurred while finding the global variable'));
      const test = await budgetFindUtil(app, db, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('An error occurred while finding the global variable'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('An error occurred while finding the global variable');
    });

    it('Should return an error if there is an error getting the projectId', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '' });
      expect(test).toEqual(new Error('The project ID is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The project ID is not valid');
    });

    it('Should return a 400 with an error if there is an error getting the projectTitle', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '3AADC99D-0B7F-40e7-8780-881E02CD8661}', projectTitle: '' });
      expect(test).toEqual(new Error('The project title is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The project title is not valid');
    });

    it('Should return an error if there is an error getting the systemId', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}', projectTitle: 'Project Title', systemId: '' });
      expect(test).toEqual(new Error('The system ID is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The system ID is not valid');
    });

    it('Should return a result when given a project id when onlyIds is false or null ', async () => {
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);

      const test = await budgetFindUtil(app, db, { projectId: '000004', projectTitle: 'Project Title' });
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    it('Should return an error message when onlyIds is false and findData returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Find Data Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          projectId: '000004',
          projectTitle: 'Project Title',
          systemId: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
          onlyIds: '',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return an error if findDataIdsOnly returns an error and onlyIds is true', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('findDataIdsOnly Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          onlyIds: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return a result when given just a project id and onlyIds is true', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsAPIResponse]);
      const test = await budgetFindUtil(
        app,
        db,
        {
          onlyIds: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    it('Should return an error if findDataIdsOnly returns an error and idsOnly is true', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('findDataIdsOnly Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          idsOnly: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return a result when given just a project id and idsOnly is true', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsAPIResponse]);
      const test = await budgetFindUtil(
        app,
        db,
        {
          idsOnly: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });
  });
});
