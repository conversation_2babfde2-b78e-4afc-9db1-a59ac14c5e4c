import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import {
  getData,
  findData,
  getDataIdsOnly,
  findDataIdsOnly,
  budgetFindUtil,
  executeSparxBudgetOperation,
  BudgetRequest,
} from '../../src/utils/budget';
import {
  allBudgetDbResponse,
  oidsDbResponse,
  oidsAPIResponse,
  systemIdDbResponse,
  systemIdAPIResponse,
  projectIdTitleDbResponse,
  projectIdTitleAPIResponse,
  allBudgetAPIResponse,
} from './budgetFindTestData';
import { Where, OrObject, CMSApp } from '../../src/types';
import {
  createApp,
  testLogger,
} from '../test-utils';
import MssqlData from '../../src/data-sources/mssql';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
  findDataIdsOnly: vi.fn(),
  findData: vi.fn(),
  getData: vi.fn(),
  getDataIdsOnly: vi.fn(),
  findGlobalVariable: vi.fn().mockResolvedValue({ globalValue: '2022' }),
  getDb: vi.fn().mockReturnValue({}),
}));

vi.mock('../../../src/subsystems/global-variables/util', () => ({
  findGlobalVariable: mocks.findGlobalVariable,
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

vi.mock(import('../../src/subsystems/global-variables/util'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    findGlobalVariable: mocks.findGlobalVariable,
  };
});

const app = await createApp();
const db = mocks.db();

describe('Budget util tests', () => {
  const yearGlobal: string = '2022';
  describe('getData', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await getData(null as unknown as CMSApp, db, yearGlobal);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await getData(app, null as unknown as MssqlData, yearGlobal);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await getData(app, new Error('Database Error') as unknown as MssqlData, yearGlobal);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await getData(app, db, yearGlobal);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await getData(app, db, yearGlobal);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return an object with count and Budgets', async () => {
      const whereObj: Where = {
        where: {
          operation: {
            column: 'OFM Project ID',
            operator: '=',
            value: '000004',
          },
        },
      };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await getData(app, db, yearGlobal, whereObj);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });
  });

  describe('findData', () => {
    it('Should return an error if there is no app in the params', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(null as unknown as CMSApp, db, yearGlobal, queryData);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(app, null as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      const test = await findData(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    // projectId flow
    it('Should return an error if getData returns an error with a projectId param', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectId
    it('Should return budget data filtered by projectId', async () => {
      const queryData = { projectId: '000004', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && projectTitle
    it('Should return budget data filtered by projectId and projectTitle', async () => {
      const queryData = { projectId: '000004', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && systemId
    it('Should return budget data filtered by projectId and systemId', async () => {
      const queryData = { projectId: '001064', projectTitle: '', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectId && projectTitle && systemId
    it('Should return budget data filtered by projectId, projectTitle, and systemId', async () => {
      const queryData = { projectId: '001064', projectTitle: '001064-Project Management Support', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([systemIdDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: systemIdAPIResponse,
      });
    });

    // projectTitle flow
    it('Should return an error if getData returns an error with a projectTitle param', async () => {
      const queryData = { projectTitle: '001064-Project Management Support', projectId: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectTitle
    it('Should return budget data filtered by projectTitle', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // projectTitle && systemId
    it('Should return budget data filtered by projectTitle and systemId', async () => {
      const queryData = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    // systemId flow
    it('Should return an error if getData returns an error with a systemId param', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findData(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    it('Should return budget data filtered by systemId', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '{3F585533-9CF2-4d85-AC58-BE91AE68303C}' };
      mocks.queryView.mockResolvedValueOnce([systemIdDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 1,
        Budgets: systemIdAPIResponse,
      });
    });

    it('Should return budget data with no filter', async () => {
      const queryData = { projectId: '', projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([allBudgetDbResponse]);
      const test = await findData(app, db, yearGlobal, queryData);
      expect(test).toEqual({
        count: 3,
        Budgets: allBudgetAPIResponse,
      });
    });
  });

  describe('getDataIdsOnly', () => {
    const projectId = '000004';
    const projectTitle = '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments';
    const whereObj: Where = {
      where: {
        operation: {
          column: 'OFM Project ID',
          operator: '=',
          value: projectId as string,
        },
      },
      or: [
        {
          column: 'Budget ProjectName',
          operator: 'LIKE',
          value: projectTitle as string,
        } as OrObject,
      ],
    };

    it('Should return an error if there is no app in the params', async () => {
      const test = await getDataIdsOnly(null as unknown as CMSApp, db, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await getDataIdsOnly(app, null as unknown as MssqlData, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await getDataIdsOnly(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, whereObj);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if queryView returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query View Error');
    });

    it('Should return an error if queryView returns a critical error', async () => {
      mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Critical Query View Error');
    });

    it('Should return an object with count and Budgets', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await getDataIdsOnly(app, db, yearGlobal, whereObj);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });
  });

  describe('findDataOnlyIds', () => {
    const projectId = '000004';
    const projectTitle = '000004-Waiver of Cap on Annual Payments for Nursing and Allied Health Education Payments';
    const queryData = {
      projectId,
      projectTitle,
      systemId: '',
    };

    it('Should return an error if there is no app in the params', async () => {
      const test = await findDataIdsOnly(null as unknown as CMSApp, db, yearGlobal, queryData);
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await findDataIdsOnly(app, null as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await findDataIdsOnly(app, new Error('Database Error') as unknown as MssqlData, yearGlobal, queryData);
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    // projectId flow
    it('Should return an error if getData returns an error with a projectId param', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findDataIdsOnly(app, db, yearGlobal, queryData);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectId
    it('Should return budget data filtered by projectId', async () => {
      const query = { projectId, projectTitle: '', systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    // projectId && projectTitle
    it('Should return budget data filtered by projectId and projectTitle', async () => {
      const query = { projectId, projectTitle, systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    // projectTitle flow
    it('Should return an error if getData returns an error with a projectTitle param', async () => {
      const query = { projectId: '', projectTitle: '001064-Project Management Support', systemId: '' };
      mocks.queryView.mockResolvedValueOnce(new Error('Get Data Error'));
      const test = await findDataIdsOnly(app, db, yearGlobal, query);

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Get Data Error');
    });

    // only projectTitle
    it('Should return budget data filtered by projectTitle', async () => {
      const query = { projectId: '', projectTitle, systemId: '' };
      mocks.queryView.mockResolvedValueOnce([oidsDbResponse]);
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    it('Should return a count of 0 with no filter', async () => {
      const query = { projectId: '', projectTitle: '', systemId: '' };
      const test = await findDataIdsOnly(app, db, yearGlobal, query);
      expect(test).toEqual({
        count: 0,
      });
    });
  });

  describe('budgetFindUtil', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await budgetFindUtil(null as unknown as CMSApp, db, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Unable to get the application from request'));
    });

    it('Should return an error if db is empty', async () => {
      const test = await budgetFindUtil(app, null as unknown as MssqlData, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Database Unavailable'));

      expect(test).toEqual(new Error('Database Unavailable'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
    });

    it('Should return an error if getDb returns an error', async () => {
      const test = await budgetFindUtil(app, new Error('Database Error') as unknown as MssqlData, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('Database Error'));

      expect(test).toEqual(new Error('Database Error'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Database Error');
    });

    it('Should return an error if there is an issue with finding the global variable', async () => {
      mocks.findGlobalVariable.mockResolvedValueOnce(new Error('An error occurred while finding the global variable'));
      const test = await budgetFindUtil(app, db, { projectId: 'bad app id' });
      expect(test).toEqual(new Error('An error occurred while finding the global variable'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('An error occurred while finding the global variable');
    });

    it('Should return an error if there is an error getting the projectId', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '' });
      expect(test).toEqual(new Error('The project ID is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The project ID is not valid');
    });

    it('Should return a 400 with an error if there is an error getting the projectTitle', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '3AADC99D-0B7F-40e7-8780-881E02CD8661}', projectTitle: '' });
      expect(test).toEqual(new Error('The project title is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The project title is not valid');
    });

    it('Should return an error if there is an error getting the systemId', async () => {
      const test = await budgetFindUtil(app, db, { projectId: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}', projectTitle: 'Project Title', systemId: '' });
      expect(test).toEqual(new Error('The system ID is not valid'));

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The system ID is not valid');
    });

    it('Should return a result when given a project id when onlyIds is false or null ', async () => {
      mocks.queryView.mockResolvedValueOnce([projectIdTitleDbResponse]);

      const test = await budgetFindUtil(app, db, { projectId: '000004', projectTitle: 'Project Title' });
      expect(test).toEqual({
        count: 1,
        Budgets: projectIdTitleAPIResponse,
      });
    });

    it('Should return an error message when onlyIds is false and findData returns an error', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('Find Data Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          projectId: '000004',
          projectTitle: 'Project Title',
          systemId: '{3AADC99D-0B7F-40e7-8780-881E02CD8661}',
          onlyIds: '',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return an error if findDataIdsOnly returns an error and onlyIds is true', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('findDataIdsOnly Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          onlyIds: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return a result when given just a project id and onlyIds is true', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsAPIResponse]);
      const test = await budgetFindUtil(
        app,
        db,
        {
          onlyIds: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });

    it('Should return an error if findDataIdsOnly returns an error and idsOnly is true', async () => {
      mocks.queryView.mockResolvedValueOnce(new Error('findDataIdsOnly Error'));
      const test = await budgetFindUtil(
        app,
        db,
        {
          idsOnly: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual(new Error('There was an issue fetching information from the database'));
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an issue fetching information from the database');
    });

    it('Should return a result when given just a project id and idsOnly is true', async () => {
      mocks.queryView.mockResolvedValueOnce([oidsAPIResponse]);
      const test = await budgetFindUtil(
        app,
        db,
        {
          idsOnly: 'true',
          projectId: '000004',
        },
      );

      expect(test).toEqual({
        count: '1',
        Budgets: oidsAPIResponse,
      });
    });
  });

  describe('executeSparxBudgetOperation', () => {
    const mockStoredProcedures = vi.hoisted(() => ({
      queryStoredProcedures: vi.fn(),
    }));

    const mockDb = {
      queryStoredProcedures: mockStoredProcedures.queryStoredProcedures,
    } as unknown as MssqlData;

    const validBudgets = [
      {
        projectId: '{11111111-2222-3333-4444-555555555555}',
        systemId: '{22222222-3333-4444-5555-666666666666}',
        funding: 'Operational Budget',
        projectTitle: 'Test Project',
        FiscalYear: '2024',
      },
    ];

    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('Should return an error if app is not provided', async () => {
      const result = await executeSparxBudgetOperation(
        null as unknown as CMSApp,
        mockDb,
        validBudgets,
      );

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if database is not provided', async () => {
      const result = await executeSparxBudgetOperation(
        app,
        null as unknown as MssqlData,
        validBudgets,
      );

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('Database Unavailable');

      // Verify error was logged
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    });

    it('Should return an error if database is an error object', async () => {
      const dbError = new Error('Database connection failed');

      const result = await executeSparxBudgetOperation(
        app,
        dbError as unknown as MssqlData,
        validBudgets,
      );

      expect(result).toEqual(dbError);

      // Verify error was logged
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toEqual(dbError);
    });

    it('Should return an error if budgets array is empty', async () => {
      const result = await executeSparxBudgetOperation(app, mockDb, []);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('No budgets provided');
    });

    it('Should return an error if budgets array is null', async () => {
      const result = await executeSparxBudgetOperation(
        app,
        mockDb,
        null as unknown as BudgetRequest[],
      );

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('No budgets provided');
    });

    it('Should successfully execute SparX operation for single budget', async () => {
      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

      const result = await executeSparxBudgetOperation(app, mockDb, validBudgets);

      expect(result).toEqual({ success: true, count: 1 });

      // Verify stored procedure was called with correct parameters
      expect(mockStoredProcedures.queryStoredProcedures).toHaveBeenCalledWith(
        'SP_Insert_SystemBudget_json',
        [{ name: 'RETURN_VALUE', type: 'int' }],
        [{
          name: 'jsonInput',
          value: JSON.stringify({
            CurrentProfile: 'API User',
            Objects: [{
              ClassName: 'ProjectArch',
              Id: '1',
              Values: { cms_funding: 'Operational Budget' },
            }],
            Relations: [
              {
                FromId: '1',
                Property: 'project',
                ToRef: '{11111111-2222-3333-4444-555555555555}',
              },
              {
                FromId: '1',
                Property: 'object',
                ToRef: '{22222222-3333-4444-5555-666666666666}',
              },
            ],
          }),
        }],
        [{ resultKey: 'queryStatus', paramName: 'RETURN_VALUE', wrapAsParam: true }],
      );
    });

    it('Should successfully execute SparX operation for multiple budgets', async () => {
      const multipleBudgets = [
        {
          projectId: '{11111111-2222-3333-4444-555555555555}',
          systemId: '{22222222-3333-4444-5555-666666666666}',
          funding: 'Budget A',
        },
        {
          projectId: '{33333333-4444-5555-6666-777777777777}',
          systemId: '{44444444-5555-6666-7777-888888888888}',
          funding: 'Budget B',
        },
      ];

      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

      const result = await executeSparxBudgetOperation(app, mockDb, multipleBudgets);

      expect(result).toEqual({ success: true, count: 2 });

      // Verify correct JSON was generated for multiple budgets
      const callArgs = mockStoredProcedures.queryStoredProcedures.mock.calls[0];
      const jsonInput = JSON.parse(callArgs[2][0].value);

      expect(jsonInput.Objects).toHaveLength(2);
      expect(jsonInput.Objects[0].Id).toEqual('1');
      expect(jsonInput.Objects[1].Id).toEqual('2');
      expect(jsonInput.Relations).toHaveLength(4); // 2 budgets × 2 relations each
    });

    it('Should handle budget without systemId (only project relation)', async () => {
      const budgetWithoutSystemId = [{
        projectId: '{11111111-2222-3333-4444-555555555555}',
        funding: 'Test Budget',
        // systemId is missing
      }];

      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

      const result = await executeSparxBudgetOperation(app, mockDb, budgetWithoutSystemId);

      expect(result).toEqual({ success: true, count: 1 });

      // Verify only project relation was created (no object relation)
      const callArgs = mockStoredProcedures.queryStoredProcedures.mock.calls[0];
      const jsonInput = JSON.parse(callArgs[2][0].value);

      expect(jsonInput.Relations).toHaveLength(1);
      expect(jsonInput.Relations[0].Property).toEqual('project');
    });

    it('Should return original error when stored procedure execution fails', async () => {
      const spError = new Error('SP execution failed');
      mockStoredProcedures.queryStoredProcedures.mockRejectedValueOnce(spError);

      const result = await executeSparxBudgetOperation(app, mockDb, validBudgets);

      expect(result).toEqual(spError);

      // Verify error was logged with proper context
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toEqual(spError);
      expect(get(errorLog, '[0]', []).message).toEqual('Database stored procedure execution failed');
      expect(get(errorLog, '[0]', [])).toHaveProperty('payload');
    });

    it('Should return original error when stored procedure returns error object', async () => {
      const spError = new Error('SP returned error');
      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce(spError);

      const result = await executeSparxBudgetOperation(app, mockDb, validBudgets);

      expect(result).toEqual(spError);

      // Verify error was logged
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toEqual(spError);
    });

    it('Should return error when stored procedure returns non-zero status', async () => {
      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 1 }]]);

      const result = await executeSparxBudgetOperation(app, mockDb, validBudgets);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('Stored procedure returned non-zero status');

      // Verify error was logged with proper context
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).message).toEqual('Stored procedure returned non-zero status.');
      expect(get(errorLog, '[0]', []).queryStatus).toEqual(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('payload');
    });

    it('Should handle missing queryStatus in stored procedure result', async () => {
      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{}]]);

      const result = await executeSparxBudgetOperation(app, mockDb, validBudgets);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toEqual('Stored procedure returned non-zero status');

      // Verify error was logged with -1 as default queryStatus
      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(get(errorLog, '[0]', []).queryStatus).toEqual(-1);
    });

    it('Should generate correct SparX payload structure', async () => {
      const testBudgets = [
        {
          projectId: '{project-1}',
          systemId: '{system-1}',
          funding: 'Test Funding 1',
        },
        {
          projectId: '{project-2}',
          // systemId missing for second budget
          funding: 'Test Funding 2',
        },
      ];

      mockStoredProcedures.queryStoredProcedures.mockResolvedValueOnce([[{ queryStatus: 0 }]]);

      await executeSparxBudgetOperation(app, mockDb, testBudgets);

      const callArgs = mockStoredProcedures.queryStoredProcedures.mock.calls[0];
      const jsonInput = JSON.parse(callArgs[2][0].value);

      // Verify overall structure
      expect(jsonInput).toHaveProperty('CurrentProfile', 'API User');
      expect(jsonInput).toHaveProperty('Objects');
      expect(jsonInput).toHaveProperty('Relations');

      // Verify Objects
      expect(jsonInput.Objects).toHaveLength(2);
      expect(jsonInput.Objects[0]).toEqual({
        ClassName: 'ProjectArch',
        Id: '1',
        Values: { cms_funding: 'Test Funding 1' },
      });
      expect(jsonInput.Objects[1]).toEqual({
        ClassName: 'ProjectArch',
        Id: '2',
        Values: { cms_funding: 'Test Funding 2' },
      });

      // Verify Relations (3 total: 2 project + 1 object)
      expect(jsonInput.Relations).toHaveLength(3);

      // Check project relations
      expect(jsonInput.Relations).toContainEqual({
        FromId: '1',
        Property: 'project',
        ToRef: '{project-1}',
      });
      expect(jsonInput.Relations).toContainEqual({
        FromId: '2',
        Property: 'project',
        ToRef: '{project-2}',
      });

      // Check object relation (only for first budget)
      expect(jsonInput.Relations).toContainEqual({
        FromId: '1',
        Property: 'object',
        ToRef: '{system-1}',
      });
    });
  });
});
