import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import express from 'express';
import { get, set } from 'lodash';
import { initLdap, getCredentials } from '../../src/utils/auth';
import { CMSApp } from '../../src/types';

const mocks = vi.hoisted(() => ({
  getSecret: vi.fn(),
}));

vi.mock(import('../../src/subsystems/secrets'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getSecret: mocks.getSecret,
  };
});

describe('Auth Utilities tests', () => {
  const app = express() as CMSApp;
  set(app, 'config', {
    systems: {
      auth: {
        secretString: 'test-secret',
      },
    },
  });

  describe('initLdap', () => {
    it('Should return an error if the ldap secret string is not a string', async () => {
      const badApp = express() as CMSApp;
      set(badApp, 'config', {
        systems: {
          authorization: {
            secretString: 'test-secret',
          },
        },
      });
      const test = await initLdap(badApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve LDAP Secret configuration');
    });

    it('Should return an error if the ldap secret string is empty', async () => {
      const badApp = express() as CMSApp;
      set(badApp, 'config', {
        systems: {
          auth: {
            secretString: '',
          },
        },
      });
      const test = await initLdap(badApp) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Unable to retrieve LDAP Secret configuration');
    });

    it('Should return an error if getSecret returns an error', async () => {
      mocks.getSecret.mockImplementationOnce(() => new Error('Bad Connection'));

      const test = await initLdap(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Bad Connection');
    });

    it('Should return an error if ldapServer is invalid', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({
        ldap_user: 'user',
        ldap_pass: 'pass',
      }));

      const test = await initLdap(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid credentials');
    });

    it('Should return an error if ldapAdmin is invalid', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({
        ldap_url: 'example.com',
        ldap_pass: 'pass',
      }));

      const test = await initLdap(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid credentials');
    });

    it('Should return an error if ldapAdminPass is invalid', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({
        ldap_url: 'example.com',
        ldap_user: 'user',
      }));

      const test = await initLdap(app) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid credentials');
    });

    it('Should return true and set the ldap settings', async () => {
      mocks.getSecret.mockImplementationOnce(() => ({
        ldap_url: 'example.com',
        ldap_user: 'user',
        ldap_pass: 'pass',
      }));

      const beforeCredentialsUrl = get(getCredentials(), 'clientOptions.url');
      expect(beforeCredentialsUrl).toEqual('undefined://undefined:undefined');
      const test = await initLdap(app);
      expect(test).toEqual(true);
      const afterCredentialsUrl = get(getCredentials(), 'clientOptions.url');
      expect(afterCredentialsUrl).toEqual('ldaps://example.com:11636');
    });
  });

  describe('getCredentials', () => {
    it('Should return the ldapAdmin, ldapAdminPass, and clientOptions', () => {
      const test = getCredentials();

      expect(test).toHaveProperty('ldapAdmin');
      expect(test.ldapAdmin).toEqual('user');
      expect(test).toHaveProperty('ldapAdminPass');
      expect(test.ldapAdminPass).toEqual('pass');
      expect(test).toHaveProperty('clientOptions');
      expect(test.clientOptions).toEqual({
        url: 'ldaps://example.com:11636',
        tlsOptions: {
          rejectUnauthorized: false,
        },
      });
    });
  });
});
