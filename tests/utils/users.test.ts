import {
  expect,
  describe,
  vi,
  it,
} from 'vitest';
import { get } from 'lodash';
import {
  findSparxUser,
  getUsersSP,
} from '../../src/utils/users';
import {
  createApp,
  testLogger,
} from '../test-utils';
import { CMSApp, SparxUserData } from '../../src/types';
import MssqlData from '../../src/data-sources/mssql';
import {
  testQueryData,
  userNameErrorQueryData,
  firstNameErrorQueryData,
  lastNameErrorQueryData,
  phoneErrorQueryData,
  emailErrorQueryData,
  fakeQueryStatusDataNegOne,
  fakeQueryStatusDataNull,
  fakeQuery,
  fakeQueryEmpty,
  fakeQueryResult,
  fakeQueryString,
  fakeQueryNoMappedResults,
  testQueryPresentData,
  userNameEmptyQueryData,
  firstNameEmptyQueryData,
  lastNameEmptyQueryData,
  phoneEmptyQueryData,
  emailEmptyQueryData,
  queryProcedureQueryTable,
  queryProcedureQueryDataFull,
  queryProcedureQueryParams,
  queryProcedureQueryResults,
} from './userUtilTestData';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
const db = mocks.getDb();

describe('User util tests', () => {
  describe('findSparxUser', () => {
    it('Should return an error if there is no app in the params', async () => {
      const test = await findSparxUser(null as unknown as CMSApp, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from request');
    });

    it('Should return an error if there is no db in the params', async () => {
      const test = await findSparxUser(app, null as unknown as MssqlData, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Database Unavailable');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Database Unavailable');
    });

    it('Should return an error if the foundSparxUser returns an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Unable to retrieve Sparx User'));
      const test = await findSparxUser(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Sparx User');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Sparx User');
    });

    it('Should return an error if the foundSparxUser returns a critical error', async () => {
      mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Query Stored Procedure Critical Error'));
      const test = await findSparxUser(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Query Stored Procedure Critical Error');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Query Stored Procedure Critical Error');
    });

    it('Should return an empty object if the foundSparxUser returns an empty object', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQueryEmpty);
      const test = await findSparxUser(app, db, testQueryData);
      expect(test).toEqual({
        count: 0,
        users: [],
      });
    });

    it('Should return an object with the count and an array of found users', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await findSparxUser(app, db, testQueryData);
      expect(test).toEqual({
        count: 1,
        users: [{
          id: 'fakeid',
          userName: 'ABCD',
          firstName: 'Test',
          lastName: 'User',
          phone: '************',
          email: '<EMAIL>',
        }],
      });
    });
  });

  describe('getUsersSP', () => {
    it('Should return an error if the username is invalid', async () => {
      const test = await getUsersSP(
        app,
        db,
        userNameErrorQueryData as unknown as SparxUserData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the first name is invalid', async () => {
      const test = await getUsersSP(
        app,
        db,
        firstNameErrorQueryData as unknown as SparxUserData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the last name is invalid', async () => {
      const test = await getUsersSP(
        app,
        db,
        lastNameErrorQueryData as unknown as SparxUserData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the phone is invalid', async () => {
      const test = await getUsersSP(
        app,
        db,
        phoneErrorQueryData as unknown as SparxUserData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the email is invalid', async () => {
      const test = await getUsersSP(
        app,
        db,
        emailErrorQueryData as unknown as SparxUserData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should not have a username param in the queryData if it is not present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, userNameEmptyQueryData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        [{
          name: 'Outputjson',
          value: 'result',
          isOutput: true,
        }, {
          name: 'firstName',
          value: 'Test',
        }, {
          name: 'lastName',
          value: 'User',
        }, {
          name: 'phone',
          value: '************',
        }, {
          name: 'email',
          value: '<EMAIL>',
        }],
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should have a username param in the queryData if it is present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryPresentData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        queryProcedureQueryDataFull,
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should not have a firstName param in the queryData if it is not present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, firstNameEmptyQueryData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        [{
          name: 'Outputjson',
          value: 'result',
          isOutput: true,
        }, {
          name: 'username',
          value: 'ABCD',
        }, {
          name: 'lastName',
          value: 'User',
        }, {
          name: 'phone',
          value: '************',
        }, {
          name: 'email',
          value: '<EMAIL>',
        }],
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should have a firstName param in the queryData if it is present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryPresentData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        queryProcedureQueryDataFull,
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should not have a lastName param in the queryData if it is not present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, lastNameEmptyQueryData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        [{
          name: 'Outputjson',
          value: 'result',
          isOutput: true,
        }, {
          name: 'username',
          value: 'ABCD',
        }, {
          name: 'firstName',
          value: 'Test',
        }, {
          name: 'phone',
          value: '************',
        }, {
          name: 'email',
          value: '<EMAIL>',
        }],
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should have a lastName param in the queryData if it is present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryPresentData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        queryProcedureQueryDataFull,
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should not have a phone param in the queryData if it is not present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, phoneEmptyQueryData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        [{
          name: 'Outputjson',
          value: 'result',
          isOutput: true,
        }, {
          name: 'username',
          value: 'ABCD',
        }, {
          name: 'firstName',
          value: 'Test',
        }, {
          name: 'lastName',
          value: 'User',
        }, {
          name: 'email',
          value: '<EMAIL>',
        }],
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should have a phone param in the queryData if it is present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryPresentData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        queryProcedureQueryDataFull,
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should not have a email param in the queryData if it is not present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, emailEmptyQueryData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        [{
          name: 'Outputjson',
          value: 'result',
          isOutput: true,
        }, {
          name: 'username',
          value: 'ABCD',
        }, {
          name: 'firstName',
          value: 'Test',
        }, {
          name: 'lastName',
          value: 'User',
        }, {
          name: 'phone',
          value: '************',
        }],
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should have a email param in the queryData if it is present', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryPresentData);
      expect(mocks.queryStoredProcedures).toBeCalledWith(
        queryProcedureQueryTable,
        queryProcedureQueryParams,
        queryProcedureQueryDataFull,
        queryProcedureQueryResults,
      );
      expect(test).toEqual(fakeQueryResult);
    });

    it('Should return an error if queryStoredProcedures returns an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Unable to retrieve Sparx User'));
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Sparx User');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Sparx User');
    });

    it('Should return an error if queryStoredProcedures returns an critical error', async () => {
      mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Unable to retrieve Sparx User'));
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to retrieve Sparx User');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to retrieve Sparx User');
    });

    it('Should return an error if queryStoredProcedures returns with a query status other than 0', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQueryStatusDataNegOne);
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Status of the query was invalid');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Status of the query was invalid');
    });

    it('Should return an error if findSparxUserResult returns null', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQueryStatusDataNull);
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get findSparxUserResult from query');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to get findSparxUserResult from query');
    });

    it('Should return an error if mappedResults returns null', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQueryNoMappedResults);
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get mappedResults from query');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to get mappedResults from query');
    });

    it('Should return an error if the parsedObject is a string and also an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQueryString);
      const test = await getUsersSP(app, db, testQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toContain("Expected ',' or '}' after property value in JSON at position 12");

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain("Expected ',' or '}' after property value in JSON at position 12");
    });

    it('Should return users if queryStoredProcedures returns users', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(fakeQuery);
      const test = await getUsersSP(app, db, testQueryData);
      expect(test).toEqual(fakeQueryResult);
    });
  });
});
