import {
  expect,
  describe,
  vi,
  it,
} from 'vitest';
import { get } from 'lodash';
import {
  formatQueryData,
  createPromise,
  insertPersonSP,
  InsertUserPromise,
} from '../../src/utils/person';
import {
  createApp,
  testLogger,
} from '../test-utils';
import {
  CMSApp,
  StoredProcedureParam,
  StoredProcedureQuery,
  StoredProcedureData,
} from '../../src/types';
import MssqlData from '../../src/data-sources/mssql';
import {
  goodQueryData,
  goodMultipleQueryData,
  formattedGoodQueryData,
  badExternalSourceQueryData,
  badFirstNameQueryData,
  badLastNameQueryData,
  badPhoneQueryData,
  badTechNameQueryData,
  badUserNameQueryData,
  badEmailQueryData,
  badGUIDQueryData,
  mockFailedInsertPersonResults,
  mockInsertPersonResults,
} from './insertPersonSPTestData';

const mocks = vi.hoisted(() => ({
  queryStoredProcedures: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
}));

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

const app = await createApp();
const db = mocks.getDb();

const queryParams: StoredProcedureParam[] = [{
  name: 'result',
  type: 'nvarchar',
  param: 'max',
}];

const queryResults: StoredProcedureQuery[] = [{
  resultKey: 'result',
  paramName: 'result',
}];

describe('Person util tests', () => {
  describe('formatQueryData', () => {
    it('Should return an error if the app is empty', async () => {
      const test = await formatQueryData(
        null as unknown as CMSApp,
        badExternalSourceQueryData,
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('No app was passed into the parameters');
    });

    it('Should return an error if the externalSource is not valid', async () => {
      const test = await formatQueryData(app, badExternalSourceQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the firstName is not valid', async () => {
      const test = await formatQueryData(app, badFirstNameQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the lastName is not valid', async () => {
      const test = await formatQueryData(app, badLastNameQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the phone is not valid', async () => {
      const test = await formatQueryData(app, badPhoneQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the techName is not valid', async () => {
      const test = await formatQueryData(app, badTechNameQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the userName is not valid', async () => {
      const test = await formatQueryData(app, badUserNameQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the email is not valid', async () => {
      const test = await formatQueryData(app, badEmailQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if the GUID is not valid', async () => {
      const test = await formatQueryData(app, badGUIDQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return queryData that is formatted for stored procedure use', async () => {
      const test = await formatQueryData(app, goodQueryData);
      expect(test).toEqual(formattedGoodQueryData);
    });
  });

  describe('InsertUserPromise', () => {
    it('Should return an error when queryStoredProcedures returns an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Query Stored Procedures Error'));
      const queryData: StoredProcedureData[] = formatQueryData(
        app,
        goodQueryData,
      ) as StoredProcedureData[];
      const test = await InsertUserPromise(app, db, 'SP_Insert_Person', queryParams, queryData, queryResults) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Query Stored Procedures Error');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Query Stored Procedures Error');
    });

    it('Should return an error when queryStoredProcedures returns ac ritical error', async () => {
      mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Critical Error: Query Stored Procedures Error'));
      const queryData: StoredProcedureData[] = formatQueryData(
        app,
        goodQueryData,
      ) as StoredProcedureData[];
      const test = await InsertUserPromise(app, db, 'SP_Insert_Person', queryParams, queryData, queryResults) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Critical Error: Query Stored Procedures Error');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Error: Query Stored Procedures Error');
    });

    it('Should return an array when queryStoredProcedures returns successfully', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(mockInsertPersonResults);
      const queryData: StoredProcedureData[] = formatQueryData(
        app,
        goodQueryData,
      ) as StoredProcedureData[];
      const test = await InsertUserPromise(app, db, 'SP_Insert_Person', queryParams, queryData, queryResults);
      expect(test).toEqual([[
        {
          PropertyID: 12345678,
          Object_ID: 0,
          Property: 'Fake Prop',
          Value: null,
          Notes: '',
          ea_guid: '{11111111-1111-1111-1111-111111111111}',
        },
        {
          PropertyID: 23456789,
          Object_ID: 0,
          Property: 'Fake Prop',
          Value: 'John Smith',
          Notes: '',
          ea_guid: '{11111111-1111-1111-1111-111111111112}',
        },
        { queryStatus: 0, result: null },
      ], 2]);
      expect(test).toHaveLength(2);
    });
  });

  describe('createPromise', () => {
    it('Should return an error if `formatQueryData` returns an error', async () => {
      const test = await createPromise(app, db, badFirstNameQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('"value" must be a string');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('"value" must be a string');
    });

    it('Should return an error if queryStoredProcedures returns an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Query Stored Procedures Error'));

      const test = await createPromise(app, db, goodQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Query Stored Procedures Error');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Query Stored Procedures Error');
    });

    it('Should return an error if queryStoredProcedures returns empty', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([]);

      const test = await createPromise(app, db, goodQueryData) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('There was an error inserting a person in the database');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('There was an error inserting a person in the database');
    });

    it('Should return an error if queryStoredProcedures is successful, but has a query status of 1', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(mockFailedInsertPersonResults);

      const test = await createPromise(app, db, goodQueryData) as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('There was an error inserting a person in the database');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('There was an error inserting a person in the database');
    });

    it('Should return true if queryStoredProcedures is successful and has a query status of 0', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(mockInsertPersonResults);

      const test = await createPromise(app, db, goodQueryData);
      expect(test).toEqual(true);
    });
  });

  describe('insertPersonSP', () => {
    it('Should return an error if the app is invalid', async () => {
      const test = await insertPersonSP(
        null as unknown as CMSApp,
        db,
        [goodQueryData],
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get the application from parameters');
    });

    it('Should return an error if the db is invalid', async () => {
      const test = await insertPersonSP(
        app,
        null as unknown as MssqlData,
        [goodQueryData],
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Database not in parameters');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Database not in parameters');
    });

    it('Should return an error if there is no person data', async () => {
      const test = await insertPersonSP(
        app,
        db,
        [],
      ) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Unable to get person data from parameters');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Unable to get person data from parameters');
    });

    it('Should return an error if there is an error seeding the database', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Could not insert person'));
      const test = await insertPersonSP(app, db, [goodQueryData]) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Could not insert person');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Could not insert person');
    });

    it('Should return an error if there is a critical error seeding the database', async () => {
      mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('Critical Error: Could not insert person'));
      const test = await insertPersonSP(app, db, [goodQueryData]) as Error;
      expect(test).toBeInstanceOf(Error);
      expect(test).toHaveProperty('message');
      expect(test.message).toEqual('Critical Error: Could not insert person');
      const errorLog = testLogger.error.mock.lastCall;

      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', []).error).toHaveProperty('message');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Critical Error: Could not insert person');
    });

    it('Should return an error if multiple instances of queryStoredProcedures return an error', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Query Stored Procedures Error'));
      mocks.queryStoredProcedures.mockResolvedValueOnce(new Error('Query Stored Procedures Error'));
      mocks.queryStoredProcedures.mockResolvedValueOnce(mockInsertPersonResults);

      const test = await insertPersonSP(app, db, goodMultipleQueryData);
      expect(test).toEqual([
        new Error('Query Stored Procedures Error'),
        new Error('Query Stored Procedures Error'),
      ]);

      const errorLog = testLogger.error.mock.lastCall;
      expect(get(errorLog, '[0]', []).error).not.toBeUndefined();
      expect(get(errorLog, '[0]', []).error).toEqual([
        new Error('Query Stored Procedures Error'),
        new Error('Query Stored Procedures Error'),
      ]);
    });

    it('Should return true if the the stored procedure behaves as expected', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce(mockInsertPersonResults);
      const test = await insertPersonSP(app, db, [goodQueryData]);
      expect(test).toEqual(true);
    });
  });
});
