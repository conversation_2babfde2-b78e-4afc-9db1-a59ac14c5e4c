import {
  describe,
  beforeEach,
  afterEach,
  it,
  expect,
  vi,
} from 'vitest';
import dayjs, { Dayjs } from 'dayjs';
import { isError } from 'lodash';
import {
  nowUtc,
  defaultFormat,
  getFormatString,
  nowUtcFormatted,
  formatDate,
  getDuration,
  addToUTC,
  sleep,
  timeFromNowUtc,
  isDayJs,
  isValidFormat,
} from '../../src/utils/time';

const getTwoDigit = (dateItem) => (
  `0${dateItem}`.slice(-2)
);

describe('Time Utilities', () => {
  const testingDateString = '2024-10-31T13:45:12.123Z';
  const testingDateStringFuture = '2024-10-31T13:45:13.123Z';
  const testingDateStringPast = '2024-10-31T13:45:11.123Z';
  const testingDate = new Date(testingDateString);

  beforeEach(() => {
    vi.useFakeTimers({ shouldAdvanceTime: true });
    vi.setSystemTime(testingDate);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('nowUtc', () => {
    it('Should return a DayJS object with the current date and time', () => {
      const now = nowUtc();

      expect(dayjs.isDayjs(now)).toBeTruthy();
      expect(now.toISOString()).toBe(testingDate.toISOString());
    });
  });

  describe('getFormatString', () => {
    it('Should return the default format if no format is passed', () => {
      const dtFormat = getFormatString();
      expect(dtFormat).toEqual(defaultFormat);
    });

    it('Should return the default format if the provided format is not a string', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const dtFormat = getFormatString(['not a string']);
      expect(dtFormat).toEqual(defaultFormat);
    });

    it('Should return the passed format', () => {
      const format = 'MM/DD/YYYY';
      const dtFormat = getFormatString(format);
      expect(dtFormat).toEqual(format);
    });
  });

  describe('nowUtcFormatted', () => {
    it('Should return now local in the default format', () => {
      const now = nowUtcFormatted();
      expect(now).toEqual(testingDateString);
    });

    it('Should return now local in the provided format', () => {
      const now = nowUtcFormatted('DD/MM/YYYY');
      const testingShort = `${getTwoDigit(testingDate.getDate())}/${getTwoDigit(testingDate.getMonth() + 1)}/${testingDate.getFullYear()}`;
      expect(now).toEqual(testingShort);
    });
  });

  describe('formatDate', () => {
    it('Should return an error if the date is invalid', () => {
      const test = formatDate('badDateString') as Error;

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Invalid date');
    });

    it('Should return a formatted date with the default format', () => {
      const test = formatDate('05-22-2017');

      expect(test).toEqual('2017-05-22T00:00:00.000Z');
    });

    it('Should return a formatted date with a custom format', () => {
      const test = formatDate('05-22-2017', 'DDMMMYYYY');

      expect(test).toEqual('22May2017');
    });
  });

  describe('getDuration', () => {
    it('Should return the milliseconds difference between a past and current time', () => {
      const diff = getDuration(testingDateStringPast, testingDateString);
      expect(diff).toEqual(-1000);
    });

    it('Should return the milliseconds difference between the current time and current time', () => {
      const diff = getDuration(testingDateString, testingDateString);
      expect(diff).toEqual(0);
    });

    it('Should return the milliseconds difference between a future and current time', () => {
      const diff = getDuration(testingDateStringFuture, testingDateString);
      expect(diff).toEqual(1000);
    });

    it('Should return the milliseconds difference between a past time and now', () => {
      const diff = getDuration(testingDateStringPast);
      expect(diff).toEqual(-1000);
    });

    it('Should return the milliseconds difference between now and now', () => {
      const diff = getDuration(testingDateString);
      expect(diff).toEqual(0);
    });

    it('Should return the milliseconds difference between a future time and now', () => {
      const diff = getDuration(testingDateStringFuture);
      expect(diff).toEqual(1000);
    });

    it('Should return the seconds difference between a past and current time', () => {
      const diff = getDuration(testingDateStringPast, testingDateString, 'seconds');
      expect(diff).toEqual(-1);
    });

    it('Should return the seconds difference between the current time and current time', () => {
      const diff = getDuration(testingDateString, testingDateString, 'seconds');
      expect(diff).toEqual(0);
    });

    it('Should return the seconds difference between a future and current time', () => {
      const diff = getDuration(testingDateStringFuture, testingDateString, 'seconds');
      expect(diff).toEqual(1);
    });

    it('Should return the seconds difference between a past time and now', () => {
      const diff = getDuration(testingDateStringPast, null, 'seconds');
      expect(diff).toEqual(-1);
    });

    it('Should return the seconds difference between now and now', () => {
      const diff = getDuration(testingDateString, null, 'seconds');
      expect(diff).toEqual(0);
    });

    it('Should return the seconds difference between a future time and now', () => {
      const diff = getDuration(testingDateStringFuture, null, 'seconds');
      expect(diff).toEqual(1);
    });

    it('Should accept a DayJS object for the start date', () => {
      const diff = getDuration(nowUtc().add(5, 'seconds'), null, 'seconds');
      expect(diff).toEqual(5);
    });

    it('Should return an error if the start date is invalid', () => {
      const diff = getDuration('badDate') as Error;

      expect(diff).toBeInstanceOf(Error);
      expect(diff.message).toEqual('Invalid start date');
    });

    it('Should accept a DayJS object for the end date', () => {
      const diff = getDuration(testingDateStringFuture, nowUtc(), 'seconds');

      expect(diff).toEqual(1);
    });

    it('Should return an error if the end date is invalid', () => {
      const diff = getDuration(testingDateStringFuture, 'badDate') as Error;

      expect(diff).toBeInstanceOf(Error);
      expect(diff.message).toEqual('Invalid end date');
    });
  });

  describe('addToUtc', () => {
    it('Should return the start time plus 5 seconds', () => {
      const futureTime = addToUTC(testingDateString, 5, 'seconds');
      if (isError(futureTime)) {
        expect(futureTime).not.toBeInstanceOf(Error);
        return;
      }
      const diff = getDuration(futureTime, testingDateString, 'seconds');
      expect(diff).toEqual(5);
    });

    it('Should return the start time plus 5 day in the DD/MM/YYYY format', () => {
      const futureTime = addToUTC(testingDateString, 5, 'days', true, 'DD/MM/YYYY');
      expect(futureTime).toEqual('05/11/2024');
    });

    it('Should return an updated DayJS object', () => {
      const futureTime = addToUTC(testingDateString, 5, 'days', false) as Dayjs;

      expect(isDayJs(futureTime)).toEqual(true);
      expect(formatDate(futureTime)).toEqual('2024-11-05T13:45:12.123Z');
    });

    it('Should return an error if the provided time is invalid', () => {
      const badTime = addToUTC('bad', 5, 'days');
      if (!isError(badTime)) {
        expect(badTime).toBeInstanceOf(Error);
        return;
      }
      expect(badTime.message).toEqual('Invalid date');
    });
  });

  describe('sleep', () => {
    it('Should set a timeout to resolve after 100ms', async () => {
      const testSleep = async () => {
        const start = nowUtcFormatted();
        await sleep(100);
        return getDuration(start, nowUtcFormatted());
      };

      const testDuration = await testSleep();
      expect(testDuration).toEqual(-100);
    });

    it('Should set a timeout to resolve after 100ms and call a custom function', async () => {
      let test = 'failed';
      const customFunc = () => {
        test = 'success';
      };

      const testSleep = async () => {
        const start = nowUtcFormatted();
        await sleep(100, (resolve) => {
          customFunc();
          resolve();
        });
        return getDuration(start, nowUtcFormatted());
      };

      const testDuration = await testSleep();
      expect(testDuration).toEqual(-100);
      expect(test).toEqual('success');
    });
  });

  describe('timeFromNowUtc', () => {
    it('Should return 5 minutes from now', () => {
      const futureTime = timeFromNowUtc(5, 'minutes');
      if (isError(futureTime)) {
        expect(futureTime).not.toBeInstanceOf(Error);
        return;
      }
      expect(getDuration(futureTime, null, 'minutes')).toEqual(5);
    });

    it('Should return an error if the amount is not a number', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const badTime = timeFromNowUtc('bad', 'minutes');
      if (!isError(badTime)) {
        expect(badTime).toBeInstanceOf(Error);
        return;
      }
      expect(badTime.message).toEqual('Invalid amount or unit');
    });

    it('Should return an error if unit is not a string', () => {
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const badTime = timeFromNowUtc(5, ['bad']);
      if (!isError(badTime)) {
        expect(badTime).toBeInstanceOf(Error);
        return;
      }
      expect(badTime.message).toEqual('Invalid amount or unit');
    });
  });

  describe('isDayJs', () => {
    it('Should return false if time is a valid string', () => {
      const test = isDayJs('05-22-2017');

      expect(test).toEqual(false);
    });

    it('Should return false if time is a valid date', () => {
      const test = isDayJs(new Date('05-22-2017'));

      expect(test).toEqual(false);
    });

    it('Should return true if time is a valid dayjs object', () => {
      const test = isDayJs(nowUtc());

      expect(test).toEqual(true);
    });
  });

  describe('isValidFormat', () => {
    it('Should return true if the date matches the format', () => {
      const test = isValidFormat('2021-05-12', 'YYYY-DD-MM');
      expect(test).toEqual(true);
    });

    it('Should return false if the date does not match the format', () => {
      const test = isValidFormat('05-12-2021', 'YYYY-DD-MM');
      expect(test).toEqual(false);
    });

    it('Should return false if the date is invalid', () => {
      const test = isValidFormat('2021-05-38', 'YYYY-DD-MM');
      expect(test).toEqual(false);
    });
  });
});
