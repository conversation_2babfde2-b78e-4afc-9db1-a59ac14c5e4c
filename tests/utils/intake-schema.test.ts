import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import Joi, { AnySchema } from 'joi';
import { createApp, testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';
import {
  getSchema,
  createRule,
  processRules,
  validateWithSchema,
} from '../../src/utils/intake-schema';

const mocks = vi.hoisted(() => ({
  findOne: vi.fn(),
  getDb: vi.fn().mockImplementation(() => ({
    findOne: mocks.findOne,
  })),
}));
const intakeSchema = '{"foo":"string,required,allow:null","bar":{"biz":"boolean,required,strict","baz":"string"},"biz":["string,required"]}';
mocks.findOne.mockResolvedValue(intakeSchema);

vi.mock(import('../../src/utils/db-helpers'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    getDb: mocks.getDb,
  };
});

describe('Intake Schema Utilities tests', () => {
  describe('getSchema', () => {
    const app = {
      logger: testLogger,
    } as unknown as CMSApp;

    it('Should return an error if getDb returns an error', async () => {
      mocks.getDb.mockImplementationOnce(() => new Error('Bad Connection'));
      const test = await getSchema(app, 'testing');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Bad Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return an error if the name is empty', async () => {
      const test = await getSchema(app, '');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('The schema name is invalid');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('The schema name is invalid');
    });

    it('Should return an error if findOne returns an error', async () => {
      mocks.findOne.mockResolvedValueOnce(new Error('Bad Connection'));
      const test = await getSchema(app, 'testing');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Bad Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Connection');
    });

    it('Should return an error if findOne returns a critical error', async () => {
      mocks.findOne.mockRejectedValueOnce(new Error('Bad Critical Connection'));
      const test = await getSchema(app, 'testing');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Bad Critical Connection');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Bad Critical Connection');
    });

    it('Should return an error if findOne returns an empty result', async () => {
      mocks.findOne.mockResolvedValueOnce(undefined);
      const test = await getSchema(app, 'testing');

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('No result returned');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('No result returned');
    });

    it('Should return a schema from the database', async () => {
      const test = await getSchema(app, 'testing');

      expect(test).toEqual(intakeSchema);
    });
  });

  describe('createRule', () => {
    it('Should return an error if the schema type is invalid', () => {
      const rule = 'strng,required';
      const validator = createRule(rule);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid schema type');
    });

    it('Should return a rule without parameters and validate successfully', () => {
      const rule = 'string,required';
      const validator = createRule(rule) as AnySchema;
      expect(validator).not.toBeInstanceOf(Error);
      const valid1 = validator.validate('');
      const valid2 = validator.validate('test');

      expect(valid1.error).toBeDefined();
      expect(valid1.error).toBeInstanceOf(Error);
      expect((valid1.error as Error).message).toEqual('"value" is not allowed to be empty');

      expect(valid2.error).toBeUndefined();
      expect(valid2.value).toEqual('test');
    });

    it('Should return a rule with parameters and validate successfully', () => {
      const rule = 'string,required,allow:null';
      const validator = createRule(rule) as AnySchema;
      expect(validator).not.toBeInstanceOf(Error);
      const valid1 = validator.validate('test');
      const valid2 = validator.validate(null);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual('test');
      expect(valid2.error).toBeUndefined();
      expect(valid2.value).toEqual(null);
    });

    it('Should ignore invalid rules without parameters', () => {
      const rule = 'string,badRule';
      const validator = createRule(rule) as AnySchema;
      expect(validator).not.toBeInstanceOf(Error);
      const valid1 = validator.validate('test');
      const valid2 = validator.validate(null);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual('test');
      expect(valid2.error).toBeDefined();
      expect(valid2.error).toBeInstanceOf(Error);
      expect((valid2.error as Error).message).toEqual('"value" must be a string');
    });

    it('Should ignore invalid rules without parameters', () => {
      const rule = 'string,required,badRule:null';
      const validator = createRule(rule) as AnySchema;
      expect(validator).not.toBeInstanceOf(Error);
      const valid1 = validator.validate('test');
      const valid2 = validator.validate(null);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual('test');
      expect(valid2.error).toBeDefined();
      expect(valid2.error).toBeInstanceOf(Error);
      expect((valid2.error as Error).message).toEqual('"value" must be a string');
    });
  });

  describe('processRules', async () => {
    const app = await createApp();
    it('Should return an error if the rules are not a string, array, or object', async () => {
      const root = Joi;
      const rules = 3;
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Cannot configure validator');
    });

    it('Should handle a string rule', async () => {
      const root = Joi;
      const rules = 'string,required,allow:null';

      const validator = await processRules(app, root, rules) as AnySchema;
      expect(validator).not.toBeInstanceOf(Error);

      const valid1 = validator.validate('test');
      const valid2 = validator.validate(null);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual('test');
      expect(valid2.error).toBeUndefined();
      expect(valid2.value).toEqual(null);
    });

    it('Should return an error if the rule is a string and createRule returns an error', async () => {
      const root = Joi;
      const rules = 'strng,required';

      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid schema type');
    });

    it('Should handle an array with a string rule', async () => {
      const root = Joi;
      const rules = ['string,required'];
      const input = ['foo', 'bar'];
      const validator = await await processRules(app, root, rules) as AnySchema;
      const valid1 = validator.validate(input);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual(input);
    });

    it('Should handle an array with an object rule', async () => {
      const root = Joi;
      const rules = [{
        foo: 'string,required',
        bar: 'boolean,required,strict',
      }];
      const input = [{
        foo: 'first',
        bar: true,
      }, {
        foo: 'second',
        bar: false,
      }];
      const validator = await processRules(app, root, rules) as AnySchema;
      const valid1 = validator.validate(input);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual(input);
    });

    it('Should handle an array with an array rule', async () => {
      const root = Joi;
      const rules = [['string,required']];
      const input = [['foo', 'bar'], ['biz', 'baz']];
      const validator = await processRules(app, root, rules) as AnySchema;
      const valid1 = validator.validate(input);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual(input);
    });

    it('Should return an error if the array does not contain a string, array, or object', async () => {
      const root = Joi;
      const rules = [3];
      // Disable type check to verify error handling logic
      // @ts-expect-error: TS2345
      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid array rule configuration');
    });

    it('Should return an error if the array is empty', async () => {
      const root = Joi;
      const rule = [];
      const test = await processRules(app, root, rule);

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Incorrect number of rules in the array configuration');
    });

    it('Should return an error if the array has more than 1 rule defined', async () => {
      const root = Joi;
      const rule = ['string,required', 'boolean,required,strict'];
      const test = await processRules(app, root, rule);

      expect(test).toBeInstanceOf(Error);
      expect(test.message).toEqual('Incorrect number of rules in the array configuration');
    });

    it('Should return an error if the string rule returns an error', async () => {
      const root = Joi;
      const rules = ['strng,required'];

      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid schema type');
    });

    it('Should return an error if the object rule returns an error', async () => {
      const root = Joi;
      const rules = [{
        foo: 'strng,required',
      }];

      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid schema type');
    });

    it('Should return an error if the array rule returns an error', async () => {
      const root = Joi;
      const rules = [['strng,required']];

      const validator = await processRules(app, root, rules);

      expect(validator).toBeInstanceOf(Error);
      expect(validator.message).toEqual('Invalid schema type');
    });

    it('Should handle an empty object', async () => {
      const newRule = Joi;
      const rules = {};
      const validator = await processRules(app, newRule, rules) as AnySchema;
      const valid1 = validator.validate({
        foo: 'bar',
        bar: {
          biz: 'baz',
        },
      });

      const valid2 = validator.validate({});

      expect(valid1.error).toBeDefined();
      expect(valid1.error).toBeInstanceOf(Error);
      expect((valid1.error as Error).message).toEqual('"foo" is not allowed');

      expect(valid2.error).toBeUndefined();
      expect(valid2.value).toEqual({});
    });

    it('Should handle a key with a string value', async () => {
      const newRule = Joi;
      const rules = {
        foo: 'string,required',
      };
      const validator = await processRules(app, newRule, rules) as AnySchema;
      const valid1 = validator.validate({
        foo: 'bar',
      });
      const valid2 = validator.validate({
        foo: ['bar'],
      });
      const valid3 = validator.validate({
        notFoo: 'bar',
      });

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual({
        foo: 'bar',
      });

      expect(valid2.error).toBeDefined();
      expect(valid2.error).toBeInstanceOf(Error);
      expect((valid2.error as Error).message).toEqual('"foo" must be a string');

      expect(valid3.error).toBeDefined();
      expect(valid3.error).toBeInstanceOf(Error);
      expect((valid3.error as Error).message).toEqual('"foo" is required');
    });

    it('Should handle a key with an object value', async () => {
      const newRule = Joi;
      const rules = {
        bar: {
          biz: 'boolean,required,strict',
        },
      };
      const validator = await processRules(app, newRule, rules) as AnySchema;
      const valid1 = validator.validate({
        bar: {
          biz: true,
        },
      });
      const valid2 = validator.validate({
        bar: {
          biz: false,
        },
      });
      const valid3 = validator.validate({
        bar: {
          biz: 'true',
        },
      });
      const valid4 = validator.validate({
        bar: {
          notBiz: false,
        },
      });

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual({
        bar: {
          biz: true,
        },
      });

      expect(valid2.error).toBeUndefined();
      expect(valid2.value).toEqual({
        bar: {
          biz: false,
        },
      });

      expect(valid3.error).toBeDefined();
      expect(valid3.error).toBeInstanceOf(Error);
      expect((valid3.error as Error).message).toEqual('"bar.biz" must be a boolean');

      expect(valid4.error).toBeDefined();
      expect(valid4.error).toBeInstanceOf(Error);
      expect((valid4.error as Error).message).toEqual('"bar.biz" is required');
    });

    it('Should handle a key with a schema value', async () => {
      mocks.findOne.mockResolvedValueOnce({ schema: '{"bar": "string,required"}' });
      const newRule = Joi;
      const rules = {
        foo: 'schema:ValidJson',
      };
      const validator = await processRules(app, newRule, rules) as AnySchema;

      const valid1 = validator.validate({
        foo: {
          bar: 'bar',
        },
      });
      const valid2 = validator.validate({
        foo: {
          bar: ['bar'],
        },
      });
      const valid3 = validator.validate({
        notFoo: 'bar',
      });

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual({
        foo: {
          bar: 'bar',
        },
      });

      expect(valid2.error).toBeDefined();
      expect(valid2.error).toBeInstanceOf(Error);
      expect((valid2.error as Error).message).toEqual('"foo.bar" must be a string');

      expect(valid3.error).toBeDefined();
      expect(valid3.error).toBeInstanceOf(Error);
      expect((valid3.error as Error).message).toEqual('"notFoo" is not allowed');
    });

    it('Should return an error if getSchema returns an error', async () => {
      mocks.findOne.mockResolvedValueOnce(new Error('Bad Connection'));
      const newRule = Joi;

      const test = await processRules(app, newRule, { foo: 'schema:ErrorOutput' });
      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Bad Connection');
    });

    it('Should return an error if parseSchema returns an error', async () => {
      mocks.findOne.mockResolvedValueOnce({ schema: '{"bar": "string,required"' });
      const newRule = Joi;

      const test = await processRules(app, newRule, { foo: 'schema:BadParse' });
      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('An error occurred while setting up the schema validator');
    });

    it('Should handle multiple keys with one being a string and the other an object', async () => {
      const newRule = Joi;
      const rules = {
        foo: 'string,required',
        bar: {
          biz: 'boolean,required',
        },
      };
      const validator = await processRules(app, newRule, rules) as AnySchema;
      const valid1 = validator.validate({
        foo: 'bar',
        bar: {
          biz: true,
        },
      });

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual({
        foo: 'bar',
        bar: {
          biz: true,
        },
      });
    });

    it('Should handle an object with an array rule', async () => {
      const root = Joi;
      const rules = {
        foo: ['string,required'],
      };
      const input = { foo: ['foo', 'bar'] };
      const validator = await processRules(app, root, rules) as AnySchema;
      const valid1 = validator.validate(input);

      expect(valid1.error).toBeUndefined();
      expect(valid1.value).toEqual(input);
    });
  });

  describe('validateWithSchema', () => {
    const app = {
      logger: testLogger,
    } as unknown as CMSApp;

    it('Should return an error if the schema is empty', async () => {
      const test = await validateWithSchema(app, '', {});

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Empty schema provided');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('Empty schema provided');
    });

    it('Should return an error if the schema cannot be parsed', async () => {
      const test = await validateWithSchema(app, '{ "bad": "json"', {});

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('An error occurred while setting up the schema validator');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toContain('Expected \',\' or \'}\' after property value in JSON at position 15');
    });

    it('Should return an error if processRules returns an error', async () => {
      const schema = '{"foo":"strng,required"}';
      const test = await validateWithSchema(app, schema, {});

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('Invalid schema type');
    });

    it('Should return an error if the body is invalid', async () => {
      const parsedBody = {
        foo: 'good value',
        bar: {
          biz: 'false',
        },
      };
      const test = await validateWithSchema(app, intakeSchema, parsedBody);

      expect(test).toBeInstanceOf(Error);
      expect((test as Error).message).toEqual('The body is not valid');

      const errorLog = testLogger.error.mock.lastCall;
      expect(errorLog).not.toBeUndefined();
      expect(errorLog).toHaveLength(1);
      expect(get(errorLog, '[0]', [])).toHaveProperty('error');
      expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
      expect(get(errorLog, '[0]', []).error.message).toEqual('"bar.biz" must be a boolean');
    });

    it('Should return true if the body is valid', async () => {
      const parsedBody = {
        foo: 'good value',
        bar: {
          biz: false,
          baz: 'here',
        },
        biz: [
          'value1',
          'value2',
        ],
      };
      const test = await validateWithSchema(app, intakeSchema, parsedBody);

      expect(test).toEqual(true);
    });
  });
});
