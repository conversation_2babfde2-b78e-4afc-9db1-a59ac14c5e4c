const testQueryData = {
  userName: 'S6YI',
  firstName: '',
  lastName: '',
  phone: '',
  email: '',
};

const userNameErrorQueryData = {
  userName: 1234,
  firstName: '',
  lastName: '',
  phone: '',
  email: '',
};

const firstNameErrorQueryData = {
  userName: '',
  firstName: 1234,
  lastName: '',
  phone: '',
  email: '',
};

const lastNameErrorQueryData = {
  userName: '',
  firstName: '',
  lastName: 1234,
  phone: '',
  email: '',
};

const phoneErrorQueryData = {
  userName: '',
  firstName: '',
  lastName: '',
  phone: 1234,
  email: '',
};

const emailErrorQueryData = {
  userName: '',
  firstName: '',
  lastName: '',
  phone: '',
  email: 1234,
};

const testQueryPresentData = {
  userName: 'ABCD',
  firstName: 'Test',
  lastName: 'User',
  phone: '************',
  email: '<EMAIL>',
};

const userNameEmptyQueryData = {
  userName: '',
  firstName: 'Test',
  lastName: 'User',
  phone: '************',
  email: '<EMAIL>',
};

const firstNameEmptyQueryData = {
  userName: 'ABCD',
  firstName: '',
  lastName: 'User',
  phone: '************',
  email: '<EMAIL>',
};

const lastNameEmptyQueryData = {
  userName: 'ABCD',
  firstName: 'Test',
  lastName: '',
  phone: '************',
  email: '<EMAIL>',
};

const phoneEmptyQueryData = {
  userName: 'ABCD',
  firstName: 'Test',
  lastName: 'User',
  phone: '',
  email: '<EMAIL>',
};

const emailEmptyQueryData = {
  userName: 'ABCD',
  firstName: 'Test',
  lastName: 'User',
  phone: '************',
  email: '',
};

const queryProcedureQueryParams = [{
  name: 'result',
  type: 'nvarchar',
  param: 'max',
}];

const queryProcedureQueryTable = 'SP_Get_UserList';

const queryProcedureQueryResults = [{
  resultKey: 'result',
  paramName: 'result',
}];

const queryProcedureQueryDataFull = [{
  name: 'Outputjson',
  value: 'result',
  isOutput: true,
}, {
  name: 'username',
  value: 'ABCD',
}, {
  name: 'firstName',
  value: 'Test',
}, {
  name: 'lastName',
  value: 'User',
}, {
  name: 'phone',
  value: '************',
}, {
  name: 'email',
  value: '<EMAIL>',
}];

const fakeQueryStatusDataNegOne = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
{
  queryStatus: -1,
  result: {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
}, 1,
]];

const fakeQueryStatusDataNull = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
{
  queryStatus: 0,
  result: null,
}, 1,
]];

const fakeQueryNoMappedResults = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
null,
1,
]];

const fakeQuery = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
{
  queryStatus: 0,
  result: {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'User',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
}, 1,
]];

const fakeQueryResult = [{
  ClassName: 'Person',
  id: 'fakeid',
  Values:
    {
      userName: 'ABCD',
      firstName: 'Test',
      lastName: 'User',
      phone: '************',
      email: '<EMAIL>',
    },
}];

const fakeQueryEmpty = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'BCDE',
        firstName: 'Test',
        lastName: 'Person2',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
{
  queryStatus: 0,
  result: {
    Objects: [],
  },
}, 1,
]];

const fakeQueryString = [[{
  '': {
    Objects: [{
      ClassName: 'Person',
      id: 'fakeid',
      Values: {
        userName: 'ABCD',
        firstName: 'Test',
        lastName: 'Person',
        phone: '************',
        email: '<EMAIL>',
      },
    }],
  },
},
{
  queryStatus: 0,
  result: '{"foo":"bar"',
}, 1,
]];

export {
  testQueryData,
  userNameErrorQueryData,
  firstNameErrorQueryData,
  lastNameErrorQueryData,
  phoneErrorQueryData,
  emailErrorQueryData,
  fakeQueryStatusDataNegOne,
  fakeQueryStatusDataNull,
  fakeQuery,
  fakeQueryEmpty,
  fakeQueryResult,
  fakeQueryString,
  fakeQueryNoMappedResults,
  testQueryPresentData,
  userNameEmptyQueryData,
  firstNameEmptyQueryData,
  lastNameEmptyQueryData,
  phoneEmptyQueryData,
  emailEmptyQueryData,
  queryProcedureQueryParams,
  queryProcedureQueryDataFull,
  queryProcedureQueryTable,
  queryProcedureQueryResults,
};
