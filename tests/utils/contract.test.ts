import {
  afterEach,
  beforeEach,
  describe,
  expect,
  it,
  vi,
} from 'vitest';
import { get } from 'lodash';

import MssqlData from '../../src/data-sources/mssql';
import { contractListUtil, contractAddUtil, ContractRequest } from '../../src/utils/contract';
import { CMSApp } from '../../src/types';
import { createApp, testLogger } from '../test-utils';
import {
  apiSystemIdList,
  apiKeywordList,
  apiFullList,
  apiPOPStartDateList,
  apiPOPEndDateList,
  apiContractNameList,
  dbKeywordList,
  dbSystemIdList,
  dbFullList,
  dbPOPStartDateList,
  dbPOPEndDateList,
  dbContractNameList,
} from './contractListTestData';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  queryStoredProcedures: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
    queryStoredProcedures: mocks.queryStoredProcedures,
  })),
  logger: {
    error: vi.fn(),
  },
}));

const app = await createApp();
const db = mocks.db();

const mockDb = {
  queryStoredProcedures: mocks.queryStoredProcedures,
} as unknown as MssqlData;

const mockApp = {
  logger: mocks.logger,
} as unknown as CMSApp;

describe('Contract List tests', () => {
  const SQLKeys = [
    '"Sparx Contract GUID" as id',
    '"Contract Number" as parentAwardId',
    '"Order Number" as awardId',
    '"Contract Amount" as Cost',
    '"Contract Name" as ContractName',
    '"POP Start Date" as POPStartDate',
    '"POP End Date" as POPEndDate',
    '"Order Number" as OrderNumber',
    '"Project Title" as ProjectTitle',
    '"Product Service Description" as ProductServiceDescription',
    '"Service Provided" as ServiceProvided',
    '"Contract Number" as ContractNumber',
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mocks.queryView.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Should also have POPStartDate in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId,
      POPStartDate: '2019-08-04',
    };
    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'POP Start Date',
            operator: '=',
            value: '2019-08-04',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [
        {
          id: '{*************-8888-9999-6306ABC0C141}',
          parentAwardId: '234567',
          awardId: null,
          Cost: '80962817.66',
          ContractName: 'Test Contract Name A',
          POPStartDate: '2019-08-04',
          POPEndDate: '2024-08-03',
          OrderNumber: null,
          ProjectTitle: 'Test Project Title A',
          ProductServiceDescription: null,
          ServiceProvided: null,
          ContractNumber: '234567',
        },
      ],
    });
  });

  it('Should also have POPEndDate in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPEndDate: '2024-08-03',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'POP End Date',
            operator: '=',
            value: '2024-08-03',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [{
        ContractName: 'Test Contract Name A',
        ContractNumber: '234567',
        Cost: '80962817.66',
        OrderNumber: null,
        POPEndDate: '2024-08-03',
        POPStartDate: '2019-08-04',
        ProductServiceDescription: null,
        ProjectTitle: 'Test Project Title A',
        ServiceProvided: null,
        awardId: null,
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
      }],
    });
  });

  it('Should also have contractName in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      contractName: 'Test Contract Name A',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'Contract Name',
            operator: '=',
            value: 'Test Contract Name A',
          },
        },
      ],
    };

    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [{
        ContractName: 'Test Contract Name A',
        ContractNumber: '234567',
        Cost: '80962817.66',
        OrderNumber: null,
        POPEndDate: '2024-08-03',
        POPStartDate: '2019-08-04',
        ProductServiceDescription: null,
        ProjectTitle: 'Test Project Title A',
        ServiceProvided: null,
        awardId: null,
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
      }],
    });
  });

  it('Should have systemId in the keys if the key is present, but the value is blank', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      systemId: '',
      contractName: 'Test Contract Name A',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '',
        },
      },
      and: [
        {
          operation: {
            column: 'Contract Name',
            operator: '=',
            value: 'Test Contract Name A',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, '', query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );

    expect(test).toEqual({
      count: 1,
      Contracts: [{
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
        awardId: null,
        Cost: '80962817.66',
        ContractName: 'Test Contract Name A',
        POPStartDate: '2019-08-04',
        POPEndDate: '2024-08-03',
        OrderNumber: null,
        ProjectTitle: 'Test Project Title A',
        ProductServiceDescription: null,
        ServiceProvided: null,
        ContractNumber: '234567',
      }],
    });
  });

  it('Should return a list of all contracts', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const test = await contractListUtil(app, db, '', {});
    expect(test).toEqual(apiFullList);
  });

  it('Should return a count and list of all contracts given a system id', async () => {
    mocks.queryView.mockResolvedValueOnce([dbSystemIdList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
    };
    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiSystemIdList);
  });

  it('Should return a list of all contracts given a keyword', async () => {
    mocks.queryView.mockResolvedValueOnce([dbKeywordList]);
    const query = {
      keyword: 'contract',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiKeywordList);
  });

  it('Should return a list of all contracts given a systemId with a popStartDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPStartDate: '2019-08-04',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiPOPStartDateList);
  });

  it('Should return a list of all contracts given a systemId with a popEndDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPEndDate: '2024-08-03',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiPOPEndDateList);
  });

  it('Should return a list of all contracts given a systemId with a contractName param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      contractName: 'Test Contract Name A',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiContractNameList);
  });

  it('Should return a list of all contracts given a blank systemId with an auxilliary param.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const query = {
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiFullList);
  });

  it('Should return a list of all contracts given a keyword with a popStartDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const query = {
      keyword: 'contract',
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiPOPStartDateList);
  });

  it('Should return a list of all contracts given a keyword with a popEndDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const query = {
      keyword: 'contract',
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiPOPEndDateList);
  });

  it('Should return a list of all contracts given a keyword with a contractName param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      keyword: 'contract',
      contractName: 'Contract',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiContractNameList);
  });

  it('Should return an error if app is not in the req', async () => {
    const test = await contractListUtil(null as unknown as CMSApp, db, '', {}) as Error;
    expect(test).toEqual(new Error('Unable to get the application from request'));
  });

  it('Should return an error if db is empty', async () => {
    const test = await contractListUtil(app, null as unknown as MssqlData, '', {});
    expect(test).toEqual(new Error('Database Unavailable'));

    expect(test).toEqual(new Error('Database Unavailable'));
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
  });

  it('Should return an error if db is an error', async () => {
    const test = await contractListUtil(app, new Error('Unable to retrieve DB') as unknown as MssqlData, '', {});
    expect(test).toEqual(new Error('Unable to retrieve DB'));

    expect(test).toEqual(new Error('Unable to retrieve DB'));
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return an error if queryView returns an error using keyword as a parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const query = {
      keyword: 'medical',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with keyword.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with keyword.');
  });

  it('Should return an error if the query view returns a critical error using keyword as a parameter.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const query = {
      keyword: 'medical',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with keyword.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with keyword.');
  });

  it('Should return an error if queryView returns an error using systemId as a parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the query view returns a critical error using systemId as a parameter.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the system id is invalid.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('The Sparx System GUID is not valid.'));

    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the system id is not a UUID.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('The Sparx System GUID is not valid.'));

    const query = {
      systemId: 'not a uuid',
    };

    const test = await contractListUtil(app, db, 'not a uuid', query);
    expect(test).toEqual(new Error('"value" must be a valid GUID'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
  });

  it('Should return an error if the keyword is invalid.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('The keyword is not valid.'));

    const query = {
      keyword: '',
    };

    const test = await contractListUtil(app, db, '', query as unknown as Record<string, string>);
    expect(test).toEqual(new Error('The keyword is not valid.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('The keyword is not valid.');
  });

  it('Should return an error if unable to retrieve the contract list with system id.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Unable to retrieve the contract list with system id.'));

    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });
});

describe('Contract Utility Tests', () => {
  const getValidContractRequest = (): ContractRequest[] => [{
    id: '{11111111-**************-************}',
    systemId: '{2222**************-5555-************}',
    ContractNumber: 'TEST-CONTRACT-001',
    IsDeliveryOrg: 'No',
    OrderNumber: 'ORD-TEST-001',
    ProductServiceDescription: 'Test Product/Service',
    ProjectTitle: 'Test Project',
    ServiceProvided: 'Test Service',
    parentAwardId: '{33333333-**************-************}',
    contractADO: 'Yes',
    awardId: 'AWARD-TEST-001',
    description: 'A test contract for unit testing.',
    POPStartDate: '2023-01-01',
    POPEndDate: '2024-12-31',
    contractName: 'Test Contract Name',
  }];

  describe('contractAddUtil', () => {
    beforeEach(() => {
      vi.clearAllMocks();
      mocks.queryStoredProcedures.mockReset();
    });

    afterEach(() => {
      vi.clearAllMocks();
    });

    it('Should successfully add a contract and return GUID', async () => {
      const mockReturnedGuid = '11111111-**************-************';
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: `{"NewObjects": {"GUID": "${mockReturnedGuid}"},"Count":1}`,
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toEqual({
        success: true,
        guid: mockReturnedGuid,
        count: 1,
      });

      expect(mocks.queryStoredProcedures).toHaveBeenCalledWith(
        'SP_Insert_SystemContract_json',
        expect.any(Array),
        expect.arrayContaining([
          expect.objectContaining({ name: 'jsonInput', value: expect.any(String) }),
          expect.objectContaining({ name: 'jsonOutput' }),
        ]),
        expect.any(Array),
      );

      // Verify the JSON payload structure
      const callArgs = mocks.queryStoredProcedures.mock.calls[0];
      const jsonInputPayload = JSON.parse(callArgs[2][0].value);
      expect(jsonInputPayload.CurrentProfile).toBe('API User');
      expect(jsonInputPayload.Objects).toHaveLength(1);
      expect(jsonInputPayload.Objects[0].ClassName).toBe('ContractDeliverable');
      expect(jsonInputPayload.Objects[0].Id).toBe('0');
      expect(jsonInputPayload.Objects[0].Values.contract).toBe(contracts[0].id);
      expect(jsonInputPayload.Objects[0].Values.architectureelement).toBe(contracts[0].systemId);
      expect(jsonInputPayload.Objects[0].Values.name).toBe(`${contracts[0].id}|${contracts[0].systemId}`);
      expect(jsonInputPayload.Objects[0].Values.cms_application_delivery_org).toBe('true'); // 'Yes' converted to 'true'

      expect(jsonInputPayload.Relations).toHaveLength(2);
      expect(jsonInputPayload.Relations[0]).toEqual({
        FromId: '0',
        Property: 'architectureelement',
        ToRef: contracts[0].systemId,
      });
      expect(jsonInputPayload.Relations[1]).toEqual({
        FromId: '0',
        Property: 'contract',
        ToRef: contracts[0].id,
      });
    });

    it('Should handle multiple contracts', async () => {
      const mockReturnedGuid = '11111111-**************-************';
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: `{"NewObjects": {"GUID": "${mockReturnedGuid}"},"Count":2}`,
      }]]);

      const contracts = [
        getValidContractRequest()[0],
        {
          id: '{aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee}',
          systemId: '{ffffffff-aaaa-bbbb-cccc-dddddddddddd}',
          ContractNumber: 'TEST-CONTRACT-002',
          ProjectTitle: 'Another Project',
        },
      ];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toEqual({
        success: true,
        guid: mockReturnedGuid,
        count: 2,
      });

      const callArgs = mocks.queryStoredProcedures.mock.calls[0];
      const jsonInputPayload = JSON.parse(callArgs[2][0].value);
      expect(jsonInputPayload.Objects).toHaveLength(2);
      expect(jsonInputPayload.Relations).toHaveLength(4);
      expect(jsonInputPayload.Objects[0].Id).toBe('0');
      expect(jsonInputPayload.Objects[1].Id).toBe('1');
    });

    it('Should return error when contract is missing id', async () => {
      const contracts = [{
        ...getValidContractRequest()[0],
        id: undefined,
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Missing or invalid \'id\' for contract at index 0.');
    });

    it('Should return error when contract is missing systemId', async () => {
      const contracts = [{
        ...getValidContractRequest()[0],
        systemId: undefined,
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Missing or invalid \'systemId\' for contract at index 0.');
    });

    it('Should return error when contract id is not a string', async () => {
      const contracts = [{
        ...getValidContractRequest()[0],
        id: 123 as unknown as string,
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Missing or invalid \'id\' for contract at index 0.');
    });

    it('Should return error when contract systemId is not a string', async () => {
      const contracts = [{
        ...getValidContractRequest()[0],
        systemId: 456 as unknown as string,
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Missing or invalid \'systemId\' for contract at index 0.');
    });

    it('Should return error when database stored procedure call fails', async () => {
      mocks.queryStoredProcedures.mockRejectedValueOnce(new Error('SP call failed'));

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Database insert failed: SP call failed');
    });

    it('Should return error when stored procedure returns non-zero status', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: -1,
        result: '{"NewObjects": [],"Count":0}',
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Stored procedure returned non-zero status');
    });

    it('Should return error when stored procedure result is empty', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: '',
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('The stored procedure did not yield a result');
    });

    it('Should return error when stored procedure result cannot be parsed', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: 'invalid json',
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Invalid Sparx results from stored procedure');
    });

    it('Should return error when parsed result is missing NewObjects', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: '{"Count":1}',
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toBeInstanceOf(Error);
      expect((result as Error).message).toBe('Invalid Sparx results from stored procedure');
    });

    it('Should return empty string when no GUID is returned', async () => {
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: '{"NewObjects": {},"Count":1}',
      }]]);

      const contracts = getValidContractRequest();
      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toEqual({
        success: true,
        guid: '',
        count: 1,
      });
    });

    it('Should handle contractADO conversion correctly', async () => {
      const mockReturnedGuid = '11111111-**************-************';
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: `{"NewObjects": {"GUID": "${mockReturnedGuid}"},"Count":1}`,
      }]]);

      const contracts = [{
        id: '{11111111-**************-************}',
        systemId: '{2222**************-5555-************}',
        contractADO: 'No',
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toEqual({
        success: true,
        guid: mockReturnedGuid,
        count: 1,
      });

      const callArgs = mocks.queryStoredProcedures.mock.calls[0];
      const jsonInputPayload = JSON.parse(callArgs[2][0].value);
      expect(jsonInputPayload.Objects[0].Values.cms_application_delivery_org).toBe('false');
    });

    it('Should handle undefined contractADO', async () => {
      const mockReturnedGuid = '11111111-**************-************';
      mocks.queryStoredProcedures.mockResolvedValueOnce([[{
        queryStatus: 0,
        result: `{"NewObjects": {"GUID": "${mockReturnedGuid}"},"Count":1}`,
      }]]);

      const contracts = [{
        id: '{11111111-**************-************}',
        systemId: '{2222**************-5555-************}',
        contractADO: undefined,
      }];

      const result = await contractAddUtil(mockApp, mockDb, contracts);

      expect(result).toEqual({
        success: true,
        guid: mockReturnedGuid,
        count: 1,
      });

      const callArgs = mocks.queryStoredProcedures.mock.calls[0];
      const jsonInputPayload = JSON.parse(callArgs[2][0].value);
      expect(jsonInputPayload.Objects[0].Values.cms_application_delivery_org).toBeUndefined();
    });
  });
});
