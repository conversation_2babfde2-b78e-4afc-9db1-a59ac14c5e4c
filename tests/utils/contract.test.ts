import {
  describe,
  it,
  vi,
  expect,
} from 'vitest';
import { get } from 'lodash';
import {
  apiSystemIdList,
  apiKeywordList,
  apiFullList,
  apiPOPStartDateList,
  apiPOPEndDateList,
  apiContractNameList,
  db<PERSON>eywordList,
  dbSystemIdList,
  dbFullList,
  dbPOPStartDateList,
  dbPOPEndDateList,
  dbContractNameList,
} from './contractListTestData';
import contractListUtil from '../../src/utils/contract';
import { createApp, testLogger } from '../test-utils';
import { CMSApp } from '../../src/types';
import MssqlData from '../../src/data-sources/mssql';

const mocks = vi.hoisted(() => ({
  queryView: vi.fn(),
  db: vi.fn().mockImplementation(() => ({
    queryView: mocks.queryView,
  })),
}));

const app = await createApp();
const db = mocks.db();

describe('Contract List tests', () => {
  const SQLKeys = [
    '"Sparx Contract GUID" as id',
    '"Contract Number" as parentAwardId',
    '"Order Number" as awardId',
    '"Contract Amount" as Cost',
    '"Contract Name" as ContractName',
    '"POP Start Date" as POPStartDate',
    '"POP End Date" as POPEndDate',
    '"Order Number" as OrderNumber',
    '"Project Title" as ProjectTitle',
    '"Product Service Description" as ProductServiceDescription',
    '"Service Provided" as ServiceProvided',
    '"Contract Number" as ContractNumber',
  ];

  it('Should also have POPStartDate in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId,
      POPStartDate: '2019-08-04',
    };
    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'POP Start Date',
            operator: '=',
            value: '2019-08-04',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [
        {
          id: '{*************-8888-9999-6306ABC0C141}',
          parentAwardId: '234567',
          awardId: null,
          Cost: '80962817.66',
          ContractName: 'Test Contract Name A',
          POPStartDate: '2019-08-04',
          POPEndDate: '2024-08-03',
          OrderNumber: null,
          ProjectTitle: 'Test Project Title A',
          ProductServiceDescription: null,
          ServiceProvided: null,
          ContractNumber: '234567',
        },
      ],
    });
  });

  it('Should also have POPEndDate in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPEndDate: '2024-08-03',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'POP End Date',
            operator: '=',
            value: '2024-08-03',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [{
        ContractName: 'Test Contract Name A',
        ContractNumber: '234567',
        Cost: '80962817.66',
        OrderNumber: null,
        POPEndDate: '2024-08-03',
        POPStartDate: '2019-08-04',
        ProductServiceDescription: null,
        ProjectTitle: 'Test Project Title A',
        ServiceProvided: null,
        awardId: null,
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
      }],
    });
  });

  it('Should also have contractName in the paramAnd array if it exists', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const systemId = '{*************-8888-9999-6306ABC0C141}';
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      contractName: 'Test Contract Name A',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '{*************-8888-9999-6306ABC0C141}',
        },
      },
      and: [
        {
          operation: {
            column: 'Contract Name',
            operator: '=',
            value: 'Test Contract Name A',
          },
        },
      ],
    };

    const test = await contractListUtil(app, db, systemId, query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );
    expect(test).toEqual({
      count: 1,
      Contracts: [{
        ContractName: 'Test Contract Name A',
        ContractNumber: '234567',
        Cost: '80962817.66',
        OrderNumber: null,
        POPEndDate: '2024-08-03',
        POPStartDate: '2019-08-04',
        ProductServiceDescription: null,
        ProjectTitle: 'Test Project Title A',
        ServiceProvided: null,
        awardId: null,
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
      }],
    });
  });

  it('Should have systemId in the keys if the key is present, but the value is blank', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      systemId: '',
      contractName: 'Test Contract Name A',
    };

    const WhereObj = {
      where: {
        operation: {
          column: 'Sparx System GUID',
          operator: '=',
          value: '',
        },
      },
      and: [
        {
          operation: {
            column: 'Contract Name',
            operator: '=',
            value: 'Test Contract Name A',
          },
        },
      ],
    };
    const test = await contractListUtil(app, db, '', query);
    expect(mocks.queryView).toBeCalledWith(
      'CEDAR_API.Sparx_System_Contract_Full_Tbl',
      SQLKeys,
      WhereObj,
    );

    expect(test).toEqual({
      count: 1,
      Contracts: [{
        id: '{*************-8888-9999-6306ABC0C141}',
        parentAwardId: '234567',
        awardId: null,
        Cost: '80962817.66',
        ContractName: 'Test Contract Name A',
        POPStartDate: '2019-08-04',
        POPEndDate: '2024-08-03',
        OrderNumber: null,
        ProjectTitle: 'Test Project Title A',
        ProductServiceDescription: null,
        ServiceProvided: null,
        ContractNumber: '234567',
      }],
    });
  });

  it('Should return a list of all contracts', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const test = await contractListUtil(app, db, '', {});
    expect(test).toEqual(apiFullList);
  });

  it('Should return a count and list of all contracts given a system id', async () => {
    mocks.queryView.mockResolvedValueOnce([dbSystemIdList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
    };
    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiSystemIdList);
  });

  it('Should return a list of all contracts given a keyword', async () => {
    mocks.queryView.mockResolvedValueOnce([dbKeywordList]);
    const query = {
      keyword: 'contract',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiKeywordList);
  });

  it('Should return a list of all contracts given a systemId with a popStartDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPStartDate: '2019-08-04',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiPOPStartDateList);
  });

  it('Should return a list of all contracts given a systemId with a popEndDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      POPEndDate: '2024-08-03',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiPOPEndDateList);
  });

  it('Should return a list of all contracts given a systemId with a contractName param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
      contractName: 'Test Contract Name A',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(apiContractNameList);
  });

  it('Should return a list of all contracts given a blank systemId with an auxilliary param.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbFullList]);
    const query = {
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiFullList);
  });

  it('Should return a list of all contracts given a keyword with a popStartDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPStartDateList]);
    const query = {
      keyword: 'contract',
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiPOPStartDateList);
  });

  it('Should return a list of all contracts given a keyword with a popEndDate param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbPOPEndDateList]);
    const query = {
      keyword: 'contract',
      POPStartDate: '2021-03-07',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiPOPEndDateList);
  });

  it('Should return a list of all contracts given a keyword with a contractName param exists.', async () => {
    mocks.queryView.mockResolvedValueOnce([dbContractNameList]);
    const query = {
      keyword: 'contract',
      contractName: 'Contract',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(apiContractNameList);
  });

  it('Should return an error if app is not in the req', async () => {
    const test = await contractListUtil(null as unknown as CMSApp, db, '', {}) as Error;
    expect(test).toEqual(new Error('Unable to get the application from request'));
  });

  it('Should return an error if db is empty', async () => {
    const test = await contractListUtil(app, null as unknown as MssqlData, '', {});
    expect(test).toEqual(new Error('Database Unavailable'));

    expect(test).toEqual(new Error('Database Unavailable'));
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Database Unavailable');
  });

  it('Should return an error if db is an error', async () => {
    const test = await contractListUtil(app, new Error('Unable to retrieve DB') as unknown as MssqlData, '', {});
    expect(test).toEqual(new Error('Unable to retrieve DB'));

    expect(test).toEqual(new Error('Unable to retrieve DB'));
    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve DB');
  });

  it('Should return an error if queryView returns an error using keyword as a parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const query = {
      keyword: 'medical',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with keyword.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with keyword.');
  });

  it('Should return an error if the query view returns a critical error using keyword as a parameter.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));
    const query = {
      keyword: 'medical',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with keyword.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with keyword.');
  });

  it('Should return an error if queryView returns an error using systemId as a parameter.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('Query View Error'));
    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the query view returns a critical error using systemId as a parameter.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Critical Query View Error'));

    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the system id is invalid.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('The Sparx System GUID is not valid.'));

    const query = {
      systemId: '{BB66**************-6666-A6666C8B2481}',
    };

    const test = await contractListUtil(app, db, '', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });

  it('Should return an error if the system id is not a UUID.', async () => {
    mocks.queryView.mockResolvedValueOnce(new Error('The Sparx System GUID is not valid.'));

    const query = {
      systemId: 'not a uuid',
    };

    const test = await contractListUtil(app, db, 'not a uuid', query);
    expect(test).toEqual(new Error('"value" must be a valid GUID'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('"value" must be a valid GUID');
  });

  it('Should return an error if the keyword is invalid.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('The keyword is not valid.'));

    const query = {
      keyword: '',
    };

    const test = await contractListUtil(app, db, '', query as unknown as Record<string, string>);
    expect(test).toEqual(new Error('The keyword is not valid.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('The keyword is not valid.');
  });

  it('Should return an error if unable to retrieve the contract list with system id.', async () => {
    mocks.queryView.mockRejectedValueOnce(new Error('Unable to retrieve the contract list with system id.'));

    const query = {
      systemId: '{*************-8888-9999-6306ABC0C141}',
    };

    const test = await contractListUtil(app, db, '{*************-8888-9999-6306ABC0C141}', query);
    expect(test).toEqual(new Error('Unable to retrieve the contract list with system id.'));

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toHaveProperty('error');
    expect(get(errorLog, '[0]', []).error).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).error.message).toEqual('Unable to retrieve the contract list with system id.');
  });
});
