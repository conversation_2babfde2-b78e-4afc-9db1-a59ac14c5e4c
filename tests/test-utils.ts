// eslint-disable-next-line import/no-extraneous-dependencies
import { vi } from 'vitest';
import express, {
  NextFunction,
  Request,
  RequestHandler,
} from 'express';
import { set } from 'lodash';
import Logger, { createLogger } from 'bunyan';
import initApp from '../src/core';
import {
  CMSApp,
  CreateBadAppOptions,
  DSSetup,
  GetResourceConfigSub,
  MockableLogger,
  SessionSetup,
} from '../src/types';
import { addAppToReq } from '../src/core/app-factory/middleware';
import DataSource from '../src/data-sources/data-source';
import errorHandler from '../src/outerceptors/error-handler';

const testUser = {
  dn: 'uid=TST1,ou=people,dc=example,dc=com',
  cn: 'Test User',
  mail: '<EMAIL>',
  givenName: 'Test',
  sn: 'User',
  telephoneNumber: '(*************',
  uid: 'TST1',
  ismemberof: [
    'cn=Test,ou=Groups,dc=example,dc=com',
  ],
};

const mocks = vi.hoisted(() => ({
  initSecrets: vi.fn().mockResolvedValue(true),
  registerAppDataSources: vi.fn().mockImplementation((app) => {
    set(app, 'dataSources', {
      register: vi.fn(),
    });
  }),
  authentication: vi.fn().mockImplementation((req: Request) => {
    req.authMethod = 'jwt';
    req.user = testUser;
    return true;
  }),
}));

vi.mock(import('../src/subsystems/secrets'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    initSecrets: mocks.initSecrets,
  };
});

vi.mock(import('../src/subsystems/authentication'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    default: mocks.authentication,
  };
});

vi.mock(import('../src/core/app-factory/data-sources'), async (importOriginal) => {
  const mod = await importOriginal();
  return {
    ...mod,
    registerAppDataSources: mocks.registerAppDataSources,
  };
});

const stub = (logger: Logger): MockableLogger => {
  const mockFunctions = {
    trace: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    fatal: vi.fn(),
    child: vi.fn().mockReturnValue(logger),
  };

  const mockLogger = logger as MockableLogger;

  // Assign all mock functions to the logger
  Object.assign(mockLogger, mockFunctions);

  // Add a helper method for resetting just this logger
  mockLogger.resetMocks = () => {
    Object.values(mockFunctions).forEach((mockFn) => {
      mockFn.mockClear();
    });
  };

  return mockLogger;
};

const testLogger = stub(createLogger({ name: 'unit-test-logger' }));

class TestDataSource extends DataSource {
  logger = testLogger;

  isDbReady(): boolean {
    this.logger.debug('Testing');
    return true;
  }

  initDb = async (): Promise<void | Error> => {
    this.logger.debug('Testing');
  };

  deleteOne(): Promise<'failed' | 'success'> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('success');
    });
  }

  findOne(): Promise<unknown> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('success');
    });
  }

  findAll(): Promise<unknown[]> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve(['success']);
    });
  }

  findAllByParameter(): Promise<unknown[]> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve(['success']);
    });
  }

  insertMany(): Promise<number[] | 'failed'> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  insertOne(): Promise<number | 'failed'> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('failed');
    });
  }

  updateOne(): Promise<'success' | 'failed'> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('success');
    });
  }

  updateMany(): Promise<'success' | 'failed'> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('success');
    });
  }

  upsert(): Promise<'success' | 'failed' | number> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve('success');
    });
  }

  closeConnection(): Promise<void | boolean> {
    this.logger.debug('Testing');
    return new Promise((resolve) => {
      resolve(true);
    });
  }
}

const createApp = async (
  dsType: string | typeof DataSource = 'FakePG',
  dsName: string = 'core',
  createSwagger: boolean = false,
) => {
  const baseApp = express() as CMSApp;
  const sessionSetup: SessionSetup = {
    enableSession: vi.fn(),
    dsName: 'core',
  };
  const dsSetup: DSSetup[] = [{
    dsType,
    dsName,
  }];
  const app = await initApp(baseApp, dsSetup, sessionSetup, createSwagger);
  app.logger = testLogger;
  return app;
};

const createBadApp = (
  path: string,
  method: string,
  module: RequestHandler,
  options: CreateBadAppOptions = {},
) => {
  const badApp = express() as CMSApp;
  badApp.use(express.json());
  badApp.use(express.urlencoded({ extended: false }));

  const fakeAuth: RequestHandler = ((req: Request, _: Response, next: NextFunction) => {
    req.authMethod = 'jwt';
    req.user = testUser;
    return next();
  }) as unknown as RequestHandler;

  const {
    includeApp = false,
    includeUser = false,
    includeLogger = false,
    middleware,
  } = options;

  const allMiddleware: RequestHandler[] = [];

  if (middleware && Array.isArray(middleware)) {
    middleware.forEach((item) => allMiddleware.push(item));
  } else if (middleware) {
    allMiddleware.push(middleware);
  }

  if (includeLogger) {
    badApp.logger = testLogger;
  }

  if (includeApp) {
    addAppToReq(badApp);
  }

  if (includeUser) {
    allMiddleware.push(fakeAuth);
  }

  // Register the route first.
  badApp[method](path, allMiddleware, module);

  // Register error handler after the route.
  badApp.use(errorHandler);

  return badApp;
};

const createUnitTestApp = async (
  config: GetResourceConfigSub,
): Promise<CMSApp> => {
  const app = await createApp();

  await app.resources.register(app, '/', (
    () => () => config().resourceConfig)())
    .catch((e) => {
      throw e;
    });
  app.resources.initResources(app);
  app.use(errorHandler);

  return app;
};

// noinspection JSUnusedGlobalSymbols
/**
 * Helper function for debugging mock issues with toHaveBeenCalledWith.
 */
const getMockCallDetails = (mockFn: ReturnType<typeof vi.fn>, mockName: string) => {
  const { calls, results } = mockFn.mock;
  return {
    name: mockName,
    callCount: calls.length,
    calls,
    results,
    lastCall: calls[calls.length - 1],
    lastResult: results[results.length - 1],
  };
};

export {
  TestDataSource,
  createApp,
  createBadApp,
  createUnitTestApp,
  getMockCallDetails,
  mocks,
  testLogger,
  testUser,
};
