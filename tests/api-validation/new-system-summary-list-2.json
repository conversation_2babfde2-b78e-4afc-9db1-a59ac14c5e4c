{"SystemSummary": [{"id": "{0799FFBB-DB4F-425d-A9F2-4D6217BB4E8C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0799FFBB-DB4F-425d-A9F2-4D6217BB4E8C}", "uuid": "2259786C-9F00-4D44-B0DB-84FB3FD969CC", "name": "Medicare Exclusion Database", "description": "MED is a central repository of sanctioned providers. The MED system receives updates each month new sanctions and reinstatements. MED creates a set of extract files each month which are distributed to over 200 MED end users to assist in FWA activities. As of June 2011, the MED is now an online system available to all users such as CMS personnel, CMS contractors, OIG, and other branches of law enforcement.  The files are downloaded from the CMS GENTRAN mailbox (or existing Connect: Direct connection).  All MED users must be approved through EIDM by the MED approver, who is the business owner, before they can access and download these files.  There are currently five files which are: 1) the current month sanctions; 2) current month re instatements;3) cumulative sanctions; 4) cumulative re instatements; and 5) Non  MED.  The data MED files are used to deny claims from any provider who is currently excluded from participating in the Medicare and Medicaid Programs. Core Function: * Maintain and Provide list of providers excluded from the Medicare based on the input received from OIG.* Provide the ability to perform any necessary data correction", "version": "25", "acronym": "MED", "status": "", "belongsTo": "", "businessOwnerOrg": "Provider Enrollment and Oversight Group", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-02-25", "atoExpirationDate": "2024-02-25"}, {"id": "{093BB525-E80D-4ee2-96A9-BDA69D2C8BD8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{093BB525-E80D-4ee2-96A9-BDA69D2C8BD8}", "uuid": "0720323F-6039-4C3D-8371-8AF9262B1BEB", "name": "Quality Management and Review System", "description": "This system allows the QIOs to carry out their statutorily mandated Quality of Care case review activities as laid out in the QIO Program.  The QIOs and CMS will be able to track, monitor and analyze data to identify opportunities for improving quality of care for Medicare beneficiaries, and to find increased efficiencies in the case review process.  The system provides QIOs an automated workflow to track and report on Quality of Care Complaints.  Core Function: * Allow the tracking and monitoring of Helpline/Intake activities* Allow review of Beneficiary Complaint Quality of Care* Allow review of general quality of care* Provide expanded data analytics, alert notifications and other standardized reporting features that shall allow QIO and CMS users to more easily monitor case review performance measures at both the state and national levels* Provide new functionalities that promote automated correspondence exchange and online management of case review documentation.", "version": "25", "acronym": "QMARS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Beneficiary Reviews and Care Management", "systemMaintainerOrg": "Information Systems Group", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-09-23", "atoExpirationDate": "2025-09-23"}, {"id": "{09F10C4E-18D5-4472-851B-44403CB62F06}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{09F10C4E-18D5-4472-851B-44403CB62F06}", "uuid": "667588DF-AD8A-45BD-A5D2-1775CE46C514", "name": "Medicaid and CHIP Program System", "description": "CFR 430.12 sets forth the authority for the submittal and collection of State Plans and plan amendment/waiver Information in a format defined by CMS. A State plan for Medicaid consists of preprinted material that covers the basic requirements, and individualized content that reflects the characteristics of the particular State's program. Pursuant to this requirement, CMS is developing the MACPRO Suite of Products.  The MACPRO Suite of Product will focus on the collection of non PHI/PII related data; rather it will collect characteristics of people being served. The purpose of the MACPro system is to support an efficient business process for submitting, reviewing and taking final action on all Medicaid and CHIP actions, including: State Plan Amendments, Waivers, Demonstrations and Advanced Planning Documents (APDs).  Within the MACPRO Suite of Products, CMS and States will no longer rely on documents to support these business processes.  The suite will have a modern, web based interface for all users of the system. Core Functions: Medicaid and CHIP state plan amendments, waivers, and quality measures.", "version": "25", "acronym": "MACPro", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Information Systems", "systemMaintainerOrg": "Division of Information Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2023-02-09", "atoExpirationDate": "2026-02-08"}, {"id": "{0CEB43CB-0E9D-443b-A570-FA745F2D28A9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0CEB43CB-0E9D-443b-A570-FA745F2D28A9}", "uuid": "f558ff35dbbafb00f99cfd721f9619b5", "name": "Centralized Data Exchange", "description": "The CDX solution would allow CMMI to have a better Center-wide capability to exchange files more effectively and efficiently with external stakeholders. In addition, exchanging data using Health IT standards with open and FHIR APIs with external stakeholders will allow communication with model participants starting in 2020. CDX will also enable ad-hoc and “manual” file exchanges to occur securely and easily without requiring a software development effort each time.", "version": "25", "acronym": "CDX", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Technology Solutions", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2023-12-14", "atoExpirationDate": "2026-12-13"}, {"id": "{10AB629D-EECF-44ea-AF51-449B5E7DF1D5}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{10AB629D-EECF-44ea-AF51-449B5E7DF1D5}", "uuid": "8DEEC461-6333-4A22-B88A-FF0BCB41B019", "name": "APM Management System", "description": "The CMMI Analytics & Management System (AMS), a.k.a Alternative Payment Model (APM) Management system, supports CMMI for the Quality Payment Program (QPP); Portfolio Management and Cross Model Management; and Reporting, Analytics, and Audits.  AMS serves as the central repository for CMMI model data including model design elements, model participation, quality measures, and a model's participation in the QPP.  Within IDR, AMS also has a Business Data Mart storing model participation data enabling users to perform analysis on model participants and linking to other data sources.\r\nAMS supports QPP by storing relevant data and making it available to the Merit-Based Incentive Payment System (MIPS).  The system also provides targeted data search capabilities for all data stored within AMS as well as user friendly dashboards to enable users to perform broader data searches.  Lastly, AMS provides users with a BI platform (Looker) to perform more targeted and complex analysis.", "version": "25", "acronym": "AMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Policy and Programs Group", "systemMaintainerOrg": "Division of Systems Support, Operation, and Security", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2022-11-02", "atoExpirationDate": "2025-11-02"}, {"id": "{114AC6AA-4151-4ad1-BAED-3762371A4EB2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{114AC6AA-4151-4ad1-BAED-3762371A4EB2}", "uuid": "B6A0F036-45B5-47FC-B43E-0CB653B0EA40", "name": "Common Working File", "description": "Common Working File (CWF) is a Medicare part A and B benefit coordination and claim validation system. Common Working File (CWF) is a tool used by the Centers for Medicare & Medicaid Services (CMS) to maintain records about beneficiaries enrolled in the Medicare fee for service health plan. The CWF is comprised of nine localized databases called Hosts. Hosts maintain total Medicare claim history and entitlement information for the beneficiaries in their jurisdiction as updated daily by Medicare contractors and other applicable entities (i.e., Social Security Administration). CWF is used to determine the Medicare beneficiaries' eligibility for Medicare services and to monitor and inform other CMS payment systems on the appropriate usage of Medicare benefits and to ensure payments for services received are appropriately determined and applied to payments based on Medicare payment and coverage rules. Also the repository provides Medicare beneficiary eligibility information that is received nightly from the Social Security Administration (SSA).  The eligibility information consists of newly enrolled beneficiaries into Medicare.   Core Function: * Verify beneficiary eligibility, entitlement and utilization. Avoid improper payment through comparison of Part A and B data. Allow BDS and FPS checking prior to CWF processing.  * CWF software currently performs data collection and validation, on line inquiry, file maintenance, reports, and archival/retrieval.  * The CWF provides the MAC with responses to claims.  MACs also submit special transactions such as Medicare Secondary Payment (MSP) data (primary insurer data when Medicare is secondary), ESRD (End Stage Renal Disease) Method of Reimbursement Computation data (only Part A MACs submit these transactions) and Certificate of Medical Necessity ((CMS) only DME MACs submit these transactions). These transactions are submitted to the CWF along with the claims and are identified by transaction type.  Claims are added to the CWF full claim history file.", "version": "25", "acronym": "CWF", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Division of Shared Systems Management", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-08-29", "atoExpirationDate": "2025-08-29"}, {"id": "{14E86ED4-B2AC-4c3a-B7DB-A9C8B0F91862}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{14E86ED4-B2AC-4c3a-B7DB-A9C8B0F91862}", "uuid": "2DD3E858-9229-4358-879C-ED41CB52E3BE", "name": "Exchange Operations Center", "description": "The XOC supports the teams running the Healthcare.gov website.  The XOC staff monitor the operations, applications, network and security of the website and perform system auditing and reporting and manage the team's change management program.  The XOC is the operations center for the Marketplace IT Group (MITG).  Core Function: * System Monitoring information for Marketplace.  Monitors secure web-based transactions of XML using JSON scripts.  The XOC supports States, Issuers and System Developers/Integrators", "version": "25", "acronym": "XOC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Marketplace IT Operations", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2024-01-05", "atoExpirationDate": "2027-01-04"}, {"id": "{17FEB255-6358-4f20-834A-1C204DE4E6A4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{17FEB255-6358-4f20-834A-1C204DE4E6A4}", "uuid": "4B87A167-FCD6-4961-8193-EBFFDA053176", "name": "Provider Customer Service Program System", "description": "The PCSP System is comprised of four web applications supporting the oversight of the provider customer service program at Medicare Fee-For-Service (FFS) contractors.  \n\nQuality Written Correspondence Monitoring (QWCM) -  Medicare FFS contractors assess and report quality for written responses to provider written inquiries (https://www.qwcmscores.com)\nQuality Call Monitoring (QCM) -  Medicare FFS contractors assess and report quality for telephone responses to provider telephone inquiries (https://www.qcmscores.com)\nPCSP Contractor Information Database (PCID) - Medicare FFS contractors report number of providers served, contract-level information, and monthly inquiry tracking data (types of inquiries) according to a set of categories and subcategories provided by CMS.  (https://www.p-cid.com)\nProvider Inquiries Evaluation System (PIES) - Medicare FFS contractors report monthly contact center information and  timeliness statistics (https://www.pie-system.com)\nMedicare FFS contractors use QCM and QWCM to score their CSR responses to provider inquiries according to CMS measures and scoring criteria. Each system creates a scorecard used to assess the response to  provider inquiry. Contractors sample their staff performance, selecting a small number of interactions monthly for each eligible CSR. Contractors can run reports on a CSR,  reviewer, supervisor, or contact center providing multiple performance views.\n\nContractors enter contract information such as contract point of contact, mailing address, and contact centers into PCID and update as necessary, allowing CMS staff to easily get information in a central web-based platform. CMS staff review PCID data to support business processes such as reviewing contract metrics.\n\nCMS Division of Contractor Provider Communications (DCPC) staff and some Regional Office staff use reports from these systems to review contractor performance data. ", "version": "25", "acronym": "PCSP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Provider Communications Technology", "systemMaintainerOrg": "Division of Contractor Provider Communications", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2021-08-26", "atoExpirationDate": "2024-08-26"}, {"id": "{1CCD89FE-AAC8-4e05-9AFA-4058E4D21EF0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{1CCD89FE-AAC8-4e05-9AFA-4058E4D21EF0}", "uuid": "f4f151a487bcad1078b562860cbb35f1", "name": "Enterprise Privacy Policy Engine Cloud", "description": "Enforces the controlled access to, disclosures of, transference and disposition of personally identifiable information (PII) contained in CMS Systems of Records (SOR) in accordance with the statutory mandates of the Privacy Act, HIPAA and FISMA.  Additionally, EPPE will standardize and automate the Data Use Agreements (DUA) process. \r\nCore Function: \r\n* Receives and processes requests for data files from users that maintain Data Use Agreements (DUAs) with CMS regarding the nature and use of CMS\r\n* Maintains and administers the DUAs and tracks the requested data files as orders within its system from initial build status on out to ship status.\r\n* Supports the shipping of multiple data files in an order delivered outside of CMS on physical media including tape, cartridge, CDROM, DVD, etc.\r\n* Tracks the disclosures of personally identifiable data to external entities thru the manage DUAs functionality.\r\n* Allows for querying of data elements, reporting, creation of role based access.\r\n* Allows access to data files retrieved via the Data Extract System (DESY). (Planned to be decommissioned in July/August 2022)\r\n* Stores supporting documentation for Data Use Agreements (DUA).\r\n* EPPE also includes a mechanism to track data disclosures that do not require a DUA, via Non DUA Tracking functionality.", "version": "25", "acronym": "EPPEC", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Information Dissemination Group", "systemMaintainerOrg": "Office of Enterprise Data and Analytics", "state": "Active", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{1E94BFAD-2756-4fed-B58E-C61B662180E2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{1E94BFAD-2756-4fed-B58E-C61B662180E2}", "uuid": "D6158E58-ED84-4143-A130-7DF21BBAA235", "name": "National Claims History", "description": "Description: The NCH is a data repository that stores all common working file (CWF) processed Part A and Part B detailed claims transaction records, beginning with service year 1991. Core Function: The NCH is a repository of Medicare Part A and Part B adjudicated claims since 1991.  The Medicare claims are received from CWF, via Conversion Medicare (CVM).  NCH is used by many CMS internal and external components to study the operation and effectiveness of the Medicare program.  * The NCH contains both institutional claims (IP/SNF, OP, HHA, Hospice) processed by Fiscal Intermediaries (FIs) /Medicare Administrative Contractors (MAC) and non institutional claims processed by local carriers (physician/supplier) and DMERCs/MACs.  * The NCH process also produces Standard Analytical Files (SAFs) and TAP Files. * NCH was also the feed to the National Claims Medicare Utilization Database (NMUD).  NMUD was retired in 2019.", "version": "25", "acronym": "NCH", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Enterprise Architecture and Data Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-06-15", "atoExpirationDate": "2025-06-15"}, {"id": "{20ADE942-E34A-4f2a-95BE-A4AC73B80EB0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{20ADE942-E34A-4f2a-95BE-A4AC73B80EB0}", "uuid": "d50fdc87db3a04909b3772840f9619e2", "name": "Website Database Discovery Stakeholder Engagement", "description": "To further the goals of modernizing and enhancing CMS' data sharing approach to drive transparency and innovation in the health sector, the Office of Enterprise Data and Analytics (OEDA) will work with a team to assess current ways in which publicly available data is shared both internally within CMS and externally to partners (e.g. providers, Accountable Care Organizations), innovators (e.g. researchers, developers), and the general public. The focus of this task is to accomplish the following primary goals (1) identify target personas that utilize data and information products released by OEDA, (2) conduct user research to better understand their specific data needs and the context for its usage, (3) implement processes, governance and tools that will ensure our target personas are able to discover and access our data easily, and (4) define channels to effectively communicate availability of data products, and provide continued outreach and support to the end user community of data.cms.gov", "version": "25", "acronym": "WDDSE", "status": "", "belongsTo": "", "businessOwnerOrg": "Office of Enterprise Data and Analytics", "systemMaintainerOrg": "Office of Enterprise Data and Analytics", "state": "Active", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2023-07-13", "atoExpirationDate": "2026-07-12"}, {"id": "{222215E3-3C7A-4b1c-BE21-6A23466C963D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{222215E3-3C7A-4b1c-BE21-6A23466C963D}", "uuid": "0E49CDB9-C332-4BC3-B4FA-4BB90B9F1516", "name": "Survey and Certification Quality, Certification and Oversight Reports", "description": "QCOR provides summarized survey and certification data to the public. It provides point and click reports on the results of onsite inspections of institutional providers. Core Function: * Web-enabled reporting application that provides summarized reports of survey and certification program data* Enables monitoring of the program through simple queries that return responses", "version": "25", "acronym": "SC QCOR", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Nursing Homes", "systemMaintainerOrg": "Division of Nursing Homes", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-03-20", "atoExpirationDate": "2026-03-19"}, {"id": "{2337D9FF-A899-4b4e-A9F4-08A01DE89335}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2337D9FF-A899-4b4e-A9F4-08A01DE89335}", "uuid": "0b5ad47c1b109110de4010ad9c4bcbd2", "name": "Model INnovation Tool", "description": "The Model INovation Tool (MINT) facilitates the intake and tracking of CMMI Innovation Models (https://innovation.cms.gov/innovation-models#views=models). MINT will support information sharing between model teams and CMS stakeholders including cross-model groups at CMMI (Business Services Group, Learning & Diffusion Group, Policies and Programs Group, and Research and Rapid-Cycle Evaluation Group) and across the Agency (including the Office of the Actuary, Office of Enterprise Data and Analytics, Center for Program Integrity, Office of Information Technology, among others). MINT also supports information sharing with contractors supporting the Fee for Service Change Requests (Medicare Administrative Contractors and Shared System Maintainers). This information sharing will help stakeholders to understand what new model concepts are being planned, to contribute insights and comments to the model teams, and to track their progress through the planning and operationalization phases. This information sharing will also help model teams to simplify information sharing with stakeholders and to learn about how and when to engage with stakeholders in the various steps of the process. The information shared will include characteristics of the new model concept such as the goal of the model, which providers and beneficiaries are impacted, which payments and quality measures are being contemplated, and what operational supports will be needed.", "version": "25", "acronym": "MINT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Technology Solutions", "systemMaintainerOrg": "IT Capital Planning Group", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-07-06", "atoExpirationDate": "2026-07-05"}, {"id": "{29E04949-D9C5-4400-A0E2-F59101F7A4D3}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{29E04949-D9C5-4400-A0E2-F59101F7A4D3}", "uuid": "2ba5b05b1bbd50902e8743bae54bcb9a", "name": "Commercial Repayment Center Intake", "description": "2023_0111: <PERSON><PERSON> advised that the project is being revived as of Jan 2023. DJD\r\n8/20/2020:  this new system, the Commercial Repayment Center Intake (CRC), has had development cancelled before going into production as per the ISSO, <PERSON><PERSON>.\r\nThe CRC Intake systems will allow the CRC to process correspondence related to Medicare Secondary Payer (MSP) recoveries initiated by and allowed under the CRC contract. Incoming mail will be scanned into an electronic format and triaged to identify necessary metadata. Scanning also includes the processing of checks, which are deposited with US Bank. Upon completion of the scanning and triaging process, the data is electronically transmitted to the Benefits Coordination and Recovery System (BCRS). Check collection information is also transmitted to the HIGAS application to be applied to appropriate Account Receivable (AR). The CRC is then able to access this electronic correspondence to review, process and respond to as appropriate.", "version": "25", "acronym": "CRC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of MSP Program Operations", "systemMaintainerOrg": "Division of MSP Program Operations", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{25FD6675-ADB4-46e6-9825-29C2B0D346DE}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{25FD6675-ADB4-46e6-9825-29C2B0D346DE}", "uuid": "ABACCA15-F643-4DDE-A8B1-CE7F6FF8334C", "name": "Centers for Medicare and Medicaid Innovation-Innovation Payment Contractor", "description": "CMMI-IPC provides financial services for various CMMI model payment programs. Financial services include: issuing payments, collecting debts, providing customer service, and issuing 1099's. Access to the CMMI-IPC system is via the IPC Portal.", "version": "25", "acronym": "CMMI-IPC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Central Contracts Services", "systemMaintainerOrg": "Division of Central Contracts Services", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2023-07-26", "atoExpirationDate": "2026-07-25"}, {"id": "{28BA1D90-7434-4905-BAE2-1E007B80CC1E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{28BA1D90-7434-4905-BAE2-1E007B80CC1E}", "uuid": "8e1b243e1b5aa01063b4a932f54bcbf7", "name": "CCSQ Data Repository and Analytics Platform", "description": "The Center for Clinical Standards and Quality (CCSQ) Data Repository and Analytics Platform (CDRAP) supports the Healthcare Quality Information System (HCQIS) Data & Modernization Project's requirement to establish a cloud-based centralized data repository (CDR) in the QualityNet cloud environment and an internet-facing multi-tool analytic solution, CCSQ Analytics Platform (CAP), for CCSQ users using the SAS Viya analytic tool. The CDR provides convenient, secure, and timely access to CCSQ quality data and commonly used datasets sourced from CMS systems of record. CAP (SAS Viya) functionality includes data wrangling, data visualization, basic statistics, and advanced modeling capabilities. The CDR allows access to the centralized Claims, Provider, Beneficiary data, as well as LOB-specific Healthcare Quality Measures, within a secure QualityNet cloud environment. The CDR increases the accessibility, security, quality, and timeliness of data. The CDR ingests data from source systems, resulting in fewer transformations, and better-quality data all around. The CDR reduces data duplication and data conflicts since all CCSQ/HCQIS users use the same data from the same source. The CCSQ Cloud Analytics Platform (CAP) is a cloud-based analytic solution providing a cost-effective, improved performance analytic platform to include a variety of analytic tools (SAS Viya, Python, Databricks) for Data Scientists users to leverage. It includes standardized and cloud-scalable technologies. CDRAP is currently hosted in the HCQIS QNET (FISMA System) Datacenter in AWS US-EAST and is currently authorized under the QNET ES FISMA system ATO.", "version": "25", "acronym": "CDRAP", "status": "", "belongsTo": "", "businessOwnerOrg": "Information Systems Group", "systemMaintainerOrg": "Division of Quality Systems Governance, Engineering and Development", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-01-28", "atoExpirationDate": "2025-01-28"}, {"id": "{2BA5B548-9296-440b-8B3C-2FF788B02575}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2BA5B548-9296-440b-8B3C-2FF788B02575}", "uuid": "C3C00CE1-DB35-41BC-8B34-C842B1A51241", "name": "Medicaid.gov", "description": "Medicaid.gov was launched in December 2011 as a one stop shop for Federal program and policy information about Medicaid and related state programs including the Children's Health Insurance Program (CHIP) and Basic Health Program (BHP) as well as the Medicaid and CHIP Scorecard. In addition to serving as our primary communication vehicle with the public about federal policy, it also serves as the vehicle for meeting our legal obligations for transparency in the review and approval of section 1115 demonstration projects. Each state's Medicaid and CHIP programs are unique, and the state specific program information on Medicaid.gov makes it possible for states, advocates, lawmakers, other stakeholders to easily stay current on what states are doing with their Medicaid and CHIP programs. This type of communication provides the basis for ongoing learning and informs future developments in Medicaid and CHIP at both the federal and state level to better serve the more than 60 million people covered by these programs. Core Function: * Regulatory and Policy Development for Medicaid and CHIP* Program Monitoring and Analysis for Medicaid and CHIP* Supports legal transparency requirements for Medicaid and CHIP", "version": "25", "acronym": "Medicaid.Gov", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Communications and Outreach", "systemMaintainerOrg": "Division of Communications and Outreach", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{315F0C28-C6A1-45df-B48D-5501138EA022}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{315F0C28-C6A1-45df-B48D-5501138EA022}", "uuid": "DCF50C81-D20D-4ED8-9153-291568CFB974", "name": "QNET Enterprise Services", "description": "Description: CCSQ QNET Enterprise Systems and Services provides various application services that can be consumed by the CCSQ community (federal staff and contractors) including applications (HQR, EQRS, QMARS, etc.). \r\nCore Function:  CCSQ QualityNet Enterprise Systems and Services provides healthcare data analytical services (CDRAP), AWS Cloud, Network, Security, identity/role management (HARP), file management (FileCloud/GoAnywhere) and collaboration services (Atlassian/Slack).", "version": "25", "acronym": "QNET ES", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Quality Systems Governance, Engineering and Development", "systemMaintainerOrg": "Division of Quality Systems Governance, Engineering and Development", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-01-28", "atoExpirationDate": "2025-01-28"}, {"id": "{34CCB539-A237-4552-AFBD-812E529AB3B4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{34CCB539-A237-4552-AFBD-812E529AB3B4}", "uuid": "8203DA8F-EF2D-46B8-994F-78AC44B61A29", "name": "Arc Geographic Information System", "description": "This system is meant to help in the fraud, waste and abuse identification in Medicare and Medicaid.  EADG, as the maintainer, is introducing ArcGIS Stack for use in the projects initially sponsored by CPI. The main users would be internal to CMS, and would comprise of employees, contractors and/or investigators interested in spatial analyses of selected IDR datasets.", "version": "25", "acronym": "ArcGIS", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Enterprise Architecture and Data Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-02-09", "atoExpirationDate": "2026-02-08"}, {"id": "{35405DE7-5B79-4d7c-9249-50C763A1E399}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{35405DE7-5B79-4d7c-9249-50C763A1E399}", "uuid": "BF453F4A-9E23-4BE5-81CF-492CDB63CFC8", "name": "CMS FISMA Controls Tracking System", "description": "CMS is required by the e-Government Act of 2002 -Federal Information Security Management Act, to track and report to OMB, all system vulnerabilities identified in either in Federal or Contractor systems that support the Agency's mission. The FISMA Controls Tracking System (CFACTS) is to help the CMS CISO automate the agency's Security Controls Assessment (SCA) and Plan of Action & Milestones (POA&M) processes. Core Function: * FISMA Control Tracking System", "version": "25", "acronym": "CFACTS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Cyber Threat and Security Operations", "systemMaintainerOrg": "Information Security and Privacy Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-07-26", "atoExpirationDate": "2026-07-25"}, {"id": "{37070F60-2790-4d1c-990D-42F4E272A1E1}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{37070F60-2790-4d1c-990D-42F4E272A1E1}", "uuid": "8F75EE87-C4C4-4A98-87D9-DAE693C55B23", "name": "ViPS Medicare Shared System", "description": "The ViPS Medicare Shared System (VMS) is the standard Medicare Fee for Service system that processes Durable Medical Equipment Prosthetics Orthotics and Supplies (DMEPOS) claims for claim adjudication and payment.  Inquiries for status of claims, for additional development requests, or for eligibility and various codes are processed. Core Function:  GDIT maintains the VMS software that processes Durable Medical Equipment claims through adjudication and payment and for Medical Review. * The VMS claims processing system is the shared system used to process claims for physician and other practitioner services, prior authorization, diagnostic tests, ambulance services, durable medical equipment prosthetics and orthotics (DMEPOS) and other services/supplies that are not covered by Part A. It interfaces directly with the Common Working File (CWF) for verification, validation, and payment authorization.", "version": "25", "acronym": "VMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Division of Shared Systems Management", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-09-22", "atoExpirationDate": "2026-09-21"}, {"id": "{38CAD7D6-4F21-4a74-A228-D99A0F54570E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{38CAD7D6-4F21-4a74-A228-D99A0F54570E}", "uuid": "D807FA39-1D68-473C-B814-93B26AC26212", "name": "Enterprise User Administration", "description": "The Enterprise User Administration (EUA) system is the primary application that CMS uses to create and manage the \"Identity and Access Management\" function for CMS employees, contractors, researchers and other Federal and state agencies' employees who require access to many CMS applications and systems at the CMS Baltimore Data Center, Central Office and Regional Offices. EUA only manages the creation of credentials, the applications that use the EUA ID and password for access are responsible for identifying and managing access to the application.", "version": "25", "acronym": "EUA", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Operations Management", "systemMaintainerOrg": "Division of Operations Management", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-09-15", "atoExpirationDate": "2025-09-15"}, {"id": "{3C10E77B-24B2-461f-994F-3FC53B389431}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3C10E77B-24B2-461f-994F-3FC53B389431}", "uuid": "FFF30A73-F1EC-4D9F-994B-D434A7F048F9", "name": "Electronic Security System", "description": "The Centers for Medicare and Medicaid Services (CMS) Electronic Security System (ESS) is an application of an HSPD 12 compliant Personnel Access Control System (PACS), Closed Circuit Television (CCTV) and Physical Access Module (PAM) which collects and stores necessary data to ensure access to CMS buildings are restricted to only those personnel who are authorized access. Limited access is granted to the PACS and CCTV systems for CMS Security and Guard Staff. General access is automatically granted to CMS employees and contractors who require access to CMS Headquarters facilities for the PAM system and limited access to CMS Security staff for management of facilities and parking. Core Function: * To provide management of security, transportation and emergency functions. * ESS manages physical access to employees, contractors and visitors. * ESS manages parking and CMS vehicle oversight. * ESS manages video surveillance and supports physical security incidents that may occur at or near CMS facilities. * ESS manages the OEO program for accountability during emergency or security breach events. * ESS manages the vulnerability assessment of CMS facilities to meet ISC standards.", "version": "25", "acronym": "ESS", "status": "", "belongsTo": "", "businessOwnerOrg": "Security Management Group", "systemMaintainerOrg": "Division of Credentialing Operations", "state": "Active", "businessOwnerOrgComp": "OSFLO", "systemMaintainerOrgComp": "OSFLO", "atoEffectiveDate": "2022-01-10", "atoExpirationDate": "2025-01-10"}, {"id": "{3DF1629C-A88A-416f-A3C4-C3930DB18914}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3DF1629C-A88A-416f-A3C4-C3930DB18914}", "uuid": "7FA691A8-53E4-41CF-A231-FCDF6AA880A2", "name": "CMS Enterprise Portal Services", "description": "Enterprise Portal is a gateway providing access to a community of CMS healthcare-based applications. Enterprise Portal users can request access to multiple Portal-integrated applications and launch them from the portal platform.\n\nEnterprise Portal Benefits:\n•\tProvides end users with a streamlined, consistent user experience from login to account management to application access.\n•\tEnterprise Portal supports both IDM and EUA based applications.\n•\tHigh availability and failover, geographically diversified.\n•\tEnterprise Portal provides integrated application with both the presentation layer and session management.\n•\tMultiple enterprise-level services available to integrated systems including notifications, chatbot, file management, maintenance pages, operational reporting, and test account management", "version": "25", "acronym": "CMS Enterprise Portal", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Data Strategy", "systemMaintainerOrg": "Division of Data Strategy", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-09-28", "atoExpirationDate": "2025-09-28"}, {"id": "{3F4A404F-99BB-4eb8-B8A3-F4ABE79284C5}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3F4A404F-99BB-4eb8-B8A3-F4ABE79284C5}", "uuid": "112a2d27db348c1073ebf50d0f9619d6", "name": "Organizational Cloud Economics and Analytics Nexus", "description": "The Organizational Cloud Economics and Analytics Nexus (OCEAN) application was developed to manage and facilitate CRMT business processes while still providing thorough analysis and oversight into cloud resource consumption. The OCEAN application accomplishes these goals by doing the following:\n1.\tAutomates the existing CRMT cloud resource request management process including submission, reviews, and approvals\n2.\tProvides real-time access to reports and dashboards tracking cloud resource utilization monitoring and project spending for MITG, CRMT, and ADO users\nThe OCEAN application provides MITG increased transparency into ADO resource requests, ability to track each step in the workflow or process and make recommendations for right sizing all while ensuring better data accuracy. OCEAN provides Analytics and Reporting (A&R) so users can review reports and dashboards related to cloud resource consumption and utilization at the MITG portfolio level down to the project level (based on role access). OCEAN application is a FIPS 199 Moderate system. \nOCEAN's Portfolio Management (PfM) acts as a single repository for maintaining the baseline inventory of the marketplace systems. PfM tool allows CCIIO stakeholders to access the key information related to the Marketplace systems, point of contacts, and processes for Application Development Organizations (ADOs).", "version": "25", "acronym": "OCEAN", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Program, Contract, and Budget Management", "systemMaintainerOrg": "Marketplace Innovation and Technology Group", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2023-02-24", "atoExpirationDate": "2026-02-23"}, {"id": "{4366A8B9-7D61-435e-87ED-54B6D6AE2751}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{4366A8B9-7D61-435e-87ED-54B6D6AE2751}", "uuid": "F44A1F06-18A3-41D0-99CB-85B60ADBB22A", "name": "Provider Enrollment Chain and Ownership System 2.0", "description": "PECOS 2.0 is a ground up redesign of the current Medicare Enrollment System and focused on transitioning the system from a single purpose form applications processing product to a modernized, enterprise resource that is a platform for all provider enrollments across Medicare, Medicaid, and emerging programs.", "version": "25", "acronym": "PECOS 2.0", "status": "", "belongsTo": "", "businessOwnerOrg": "Provider Enrollment and Oversight Group", "systemMaintainerOrg": "Data Analytics and Systems Group", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-06-22", "atoExpirationDate": "2024-06-22"}, {"id": "{44883743-E18E-44b9-87E7-C39D412E4D2D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{44883743-E18E-44b9-87E7-C39D412E4D2D}", "uuid": "B37ED0CF-1CED-400E-B475-8EB19A8A1FD3", "name": "Center for Medicare and Medicaid Innovation Cloud Service Provider Salesforce", "description": "The Center for Medicare and Medicaid Innovation (CMMI) is leveraging the Salesforce Government Cloud Platform to support essential model needs. These solutions are collectively referred to as the “CMMI CSP Salesforce.” CMMI CSP Salesforce will host CMMI systems to accomplish its functional mission:• Identifies, validates and disseminates information about new care models and payment approaches to serve Medicare and Medicaid beneficiaries seeking to enhance the quality of health and health care and reducing cost through improvement. • Consults with representatives of relevant Federal agencies, and clinical and analytical experts with expertise in medicine and health care management, including providers, payers, states, businesses, and community agencies, to develop new and effective models of care. • Creates and tests new models in clinical care, integrated care and community health, and disseminates information on these models through CMS, HHS, states, local organizations, and industry channels. • Performs rapid cycle evaluation of innovation and demonstration activities to determine effectiveness and feasibility for broader dissemination, scale, and sustainability. • Works closely with other CMS components and regional offices to study health care industry trends and data for the purposes of designing, implementing, and evaluating innovative payment and service delivery models, and to disseminate information about effective models. • Creates and tests innovative payment and service delivery models, building collaborative learning networks to facilitate the collection and analysis of innovation, as well as the implementation of effective practices, and developing necessary technology to support this • Carries out core business functions (e.g., budget, facilities, HR, communications).", "version": "25", "acronym": "CMMI CSP SF", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Technology Solutions", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2023-01-27", "atoExpirationDate": "2026-01-26"}, {"id": "{45E858C9-5C9B-4d83-8BF7-41E6110AD1B2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{45E858C9-5C9B-4d83-8BF7-41E6110AD1B2}", "uuid": "3F3D378B-4411-4FDA-B5EC-7F700C698E29", "name": "Comprehensive Error Rate Testing - RC", "description": "This represents the Comprehensive Error Rate Testing (CERT) Review Contractor (RC). The CERT Program is a strategic method of assessing the success of the work of CMS to pay providers accurately. The program provides CMS and taxpayers with more useful information, and results in fewer hassels for physicians, providers, and their staff. The process begins with arandom sample of Medicare claims, which are subjected to medical records review by the CERT staff. The results from the medical review are used to calculate the paid claim error rates. The CERT program produces national, contractor specific, provider type, and benefit category specific paid claim error rates. The error rates are then used to help CMS formulate corrective actions.The CERT Review Contractor: •Select a stratified random selection of claims from the Medicare FFS claims universe•Provide customer service support to the Medicare Administrative Contractors (MACs) and providers who have claims selected for review•Conduct medical review (MR) on claims selected for CERT and other accuracy reviews, as specified by CMS •Communicate MR results to the MACs, providers, and other stakeholders•Collect and transmit data to the CERT Statistical Contractor (CERT SC) for purposes of calculating improper payment rates •Maintain various websites for internal and external stakeholders•Establish mechanisms to manage unexpected fluctuations in the Contractor's processing requirements •Perform additional tasks to support the mandate of the CERT program, as directed by CMS and this Statement of Work (SOW)", "version": "25", "acronym": "CERT-RC", "status": "", "belongsTo": "", "businessOwnerOrg": "Payment Accuracy and Reporting Group", "systemMaintainerOrg": "Payment Accuracy and Reporting Group", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-12-06", "atoExpirationDate": "2026-12-05"}, {"id": "{48322C3B-5E22-4068-9653-CC77A8B88B9D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{48322C3B-5E22-4068-9653-CC77A8B88B9D}", "uuid": "63BC9F16-A027-4C30-A86A-CC65118E6B32", "name": "Medicaid and Children's Health Insurance Program Budget and Expenditure System", "description": "CMS tracks state expenditures through the automated Medicaid Budget and Expenditure System/State Children's Health Insurance Budget and Expenditure System (MBES/CBES).\r\nThe MBES/CBES is a web-based application system that has been implemented nationwide. The system allows states to report actual expenditures for Medicaid and the Children's Health Insurance Program (CHIP), by electronically submitting their Form CMS-64 directly to the CMS Data Center and the Medicaid data base.\r\nThe system uses the information from each state to compute the amount of Federal Financial Participation (FFP) the Agency will provide to the state to fund program operations. The MBES/CBES also stores the state and territories historical expenditure records for data analysis purposes. This system provide CMCS FMG the ability to conduct analysis of the Medicaid and CHIP expenditure data, along with reporting to stakeholders, such as Congress and the public, based on that data.\r\nMost of the MBES-CBES functionality has already migrated over to the new platform called Medicaid and CHIP Financial (MACFin). The remaining module that supports Medicaid and CHIP expenditures and some reporting features are being developed and will migrate to MACFin soon.", "version": "25", "acronym": "MBES-CBES", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Financial Operations East", "systemMaintainerOrg": "Division of Business Essential Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2022-07-14", "atoExpirationDate": "2025-07-13"}, {"id": "{48C77240-8690-4833-944B-94F5AD31650F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{48C77240-8690-4833-944B-94F5AD31650F}", "uuid": "04522B5D-304D-4371-BEE3-F15F4C175F62", "name": "Medicare Appeals System", "description": "The Medicare Appeals System (MAS) is suite of applications designed to support the appeals process. In the MAS, the Part A Medicare Administrative Contractors (MACs) for FFS, the Qualified Independent Contractors (QICs) for FFS, the Independent Review Entity for Medicare Advantage, the Part D QIC, and the Office of Medicare Hearings and Appeals (OMHA) process and adjudicate Medicare appeals in MAS.  Core Function: * Medicare appeals tracking and processing  * Dates, claims, participants, and determinations* Medicare appeals data reporting using enterprise Cognos* Scanned Image Capture creates image documents, associates them with MAS Appeals, and releases them to Enterprise Content Manager", "version": "25", "acronym": "MAS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Appeals Policy", "systemMaintainerOrg": "Division of Mid-Tier Applications Management", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-01-17", "atoExpirationDate": "2026-01-16"}, {"id": "{4AF2E646-4D7A-4734-A66B-3E874B24F660}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{4AF2E646-4D7A-4734-A66B-3E874B24F660}", "uuid": "37f459031b8641102e8743bae54bcbfa", "name": "MedTrak", "description": "The Clinical Data Abstraction Center (CDAC) contractor is utilizing the MedTrak Electronic Medical Records tool to collect clinical data from Hospitals and QIOs to be used as part of the quality measure used in the Medicare.gov Hospital Compare site.\r\nThe MedTrak is the Clinical Data Abstraction Center Enterprise Tracking System being implemented to collect medical records or information from QIOs or hospitals as part of the Clinical Data Abstraction Center (CDAC). MedTrak is responsible for processing over 80,000 medical records per year. MedTrak is designed to request, store, and retrieve medical records for use in medicals studies. The studies are created and managed through various agencies within the government. MedTrak supports the workflow, which includes tracking used by CDAC to perform abstraction and validation of the medical records. The work conducted by the CDAC is finalized in the Inpatient Prospective Payment System (IPPS) final rule which is a requirement of the statute finalized by congress. \r\nWith MedTrak, we are validating provider reported quality measure outcome data by reviewing the medical record, and performing medical record reviews for healthcare related adverse events. It is not only important to make sure that the data received by CMS is quality data but it is a mandatory requirement based on the statute finalized by congress. Based on the information gained by collecting/abstracting/validating medical records CMS is able to hold providers accountable for not only providing quality data but also improve the overall care provided to the CMS beneficiaries in the participating hospitals across the nation. \r\nMedTrak is not a public facing application.", "version": "25", "acronym": "MedTrak", "status": "", "belongsTo": "", "businessOwnerOrg": "Quality Measurement and Value-Based Incentives Group", "systemMaintainerOrg": "Information Systems Group", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-11-03", "atoExpirationDate": "2025-11-03"}, {"id": "{4E450864-7657-4c56-8669-372B44D8A65A}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{4E450864-7657-4c56-8669-372B44D8A65A}", "uuid": "e62628301beaf410bca26311f54bcb35", "name": "Call Center IT Systems", "description": "The Call Center IT Systems (CCITS) transforms siloed, geographically dispersed CMS contact centers into a fully integrated, robust multi-channel customer service environment that provides the American public with 24x7 access to accurate health care information, empowering them to make informed decisions.\r\nThis system combines the National Data Warehouse (NDW) and Next Generation Desktop (NGD) systems under one umbrella system.", "version": "25", "acronym": "CCITS", "status": "", "belongsTo": "", "businessOwnerOrg": "Call Center Operations Group", "systemMaintainerOrg": "Division of Call Center Operations", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{4F79A9D4-3D6D-421e-9B9D-1DA50862956D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{4F79A9D4-3D6D-421e-9B9D-1DA50862956D}", "uuid": "3F9B8D09-DFEA-4C26-BB1D-81616E6B6159", "name": "Salesforce Enterprise Integration", "description": "An enterprise coordination for the use of the Salesforce (SF) application within CMS is being implemented. This strategy would address current gaps and inefficiencies in CMS' use of Salesforce, specifically in the security, contract, and product management areas. This strategy would facilitate SF onboarding requirements, ensure continuous security monitoring, as well as centralize contractual oversight, including acquisition planning and license pricing. The primary objective of the Enterprise Integration project is to launch a single sign-on integration between CMS Enterprise Identify Management and Enterprise Portal (ePortal) shared services while utilizing the Salesforce platform. The Office of Information Technology (OIT) will utilize ePortal and EIDM authentication and authorization capabilities. Additionally, the scope of the project will also be responsible for implementing solutions which address four separate 2016 SF SCA findings: [1] Identity proofing; [2] Multifactor authentication; [3] Malware detection, and [4] Continuous monitoring.  The collective remediation across these findings is to obtain one ATO across instances. In addition to these security findings, the SF SCA raised several business, infrastructure, and cost inefficiencies. Given the complexities of this application, there is a need for a single point of contact -- The SF “Enterprise Org” -- to provide oversight and management for assisting Component Org's from a security, product, and acquisitions perspective. Core Function: Supports Single Sign On, Malware Detection, Event Monitoring, Field Level logging for Salesforce Enterprise Orgs", "version": "25", "acronym": "SEI", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enterprise Services", "systemMaintainerOrg": "Division of Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-12-06", "atoExpirationDate": "2026-12-05"}, {"id": "{5162EF05-4929-4cb2-9C26-3193C90E117E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5162EF05-4929-4cb2-9C26-3193C90E117E}", "uuid": "25DB857A-BD31-4CA2-AD98-047FCFC52725", "name": "Retiree Drug Subsidy System", "description": "Serves as a payment system for Plan Sponsors participating in the Retiree Drug Subsidy (RDS). The RDS Program is important in controlling the costs of CMS Medicare Part D program because it encourages private retirement plan providers to retain their Medicare eligible beneficiaries by providing a prescription drug benefit that is actuarially equivalent or superior to the standard Medicare Part D coverage. Core Function: * Enrolls Organizations (Plan Sponsors) into the Retiree Drug Subsidy (RDS) program. * Allows Plan Sponsor to create application for the plan year. * Verifies the benefit provided by Plan Sponsor is equivalent or better than Medicare Part D through Actuarial Attestation. * Accepts retiree information from Plan Sponsors, Vendors, Mandatory Insurer Reporting and Voluntary Data Sharing Agreement partners. * Matches the retiree information with Medicare Beneficiary Database. Provides RDS eligibility to the Plan Sponsor. * Accepts allowable summarized Part D drug cost from Plan Sponsor and Vendors. * Notifies Plan Sponsor through a notification file of any change in eligibility or enrollment. * Calculates and generates payments to the authorized Plan Sponsors. * Provides capability for Plan Sponsor to reconcile costs and retirees and appeal adverse determination.", "version": "25", "acronym": "RDS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Payment Reconciliation", "systemMaintainerOrg": "Division of Payment Reconciliation", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2022-08-01", "atoExpirationDate": null}, {"id": "{546690B1-3802-40ca-B8FF-D4879CAAF24C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{546690B1-3802-40ca-B8FF-D4879CAAF24C}", "uuid": "CD9D1FB2-63B2-4D2E-81EE-D6D0A27739C5", "name": "CMS Measures Inventory Tool", "description": "CMIT allows users to view all of the quality measures that CMS uses in its incentive programs.  The user can search, filter, and sort the measures and see detailed information about each measure.  Designated users can edit the measures. Core Function: * Repository for Measures* Environmental Scan Public Site; MERIT allows stakeholders to submit quality measures for consideration for inclusion in CMS quality programs and enables the review of the submitted measures; MMS Hub is a website providing information about quality measure development; MIDS Library is a repository of MIDS IDIQ contract deliverables.", "version": "25", "acronym": "CMIT", "status": "", "belongsTo": "", "businessOwnerOrg": "Quality Measurement and Value-Based Incentives Group", "systemMaintainerOrg": "Division of Program and Measurement Support", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-09-07", "atoExpirationDate": "2026-09-06"}, {"id": "{549C0CF3-4758-493c-B500-22E93225C5DE}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{549C0CF3-4758-493c-B500-22E93225C5DE}", "uuid": "A73C696A-666E-4CF3-AA19-044A42CB716A", "name": "Multi-Carrier System", "description": "MCS is a mainframe system that Medicare Part B carriers use to process Medicare Part B Claims nationwide. It processes claims for physician care, durable medical equipment, and other outpatient services. Like its Part A counterpart, claims are entered, corrected, adjusted, or canceled. Inquiries for status of claims, for additional development requests, or for eligibility and various codes are processed. Core Function: * The MCS is the shared system used to process Medicare Part B claims for physician care and other outpatient services nationwide.  * It interfaces directly with the Common Working File (CWF).  * It meets CMS core requirements for processing Medicare Part B claims, to include: data collection and validation, claims control, pricing, adjudication, correspondence, on line inquiry, file maintenance, reimbursement, and financial processing.", "version": "25", "acronym": "MCS", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Division of Shared Systems Management", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-09-22", "atoExpirationDate": "2024-09-22"}, {"id": "{54F9C250-207B-4384-B9B5-C1B2CC91558A}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{54F9C250-207B-4384-B9B5-C1B2CC91558A}", "uuid": "54E989AE-EAFA-4841-9656-02318DDF2C45", "name": "Fraud Prevention System 2.0", "description": "The Fraud Prevention System 2.0 (FPS2.0) streams in claims data that is used to analyze and validate schemes/patterns of fraud, waste and abuse, alert investigators to explore said patterns/schemes to see if action is necessary and it also rejects and denies claims that policy dictates are not valid.  The FPS2.0 system will be used by Unified Program Integrity contractors (UPICs), Law Enforcement, Medicare Administrative Contractors (MACs), along with multiple divisions within the agency.  Core Function:  This investment will provide state of the art fraud fighting analytical tools to help CMS predict and prevent potentially wasteful, abusive or fraudulent payments before they occur.   * The Fraud Prevention System (FPS) 2 streams in claims data that is used to analyze and validate schemes/patterns of fraud, waste and abuse, alert investigators to explore said patterns/schemes to see if action is necessary and it also rejects and denies claims that policy dictates are not valid.", "version": "25", "acronym": "FPS 2.0", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Investigative Systems Management", "systemMaintainerOrg": "Division of Investigative Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-03-25", "atoExpirationDate": "2024-03-24"}, {"id": "{5B2D0211-0E4D-494f-B107-DDDF6078B823}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5B2D0211-0E4D-494f-B107-DDDF6078B823}", "uuid": "8A1693DD-21A9-42B8-ABA8-5169448124D0", "name": "Federally Facilitated Marketplaces", "description": "The system will maintain records used to support all Health Insurance Exchange Programs established by the Centers for Medicare and Medicaid Services (CMS) under health care reform provisions of the recently passed Affordable Care Act (Public Law 11 148). This system, starting in 2014, will help qualified individuals and small employers shop for, select, and pay for high quality, affordable health coverage. Exchanges will have the capability to determine eligibility for coverage through the Exchange, for tax credits and cost sharing reductions, and for Medicaid, Basic Health Plan (BHP) and CHIP coverage. As part of the eligibility and enrollment process, financial, demographic, and (potentially) health information will flow through the Exchange. HIX IT activities are organized around two key systems, the Exchanges and Data Services Hub.  Exchange Systems support the core business functions of an Exchange including administration, health plan management, eligibility and enrollment, risk adjustment, premium tax credit administration, program integrity, and portal for customers. The Data Services Hub acts as a broker of information and will facilitate accessing and management of complex set of data from a variety of sources, including multiple Federal agencies, with exchanges, and Medicaid systems. Core Function: * Eligibility and Enrollment* Plan Management* Financial Management* MCR* DSRS", "version": "25", "acronym": "FFM", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Innovation and Technology Group", "systemMaintainerOrg": "Marketplace Innovation and Technology Group", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{5C41D87D-9258-4ae1-9966-19EA1522AACE}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5C41D87D-9258-4ae1-9966-19EA1522AACE}", "uuid": "0A5A0CE9-962E-4C9C-8027-02B8EAED7C27", "name": "Medicaid and CHIP DataConnect", "description": "Medicaid & CHIP DataConnect enables Medicaid and CHIP program staff in CMCS to access, analyze and produce reports from Medicaid and CHIP data.  The product brings together all CMCS data and makes it available through a variety of analytic tools to empower Federal oversight, programmatic monitoring, evaluation and research into Medicaid and CHIP nationally.", "version": "25", "acronym": "MAC-DC", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Systems Group", "systemMaintainerOrg": "Data and Systems Group", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2022-02-04", "atoExpirationDate": "2025-02-03"}, {"id": "{62CDFE40-77EA-4e5b-8567-39FB3426AED9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{62CDFE40-77EA-4e5b-8567-39FB3426AED9}", "uuid": "F70E912C-4CF1-4CB4-A31D-56F61A26C403", "name": "Health Plan Management System", "description": "The Health Plan Management System (HPMS) is a web enabled information system that serves a critical role in the daily business operations and high profile initiatives of the Medicare Advantage (MA) and Prescription Drug (Part D) programs.  HPMS facilitates the numerous data collection and reporting activities mandated for MA and Part D plans by legislation, including, but not limited to, the Medicare Prescription Drug, Improvement and Modernization Act (MMA) of 2003, the Medicare Improvements for Patients and Providers Act (MIPPA) of 2008, the Affordable Care Act (ACA) of 2010, and the Medicare Access and CHIP Reauthorization Act of 2015 (MACRA).  The MA and Part D programs are also governed by regulatory and sub regulatory guidance that resides in Section 42 CFR Parts 417, 422, 423, and 480.  Specifically, HPMS provides system support for the ongoing operations of the MA and Part D plan enrollment and plan compliance business functions as well as for the agency's strategic planning and program analysis activities.  In total, there are approximately 70 HPMS software modules, of varying sizes and complexity, which support MA and Part D functions, including, but not limited to: application submission, automated network adequacy review, formulary submission and review, bid and benefit package submissions and review, marketing material reviews, plan audits and compliance, performance monitoring, complaints tracking, plan connectivity, financial reporting, financial and plan bid audits, plan surveys, operational data feeds for enrollment, payment, and premium withhold, and data support for the Medicare and You handbook and the www.medicare.gov website.   Core Function:  Collect Medicare Advantage (MA), Prescription Drug (Part D), Special Needs Plan (SNP), Cost, Program for All Inclusive Care for the Elderly (PACE), Employer Plan, and Medicare Medicaid Plan (MMP) applications from new organizations seeking to enter the Medicare program and from existing organizations intending to expand their contract service area.* Enumerate all MA, PDP, Cost, PACE, and MMP contracts. * Review MA, Part D, SNP, Cost, PACE, Employer, and MMP applications, including pharmacy network and health services delivery adequacy.  * Collect and review plan bids and benefit packages.* Collect and review plan formularies, formulary pricing data and transition policies. Collect and review Medication Therapy Management Programs (MTMP).    * Collect and review direct and indirect remuneration (DIR) data from contracts offering Part D for payment reconciliation.    * Collect beneficiary complaints and perform casework resolution activities. * Perform program, financial, bid, and cost report audits. * Collect and review marketing material submissions.    * Collect and review fiscal solvency data. * Collect and present plan reporting and plan metrics data. * Calculate benchmarks and Part D plan premiums.   *Collect and review Fraud, Waste and Abuse Data. * Provides monthly operational data feeds to downstream systems in OIT, SSA, OC, etc.  *Provide data to Medicare Plan Finder. * Provide an interactive dashboard to assess plan and program performance.", "version": "25", "acronym": "HPMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Plan Data", "systemMaintainerOrg": "Division of Plan Data", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2022-08-01", "atoExpirationDate": "2025-08-01"}, {"id": "{694C27AF-CADE-466b-851D-71C0F7F16931}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{694C27AF-CADE-466b-851D-71C0F7F16931}", "uuid": "e2570bb31b62d0108b1163dbe54bcbd6", "name": "Conversion Medicare", "description": "The CVM system resides in AWS and replaced the mainframe MQR/MQA systems\nMain functions are:\n1.    Accept Adjudicated Medicare Claims and Beneficiary Maintenance records from CWF.\n2    Perform quality assurance & validation and duplicate checks of those claims.\n3.   Validation of those Claims prior to consumer use.\n4.    Reformat them for CMS consumer use", "version": "25", "acronym": "CVM", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Operations and Maintenance", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-09-03", "atoExpirationDate": "2024-09-03"}, {"id": "{69C3E0FA-D97D-4eaf-A894-9191D287110C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{69C3E0FA-D97D-4eaf-A894-9191D287110C}", "uuid": "51D252AB-2179-4FF9-A869-FB0BD9E1FAC1", "name": "Medicare Coverage Information Management", "description": "Medical doctors, employed by the Medicare Administrative Contractors (MACs), submit Local Coverage Determinations (LCDs) and Articles intended for publication on the Medicare Coverage Database (MCD) website. LCDs and Articles that have been approved are moved manually once a week to the MCD database. Expired or superseded LCDs and Articles are also hosted in the MCD Archive application, which is available to the public.  Core Function: * A main functionality of the LCBE is to provide access to LCDs and Articles through the MCD that are created and managed by contractors through this portal. The administration area manages the documents* which are presented as .pdf files. In general, the management involves Adding, Editing, Copying and Viewing the LCDs and Articles.", "version": "25", "acronym": "MCIM", "status": "", "belongsTo": "", "businessOwnerOrg": "Coverage and Analysis Group", "systemMaintainerOrg": "Division of Policy Coordination and Implementation", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2021-08-26", "atoExpirationDate": "2024-08-26"}, {"id": "{6B51569D-C230-429e-8022-B0EA71968174}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{6B51569D-C230-429e-8022-B0EA71968174}", "uuid": "7AA99404-08DB-4438-A253-4077C5FBC215", "name": "Provider Enrollment Chain and Ownership System", "description": "PECOS is the system utilized by CMS to collect, manage, and maintain Medicare provider enrollment.  This system collects and maintains information about the provider's or supplier's initial enrollment into the Medicare program, changes of information, reassignments, and CMS mandated revalidations or re-enrollments.  In addition to collecting information about individual practitioners or organizational entities, PECOS collects information about ownership, Authorized Officials (AOs), Delegated Officials (DOs), managing employees, practice locations, provider or supplier type, provider and supplier specific information, and affiliated provider information.  PECOS maintains this data in a national database.  In addition to capturing and relating information contained in the Medicare enrollment application, PECOS also provides a logging and tracking function, an inquiry/reporting capability, and a data exchange process which forwards enrollment and chain of ownership information to other systems. Core Function: PECOS is the system utilized by CMS to collect, manage and maintain Medicare enrollment.  This system collects and maintains information about the provider's or supplier's initial enrollment into the Medicare program, changes of information, reassignments, and CMS mandated revalidations or re-enrollments.  In addition to collecting information about individual practitioners or organizational entities, the PECOS collects information about ownership, Authorized Officials (AOs), Delegated Officials (DOs), managing employees, practice locations, provider or supplier type, provider and supplier specific information, and affiliated provider information.  PECOS maintains this data in a national database.  In addition to capturing and relating information contained in the Medicare enrollment application, PECOS also provides a logging and tracking function, an inquiry/reporting capability, and a data exchange process which forwards enrollment and chain information to other systems. In the near future PECOS will be expanding to include Medicaid enrollment data and processes.", "version": "25", "acronym": "PECOS", "status": "", "belongsTo": "", "businessOwnerOrg": "Provider Enrollment and Oversight Group", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-06-07", "atoExpirationDate": "2026-06-06"}, {"id": "{719207CD-205A-4dab-BF95-E072FA5BF926}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{719207CD-205A-4dab-BF95-E072FA5BF926}", "uuid": "C9B9A131-F974-49BD-BBFF-1B0235747C0E", "name": "Healthcare Integrated General Ledger Accounting System", "description": "The HIGLAS provides CMS a uniform financial management system to account for over one Trillion of federal outlays each year. Oracle Federal Financials is the base application providing CMS with the ability to manage the Medicare benefits, Medicare Secondary Payer, Medicaid program, Federal Facilitated Marketplace and Administrative Program Accounting functions.  Core Function: * HIGLAS strengthens Medicare's management of accounts receivable and allows more timely and effective collection activities on outstanding debts* HIGLAS enhances CMS oversight of contractor financial operations, including data entry, transaction processing and reporting.* HIGLAS produces automated agency financial statements and other required reports, leading to fewer errors in financial reporting and a reduction in manual labor.* HIGLAS allows for the elimination of redundant accounting processes and provides standardize accounting business practices.* HIGLAS enables Medicare Contractors to cut back on the number of cuff systems currently used to track financial data.* Provides a standard General Ledger accounting system, standardized accounting and financial management process for CMS central office and administrative program accounting activities.* HIGLAS incorporates Medicaid and CHIP government data.", "version": "25", "acronym": "HIGLAS", "status": "", "belongsTo": "", "businessOwnerOrg": "Accounting Management Group", "systemMaintainerOrg": "Financial Management Systems Group", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-02-10", "atoExpirationDate": "2026-02-09"}, {"id": "{720FDB8A-A653-4c73-90C5-A92C1899E7F7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{720FDB8A-A653-4c73-90C5-A92C1899E7F7}", "uuid": "B4D19F4D-5C47-4E66-89C2-A1EC58027083", "name": "Recovery Audit Contractor Data Warehouse", "description": "The Recovery Audit Contractor (RAC) Data Warehouse is a web based system that provides CMS with a comprehensive, effective and largely automated means of overseeing RAC activities. CMS uses the system to monitor RAC audits while ensuring that the RACs do not select claims that are exempt from review for various reasons, or where the providers are currently under investigation by program integrity and/or law enforcement entities. CMS also tracks RAC error findings via the Data Warehouse for the purpose of developing corrective actions. Core Function:", "version": "25", "acronym": "RACDW", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Recovery Audit Operations", "systemMaintainerOrg": "Division of Recovery Audit Operations", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-01-18", "atoExpirationDate": "2026-01-17"}, {"id": "{7435053E-C2D3-439d-93AA-DB779AEC8818}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{7435053E-C2D3-439d-93AA-DB779AEC8818}", "uuid": "02931948-AD52-4B28-B2F3-7771372A2D14", "name": "Federal Data Services Hub", "description": "The Data Services Hub (DSH) is a support system for The Federal Exchange Program System (FEPS) and state Health Insurance Exchanges (HIX). DSH acts as a single interface point for Health Insurance Exchanges (HIX) to all federal agency partners and provides common functional service support. A single interface simplifies the integration required of the Exchanges. Common services allow for adherence to federal and industry standards regarding security, data transport, and information safeguards management.  Core Function:  Facilitation of the exchange of data between State Exchanges, Federal Exchanges, and Federal agencies* Enabling verification of coverage eligibility* Providing data for paying insurers* Providing data to be used in portals (Health Exchanges) for consumers", "version": "25", "acronym": "FDSH", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of HUB Data and State Support", "systemMaintainerOrg": "Division of HUB Data and State Support", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-01-28", "atoExpirationDate": "2025-01-28"}, {"id": "{743EB745-DDA8-4e01-B7EF-A12C60D23CAA}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{743EB745-DDA8-4e01-B7EF-A12C60D23CAA}", "uuid": "81462f1adb1877009b3772840f961965", "name": "Medicaid And CHIP Financial", "description": "The Medicaid And CHIP Financial (MACFin) system is the modernized solution for CMS, states and territories to improve the accuracy of over $800 billion in annual state-reported budget and expenditure data, and is essential for federal and state administration of the Medicaid program and Children's Health Insurance Program (CHIP).  It empowers federal, state, and territory administration to evolve efficiently with ongoing regulation, programmatic and technological changes.  MACFin improves the technology, functionality, and efficiency of the existing financial processes, and provides a consolidated solution to Medicaid and CHIP financial administration and oversight.  MACFin is the only system that CMS uses to determine Federal payment amounts to states and territories for the Medicaid and CHIP programs.\nThe MACFin system is absorbing and modernizing the features and functions from the legacy MBES/CBES system as well as integrating and automating financial management processes currently external to MBES/CBES.  MACFin has integrated the legacy IBNRS system into MACFin, as well as the DSH Allotments, DSH Audits, Grants, CMS 37 Submission and Review, and CMS 21B Submission and Review functionalities. MACFin ensures states, territories, and CMS execute the quarterly budgeting and expenditures processes that calculate and fund the federal match for the critical State-based Medicaid Programs. MACFin makes  it easier for states to submit timely data to CMS, CMS to award grants more efficiently, calculate federal grants/payment amounts using current reg. formulas, manage the financial issues reports,  and support Upper Payment Limit processes.", "version": "25", "acronym": "MACFin", "status": "", "belongsTo": "", "businessOwnerOrg": "Financial Management Group", "systemMaintainerOrg": "Division of Business Essential Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2022-06-16", "atoExpirationDate": null}, {"id": "{756A54B6-E9CE-41c5-B78D-36C822AFB64C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{756A54B6-E9CE-41c5-B78D-36C822AFB64C}", "uuid": "6C0CBCD4-5BD4-426B-A1FD-F76C1DF62DFD", "name": "Blue Button API on Fast Healthcare Interoperability Resources", "description": "CMS Blue Button API is a Data-As-A-Service platform that allows beneficiaries to access their Medicare health data and share it with third party applications that they trust, such as research platforms. CMS Blue Button API (aka known as Blue Button 2.0 API) is built using the Fast Healthcare Interoperability Resources (FHIR), an international standard for exchanging health care information electronically. Blue Button 2.0 (BB2.0) delivers a service that can be easily integrated with other applications and services and will significantly improve the usability of CMS beneficiaries' health. Blue Button 2.0 also has the potential to set a precedent for connecting consumer/patient health related data to health applications and services, creating a model that can be extended to other patient populations beyond Medicare. The Blue Button 2.0 system is comprised of two platforms: Front end platform to manage consumer and developer access using OAuth2.0 protocols for authorization; Back end platform (Beneficiary FHIR Database) providing a standard FHIR database to provide claims information in FHIR Explanation of Benefit Resource format. Blue Button 2.0 is currently configured in a CMS enclave within AWS.", "version": "25", "acronym": "BBAPI", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Analytics Strategy Group", "systemMaintainerOrg": "Data and Analytics Strategy Group", "state": "Active", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2022-01-10", "atoExpirationDate": "2025-01-10"}, {"id": "{7755CBBF-22AF-46bf-A055-E8B06D5B475F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{7755CBBF-22AF-46bf-A055-E8B06D5B475F}", "uuid": "90D17CC9-6271-4077-B59E-077A8DEA7A90", "name": "Transformed Medicaid Statistical Information System", "description": "T-MSIS collects Medicaid and Children's Health Insurance Program (CHIP) data from U.S. states, territories, and the District of Columbia into the largest national resource of beneficiary information.  This data is crucial for research and policy on Medicaid and CHIP and helping the Centers for Medicare & Medicaid Services (CMS) conduct program oversight, administration, and integrity.\r\nTo meet the reporting needs of states and CMS stakeholders, T-MSIS features an operations dashboard for state and territory use to validate a timely, accurate, and complete data set.\r\nT-MSIS uses cloud infrastructure services for advanced data processing, security, and storage. CMS continually works to enhance T-MSIS in accordance with the U.S. federal government's Digital Services Playbook. New product features are delivered in a rapid, iterative, and secure manner using agile development and DevSecOps practices.", "version": "25", "acronym": "T-MSIS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Information Systems", "systemMaintainerOrg": "Division of Information Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2023-07-14", "atoExpirationDate": "2024-11-30"}, {"id": "{78E4CC02-EE5E-47e1-957D-2C8130C06BB8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{78E4CC02-EE5E-47e1-957D-2C8130C06BB8}", "uuid": "AEB9B0A6-1D8D-43CD-BE3D-96CD489BDDF2", "name": "Accountable Care Organization-Operational System", "description": "ACO OS serves two business purposes relative to Shared Savings Programs and  Alternative Payment Models (APMs). Receipt and control of data files that move between CMS systems and to/from Accountable Care Organizations. Management of data pertaining to Accountable Care Organizations, participating beneficiaries and providers/suppliers. Core Function: The ACO OS (Accountable Care Organization - Operational System) is the data process system developed to store program information and to enable data flow and program deliverables to and from the program and the entities participating in the program as well as managing interactions between the program contractors and the various CMS peer systems.  The ACO-OS serves as the system of record for various program information, and has a reporting component that is responsible for generating and distributing various business intelligence reports as well as raw data extracts. The front-end of the ACO-OS is the 4Innovation (4i) application. This front-end is used by ~7000 users, however, does not account for the thousands of files distributed to CMS stakeholders and partnering systems each year. The 4i is primarily used by APMs to manage participants. 4i is also used by APMs to securely deliver files and reports, transfer knowledge, and manage model participation agreements.", "version": "25", "acronym": "ACO-OS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of ACO Finance and Data Analytics", "systemMaintainerOrg": "Division of Applications Development and Support", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-01-13", "atoExpirationDate": "2026-01-12"}, {"id": "{********-EA6A-44b7-BB3A-5DCA291C06F7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{********-EA6A-44b7-BB3A-5DCA291C06F7}", "uuid": "8EEE4AE4-2D3E-4F74-BB83-ECF3D5D0A748", "name": "CM - Maximus", "description": "CM-Maximus enables Maximus Federal Services to process second level appeals for Part A, Part C, and Part D for CMS as a Qualified Independent Contractor (QIC).  The boundary is comprised of a network segment of the Maximus corporate domain that is segmented off from other projects and features several internal applications that allow efficiencies and prosper automation for the second level appeal tasks.   The CM-Maximus does also have a dedicated interconnection to the Medicare Appeals System (MAS).", "version": "25", "acronym": "CM-Maximus", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Enrollment and Appeals Group", "systemMaintainerOrg": "Medicare Enrollment and Appeals Group", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2021-06-15", "atoExpirationDate": "2024-06-15"}, {"id": "{82051DE1-CCE8-4d9c-8894-91CE19406159}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{82051DE1-CCE8-4d9c-8894-91CE19406159}", "uuid": "EEAF9D12-E1AB-4F10-936E-AF3418263018", "name": "Program Integrity Contractor <PERSON>nt<PERSON>ridge", "description": "The Program Integrity Contractor assists CMS/CPI with the investigations of suspected fraud, waste, and abuse; and supports benefit integrity efforts through medical reviews, national and regional data analysis, and the referral of cases to law enforcement for consideration and initiation of civil or criminal prosecution.\nThis FISMA system represents a specific Program Integrity Contractor (PIC) which assists CMS's efforts in combating fraud, waste and abuse in Medicare and Medicaid (all claim types).  Named changed to Program Integrity Contractor Advance Med from Zoned Program Integrity Contractors Zone 2 - AdvanceMed per email from David dated 3/1/2019 \nThis also combines two ZPICs: Zoned Program Integrity Contractors Zone 2 - AdvanceMed AND\nZoned Program Integrity Contractor Zone 5 - AdvanceMed", "version": "25", "acronym": "PI-CoventBridge", "status": "", "belongsTo": "", "businessOwnerOrg": "Center for Program Integrity", "systemMaintainerOrg": "Center for Program Integrity", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2022-08-01", "atoExpirationDate": "2025-08-01"}, {"id": "{8426CE39-3EF5-4ee8-BA10-98C3452534D0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8426CE39-3EF5-4ee8-BA10-98C3452534D0}", "uuid": "57ACB6F2-A3AE-41F4-905F-29135060817C", "name": "CCIIO Enrollment Resolution and Reconciliation System", "description": "The CCIIO Enrollment Resolution and Reconciliation System (CERRS) is a web based application which supports the Enrollment Resolution and Reconciliation (ER&R) and payment disputes (Form 1095-A) work.  Core functions include research and resolution of Form 1095-A casework, resolve and reconcile enrollment and payment data for issuer disputes and Case Management System. Core Function: * Used to facilitate reporting and tracking of the resolution and reconciliation of issuer and payment data discrepancies and the research and resolution for Form 1095-A consumer disputes data discrepancies.", "version": "25", "acronym": "CERRS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Marketplace Eligibility Policy and Operations", "systemMaintainerOrg": "Division of Enrollment Policy and Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-01-10", "atoExpirationDate": "2025-01-10"}, {"id": "{8904B7AE-DBF6-4cdd-A973-A03A521EDE8B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8904B7AE-DBF6-4cdd-A973-A03A521EDE8B}", "uuid": "cadce222dbd76f0085c976708c9619ae", "name": "CMS Acquisition Lifecycle Modernization", "description": "The scope of the CMS Acquisition Lifecycle Management (CALM) project is to support an agile and iterative transition of the CMS Office of Acquisition and Grants Management (OAGM) current IT portfolio that supports FAR-based procurements, inter-agency agreements, and grants (CMS Acquisition Infrastructure) to a modern architecture, focusing on Data Management, Acquisition Infrastructure, Applications, and Business Process Improvement. Core Function: Acquisition lifecycle management", "version": "25", "acronym": "CALM", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Data, Systems and Certification", "systemMaintainerOrg": "Division of Data, Systems and Certification", "state": "Active", "businessOwnerOrgComp": "OAGM", "systemMaintainerOrgComp": "OAGM", "atoEffectiveDate": "2023-04-14", "atoExpirationDate": "2026-04-13"}, {"id": "{8AEE6B3B-7E51-48f5-9328-57E7CFBA59A2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8AEE6B3B-7E51-48f5-9328-57E7CFBA59A2}", "uuid": "5adec1eddb887340296cfd0e0f96197e", "name": "HEDIS Patient Data", "description": "The purpose of this application is to oversee the annual data collection and management of the Healthcare Effectiveness Data and Information Set (HEDIS®) Patient Level Detail (PLD) Data Collection for Medicare Advantage (MA) organizations for the Centers for Medicare & Medicaid Services (CMS). The HEDIS PLD data is used in the CMS MA Star Ratings and the MA Quality Bonus Payments program. The HEDIS PLD supports the Health Equity Index Reward for MA plans. MA Plans submit PLD Data annually that attributes to clinical measures assessing effectiveness of care and access/availability of care measures. The PLD data file is validated as per the file specifications published by National Committee for Quality Assurance (NCQA). Data extracts of the HEDIS PLD data is sent to CMS, RAND, and Research Organization for data research purposes. The Web Portal disseminates information to the MA Plans regarding the status of file submission and provides reports on file submission status to CMS and Help Desk Staff. ", "version": "25", "acronym": "HEDIS PLD", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Consumer Assessment and Plan Performance", "systemMaintainerOrg": "Division of Consumer Assessment and Plan Performance", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2023-12-06", "atoExpirationDate": "2026-12-05"}, {"id": "{919988F4-9ED7-479f-9BD1-674141CD9AB9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{919988F4-9ED7-479f-9BD1-674141CD9AB9}", "uuid": "00A23131-F7D5-4CB0-BBA9-95D571B742CB", "name": "Recovery Audit Contractor Regions 1, 2 and 5", "description": "The CMS RAC (Recovery Audit Contract) contract is a Medical Review and Data Analytics based contract that audits for overpaid claims, where Medicare (CMS) may have overpaid the provider of service (physician, hospital, lab, etc.).  We audit the claims and then notify the provider of service of the potential overpayment.  The provider then either pays the overpayment back to Medicare or appeals it.", "version": "25", "acronym": "RACs- 1, 2 and 5", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Recovery Audit Operations", "systemMaintainerOrg": "Division of Recovery Audit Operations", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-05-05", "atoExpirationDate": "2026-05-04"}, {"id": "{9586461F-1769-4fc5-8725-D169EDB5BFE3}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9586461F-1769-4fc5-8725-D169EDB5BFE3}", "uuid": "ed049bcddbf608500887f4d40f9619bc", "name": "Provider Compliance Group-Fast Healthcare Interoperability Resources", "description": "The PCG-FHIR system provides a CMS FHIR service end-point allowing Health Care Providers to submit medical documents and Prior Authorization Requests to CMS in FHIR format. This will transform paper documentation exchange to an electronic exchange of structured documentation that will primarily benefit CMS in the following areas:\n•    Reduce improper payments and combat fraud, waste, and abuse\n•    Improve efficiencies in documentation reviews and minimize appeals for denied claims\n•    Simplify administration and reduce provider burden\n•    Ensure compliance with HIPAA mandated X12N 278 transactions for Prior Authorization\n\nAlso, there are two additional modules added under PCG-FHIR's ATO which are Special Health Information Handlers Management and Support (SHMS) and Electronic Clinical Templates API (ECTA). The SHMS solution will act as a Health Information Handlers (HIH) on behalf of CMS. This will introduce a new set of Application Programming Interface (APIs) and new graphical user interface (GUI).\n\nThe primary objective of the Electronic Clinical Templates API (ECTA) contract is to support the collaborative transformation of the Medicare FFS coverage rules from paper-based (including PDF version) into a machine-processable representation in a standard format interoperable across systems. The secondary objective of the ECT A contract is to expose the digitized rules library through API services available for all systems to leverage in performing several functionalities. These include transparent information sharing on coverage rules, checking or verifying documentation requirements, clinical data collection support, validation of collected data, verifying data and documentation completeness, medical necessity or compliance checks, etc.\n", "version": "25", "acronym": "PCG-FHIR", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Compliance Projects and Demonstrations", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-12-14", "atoExpirationDate": "2026-12-13"}, {"id": "{9AB31E22-1993-4a67-82A5-1A15C2EFC33C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9AB31E22-1993-4a67-82A5-1A15C2EFC33C}", "uuid": "93BFA6DB-C472-4784-8D7F-A35A0B2FB4A8", "name": "Data Exchange System", "description": "The Data Exchange System (DEX) allows States and CMS to share information about providers that each State has terminated from its Medicaid program and that CMS and the HHS OIG have terminated from their programs. The system supports the Government in its efforts to aggregate and distribute information about providers across Medicare, Medicaid, and the States. Core Function: * The Data Exchange System (DEX) allows States and CMS to share information about providers that each State has terminated from its Medicaid program and that CMS and the HHS OIG have terminated from their programs. * The system supports the Government in its efforts to aggregate and distribute information about providers across Medicare, Medicaid, and the States.", "version": "25", "acronym": "DEX", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enrollment Policy and Operations", "systemMaintainerOrg": "Division of Enrollment Systems", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-04-08", "atoExpirationDate": "2024-04-07"}, {"id": "{9C8EE522-642B-4719-AC54-3F535E1703DB}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9C8EE522-642B-4719-AC54-3F535E1703DB}", "uuid": "86E949C7-592F-46C6-B684-43D6C1DD7584", "name": "Maryland Primary Care Program System", "description": "The Maryland Primary Care Program (MDPCP) Model is designed to support Practices along the continuum of transformation to deliver better care to patients and promote smarter spending. MDPCP is both a care delivery and payment redesign model. MDPCP provides participating Practices and Care Transformation Organizations (CTOs) with tools to assist with providing information pertaining to their demographic, practice, and organizational information and composition; their reporting of practice and quality milestones, as well as to provide a platform where participating Practices and CTOs can download reports essential to their success in this initiative. The Center for Medicare & Medicaid Innovation (CMMI) and contractors supporting the initiative will use MDPCP to monitor the progress and compliance of participating Practices and CTOs. Core Function: MDPCP maintains demographic information and organization information about the participating MDPCP practices and CTOs, as well as the demographic information of points of contacts and participating providers. This information is necessary for model operations and serves as a single point of reference for practices, CTOs and other MDPCP stakeholders. MDPCP Practices, CTOs, and participating payers receive information on a routine basis to support their work in the model. For Practices and CTOs, these include Payment and Attribution, Beneficiary Attribution Reports, other Administrative documents such as Participation Agreements and Letters of Support. Participating payers also receive their Provider Roster reports through MDPC. This is critical information for payer operations because it determines the providers in MDPCP who are to receive care management fees.", "version": "25", "acronym": "MDPCP", "status": "", "belongsTo": "", "businessOwnerOrg": "State and Population Health Group", "systemMaintainerOrg": "Division of Technology Solutions", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2022-11-16", "atoExpirationDate": "2025-11-16"}, {"id": "{9E825725-9697-4def-A8FF-20F57382F4A0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9E825725-9697-4def-A8FF-20F57382F4A0}", "uuid": "30229C93-9965-4AD0-B179-75E0A4561DFD", "name": "Payment Recovery Information System", "description": "The Payment Recovery Information System (PRIS) supports financial audit activities for Medicare Part C (Medicare Advantage) and Medicare Part D programs. PRIS provides services for CMS to request approval of a New Audit Issues, Medicare Advantage Recovery Audit Data Validation (RAD-V) appeals for Payment Error Calculation (PEC) and Medical Record Review Determination (MRRD), and to enter Improper Payments results identified in Medicare Parts C & D Recovery Audit Contractor (RAC) programs. PRIS supports the Lifecycle of the Medicare  Advantage (Part C) and Part D RACs programs and the Medicare Advantage RAD-V program. Features of the lifecycle include activities from the RACs,  Data Validation Contractor (DVC), Appeals along with activities associated with the processing of requests to offset an improper payment to adjust a plan's monthly payment along with validating the contingency amount owed to the RAC. For the Medicare Advantage RAD-V program, it includes appeals for the Payment Error Calculation (PEC) and the Medical Record Review Determination (MRRD), status reports, and final decision outputs with an integration with the Centralized Data Abstraction Tool (CDAT).  PRIS provides reports capturing the recovery audit activities. It allows CMS policy personnel to review the issues for interpretation of CMS policy. It allows the secure transfer of imaged documentation from the RAC to CMS over the Multi Protocol Label Switching (MPLS) service network. These images are stored in Enterprise Content Manager (ECM). Core Function: * PRIS provides services over MPLS lines for CMS to request approval of New Audit Issues and to enter Improper Payments results identified in Medicare Parts C and D. * PRIS supports the Lifecycle of the Parts C and D Recovery Audit Contractors (RACs) program.", "version": "25", "acronym": "PRIS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Prescription Drug Audits", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-07-14", "atoExpirationDate": "2025-07-14"}, {"id": "{A568C57B-FC15-40fa-8A32-7519859BB173}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A568C57B-FC15-40fa-8A32-7519859BB173}", "uuid": "414f2afedb5233400db67e560f9619ff", "name": "Medicare Payment System Environment", "description": "The Medicare Payment System Environment (MPSE) is part of the Medicare Payment Systems Modernization initiative, an important effort to modernize the CMS Medicare FFS Shared Systems. MPSE generally hosts system-to-system interconnections between MAC and FFS systems at VDCs. Currently, MPSE provides a FISS claims information service accessed via a REST/JSON API for use by MAC backend systems to replace existing FISS Screen scraping solutions. This MAC API consists of four services (Claim Summary, Claim Line Item, Beneficiary Info, Payee History). MPSE also replaces the mainframe based Inpatient Rehabilitation Facility (IRF) Pricer with a java based cloud hosted web service accessed by FISS via a standard API. Other modernization objectives will be implemented in MPSE over time.", "version": "25", "acronym": "MPSE", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Change and Operations Management", "systemMaintainerOrg": "Division of Medicare System Modernization", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-11-28", "atoExpirationDate": "2025-11-28"}, {"id": "{A6BC79A1-9C2A-4186-8D54-8BAD52AB1447}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A6BC79A1-9C2A-4186-8D54-8BAD52AB1447}", "uuid": "9009F83A-D7B8-4C11-A48A-6A7AA284F727", "name": "Health Insurance Casework System", "description": "HICS facilitates the tracking and resolution of consumer issues related to the exchanges (e.g., identify verification, eligibility determinations, denials, appeals, etc.).  It also provides data and insight into program and issuer performance.   Core Function: * Support casework management for Affordable Care Act (ACA) federally operated and  supported marketplace models as well as for other private health insurance cases. * Facilitate the tracking and resolution of consumer issues related to the exchanges (e.g. identify verification, eligibility determinations, denials, appeals, etc.). * The system will also provide data and insight into program and issuer performance.", "version": "25", "acronym": "HICS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Consumer Services", "systemMaintainerOrg": "Division of Insurance Oversight and Transparency Application", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-03-04", "atoExpirationDate": "2025-03-04"}, {"id": "{A928F2ED-FED8-4483-A82B-F8D73124DA92}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A928F2ED-FED8-4483-A82B-F8D73124DA92}", "uuid": "56661e501b5c7410b98f62cfe54bcbcb", "name": "Payment Recovery Information System (PRIS) Plan Portal", "description": "The Payment Recovery Information System for Medicare Parts C & D Plan Portal (PRIS Plan Portal/PMPP) isThe PMPP is a web-based application that will house MA and Part D programs audit information for CMS's Center for Program Integrity (CPI) Division of Prescription Drug Audits (DPDA) and PPI MEDIC to conduct MA and Part D program audits and be a communication and document delivery tool for MA and Part D plans that are chosen for an audit. The end users of this application will be CMS Business Owner, CMS/CPI DPDA Audits Team, PPI MEDIC, and the MA and Part D Plan Users.\nThe application will be available for access by these users through the internet. Since this is a new the application, it is set-up only for the Self-Audit Process.", "version": "25", "acronym": "PMPP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Prescription Drug Audits", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-08-26", "atoExpirationDate": "2024-08-26"}, {"id": "{AEFAEB64-DA28-46a8-BAE3-DBC01C7112F2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{AEFAEB64-DA28-46a8-BAE3-DBC01C7112F2}", "uuid": "63DFEB84-E382-44CE-808F-05FA1A0F43E7", "name": "PRI Review System", "description": "Description:  The PRI (Provider Resources, Inc.) Review System (PRI) provides a secured system to support CMS activities where medical records, claims data, or a subset of claims must be analyzed and evaluated in a clinical and analytic review. PRI provides the following core functionality: the secure transfer of the subset of claims and supporting documents; the secured access for authorized reviewers; Workflow assignment and tracking of reviews; and Reporting of results to CMS. PRI supports the following Programs: the Medicare Coverage Gap Discount Program (CGDP); Federal Independent Dispute Resolution (IDRE); and the Beneficiary and Family Centered Care (BFCC) Quality Improvement Organization (QIO) IDIQ task orders. This system is paid for through the collection of fees.  Core Function: * Promotes the integrity of the Medicare and Medicaid programs and CHIP through provider/contractor audits", "version": "25", "acronym": "PRI", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Part D Policy", "systemMaintainerOrg": "Division of Part D Policy", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2021-12-13", "atoExpirationDate": "2024-12-13"}, {"id": "{B4D666F6-E14F-4478-8167-A0946638D2B8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B4D666F6-E14F-4478-8167-A0946638D2B8}", "uuid": "89AF190B-FBBC-4BFD-9DF7-************", "name": "Deliverable Administration, Report, and Repository Tool", "description": "The system allows the QIOs to be able to document their contract deliverables (documented in each QIO's contract, Schedule F) and for CORs to have a place to review the documents that require the COR review and provide feedback where applicable.  The system will also provide an updated schedule of deliverables organized by categories and accessible by the QIOs. Core Function: * Allows uploading of QIO contract deliverables* Allows CORs to acknowledge, approve, and deny contractor deliverables* Allows BFCC QIOs to refer Quality Improvement Initiatives to QIN QIOs* Allows users to upload reports to a repository", "version": "25", "acronym": "DARRT", "status": "", "belongsTo": "", "businessOwnerOrg": "iQuality Improvement and Innovation Group", "systemMaintainerOrg": "Information Systems Group", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-04-28", "atoExpirationDate": "2025-04-28"}, {"id": "{B669DA4A-A229-485e-AFF0-71E10664E857}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B669DA4A-A229-485e-AFF0-71E10664E857}", "uuid": "CB4CB486-C11C-4405-ADA9-D35810062DFA", "name": "Acumen Web Portals", "description": "The Acumen Web Portal (AWP) system enables CMS, Acumen, and/or CMS-designated organizations to securely exchange contract-related deliverables and discuss contract-related matters. Although the AWP Application supports a number of CMS-defined workflows, the Application broadly provides the following online capabilities: (1) Data Transfer. The Application establishes methods of secure data exchange, including encrypting transmitted data and instituting restricted levels of access to sensitive materials without unduly constraining the activities of Web Portal users; (2) Project Discussions. The Application enables geographically dispersed participants to discuss potentially sensitive project information through secure online discussion boards; and (3) Information Archives. The Application serves as a centralized information archive, retaining project-related communications and materials such that real-time and historical project information can be accessed by authorized project participants. The AWP Application hosts web portals supporting several CMS programs including Medicare Part A, Medicare Part B, DMEPOS, End Stage Renal Disease (ESRD) Program, Medicare Advantage, Medicare Part D, Medicare-Medicaid Coordination (Duals), Health Insurance Marketplaces, Innovation Center initiatives, Program Integrity, and Value and Incentive Programs. Core Functions: Remote Identity Proofing (RIDP), Multifactor Authentication (MFA), Account Management, User Authorization, secure data and file transfer, protected project discussions, activity status-tracking.", "version": "25", "acronym": "AWP", "status": "", "belongsTo": "", "businessOwnerOrg": "Hospital and Ambulatory Policy Group", "systemMaintainerOrg": "Hospital and Ambulatory Policy Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2021-09-07", "atoExpirationDate": "2024-09-07"}, {"id": "{B7B245F6-EBC6-4f34-B98C-CD07F177D017}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B7B245F6-EBC6-4f34-B98C-CD07F177D017}", "uuid": "a990031bdbdb37000db67e560f9619f0", "name": "Integrated Data Repository Cloud", "description": "Centers for Medicare and Medicaid Services (CMS) maintains numerous systems housing Medicare beneficiary Parts A, B, C and D entitlement, enrollment, utilization, quality, and provider performance information. Additionally, CMS maintains data on physicians, providers, employer plans, Medicaid recipients and Medicare secondary payers. Implementation of provisions of the Medicare Prescription Drug, Improvement, and Modernization Act (MMA) requires that CMS develop and maintain databases and systems to manage the enrollment of individuals in the drug benefit or subsidy assistance programs, pay prescription drug plans, evaluate the quality of the new prescription drug benefit, support drug research, provide better access to data, and provide opportunities for other government and research organizations to improve healthcare for the public. The Integrated Data Repository Cloud (IDRC) is the enterprise resource designated to house such information. To serve the growing needs of using the IDRC data for various analytic workloads and data sharing across the agency and outside at scale, the IDRC ecosystem includes a mature Cloud Data Warehouse with independent storage and compute elasticity capabilities. IDRC is an “all-purpose” Cloud Architecture that leverages leading cloud data warehouse technologies that offer elastic scalability, enhanced system throughput and user experience, security, and most importantly provide cost savings The IDRC provides to all of CMS and its partners a single authoritative source of information.", "version": "25", "acronym": "IDRC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enterprise Information Management Services", "systemMaintainerOrg": "Division of Enterprise Information Management Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-06-27", "atoExpirationDate": "2026-06-26"}, {"id": "{BB5B4F0F-86E6-4203-914B-26CDEF2BE7E1}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{BB5B4F0F-86E6-4203-914B-26CDEF2BE7E1}", "uuid": "4af452c1db17f380512770131f961990", "name": "Risk Adjustment Data", "description": "The Risk Adjustment Data (RAD) system is a suite of data analysis and validation tools and functionalities instantiated by the Division of Encounter Data Risk Adjustment Operations (DEDRAO) Medicare Plan Payment Group (MPPG). The RAD has a current Authority to Operate (ATO) at a FISMA Moderate impact level. The system resides wholly within the CMS Cloud Hosting Facility (CHF) known as “CMS Cloud” which is maintained by CMS Office of Information Technology (OIT).\n\nThe RAD is comprised of two major components. The first, known as the RAD Tool application, is a web-based longitudinal validation and analytic tool and data repository instantiated by the DEDRAO, specifically within MPPG.\n\nThe second component, known as the RAD Data environment, is a data processing infrastructure environment with no traditional front-end user interface.  This environment supports data analysis, the Independent Verification and Validation (IV&V) of plan reporting (MAO-004 and MOR reports), and the creation of Long-Term Institutional Flags (LTI) which are critical to payment calculations. Future releases will support risk score IV&V, and monitoring for payments under the Medicare Part C [Medicare Advantage (MA)] and Part D (Medicare Drug) programs.\n\nThe RAD Data environment of the RAD System is a modernization of data creation and validation processes that previously occurred within the CMS Mainframe hosted in the Baltimore Data Center (BDC) but which are being phased out by CMS.", "version": "25", "acronym": "RAD", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Encounter Data and Risk Adjustment Operations", "systemMaintainerOrg": "Division of Encounter Data and Risk Adjustment Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2022-06-09", "atoExpirationDate": "2025-06-09"}, {"id": "{BC2C09FA-63CD-46e5-99EB-D27DB538FD6A}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{BC2C09FA-63CD-46e5-99EB-D27DB538FD6A}", "uuid": "7F0709DB-D0A4-4F9C-BFA4-7F67D7AF93F7", "name": "CMS Analysis, Reporting, and Tracking System", "description": "CMS ARTS is the system of record for tracking Contractor Business Proposals, Cost Reports, Deliverables, and Workload Information for various departments within the agency and contains financial, budget and cost related information which includes, but is not limited to: dashboard reports, contract performance reports and FOIA requests. Cost information reports allow analysis of the contractor's performance to the contract estimated costs and milestones.   Core Function:  A contractor performance management system tracking estimated costs vs actual costs for contracts awarded by CMS.  It is also a repository for contract deliverables.", "version": "25", "acronym": "CMS ART", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Applications Management Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-11-17", "atoExpirationDate": "2024-11-17"}, {"id": "{C1D13D97-899A-46ca-B6C3-702DF45B4FD2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C1D13D97-899A-46ca-B6C3-702DF45B4FD2}", "uuid": "86989640-1DE2-48E4-9281-739F7271F345", "name": "Encounter Data Processing System", "description": "The Encounter Data Processing System is a system that accepts X12 837 (version 5010) format encounter data records. The system is required to accept, process, validate, report, analyze and price encounter data records (EDR) received from Medicare Advantage organizations and third party submitters. Under this contract, CMS has procured engineering, development operations, maintenance and support services. To date, CMS has collected and processed over 10 billion MA encounter data records \nThe program objectives for the Medicare Advantage Risk Adjustment Program and for this contract are to (1) accept and process encounter data records, (2) perform detail validation edits of the data, (4) provide reports and files to CMS systems and to MAOs, (5) calculate a price for each encounter, (5) analyze encounter data records, (6) transfer encounter data records to the CMS Integrated Data Repository, and (7) maintain the encounter data system in a manner in which the system can support growth estimates in the Medicare Advantage Program.", "version": "25", "acronym": "EDPS", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Plan Payment Group", "systemMaintainerOrg": "Division of Encounter Data and Risk Adjustment Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2023-01-10", "atoExpirationDate": "2026-01-09"}, {"id": "{C53B8B07-6777-41b7-8EC5-6065594F77DD}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C53B8B07-6777-41b7-8EC5-6065594F77DD}", "uuid": "B6120873-8DDD-4861-A927-19FB29244191", "name": "Data Element Library", "description": "The Data Element Library (DEL) will house assessment instrument data elements in a centralized repository. The purpose of the DEL is to centralize the creation, management, and governance of the setting specific data elements and to publish the relational mapping of the data elements across the instruments and to national health information technology (HIT) vocabulary standards. DEL capabilities promote standardization across settings and interoperability of data elements by mapping data elements to HIT standards. It will be accessible to both CMS and the public with separate levels of access for each.  Core Function: * The purpose of the Data Element Library application is to serve as a centralized and authoritative resource of post acute care assessment instrument data elements and their associated health information technology (HIT) standards, to support standardization and interoperability of PAC data elements as called for in the IMPACT Act.  * The DEL is intended to serve CMS and the public.", "version": "25", "acronym": "DEL", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Chronic and Post Acute Care", "systemMaintainerOrg": "Division of Quality Systems for Assessments and Surveys", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-07-19", "atoExpirationDate": "2026-07-18"}, {"id": "{C6CEABB3-8094-4743-9FC4-6002EBA53C1E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C6CEABB3-8094-4743-9FC4-6002EBA53C1E}", "uuid": "A483424D-7892-403D-BAA4-AC525040A49F", "name": "Acquisition and Grants Exchange", "description": "As part of our IT Modernization efforts, the OAGM websites, both internal and external, need operational and maintenance services to accomplish the goal of delivering a comprehensive one stop shop for Acquisitions & Grants process and information. The new OAGM home page website enhances access to available policies, procedures, options, and acquisition tools.  In alignment with the modernization efforts, OAGM has begun the process of automating and streamlining workflows to increase transparency, visibility, and access to various phases of the acquisition life cycle.  The consolidation of acquisition interfaces into a web portal will improve our current internal business practices.  A web portal will increase efficiency of work practices and business standardization in support of the Federal Acquisition Lifecycle.  The goal is to enhance and leverage the use of a centralized information resource for the acquisition workforce and our stakeholders through an open access environment. In alignment with the modernization efforts, OAGM has begun the process of automating and streamlining workflows to increase transparency, visibility, with the access to various types of acquisition information for all phases of the acquisition life cycle.  The consolidation of acquisition interfaces into a web portal will improve our current internal business practices.  A web base data warehouse was implemented to increase efficiency of work practices and business standardization in support of the Federal Acquisition Lifecycle.  The goal is to enhance and leverage the use of a centralized information resource for the acquisition workforce and our stakeholders through an open access environment. The Data Warehouse will require operations and maintenance service also. The external OAGM webpage will also be enhanced to provide contractors with more information, which will also support the President's transparency initiatives. Core Function: * Stores Templates, Links and other acquisition policy Content, Training, Policies, Communications related to Acquisition", "version": "25", "acronym": "AGX", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Data, Systems and Certification", "systemMaintainerOrg": "Customer Relations Group", "state": "Active", "businessOwnerOrgComp": "OAGM", "systemMaintainerOrgComp": "OAGM", "atoEffectiveDate": "2022-07-21", "atoExpirationDate": "2025-07-20"}, {"id": "{CA9A70E8-433A-43a1-9C13-A1C984B90716}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{CA9A70E8-433A-43a1-9C13-A1C984B90716}", "uuid": "5197D267-79A9-4AE1-89C3-CBED5C748D53", "name": "Central Data Abstraction Tool-Modernized", "description": "CMS conducts Risk Adjustment Data Validation (RADV) audits to validate the accuracy of risk adjustment data submitted to CMS by MA organizations for Part C payments. To facilitate the RADV process CMS has developed the Centralized Data Abstraction Tool-Modernized (CDAT-M). CDAT-M shall be used to manage the collection and distribution of medical records, medical record abstraction, reconsideration, management of RADV project data, and other project information including some activities related to payment error calculation. The entities involved in using CDAT-M to facilitate the flow of information will include CMS, RADV contractors and MA organizations. Core Function: Financial Oversight and Auditing and Program Monitoring and Analysis.", "version": "25", "acronym": "Modernized-CDAT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Medicare Advantage Audits", "systemMaintainerOrg": "Division of Provider Investigations", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-05-14", "atoExpirationDate": "2024-05-14"}, {"id": "{CF63B160-FB80-4379-8047-AF95AD952D00}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{CF63B160-FB80-4379-8047-AF95AD952D00}", "uuid": "A1837A4F-4EF5-42D1-9537-82178667A8EB", "name": "CMS National Training Program Learning Management System", "description": "CMS National Training Program (NTP) is a critical part of CMS Outreach and Education efforts along with the Medicare Learning Network (MLN) and 1 800 Medicare. Whereas 1 800 Medicare focuses on supporting Medicare beneficiaries directly and the MLN focuses on providing education and support for Medicare providers, the NTP is responsible for providing training and educational outreach to external stakeholders who themselves directly advise or work with Medicare beneficiaries. Core Function: * Provide training resources, course registration, job aids and other informational materials to partners, beneficiaries, public and other stakeholders. System sends email reminders about upcoming courses for attendance and provides a record of training history completion.", "version": "25", "acronym": "NTP LMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Training", "systemMaintainerOrg": "Division of Training", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2023-12-06", "atoExpirationDate": "2026-12-05"}, {"id": "{D288BFA8-496E-48d3-8FB1-A996CC8BBFA1}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{D288BFA8-496E-48d3-8FB1-A996CC8BBFA1}", "uuid": "AAC21B4A-9F71-4137-A225-31828F8583D9", "name": "Federally Facilitated Exchange Analysis Tools", "description": "Federally Facilitated Exchange Analysis Tools (FFEAT) is a Moderate level system located at the Amazon Web Services GovCloud (AWS GovCloud) IaaS/PaaS. FFEAT is a suite of reporting and analytic tools that enables automated reviews and provides the Center for Consumer Information and Insurance Oversight (CCIIO) with timely ad-hoc analysis and reports. FFEAT is used to review issuer applications, and rate and benefit packages for Qualified Health Plans and Stand-Alone Dental Plans on the Federally-Facilitated Exchange (FFE).", "version": "25", "acronym": "FFEAT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enrollment Payment Data", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2023-12-15", "atoExpirationDate": "2026-12-14"}, {"id": "{D8C700DB-6F94-492f-B8AC-948EEAB47E77}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{D8C700DB-6F94-492f-B8AC-948EEAB47E77}", "uuid": "02e43946dbc544500db67e560f9619fb", "name": "Healthcare Fraud Prevention Partnership Trusted Third Party 2.0", "description": "The purpose of the HFPP TTP information system is to provide analytic support and a technical solution for the Centers for Medicare & Medicaid Services (CMS) launched Healthcare Fraud Prevention Partnership (HFPP) with the Department of Health and Human Services (HHS) Office of the Inspector General (OIG), Department of Justice (DOJ), Federal Bureau of Investigation (FBI), private health insurance companies, state and local agencies, and other healthcare and anti-fraud groups and associations. The partnership requires the services of a Trusted Third Party (TTP) in order to support the partnership with the analytic and management ability to exchange healthcare fraud and abuse information and identify innovative measures to detect and prevent healthcare fraud, waste, and abuse. \r\nThe HFPP is a public-private partnership established to detect and deter fraudulent behaviors within the healthcare system. The TTP shall support the HFPP by providing the necessary services to design and execute HFPP studies, facilities to safeguard sensitive healthcare data, and a multi-disciplinary analytic and management capability to engage HFPP partners in a customer service-oriented manner to support data collection, analysis, report generation, and communications. The TTP shall act as a trusted and objective organization that facilitates collaboration and builds trust among HFPP partners and is able to provide independent and impartial judgment necessary to perform analysis of partner data.", "version": "25", "acronym": "HFPP TTP 2.0", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Fraud Prevention Partnerships", "systemMaintainerOrg": "Division of Fraud Prevention Partnerships", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-05-05", "atoExpirationDate": "2026-05-04"}, {"id": "{E4B03C16-AC69-463b-8DE9-DB3B0AD14D13}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E4B03C16-AC69-463b-8DE9-DB3B0AD14D13}", "uuid": "d917b2431b0a1410c74fb913cc4bcb11", "name": "Cloud Content Management", "description": "CMS implemented an enterprise-wide content management solution to facilitate the life cycle management of unstructured data with the goal of meeting the near-term requirements of CMS business drivers while aligning with the strategic target architecture, and remain scalable and extensible enough to support future requirements.  \nCCM is the AWS cloud platform hosting the applications using the content management platform.  Nearly all applications using the BDC Enterprise Content Management (ECM) system have been migrated to the CCM platform, and the remaining ECM application will be moved to CCM by end of FY24.  We will begin decommissioning the ECM platform once the final application is successfully  migrated.", "version": "25", "acronym": "CCM", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Data Enterprise Services", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-03-26", "atoExpirationDate": "2024-03-26"}, {"id": "{E5D31701-4C3A-4ba0-8CDF-BD53B2217952}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E5D31701-4C3A-4ba0-8CDF-BD53B2217952}", "uuid": "c505b9f21b46901044e4ed7bbc4bcbac", "name": "Medicare Secondary Payer Systems Contractor - Major Application", "description": "The Medicare Secondary Payer Systems Contractor - Major Application (MSPSC MA) is an umbrella system (and ATO) which encompasses three existing systems (Benefits Coordination and Recovery System (BCRS), Coordination of Benefits and Recovery System (COB-R), and Coordination of Benefits-Secure Website (COB-SW)).\nThe Coordination of Benefits and Recovery (COB-R) program is the consolidation of the Coordination of Benefits (COB) and Medicare Secondary Payer Recovery (MSPR) activities into a centralized operation.  The Medicare Secondary Payer Systems Contractor (MSPSC) Major Application consists of all MSP and COBR applications and will be represented as such whereas all references to the MSPSC system will be represented by “MSPSC (MA)”.\nThese applications perform the activities that support the collection, management, and reporting of other insurance coverage of Medicare beneficiaries and the collection of conditional payments or mistaken primary payments.\nMSPSC MA supports operations required to provide quality customer service and a single source to Medicare providers, suppliers, beneficiaries, insurers, and other stakeholders by streamlining the Medicare Secondary Payer data and debt collection processes while ensuring the integrity of the Medicare Trust Funds.", "version": "25", "acronym": "MSPSC MA", "status": "", "belongsTo": "", "businessOwnerOrg": "Financial Services Group", "systemMaintainerOrg": "Division of MSP Program Operations", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-05-25", "atoExpirationDate": "2026-05-24"}, {"id": "{E887BB3D-16CA-4760-B26F-4446B451F8E2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E887BB3D-16CA-4760-B26F-4446B451F8E2}", "uuid": "69175558-4FBC-4FFA-B26C-4EEB739E80CC", "name": "Performance Metrics Database and Analytics", "description": "1115 PMDA application offers a source of high quality and timely data to improve CMCS ability to monitor demonstrations for the achievement of desired outcomes and projected cost savings. Content Management Application (CMA) within 1115 PMDA provides the capability for CMS Project Officers to organize and maintain the reporting necessary to monitor various 1115 demonstration types for all participating states; and, to accept or request the resubmission of reports required from the states for monitoring. Project Officers have the ability to access the Special Terms and Conditions (STC) functionality within CMA to manage and generate draft STC documents.  CMA also provides the capability for State Users to upload and monitor their demonstrations and deliverables and for Evaluation Analysts to review and download accepted deliverables. Core Function: * Collecting programmatic quality and other performance metrics, related reports and other information associated with selected 1115  demonstrations* Providing electronic reports that support CMCS oversight, monitoring and evaluation of 1115 demonstration performance, particularly on quality and other performance metrics* Producing analytic files to support demonstration evaluation* Additional functionality being developed and added to manage the submission and review of Section 1115 demonstrations", "version": "25", "acronym": "PMDA", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Demonstration Monitoring and Evaluation", "systemMaintainerOrg": "Division of Demonstration Monitoring and Evaluation", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2022-02-24", "atoExpirationDate": "2025-02-23"}, {"id": "{E91EEC39-2FC8-43d1-A599-357BBF62636D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E91EEC39-2FC8-43d1-A599-357BBF62636D}", "uuid": "da3c52431bdf5010a60dea41f54bcb30", "name": "Capitol Bridge Worker's Compensation Case Tracking System", "description": "This contractor owned business application manages the workflows followed to process WCMSA submissions (Workers' Compensation Medicare Set-Aside Arrangement), allowing the Workers Compensation Review Contractor (WCRC) to track progress as cases are handled by reviewers. This application also collects and stores data used by the WCRC to report and manage on contract SLAs, drive QA/CQI efforts, and provide detailed reporting per CMS requirements.", "version": "25", "acronym": "CBWCCTS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of MSP Program Operations", "systemMaintainerOrg": "Division of MSP Program Operations", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2022-01-28", "atoExpirationDate": "2025-01-28"}, {"id": "{E929E80C-73E0-46d7-A6E3-40D5670AE5B0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E929E80C-73E0-46d7-A6E3-40D5670AE5B0}", "uuid": "2068E0D8-77D9-4A8E-92D2-F1817667412C", "name": "Medicaid Drug Programs", "description": "Medicaid Drug Programs is the modernized solution for CMS, states, and drug manufactures to manage and oversee the $38+ Billion Medicaid drug rebate program to ensure that Medicaid drug expenses comply with Federal regulations and statutes. MDP assumed responsibility and business functions previously provided by the existing Medicaid Drug Rebate (MDR) system, Federal Upper Limit pricing (FUL) processes, Drug Utilization Review survey and annual report (DUR), and the Branded Prescription Drugs (BPD) processes with the IRS. MDP allows for analysis, data sharing, program monitoring, impact analysis, metrics, program-to-operations comparisons, state-to-state comparisons, and support manufacturers and other stakeholders. MDP provides CMS the ability to properly oversee the Medicaid Drug Programs while providing support to drug manufacturing companies and state agencies. The MDP system will support rebate agreements administration; product, monthly pricing, and the quarterly data file submission process; state data submissions processes; drug utilization review process; drug utilization discrepancy process; and the federal upper limits process.", "version": "25", "acronym": "MDP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Pharmacy", "systemMaintainerOrg": "Division of Business Essential Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2022-07-08", "atoExpirationDate": "2025-07-07"}, {"id": "{EBE46F8F-A5F4-4673-8A4E-AF20860BF545}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{EBE46F8F-A5F4-4673-8A4E-AF20860BF545}", "uuid": "C8A43D26-9032-47A5-A77F-2AFC8883A799", "name": "MAC/CMS Data Exchange Portal", "description": "The MAC/CMS Data Exchange Portal (MDX Portal) provides CMS with a data importing tool, a centralized data repository, automated data validation, and advanced analysis and reporting capability. It allows CMS to efficiently monitor and report on the performance of the Fee for Service (FFS) Medicare Administrative Contractors (MACs) contract activities.", "version": "25", "acronym": "MDX Portal", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Medicare Contractor Management Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2023-11-16", "atoExpirationDate": "2026-11-15"}, {"id": "{ED3E4AF0-AB87-4e49-85F2-2D57F1EFD9B8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{ED3E4AF0-AB87-4e49-85F2-2D57F1EFD9B8}", "uuid": "59471325-347D-47ED-9BE9-0CB80F1B0143", "name": "Opportunity to Network and Engage", "description": "Core Function:  Collaboration and content management platform for internal and external stakeholders (Issuers, States, Federal partners, etc.)The purpose of the Opportunity to Network and Engage (zONE) system is to foster low cost, scalable, independent online communities of interest for internal CMS staff and external CMS partner stakeholders.   Each new community appoints a designated Community Manager, and thereafter the community is equipped to provide for targeted dissemination of information to the community members, independent governance and oversight, calendars and events, discussion threads, document sharing, wiki kbs, and polls.  Functionality interests and feature interests are actively collected from the community leaders, and they are prioritized, researched, and iteratively developed and released to the platform on an ongoing basis along with necessary security and software updates.  The site is available to all staff and CMS partners by way of account credentials and an End User role on the CMS Enterprise Portal, and membership and rights to private communities and content on the site are governed independently by the respective Community Managers.", "version": "25", "acronym": "zONE", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Innovation and Technology Group", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-11-17", "atoExpirationDate": "2024-11-17"}, {"id": "{EEDD7292-E3BB-47fb-8374-5B469D111EF8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{EEDD7292-E3BB-47fb-8374-5B469D111EF8}", "uuid": "5BBC4EE6-C1F7-45AA-B235-A65671B8738A", "name": "Supplemental Medical Review Contractor System", "description": "Internal systems used by Supplemental Medical Review Contractor (SMRC) primary and subcontractor employees to process, monitor, and maintain CMS-initiated medical review projects, and medical review-associated activities. \n", "version": "25", "acronym": "SMRC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Medical Review", "systemMaintainerOrg": "Provider Compliance Group", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-05-25", "atoExpirationDate": "2026-05-24"}, {"id": "{F21F0829-84F6-42d6-B663-0F2F2A508E5E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F21F0829-84F6-42d6-B663-0F2F2A508E5E}", "uuid": "5e7dce4fdbc33f400887f4d40f9619f5", "name": "Medicare Medical Review Accuracy Contractor", "description": "MRAC allows the Center for Program Integrity (CPI) to monitor the accuracy of Medical Review Contractors (MRC), medical review decision making and provides the ability to implement Medicare coverage, coding, payment, and billing policies and the identification of potential fraud, waste, or abuse. ", "version": "25", "acronym": "MRAC", "status": "", "belongsTo": "", "businessOwnerOrg": "Contract Management Group", "systemMaintainerOrg": "Division of Investigations and Audit Contracts", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-12-15", "atoExpirationDate": "2026-12-14"}, {"id": "{F235EE72-BE13-4fed-A9D4-9149E63C9D65}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F235EE72-BE13-4fed-A9D4-9149E63C9D65}", "uuid": "CB216460-BAEA-4BE5-916B-9B442F66D30A", "name": "Program Integrity Contractor <PERSON><PERSON><PERSON>", "description": "The Program Integrity Contractor assists CMS/CPI with the investigations of suspected fraud, waste, and abuse; and supports benefit integrity efforts through medical reviews, national and regional data analysis, and the referral of cases to law enforcement for consideration and initiation of civil or criminal prosecution.\nThis FISMA system represents a specific Program Integrity Contractor (PIC) which assists CMS's efforts in combating fraud, waste and abuse in Medicare and Medicaid (all claim types).  In January 2019, the remaining ZPICs were renamed. This contractor owned system was renamed from Zoned Program Integrity Contractors Zone 4 - HealthIntegrity\nto Program Integrity Contractor Qlarant.\nName changed from Zoned Program Integrity Contractors Zone 4 - HealthIntegrity  (ZIPIC-HI) to Program Integrity Contractor Qlarant( PI-Qlarant) per <PERSON>'s email dated 3/1/2019", "version": "25", "acronym": "PI-<PERSON><PERSON><PERSON>", "status": "", "belongsTo": "", "businessOwnerOrg": "Center for Program Integrity", "systemMaintainerOrg": "Center for Program Integrity", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-07-06", "atoExpirationDate": "2024-07-05"}, {"id": "{F40BE790-D8C8-42b2-A5B4-3074D994DA72}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F40BE790-D8C8-42b2-A5B4-3074D994DA72}", "uuid": "E1C44094-1044-4DF7-9B5D-1043808623D3", "name": "Unified Case Management System", "description": "The Unified Case Management (UCM) system and associated operational services supports the workload of CMS program integrity contractors across the Medicare and Medicaid programs.   The UCM system provides the capability to track leads, audits, and investigations; captures and manages workflow activities; reports workload metrics; reports status of administrative actions and referrals to law enforcement; and records outcomes or disposition of program integrity audit and investigative actions across Medicare and Medicaid programs.  UCM supports several programs including Medicare Program Integrity activities for Medicare Parts A, B, C, and D, the Medi-Medi Program, and Medicaid Audits. Core Function: * Support CMS PI activities by tracking investigations and the workload of PI contractors who investigate allegations of suspected fraud, waste, or abuse (FWA).", "version": "25", "acronym": "UCM", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Investigative Systems Management", "systemMaintainerOrg": "Division of Investigative Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-05-25", "atoExpirationDate": "2024-05-24"}, {"id": "{F7EF5EF2-02EB-4f07-94ED-978DD6D5BB02}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F7EF5EF2-02EB-4f07-94ED-978DD6D5BB02}", "uuid": "852DD1E5-E4FC-49F7-9504-E0A7A10FBC82", "name": "Medicare.gov", "description": "The Balanced Budget Act of 1997 mandated an internet site for beneficiaries.  The www.medicare.gov website was launched with mostly static content and reference information in 1998.  The site has matured to highlight 16 interactive tools which provide searchable and personalized information to beneficiaries and their caregivers so they can make informed choices about their health care. Core Function: * Beneficiary enrollment & portal* Outreach and education* Quality reporting* Customer Service", "version": "25", "acronym": "Medicare.gov", "status": "", "belongsTo": "", "businessOwnerOrg": "Web and Emerging Technologies Group", "systemMaintainerOrg": "Web and Emerging Technologies Group", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{F998759A-E3EA-47c0-BE34-EAB1730DF8D7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F998759A-E3EA-47c0-BE34-EAB1730DF8D7}", "uuid": "E8674AE3-276D-4BB3-BE96-C27D255ACA32", "name": "Health Insurance and Oversight System", "description": "The Health Insurance Oversight System (HIOS) is a web based application that allows the government to collect data from states and individual and small group market issuers, which is aggregated with other data sources and made public on a consumer facing website. One initial mechanism for the Issuers to submit their data is through the use of the HIOS Excel template. The data collected through HIOS is submitted to a third party organization for publication on the public website.  Core Function:  Allows the government to collect data from states and individual and small group market issuers, which is aggregated with other data sources and made public on a consumer facing website.", "version": "25", "acronym": "HIOS", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Innovation and Technology Group", "systemMaintainerOrg": "Division of Insurance Oversight and Transparency Application", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-03-18", "atoExpirationDate": "2025-03-18"}, {"id": "{FAFE32A4-9B81-4297-8B9D-2DDC8DCE4067}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{FAFE32A4-9B81-4297-8B9D-2DDC8DCE4067}", "uuid": "F168F3C3-3888-4151-A044-91A16F9B12C5", "name": "Benefits Coordination and Recovery Center", "description": "The business purpose of the Benefits Coordination and Recovery Center system is a call center to support BCRC contractor. The BCRC consolidates all operational activities that support the collection, management, and reporting of other insurance coverage of Medicare beneficiaries and the collection of conditional payments or mistaken primary payments outside of Group Health Plans (GHP).  The systems consist of remote access Telecom Customer Services, Correspondence Scanning, and Print & Mail.", "version": "25", "acronym": "BCRC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of MSP Program Operations", "systemMaintainerOrg": "Division of MSP Program Operations", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-01-11", "atoExpirationDate": "2026-01-10"}, {"id": "{FE9F2BD1-C7E8-4ea0-BCF6-0547A22E7E2E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{FE9F2BD1-C7E8-4ea0-BCF6-0547A22E7E2E}", "uuid": "42a1a79c1bb59850b752dbdbe54bcb45", "name": "Inquiry Management System", "description": "The Inquiry Management System (IMS) supports the Medicare Plan Payment Group to manage multiple @cms.gov mailboxes.  Utilizing the Salesforce Service Cloud, IMS provides workflow and tracking capabilities to support the inquiry management process including receipt, response, storage, and historical lookup. IMS provides the following:\n    Workflow for the mailbox management process, including receiving new inquiries automatically through the Outlook mailboxes, automatically assigning the responsible party, notifying the responsible party, and sending responses back to inquirers. \n    Increased transparency by storing responses and providing users the ability to look at inquiries based upon similar topics and by historical inquiries received by inquirers.\n    Reporting capabilities including identifying time to resolve cases, time spent on certain tasks within the workflow, aging, inquiries by user, status or category, user activity reports, etc.", "version": "25", "acronym": "IMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Encounter Data and Risk Adjustment Operations", "systemMaintainerOrg": "Division of Encounter Data and Risk Adjustment Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2021-03-10", "atoExpirationDate": "2024-03-10"}, {"id": "{54E20C2E-75FB-49ba-8C9B-E656F5780A3B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{54E20C2E-75FB-49ba-8C9B-E656F5780A3B}", "uuid": "5e85e7e91b459150c9490fe4604bcb29", "name": "Qualified Health Plan Enrollee Experience Survey", "description": "The purpose of this system is to obtain Contractors' services for the revision and administration of the QHP Enrollee Survey. The MIDS contractor in conjunction with the Center for Clinical Standards and Quality (CCSQ), the Center for Consumer Information and Insurance Oversight (CCIIO) and the Office of Communication (OC) shall revise and administrator the QHP Enrollee Survey. The QHP Enrollee Survey is used to assess the quality of the enrollees' experience with their QHPs around such areas as access to care, access to information, care coordination, cultural competence, doctor communication, and plan administration. The goals of the QHP Enrollee Survey are to:  Provide comparable and useful information to consumers about the quality of health care services and enrollee experience with QHPs offered through the Exchanges; Facilitate oversight of QHP issuer compliance with quality reporting standards set forth in the PPACA and implementing regulations; and provide actionable information that QHP issuers can use to improve quality and performance.", "version": "25", "acronym": "QHP EES", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Program and Measurement Support", "systemMaintainerOrg": "Division of Program and Measurement Support", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-02-23", "atoExpirationDate": "2026-02-22"}, {"id": "{B2365482-A1C7-4d4c-BE04-4F7021E0B466}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B2365482-A1C7-4d4c-BE04-4F7021E0B466}", "uuid": "5e3332c78746f950a0b9b9d8cebb357e", "name": "Provider Compliance Group-Comparative Billing Reports-Program for Evaluating Payments Patterns Electronic Reports", "description": "CMS protects the Medicare Trust Fund from Fraud, Waste, and Abuse (FWA) and improper payments by educating providers and analyzing data. CBRs, PEPPERs, the First-Look Analysis Tool for Hospital Outlier Monitoring (FATHOM) databases, and other ad-hoc reports assist CMS in its effort to address potential over-utilization and high-risk payment errors in the Medicare Fee-for-Service (FFS) program. Producing and delivering these reports drives change and improvement in support of protecting the Trust Fund.\nThe current CBR/PEPPER solution provides online distribution of CBR and PEPPER reports to medical providers, provides information for accessing and interpreting CBR and PEPPER reports, and provides end user support via helpdesk services.", "version": "25", "acronym": "PCG-CBR-PEPPER", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Compliance Projects and Demonstrations", "systemMaintainerOrg": "Division of Compliance Projects and Demonstrations", "state": "Never Implemented", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{5C34AEC7-21A2-4de2-8837-AC3348DAF5BE}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5C34AEC7-21A2-4de2-8837-AC3348DAF5BE}", "uuid": "626719ba878e2590fe4287b9cebb35d5", "name": "Contractor Reporting of Operational and Workload Data 2.0", "description": "The CROWD 2.0 system provides CMS with a way to monitor each Medicare contractor's performance in processing claims, as well as other activities required under contract.  The application provides the capability for the Medicare contractor to electronically submit workload data on a variety of functional areas, such as, the processing of claims, appeals, beneficiary overpayments, late interest payments, Medicare Secondary Payer activity, Provider Incentives, Participating Physician activity, etc. The submitted data, in turn, is used by CMS for estimating budgets, defining operating problems, comparing performance among Medicare contractors and determining regional and national workload trends.", "version": "25", "acronym": "CROWD 2.0", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of MAC Budget and Data Analysis", "systemMaintainerOrg": "Division of MAC Budget and Data Analysis", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{5A65B511-41C2-4b91-96A9-B9F0E50FFD1D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5A65B511-41C2-4b91-96A9-B9F0E50FFD1D}", "uuid": "BA2E9211-1373-4CD4-8BDB-00DC03AC2FE7", "name": "Enterprise Privacy Policy Engine", "description": "Enforces the controlled access to, disclosures of, transference and disposition of personally identifiable information (PII) contained in CMS Systems of Records (SOR) in accordance with the statutory mandates of the Privacy Act, HIPAA and FISMA. Additionally, EPPE will standardize and automate the Data Use Agreements (DUA) process. \nCore Function: \n* Receives and processes requests for data files from users that maintain Data Use Agreements (DUAs) with CMS regarding the nature and use of CMS\n* Maintains and administers the DUAs and tracks the requested data files as orders within its system from initial build status on out to ship status.\n* Supports the shipping of multiple data files in an order delivered outside of CMS on physical media including tape, cartridge, CDROM, DVD, etc.\n* Tracks the disclosures of personally identifiable data to external entities thru the manage DUAs functionality.\n* Allows for querying of data elements, reporting, creation of role based access.\n* Stores supporting documentation for Data Use Agreements (DUA).\n* EPPE also includes a mechanism to track data disclosures that do not require a DUA, via Non DUA Tracking functionality.", "version": "25", "acronym": "EPPE", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Information Dissemination Group", "systemMaintainerOrg": "Office of Enterprise Data and Analytics", "state": "Retired", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{413D3D67-B896-41ae-BDE7-85ACE8873F22}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{413D3D67-B896-41ae-BDE7-85ACE8873F22}", "uuid": "1a5dbdcd1ba22c10de4010ad9c4bcb55", "name": "Unified Case Management Next Generation", "description": "The Unified Case Management Next Generation (UCM-NeXgen) system and associated operational services manages the work of CMS program integrity contractors across the Medicare and Medicaid programs. The Unified Case Management (UCM- NeXGen) project: · Maintains a central repository for tracking leads, which contains all contractor workload reporting, dashboards to monitor progress, and outcome measure calculations · Establishes transparency in Medicaid and Medicare analysis, audits, and investigations workload, prioritization, and outcomes · Reduces the number of systems that require input from contractors and CMS · Strengthens national level oversight of contractor work through rapid, accurate flow of information", "version": "25", "acronym": "UCM-NeXgen", "status": "", "belongsTo": "", "businessOwnerOrg": "Data Analytics and Systems Group", "systemMaintainerOrg": "Division of Investigative Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2022-01-10", "atoExpirationDate": "2025-01-10"}, {"id": "{5C199BDF-2FF0-4654-89DC-98B9CC786376}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5C199BDF-2FF0-4654-89DC-98B9CC786376}", "uuid": "1b815a3adb770050512770131f961953", "name": "Payment Error Rate Measurement-Eligibility Review Data Collection Tool", "description": "The Payment Error Rate Measurement Eligibility Review Contractor (PERM ERC) Review System provides the ERC with an environment to review state eligibility documents and enter findings in the Eligibility Review Data Collection Tool (ERDCT). Key review information is extracted from the ERDCT system into a daily XML file that is loaded into the State Medicaid Error Rate Findings (SMERF) system. The SMERF system generates automated emails following the daily XML data transfer. These emails are sent to states and CMS to notify them of findings, additional documentation requests (ADRs), or responses to difference resolutions. The SMERF system is a separate application maintained by another CMS PERM contractor.", "version": "25", "acronym": "PERM-ERDCT", "status": "", "belongsTo": "", "businessOwnerOrg": "Payment Accuracy and Reporting Group", "systemMaintainerOrg": "Division of Payment Error Rate Measurement", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2022-07-13", "atoExpirationDate": "2025-07-13"}, {"id": "{7833361E-DB04-44fb-87A6-78D99C5C9223}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{7833361E-DB04-44fb-87A6-78D99C5C9223}", "uuid": "4eb106f71b6b49105efaa681f54bcb19", "name": "Exchange Automated IT Solution", "description": "The EIPM solution will digitize the existing manual review process that takes place today to annual review and audit Affordable Care Act applications.   Currently, an external vendor (Guidehouse) reviews a sample of these previously worked applications on an annual basis to determine if they were processed accurately.   The future system will eliminate the need for Excel spreadsheets and email to complete this process.   Automate workflow, routing, viewing of documents, and management reporting will be provided.   There will be approximately 50 internal users who will use the new solution.  The vendor review team as well as internal CMS employees will review and route the transactions around for completion. Personally Identifiable Information (PII) will be contained in the system.   The future solution will leverage Appian's FedRamp certified Gov Cloud solution and the CMS Cloud solution to process and store customer information.", "version": "25", "acronym": "EAITS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Exchange Error Rate Measurement", "systemMaintainerOrg": "Division of Exchange Error Rate Measurement", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-05-31", "atoExpirationDate": "2026-05-30"}, {"id": "{E6AB43D5-B0F3-4793-971F-F65F5AB2E787}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E6AB43D5-B0F3-4793-971F-F65F5AB2E787}", "uuid": "9063e0e48796fd50194385180cbb35b9", "name": "Medicaid and CHIP Program Integrity Reporting Portal", "description": "MCPIRP is a correspondence and communication platform handling Payment Error Rate Measurement (PERM) Corrective Action Plan (CAP)/Medicaid Eligibility Quality Control (MEQC) deliverables and noting important milestones. The goal is to streamline the processes between all stakeholders (CMS and external). It is built on the CMS Salesforce Enterprise Integration (SEI) system.\r\n\r\n", "version": "25", "acronym": "MCPIRP", "status": "", "belongsTo": "", "businessOwnerOrg": "Audits and Vulnerabilities Group", "systemMaintainerOrg": "Enterprise Systems Solutions Group", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{DEFDCDC4-A837-4cf4-879B-14C74C0CD74F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{DEFDCDC4-A837-4cf4-879B-14C74C0CD74F}", "uuid": "3f0d8ce71b02a990c9490fe4604bcb06", "name": "Enterprise Automation Environment", "description": "In order to expand the access and adoption of process automation at CMS, the Process Automation Center of Excellence (COE) is configuring a CMS Cloud AWS environment to host the UiPath Automation Suite software. UiPath consists of UiPath Studio, which is the core development platform, and various other products that can enhance the process automation development and operational experience. This environment would be managed through a partnership between OSPR and OIT and would host various “bots” or automated processes that have been developed and used across various CMS component processes. The environment will contain virtual machines that interact as “digital” users with the systems specific to each process that is hosted.This environment would provide cost benefits, as it would reduce the need for individual UiPath contracts from CMS components and would provide CMS leadership with an Enterprise view of automations at CMS. It would also standardize and centralize automation security management and operations and would increase the availability of process automation, thus increasing the various benefits that process automation provides.</br></th>", "version": "25", "acronym": "COE EAE", "status": "", "belongsTo": "", "businessOwnerOrg": "Office of Strategy, Performance and Results", "systemMaintainerOrg": "Office of Strategy, Performance and Results", "state": "Active", "businessOwnerOrgComp": "OSPR", "systemMaintainerOrgComp": "OSPR", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{F1BC93AE-EC85-4b3d-8582-DCAEA7F9511E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F1BC93AE-EC85-4b3d-8582-DCAEA7F9511E}", "uuid": "49D80675-3CD8-4A6A-A608-845C01CCC5A3", "name": "Eligibility Worker Support System", "description": "Serco Inc. has been tasked by CCIIO to build and operate an Eligibility Support (ES) function to intake and process paper applications for health care coverage under the Health Insurance Marketplace sent via US mail and provide eligibility support.  Applications will be sorted, scanned, processed, and entered into the Eligibility Support Desktop (ESD).  ESD is a function of the Federally Facilitated Marketplace hosted in the CMS Terremark Private Cloud Environment.  When consumers submit their eligibility applications for health coverage, their documentation will enter the ESD via one of two processes. Paper applications will enter the mailroom, catalogued, scanned into the system, and then securely retained until processing is completed.  Then, the information on the paper applications will be processed by Serco Eligibility Support Workers (ESWs) through a basic and complex eligibility process to determine the consumer's eligibility.  Additionally, Serco receives completed fillable PDF applications via secure ftp (sftp) download, imports these into the same processing system whereupon they are treated like paper applications. Files transferred (both uploads and downloads) will be protected through a file zip/encryption process (SecureZip) to FIPS140-2 standards.", "version": "25", "acronym": "EWSS", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Eligibility and Enrollment Group", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-10-29", "atoExpirationDate": "2024-10-29"}, {"id": "{05789675-B1DB-4eee-8BC5-6826588410CB}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{05789675-B1DB-4eee-8BC5-6826588410CB}", "uuid": "0E656148-5DFB-4F10-A8EC-972FE4122EC9", "name": "Innovation Center", "description": "The Innovation Center web application will provide a single point of entry to access CMMI applications by CMMI users including the CMMI Program Team, CMMI Support Contractor for Application Administration, Helpdesk Administrator, Operational Contractor, CMMI Model Participants (Providers), and their delegates. Innovation Center will serve as a common Landing Page where CMMI applications can be hosted for easier access. Innovation Center will utilize the Enterprise Identity Management (IDM) and the Enterprise Portal Shared Services and implement the CMS approved Technical Reference Architecture. The Innovation Center will allow the addition of new health innovation models thereby reducing the time and effort to make a new model operational. This is simply the landing page for CMMI on the Enterprise Portal. Core Function: * Innovation Center will serve as a common Landing Page where CMMI applications can be hosted for easier access.* Innovation Center will utilize the Enterprise Identity Management and the Enterprise Portal Shared Services and implement the CMS approved Technical Reference Architecture. * The Innovation Center will allow the addition of new health innovation models thereby reducing the time and effort to make a new model operational. This is simply the landing page for CMMI on the Enterprise Portal.", "version": "25", "acronym": "IHP", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Systems Support, Operation, and Security", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2021-07-16", "atoExpirationDate": "2024-07-16"}, {"id": "{F2BE8788-C2E0-4230-9C11-3A1BEFB04579}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F2BE8788-C2E0-4230-9C11-3A1BEFB04579}", "uuid": "CD15B586-0B74-4625-94EE-A74A9D0DB12E", "name": "CM - C2C Innovative Solutions Inc.", "description": "As a Qualified Independent Contractor (QIC), C2C provides the Centers for Medicare and Medicaid Services (CMS) adjudication services in support of the coverage determinations, reconsiderations, and appeals requirements set forth at section 1852(g) of the Social Security Act (the Act), as amended by Title II of the Medicare Prescription Drug, Improvement, and Modernization Act of 2003 (MMA) (Pub. L. 108-173).  C2C also serves as the national independent entity to resolve payment disputes between Medicare Advantage (MA) organizations, Private Fee for Service (PFFS) plans and deemed and non-contracted providers.", "version": "25", "acronym": "CM-C2C", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Enrollment and Appeals Group", "systemMaintainerOrg": "Medicare Enrollment and Appeals Group", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2022-03-22", "atoExpirationDate": "2025-03-25"}, {"id": "{F84F4974-DC67-4578-9BF5-36D402915008}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F84F4974-DC67-4578-9BF5-36D402915008}", "uuid": "BC3011C5-925A-4283-9D1A-BDB09B10B7B1", "name": "Eligibility Support Desktop Change Utility Tool", "description": "Eligibility Support Desktop Change Utility Tool (ESDCU) is a tool for making updates to Federally Facilitated Marketplace (FFM) eligibility and enrollment record(s) in support of case work (appeals, eligibility support, etc.) and data clean up activities. Such updates currently cannot be made within FFM itself. ESDCU was developed to allow for off line capturing/editing of information and submission back into FFM for effectuation.  Core Function: * Support retrieval and display of information from FFM and Appeals resolution process involving FFM.", "version": "25", "acronym": "ESDCU", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of HUB Data and State Support", "systemMaintainerOrg": "Division of HUB Data and State Support", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-12-13", "atoExpirationDate": "2024-12-13"}, {"id": "{E92497AB-D3AF-4be8-AF4A-C945EEE0FFE7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E92497AB-D3AF-4be8-AF4A-C945EEE0FFE7}", "uuid": "84a1765edb489090c9f1e4491396191c", "name": "Enterprise Data Mesh", "description": "CMS, at an enterprise level, has multiple applications across several departments that consume and generate various types of data. The data is structured and un-structured and originates from a variety of data sources. Several applications have this data stored in a traditional relational database, a data warehouse or even in flat files at various data centers. As the need of utilizing these data sets is ever increasing, several applications within CMS end up replicating multiple data sources which perpetuates data inefficiency, duplication, consistency, quality, and increased costs for associated infrastructure. This project will enable CMS to stand up a cloud based enterprise wide data lake with a central common Metadata catalog that would allow IT systems to consume data at source using segregation of storage and compute technologies, avoid IT system redundancies and create operational efficiencies. The enterprise data lake capability will also enable next generation of analytics with machine learning and Artificial Intelligence technologies while optimizing investments in data warehouses, databases and data reporting systems.", "version": "25", "acronym": "EDM", "status": "", "belongsTo": "", "businessOwnerOrg": "Office of Information Technology", "systemMaintainerOrg": "Enterprise Architecture and Data Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-07-29", "atoExpirationDate": "2024-07-29"}, {"id": "{D26DFC38-9F52-4807-B05B-46BAACA316F9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{D26DFC38-9F52-4807-B05B-46BAACA316F9}", "uuid": "********-6177-427A-B4C5-2A9186D117E5", "name": "Accountable Care Organization Management System", "description": "The Accountable Care Organization - Management System (ACO-MS) allows CMS to collect information from Accountable Care Organizations (ACOs) participating in the Shared Savings Program (SSP), and assists ACOs in the management of their ACO agreement. The ACO-MS will be used to support agreement enumeration and management, application submission and tracking, participant and affiliate list management activities, as well as the annual certification process. Examples of the various components of the application cycle supported include executing and maintaining the ACO agreement and contacts, the participant list, and the skilled nursing facility (SNF) affiliate list.\r\nThe ACO-MS system also supports the web application front for paper application processes called MEARiS™ - Medicare Electronic Application Request Information System™. This application allows external organizations to replace their paper submissions for the related application, and use a web interface to make a request to the government related to a medical coding, medical classification, or payment policy.\r\n(HPT)- Each hospital operating in the United States will be required to provide clear, accessible pricing information online about the items and services they provide in two ways:\r\n- As a comprehensive machine-readable file with all items and services.\r\n- In a display of shoppable services in a consumer-friendly format.\r\nThis information will make it easier for consumers to shop and compare prices across hospitals and estimate the cost of care before going to the hospital. This system will help CMS audit hospitals for compliance, in addition to investigating complaints that are submitted to CMS and reviewing analyses of non-compliance.", "version": "25", "acronym": "ACO-MS", "status": "", "belongsTo": "", "businessOwnerOrg": "Performance-Based Payment Policy Group", "systemMaintainerOrg": "Performance-Based Payment Policy Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2023-11-01", "atoExpirationDate": "2026-10-31"}, {"id": "{********-FAFE-499a-B4DA-5454C1A0429F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{********-FAFE-499a-B4DA-5454C1A0429F}", "uuid": "798bbe59db697300f99cfd721f96199f", "name": "Expanded Data Feedback Reporting", "description": "Center For Medicare And Medicaid Innovation (CMMI) Expanded Data Feedback Reporting (EDFR) system will provide transparency to stakeholders including the primary care physicians participating in CMMI Alternative Payment Models (APM) in the form of feedback for healthcare being delivered from a utilization, cost, and quality perspective.  The system will render the information visually on dashboards, and reports.", "version": "25", "acronym": "EDFR", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Technology Solutions", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2023-12-06", "atoExpirationDate": "2026-12-05"}, {"id": "{ECF8B3F6-0420-4107-8E2E-FEBE7C39AE73}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{ECF8B3F6-0420-4107-8E2E-FEBE7C39AE73}", "uuid": "8666DE88-1BAD-4272-A600-147F61172561", "name": "Master Data Management System", "description": "Description: Master data management (MDM) produces an integrated, trusted master version of the overlapping, redundant, and inconsistent data from various Medicare and Medicaid systems. To create this master version, MDM uses identity resolution to recognize distinct entities (e.g., a provider, beneficiary, organization, or program) by matching similar records across multiple data sets. MDM provides flexible and easy access to Provider and Beneficiary data through extracts, web services, and APIs.MDM is an authoritative System of Reference that integrates and stores data from CMS operational Systems of Record. The MDM data is considered to be as authoritative as the data in the source System of Record from which it is obtained.Master Data Management (MDM) is one of CMS's Enterprise Shared Services (ESS) that provides authoritative data integrated and consolidated from various source systems. MDM helps address business challenges by linking data across multiple sources for various business needs.  MDM provides:•Trusted Identity Resolution capability for CMS' expanding catalog of Master Data sources.•Integration and consolidation of Master Data from numerous disparate data sources to create a consolidated, authoritative view of each entity.•Flexible and easy access to Master Data (e.g., near real-time web services, extracts for batch processing, APIs, and analytic capabilities).•A platform for improved data quality and understanding. Core Function: * Identity Resolution of CMS enterprise data entities (Providers, Beneficiaries, Enrollees, ACO model participation)* Federation of identity resolved CMS enterprise data", "version": "25", "acronym": "MDM", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-09-28", "atoExpirationDate": "2026-09-27"}, {"id": "{D97136E5-E071-44ee-983B-EC9EFD053183}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{D97136E5-E071-44ee-983B-EC9EFD053183}", "uuid": "50CF5C69-FB39-4957-B386-859DBA29B16D", "name": "Electronic Submission of Medical Documentation", "description": "The Medicare Fee-For-Service (FFS) Program pays billions in improper payments each year. CMS employs several types of Review Contractors (RC) to prevent and to identify, measure, and correct these improper payments. The RCs request medical documentation by sending Additional Documentation Request (ADR) letters to providers. Over 2 million ADRs are sent annually by the RC to identify the improper payments by selecting a small sample of claims, requesting supporting medical documentation from the providers who submitted the claims, and manually reviewing the claims against the medical documentation to verify the provider's compliance with Medicare's rules. In the past, providers had only two options for submitting the requested documentation, which were by mail or fax. Since September 2011, the Electronic Submission of Medical Documentation (esMD) system has provided an additional option to electronically send ADR responses with medical documentation to the requesting RC. Within CMS, the Center for Program Integrity (CPI) protects the Medicare & Medicaid programs from fraud, waste, and abuse. CPI is the esMD business owner. The esMD system also aligns with CPI's goals and objectives by increasing the electronic exchange of medical documentation resulting in administration simplification/paperwork reduction and reducing improper payment rates.\nesMD is a web portal or gateway that facilitates the secure exchange of medical documentation. To use esMD, a provider finds out if their RC accepts esMD transactions and then initiates the esMD onboarding process by first obtaining access to an esMD compatible gateway. Providers have the option of either building their own compatible gateway or using a certified Health Information Handler (HIH) with an esMD compatible gateway to serve as their representing agent. The esMD system migrated to CMS AWS cloud environment in 2023 and provides Rest API's to RCs to download the claim packages securely. esMD follows the CMS Expedited Life Cycle process and presents to the Technical Review Board (TRB) for approval prior to any major functionality being added. The last TRB approval for esMD Cloud was in July 2022.\nesMD supports the reduction of improper payments on claims processing, medical reviews, and audits related to the Medicare FFS program. The esMD system supports the following CMS lines of business and Prior Authorization (PA) programs: Responses to Additional Documentation Request (ADR) letters; First Level Appeal Requests; Second Level Appeal Requests; Advanced Determination of Medical Coverage Requests; Recovery Auditor Discussion Requests; Electronic Medical Documentation Requests (eMDRs); Review Results Letters(RRL); ADR Review Results; Consolidated Clinical Documentation Architecture (Structured Medical Documentation); Internal Contractor Document Transfer; PA Requests/Responses; Repetitive Scheduled Non-Emergent Ambulance Transport of Durable Medical Equipment Prosthetics, Orthotics and Supplies; Hospital Outpatient Department Services; Review Choice Demonstration for Home Health Services.", "version": "25", "acronym": "esMD", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Compliance Projects and Demonstrations", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-11-05", "atoExpirationDate": "2024-11-05"}, {"id": "{FE44CFAB-51CB-4650-A13C-87D3C2541DEB}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{FE44CFAB-51CB-4650-A13C-87D3C2541DEB}", "uuid": "34443a7a1b4c9550f9958512f54bcb0b", "name": "batCAVE - Continuous Authorization and Verification Engine", "description": "batCAVE is being developed to provide a shared development environment for CMS Application Development Organizations (ADOs). This provides a robust Platform-as-a-Service (PaaS) environment that maximizes security control inheritance, shifts security left via security tooling and pipeline automation, facilitates emerging GitOps practices and ultimately drives down ADO costs while expediting the delivery of value for CMS stakeholders.  The project is led by <PERSON> with support from Revacomm, Aquia, Clarity, Oteemo, Fearless, and Vivsoft.", "version": "25", "acronym": "batCAVE", "status": "", "belongsTo": "", "businessOwnerOrg": "Information Security and Privacy Group", "systemMaintainerOrg": "Information Security and Privacy Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-10-28", "atoExpirationDate": "2025-10-28"}, {"id": "{DE27B998-72DE-400f-BB0A-4D04F371F23D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{DE27B998-72DE-400f-BB0A-4D04F371F23D}", "uuid": "8F03F554-A4D1-4DD2-BC99-B060C7BE08AF", "name": "Reusable Framework", "description": "The business purpose of the Reusable Framework is to identify and automate the common business functions of CMMI Models under a reusable framework. This should result in standardizing the common functionality across all Models, promoting reuse, substantially reducing costs and faster time to market and testing of CMMI Models. Core Function:  *Participant management*To collect data (quality measure, beneficiary) data from model participants*To provide data (claims, financial, performance results, etc.) to model participants", "version": "25", "acronym": "RFX", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Systems Support, Operation, and Security", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2021-12-15", "atoExpirationDate": "2024-12-15"}, {"id": "{EDF2968A-FB6D-4cbc-A277-ABBA3E29A6DC}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{EDF2968A-FB6D-4cbc-A277-ABBA3E29A6DC}", "uuid": "f807765e1b8ed510191fa682604bcbc9", "name": "Qualified Entity Certification Program CRM System", "description": "The CMS Qualified Entity (QE) Program (also known as the Medicare Data Sharing for Performance Measurement Program) enables organizations to receive Medicare claims data under Parts A, B, and D for use in evaluating provider performance. The Qualified Entity Certification Program (QECP) is the certification arm of the Medicare Data Sharing Program. The purpose of the QECP is to evaluate and certify an entity's ability to serve as a QE. Each of these entities goes through a multi-phased process where they are evaluated based on their clinical data, ability to securely store their data, and other factors. Once approved, the entities must also adhere to the rules of the program by providing certain reports on at least an annual basis and reapplying to maintain their qualified status. To accommodate these activities, OEDA uses a Salesforce Customer Relationship Management (CRM) cloud-based application since June of 2019. With the implementation of the CRM, the objective was to create a state-of-the-art application to increase the overall number of entities participating in the program by improving the application and reapplication process, enhancing data collection activities to improve self-help tools for users, and provide better reporting and analytics for OEDA.", "version": "25", "acronym": "QECP CRM", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Information Dissemination Group", "systemMaintainerOrg": "Data and Information Dissemination Group", "state": "Active", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": null, "atoExpirationDate": null}, {"id": "{DFBD7515-61C8-43ec-A616-A209DC692619}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{DFBD7515-61C8-43ec-A616-A209DC692619}", "uuid": "63B87163-4F27-4594-9A16-CAC9DD3E43E1", "name": "COMPASS WEB", "description": "Livanta LLC (Livanta) is a Beneficiary and Family Centered Care Quality Improvement Organization (BFCC - QIO) that contracts with CMS to perform reviews of Medicare beneficiary appeals and complaints about healthcare treatments, reviews of the quality of care, and Medicare providers. Livanta is one of two BFCC-QIOs in the US and manages Regions 2, 3, 5, 7, and 9, which consist of 27 states and US territories. CompassWeb is the internal database application/system used by Livanta to support those services. CMS relies on QIOs to improve the quality of healthcare for all Medicare beneficiaries. QIOs are authorized under Title XI Part B, Title XVIII, and Sections 1152-1154 of the Social Security Act. The QIO Program is an important resource in CMS’s effort to improve the quality and efficiency of care for Medicare beneficiaries by expeditiously addressing individual Medicare beneficiary complaints, provider-based notice appeals, violations of the Emergency Medical Treatment and Labor Act (EMTALA), and other Medicare beneficiary concerns outlined within the QIO regulations/law.\n\n", "version": "25", "acronym": "COMPASS WEB", "status": "", "belongsTo": "", "businessOwnerOrg": "iQuality Improvement and Innovation Group", "systemMaintainerOrg": "iQuality Improvement and Innovation Group", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-10-17", "atoExpirationDate": "2026-10-16"}, {"id": "{E14D40E5-43EC-494e-9AD7-1309B18FC52B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{E14D40E5-43EC-494e-9AD7-1309B18FC52B}", "uuid": "61E3D5B7-0C4F-495B-9A9F-EA95B3B3A80C", "name": "FFM Eligibility Appeals Support", "description": "FEAS is a General Support System (GSS) that provides the technology infrastructure to support business processes outside of the CMS’ Eligibility Appeals Case Management System (EACMS). It is made up of hardware and software that the Eligibility Appeals Operation Support (EAOS) staff use to perform the functions of the program.", "version": "25", "acronym": "FEAS", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Appeals Group", "systemMaintainerOrg": "Marketplace Appeals Group", "state": "Active", "businessOwnerOrgComp": "OHI", "systemMaintainerOrgComp": "OHI", "atoEffectiveDate": "2021-10-28", "atoExpirationDate": "2024-10-28"}, {"id": "{EFBEFCDC-5AA1-4fcf-8008-8342C9296A6B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{EFBEFCDC-5AA1-4fcf-8008-8342C9296A6B}", "uuid": "595C83B9-B220-4BC0-9B12-3C84B882930E", "name": "Marketplace Electronic Data Interchange", "description": "The Federal Data Services Hub (Federal DSH) receives enrollment files from the Federally Facilitated Marketplace (FFM) and makes them available to the Edifecs Electronic Data Interchange (EDI) tool for daily processing.  EDI team uses Edifecs Specbuilder component which is a design time tool, to create mappings for translation of X12 to XML and vice versa. Edifecs XEngine is used to validate the Health Insurance Portability and Accountability Act (HIPAA) inbound/outbound transactions. XEServer processes a workflow to orchestrate the desired translation and validation of transactions received from partners. Edifecs Enrollment Management is used to maintain the trading partnerships/agreements along with the full transaction audit.The Edifecs Enrollment Reconciliation Repository (ENR) solution, which is part of the EDI suite, is used for weekly audits/reconciliation processing between the Federal DSH and the FFM, and monthly audits/reconciliation processing between the Federal DSH and Issuers/SBMs. Core Function: * Eligibility & Enrollment transactions to/from Issuers.* X12 transactions in support of FFM.", "version": "25", "acronym": "MPEDI", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of HUB Data and State Support", "systemMaintainerOrg": "Division of HUB Data and State Support", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-02-04", "atoExpirationDate": "2025-02-04"}, {"id": "{CDA037FC-63ED-43d7-8ECB-F16352592A49}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{CDA037FC-63ED-43d7-8ECB-F16352592A49}", "uuid": "E0A1EB5B-CE52-4F0C-AD6B-F9B22B6CF4C5", "name": "CCIIO Customer Relations Management System", "description": "The CCIIO Customer Relations Management System (CCRMS) was created to support customer service efforts related to back office functions of the ACA and the Marketplace. CCRMS is comprised of different sub-systems including RICS, RARI, MATS, and SPIDR.\nRe-Insurance Contribution System (RICS)\nThe system receives and processes inquiries from plan sponsors, tracks the inquiry progress through to resolution and response.\nThe system ingests data from www.pay.gov for the remittance of Reinsurance Contributions and related discrepancies.\nRisk Adjustment and Re-Insurance (RARI)/ Risk Adjustment Data Validation (RADV)/Vendor Management (VM)\nThe system receives and processes inquiries from organizations that participate in the Marketplace. The system collects and tracks ancillary information about the companies and related EDGE Server data that participate in the Marketplace and wish to receive RA or RI payments; for example, baseline data, discrepancy reports, ACA financial appeals, contact data, etc. in support of various project. The system brings together the processes of intaking and responding to questions, hosting a library with program information, and intaking and completing audit processes.\nMarketplace Assisters Technical Support (MATS)\nThe system collects and processes inquiries from Marketplace Assisters and organization that support to consumers in completion, enrollment and eligibility assistance.\nSystem Plan and Issuer Data Reporting (SPIDR)\nSPIDR is a platform that provides automation to facilitate continuous process improvement to support the highly complex operations related to the design, display, certification, and management of qualified health plans.\nCollects, evaluates, and certifies Quality Health Plans (QHPs) from Issuers.\nFacilitates the collection of data from Issuers in order to evaluate and certify plans, including QHPs and Stand-Alone Dental Plans (SADPs). The data is collected, validated and stored within the SPIDR system.\nAllows users to access their Issuer/Plans and Case information configured to meet the CCIIO needs for managing and reporting on Issuer and Plan Management activities.", "version": "25", "acronym": "CCRMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Payment Policy and Financial Management Group", "systemMaintainerOrg": "Consumer Support Group", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-12-13", "atoExpirationDate": "2025-12-12"}, {"id": "{075374BF-7C5E-4d00-8262-3CBA64D4F14D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{075374BF-7C5E-4d00-8262-3CBA64D4F14D}", "uuid": "0fec53ca1b759190259265b3604bcbdc", "name": "Agent Broker Registry", "description": "Business Purpose: The FFM Agent and Broker Registry supports the registration and training process for agents and brokers seeking to participate in the FFMs. This includes operational support to ensure that the registration for agents and brokers can be accurately completed for each applicant, on a timely basis. There are currently over 100K Agent Broker records total (past and present).\r\nWho is involved in its use: CMS and CMS Contractors (ASG) who are managing the Agent Broker Registry.\r\nTypes of information involved in the processes: Agent Broker Personal contact data, Business Profile data, Licensure, Line of Authority etc. related information.", "version": "25", "acronym": "ABR", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Strategic Stakeholder Engagement and Operations", "systemMaintainerOrg": "Division of Strategic Stakeholder Engagement and Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2024-01-02", "atoExpirationDate": "2027-01-01"}, {"id": "{CF3016D7-39B3-41df-9C65-713A487632B8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{CF3016D7-39B3-41df-9C65-713A487632B8}", "uuid": "4b8befa61be9cd109f60859ce54bcb09", "name": "Statistical Analytical Software Enterprise Business Intelligence Cloud Platform", "description": "The SAS Enterprise Business Intelligence Cloud Platform provides CMS business systems with business analysis and reporting capabilities. It is used to aid CMS in detecting fraud and abuse, analyze trends in enrollment, claims and eligibility, and analyze the effectiveness of the MMA program by allowing the CMS employees and managers access to BI reports, data, and MMA program indicators.", "version": "25", "acronym": "SAS EBICP", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-11-03", "atoExpirationDate": "2025-11-03"}, {"id": "{B82CF38B-674E-4c1e-899D-0C8CB282F5FB}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B82CF38B-674E-4c1e-899D-0C8CB282F5FB}", "uuid": "EB9FA857-4933-4718-A52D-EBACB283D971", "name": "CMS SharePoint / CAPMS", "description": "CMS SharePoint is the implementation of MS SharePoint 2019 for the agency Supporting project management, implementing a consolidated document repository and a reporting and collaboration tool for operational and executive management. Additionally, provides an environment that allows collaboration between components, groups, divisions, and project teams as well as use to automate daily routines and workflow processes.  \nCore Function: \n* Document collaboration\n* Enterprise Content Management Capabilities\n* Control documents through detailed, extensible policy management\n* Centrally store, manage, and access documents across the enterprise\n* Simplify Web content management\n* Streamline everyday business activities", "version": "25", "acronym": "CMS SP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enterprise Services", "systemMaintainerOrg": "Division of Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-04-08", "atoExpirationDate": "2025-04-08"}, {"id": "{D0994F91-DE8C-47c6-84C9-E44F2CA070F4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{D0994F91-DE8C-47c6-84C9-E44F2CA070F4}", "uuid": "7204DD20-9139-4D15-869D-98313F1670F7", "name": "Quality Improvement and Evaluation System", "description": "The Quality Improvement and Evaluation System (QIES) supports the collection, analysis, and reporting of provider and beneficiary specific outcomes of care and performance data across a multitude of delivery sites for use in improving the quality and cost effectiveness of services provided by the Medicare and Medicaid programs. QIES consists of databases housed in the States and at CMS with direct access for Quality Improvement Organizations through QualityNet (the WAN that supports both the QIO SDPS system and QIES). QIES supports these organizations in the following ways:  CCSQ uses quality functionality, CMM uses payment functionality, and DEHPG uses Medicaid survey functionality.  These business owners determine what type of data QIES collects. The Automated Survey Processing Environment (ASPEN) is also part of QIES, supporting the Survey and Certification program requirements.  Core Functions include health provider certification, survey, complaint investigation, and enforcement. Core Function:  QIES supports collection, analysis, and reporting of provider and beneficiary specific outcomes of care and performance data across delivery sites for use in improving the quality and cost effectiveness of services provided.  It provides software for state and federal surveyors to schedule surveys, collect and track survey results, complaint investigations and enforcement activities.  * QIES provides software for nursing homes, home health agencies, inpatient rehab facilities, and swing bed hospitals to collect assessment data on residents/patients; creates a file to be submitted to CMS.  It is the staging area for assessment data from nursing homes, home health agencies, inpatient rehab facilities, and swing bed hospitals is edited and validation reports are made available.  CASPER (Certification And Survey Provider Enhanced Reporting) offers a standard, pre-determined reports library that runs against the QIES national assessment and survey database and QBIC.  * QIES makes assessments viewable for case review depending on user rights.  In addition, QIES collects census data from Nursing Homes via the Payroll Based Journaling application to ensure compliance with staffing resource standards.", "version": "25", "acronym": "QIES", "status": "", "belongsTo": "", "businessOwnerOrg": "Quality, Safety, and Oversight Group", "systemMaintainerOrg": "Division of Quality Systems for Assessments and Surveys", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-07-12", "atoExpirationDate": "2025-07-12"}, {"id": "{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{BBFDF1DC-97C1-4b29-8BAB-A20B5C8B2481}", "uuid": "c85efeb4dbb43b40c029388d7c9619a1", "name": "CMS Enterprise Data Analytics Repository", "description": "The CEDAR system allows the OIT/EADG to perform EA analysis in support of CMS' planning and critical initiatives. CEDAR provides Business Architecture analytical functions such as value stream mapping, business capability maps and models, line of sight from business strategy to technical components and resource utilization, What-If analysis, and performance measurement.\r\nThe tool supports agency activities such as business architecture modeling, investment analysis and strategic alignment support. Additionally, it is the main repository of the Division of Enterprise Architecture's (DEA) portfolio in evaluating the business activities and data elements required to execute and manage the CMS Strategy.", "version": "25", "acronym": "CEDAR", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Enterprise Architecture", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-10-28", "atoExpirationDate": "2025-10-28"}, {"id": "{F1171EC8-22CB-44d6-9ECE-F6A06DA16798}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{F1171EC8-22CB-44d6-9ECE-F6A06DA16798}", "uuid": "BD21CBA0-96D3-4718-84B0-B07DEBA02A79", "name": "CMS Administrative Technology Solutions", "description": "CMS Administrative Technology Solutions (CATS) system automates administrative and business operations processes for CMS employees and managers.  CATS is hosted on the CMS intranet, and internally developed and maintained by IT Specialists in CMS/COO/OIT/IUSG/DASM. Role-based system, access to specific modules are dependent upon the CMS employees' position indicator which is received biweekly from the Department of Health and Human Service's Personnel (EHCM) system.   Each module has a Business Owner/Subject Matter Expert (SME) to ensure the module is accurate and up to date with current information.  The Business Owner also maintains the policy for the relative module. Primary CATS Modules include:  Performance Management Appraisal Program,  CMS Workplace Flexibilities Program,  COVID Vaccination Verification, Ethics eFile, CMS e129 Exit Clearance, FMFIA/FSTARS, Resource Tracking and Analysis (RTAS), Customer Service, Detail Registry, My Employees, My Manager, Non Competitive Resume Tracker, OTIS Union Time Tracking, OEOCR PWH Tracking, PD Generator, Personnel Reporting, and Workforce Planning. Core Function: * Tasks or modules within CATS support the following business functions:  Aspiring Leaders Program, Contractor Security Attestation, COVID Vaccination Attestation, Customer Service, Ethics eFile,  Executive Officer Listing, CMS Telework, Delegations of Authority, FMFIA/FSTARS, Intra-Agency Rotation Program, Furlough Actions, Intra-Agency Rotational Program, Emergency Response Clearance, My Employees, My Manager, Executive Officers, Non-Competitive Resume Tracker, PD Generator, Union Time Tracking, Performance Management Appraisal Program, Personnel Reporting, Prevention of Workforce Harassment, HR Snapshot, Resource Tracking and Analysis (RTAS),  and Workforce Planning.", "version": "25", "acronym": "CATS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Administrative Systems Management", "systemMaintainerOrg": "Division of Administrative Systems Management", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-05-17", "atoExpirationDate": "2025-05-17"}, {"id": "{A24DC808-2140-48aa-8CB0-4956B3E2F6C4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A24DC808-2140-48aa-8CB0-4956B3E2F6C4}", "uuid": "297D2A5A-**************-5B118E8490E9", "name": "HIPAA Eligibility Transaction System", "description": "The HIPAA Eligibility Transaction System (HETS) is intended to allow the release of eligibility data to Medicare Providers, Suppliers, or their authorized billing agents for the purpose of preparing an accurate Medicare claim, determining Beneficiary liability or determining eligibility for specific services.  Core Function: HETS provides Medicare Beneficiary  Eligibility information to Medicare Fee for Service providers, HMOs and Third party clearing houses. HETS receives 270 transaction requests from TCP/IP(DNS), SOAP, or MIME connections and returns a 271 transaction responds.  The HETS Desktop (HDT) provides the tools for the help desk to analyze the system operations. HETS is a system that responds to 270 requests from other systems, not individual users.", "version": "25", "acronym": "HETS", "status": "", "belongsTo": "", "businessOwnerOrg": "Provider Communications Group", "systemMaintainerOrg": "Division of Mid-Tier Applications Management", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-12-02", "atoExpirationDate": "2024-12-02"}, {"id": "{DB6B63AA-BC3A-47c9-8B45-2D8F8B6048A4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{DB6B63AA-BC3A-47c9-8B45-2D8F8B6048A4}", "uuid": "58221C5A-AA8C-46B5-9424-8CD2CA43F729", "name": "Survey and Certification and Clinical Laboratories Improvement Amendments Act", "description": "The On line Survey and Certification/Clinical Laboratory Improvement Amendment (SC/CLIA) Budget System supports the CMS Center for Clinical Standards and Quality (CCSQ) Survey and Certification Group (SCG) programs to capture expenses incurred by the State Agencies for survey activities. The data collected is used for budgeting and estimated future funds needed by the States. The reports generated are used to ensure efficient use of funds by the State agencies in addition to answering numerous inquiries from Congressional staff and outside interests, as well as questioning the costs of various surveys. Core Function: * The Survey and Certification/Clinical Laboratory Improvement Amendment (SC/CLIA) program is administered by the Centers for Medicare and Medicaid Services (CMS) under the Department of Health and Human Services (HHS).  * The Social Security Act (SSA) mandates the establishment of minimum health and safety standards, which must be met by providers and suppliers participating in the Medicare and Medicaid programs.  CMS requires the State agencies to perform surveys to ascertain whether providers meet applicable requirements for participation in the Medicare and Medicaid programs, and to evaluate performance and effectiveness in rendering a safe and acceptable quality of care.  CMS reimburses the State agencies for the costs incurred for such surveys. CMS requires the State agencies to submit the following to assist in determining their annual budgets/workload and capture the costs incurred for their survey activity.  Central Office and Regional Offices review and maintain these forms for budget activities:*CMS 435   State Survey Agency Budget/Expenditure Report*CMS 434   State Survey Agency Certification Workload Report*CMS 1465A   State Agency Budget List of Positions*CMS 1466   State Agency Schedule for Equipment*CMS EST ? State Agency Estimate of ExpendituresCMS requires the State agencies to submit the following to assist in determining their annual budgets/workload and capture the costs incurred for their survey activity.  Central Office and Regional Offices review and maintain these forms for budget activities:*CMS 102   CLIA Budget/Expenditure Report*CMS 105   CLIA Planned Workload Reports*CMS 1466   CLIA Schedule for Equipment Purchases*CMS 1465A   CLIA State Agency Budget List of PositionsThe on line Survey and Certification/Clinical Laboratory Improvement Amendment (SC/CLIA) Budget System is used by the Center for Clinical Standards and Quality (CCSQ) Survey and Certification Group (SCG) to capture expenses incurred by the State Agencies for survey activities.  The data collected will be used for budgeting and estimated future funds needed by the States.  The reports generated will be used to ensure efficient use of funds by the State agencies in addition to answering numerous inquiries from Congressional staff and outside interests, as well as questioning the costs of various surveys.   Ongoing SC/CLIA system maintenance and enhancements will keep the system current and in compliance with changing Medicaid legislation.", "version": "25", "acronym": "SC-CLIA", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Clinical Laboratory Improvement and Quality", "systemMaintainerOrg": "Division of Quality Systems for Assessments and Surveys", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2021-07-02", "atoExpirationDate": "2024-07-02"}, {"id": "{C22EA5EF-C80C-4c53-9E0F-5FDD4AEED80E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C22EA5EF-C80C-4c53-9E0F-5FDD4AEED80E}", "uuid": "e1ee3356db4ac450f3cc7e0e0f961926", "name": "Research Accessible Products Innovation and Deployable Solutions", "description": "The purpose of the Research Accessible Products Innovation and Deployable Solutions (RAPIDS) environment will be to explore and conduct pilots focused on improving and expanding Section 508 accessibility innovations. The environment will be used to explore assistive technologies, such as screen readers and text-to-speech software, as well as inclusive design practices  that make products and services more usable for people with disabilities.  The results will be used to inform CMS leadership, technical leads, and ADOs across the agency. \r\n\r\nSee the field \"Additional Comments\" for further information regarding RAPIDS.\r\n\r\n", "version": "25", "acronym": "RAPIDS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Investment Oversight and Governance", "systemMaintainerOrg": "Division of Investment Oversight and Governance", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-08-01", "atoExpirationDate": "2026-07-31"}, {"id": "{ABD2BB07-07AB-4fe7-84D3-42D357421DD7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{ABD2BB07-07AB-4fe7-84D3-42D357421DD7}", "uuid": "66E2BF33-1DA5-4A78-986C-53D71D573D66", "name": "Drug Data Processing System", "description": "The Prescription Drug Event Front End System (PDFS) performs the initial file processing of the Prescription Drug Event data submitted by the Part D Plans. Upon completion of the initial file processing, the Prescription Drug Event data is then sent to the Drug Data Processing System (DDPS) for validation and authentication of the Medicare payment of covered drugs made by the Part D plans for their enrolled Medicare beneficiaries.  The data is then loaded into the Integrated Data Repository, to support drug, beneficiary, and plan analysis of incurred payments and payment reconciliation. Core Function:  Medicare Part D Claim processing, Data warehousing and Reporting* Annual Part D Claim data (PDE) reconciliation* Generating quarterly manufacturers invoices and performing dispute and outlier analysis.\nDDPS costs are over $2M more than last year due to the requirement to implement IRA requirements.  ", "version": "25", "acronym": "DDPS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Payment Reconciliation", "systemMaintainerOrg": "Division of Medicare Systems Support", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-09-22", "atoExpirationDate": null}, {"id": "{C195B6EE-AE59-432b-80C6-11A5A2F9C969}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C195B6EE-AE59-432b-80C6-11A5A2F9C969}", "uuid": "6616E059-3A99-4808-B22D-E4E4C0DDCE72", "name": "Comprehensive Error Rate Testing - Statistical Contractor", "description": "The Comprehensive Error Rate Testing (CERT) Statistical Contractor (SC) manages an online dashboard that contains de-identified payment information, billing and diagnosis codes and HCPCS.  The dashboard is only available to CMS project specific staff, Medicare Authorized Contractors and select SC project staff.  <PERSON><PERSON> serves as the prime statistical contractor for the Comprehensive Error Rate Testing (CERT) program. The aim of the CERT program is to provide national, contractor, provider (Part A, B, and Durable Medical Equipment Center), and benefit service category specific error estimates that assist the Centers for Medicare & Medicaid Services (CMS) to better manage the Medicare program.  The Statistical Contractor: •Develops innovative, responsive, and flexible approaches to the analysis of CERT data for example by: a.Provides data analysis, statistical analysis, and determination of statistical methodology for the CERT program;b.Sampling of claims for CERT review;i.Quality assurance methods, at a minimum, validating the weights for all contractor type and/or overall estimated rates;c.Calculating improper payment amounts;d.Producing final reports based on CERT RC review results;e.Providing statistical analyses for posting on CERT website•Producing numerous ad-hoc reports and statistical data analyses at CMS' request, with short turnaround times that may require reallocation of resources and priorities;•Producing all draft and final analyses of CERT data, including special studies reports;•Maintaining the CERT Dashboard (a self-service confidential web based tool for contractors and CMS staff)", "version": "25", "acronym": "CERT-SC", "status": "", "belongsTo": "", "businessOwnerOrg": "Payment Accuracy and Reporting Group", "systemMaintainerOrg": "Payment Accuracy and Reporting Group", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2022-06-09", "atoExpirationDate": "2025-06-09"}, {"id": "{9938490D-6BA8-4328-9384-8065D103EDD4}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9938490D-6BA8-4328-9384-8065D103EDD4}", "uuid": "F0A83838-2603-439D-B0BC-DB28A3D9D3CC", "name": "Fiscal Intermediary Shared System", "description": "The FISS is the shared system used to process Medicare Part A claims, including outpatient claims submitted under Part B.  It interfaces directly with the Common Working File (CWF) system for verification, validation, and payment authorization.  Claims are entered, corrected, adjusted, or canceled.  Inquiries for status of claims, for additional development requests, or for eligibility and various codes are processed.   Core Function: * Adjudication of Part A and Part B institutional claims. FISS is a mainframe system that Medicare Part A contractors use to process Medicare Part A and Part B institutional claims nationwide.  * Claims are entered, corrected, adjusted, or canceled.  * Inquiries for status of claims, for additional development requests, or for eligibility and various codes are processed.", "version": "25", "acronym": "FISS", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Applications Management Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-09-22", "atoExpirationDate": "2024-09-22"}, {"id": "{C60D0781-68A3-4e1d-AE2C-5A54B9052414}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C60D0781-68A3-4e1d-AE2C-5A54B9052414}", "uuid": "3221147b1b5a5c50ff05ea02f54bcbc3", "name": "Data Insights", "description": "Tableau is a Business Intelligence tool stood up within CMS AWS that can be used by other applications that have a need to do reporting/dashboards against data sources.", "version": "25", "acronym": "Data Insights", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-05-21", "atoExpirationDate": "2024-05-20"}, {"id": "{7F653FEF-3B7E-46d0-BD18-8A844CFAC751}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{7F653FEF-3B7E-46d0-BD18-8A844CFAC751}", "uuid": "6BCADD28-7FE8-4F19-9D8F-5288A30C202A", "name": "Medicaid-CHIP Payment Error Rate Measurement - NCI RC", "description": "The Medicaid-CHIP Payment Error Rate Measurement Review Contractor (PERM - AMRC) reviews Medicaid and the Children's Health Insurance Program (CHIP) programs and activities in order to report the amount of improper payments as required by IPIA, as amended by IPERA and IPERIA. The error rates are based on reviews of the fee-for-service (FFS), managed care, and eligibility components of Medicaid and CHIP services in the fiscal year (FY) under review. The system will be used by AdvanceMed users which includes review contractors, Medical Review (MR) Specialists, Data Processing (DP) Review Specialists, managers and developers. The system will also be used by CMS users, state Medicaid teams, Eligibility Review Contractors (ERCs) and Statistical Contractors (SC). The system will collect, process and store Personally Identifiable Information (PII) and Protected Health Information (PHI) in the form of state Medicaid and CHIP medical records from providers, State systems of records (SORs), claims payment policies and fee schedules, and regulations and policies that govern the state's administration of their Medicaid and CHIP programs.", "version": "25", "acronym": "PERM-NCIRC", "status": "", "belongsTo": "", "businessOwnerOrg": "Payment Accuracy and Reporting Group", "systemMaintainerOrg": "Payment Accuracy and Reporting Group", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-01-11", "atoExpirationDate": "2026-01-10"}, {"id": "{B5743C78-F59F-4f7a-824A-37B40D0434F6}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B5743C78-F59F-4f7a-824A-37B40D0434F6}", "uuid": "decb018e1bc2f410ee09a82eac4bcbb2", "name": "CMS Operations Information Network", "description": "COIN's goal is to be the secure,  single-source provider of operational business data brought together from CMS systems into one easy-to-use interface for stakeholders to drive business decisions.  OSPR uses the system. The business functions met by the system are for OSPR data processing. OSPR processes data from across CMS.", "version": "25", "acronym": "COIN", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-06-24", "atoExpirationDate": "2025-06-24"}, {"id": "{********-CD8F-42f4-ABB2-13BB888E2AD3}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{********-CD8F-42f4-ABB2-13BB888E2AD3}", "uuid": "6DA8D19B-CBC8-48E9-A3DE-E897BC479C03", "name": "Measure Authoring Development integrated Environment", "description": "The Measure Authoring Tool (MAT), https://emeasuretool.cms.gov, assists electronic Clinicial Quality Measure (eCQM) developers in creating standardized performance measures using the Quality Data Model (QDM) and Clinical Quality Language (CQL) by providing a user friendly application to guide CMS developers through the performance measures authoring process. The MAT also has public information on the tool, a process for obtaining access, information on how to participate in open source submissions, and access to an open source repository. Core Function: This system hosts a public website to provide information regarding the Measure Authoring Tool.  This system hosts the Measure Authoring Tool. This tool is used by measure authors to develop measures in a standardized format and and then test developed measures through an API with <PERSON>. The MAT system is used to create and export the measure definition.  This MAT system only manages the definitions. Under this same contract, managed by the same scrum and development team, is <PERSON>, which is the eCQM testing tool to test the logic of the measure. Both of these tools are both part of the measure development lifecycle. Bonnie is not an acronym for anything. <PERSON> was initially developed under the federally-funded research and development contract and is currently hosted on healthit.gov domain with servers hosted at Agency for Healthcare Research and Quality AHRQ. The MAT tool is currently in process of being moved to the HCQIS cloud. We do plan to move the Bonnie tool to the cloud in the next few years. The system cost and contract FTE noted above covers both tools due to the use of one scrum and development team.", "version": "25", "acronym": "MADiE", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Clinician Quality", "systemMaintainerOrg": "Division of Clinician Quality", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-10-20", "atoExpirationDate": "2025-10-20"}, {"id": "{92DE1F8C-0F14-46b2-B785-6013B35D3C57}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{92DE1F8C-0F14-46b2-B785-6013B35D3C57}", "uuid": "42DD6E47-4868-4E85-B5B5-30B1E2A94764", "name": "Provider Statistical and Reimbursement System", "description": "The Provider Statistical and Reimbursement System (PS-R) accumulates all Medicare Part A paid claims for all Medicare Part A providers. Medicare providers used the summarized accumulated paid claims data to file their Medicare cost report, and the MACs use the data for final settlements of the Medicare cost reports. .  Core Function: * Data warehouse that receives daily Medicare Part A paid claim information from the MACs data center* Providers generate summary and detail reports for Part A claims to create cost reports. Supports 170 claim data reports used by provider, MACs and CMS analysts track each Medicare part A cost report through its review and audit process.", "version": "25", "acronym": "PS-R", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Provider Audit Operations", "systemMaintainerOrg": "Financial Management Systems Group", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2021-07-29", "atoExpirationDate": "2024-07-29"}, {"id": "{B7E62260-832E-4d45-BE45-9FDFFE8E6671}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{B7E62260-832E-4d45-BE45-9FDFFE8E6671}", "uuid": "F3B4B4B4-181B-4B1A-BE2B-07824B07026A", "name": "Program Integrity Contractor SGS", "description": "The Program Integrity Contractor assists CMS/CPI with the investigations of suspected fraud, waste, and abuse; and supports benefit integrity efforts through medical reviews, national and regional data analysis, and the referral of cases to law enforcement for consideration and initiation of civil or criminal prosecution.\nThis FISMA system represents a specific Program Integrity Contractor (PIC) which assists CMS's efforts in combating fraud, waste and abuse in Medicare and Medicaid (all claim types).  \nIn January 2019, the remaining ZPICs were renamed. This contractor owned system was renamed from Zoned Program Integrity Contractors - SGS\nto Program Integrity Contractor SGS.  \nAlso combined three ZPICs/PSCs: Zoned Program Integrity Contractors Zone 1- SafeGuard Services  AND Zoned Program Integrity Contractors Zone 7- Safeguard Services AND Payment Safeguard Contractors - Safeguard Services", "version": "25", "acronym": "PI-SGS", "status": "", "belongsTo": "", "businessOwnerOrg": "Center for Program Integrity", "systemMaintainerOrg": "Center for Program Integrity", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-03-18", "atoExpirationDate": "2024-03-18"}, {"id": "{9E7716F4-6F7A-4e46-BA60-122789430525}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9E7716F4-6F7A-4e46-BA60-122789430525}", "uuid": "3C122582-9917-4D51-909F-1AA77BBB6621", "name": "ESRD Quality Reporting System", "description": "The End Stage Renal Disease Quality Reporting System (EQRS) serves both value-based payment and qulaity reporting function.  The End Stage Renal Disease Quality Incentive Program (ESRD QIP) links quality measurement information to ESRD payment system Medicare payment based on facility performance and reporting on quality measures.  The EQRS calculates QIP measure rates, scores, and payment status, and provides feedback reports to facilities for their information and quality improvement purposes.  The system also produces a public reporting file quality information for ESRD QIP display on the CMS Dialysis Facility Compare website maintained by the Office of Communications.The Consolidated Renal Operations in a Web enabled Network (CROWNWeb) is an Internet, roles based application that allows dialysis facilities to electronically transmit patient, clinical and facility data. CROWNWeb enables the sharing of data among CMS, ESRD facilities, ESRD Network Organizations, and the public via the Dialysis Facility Compare/Reporting websites.CROWNWeb is a web based data collection system that is mandated by CMS to enable dialysis facilities to meet Section 494.180(h) of the 2008 updated Conditions for Coverage for ESRD Dialysis Facilities, which calls for the electronic submission of administrative and clinical data by all Medicare certified dialysis facilities in the United States.  This process eliminates the additional data submission steps required by previously used legacy systems, and enables facilities to validate and submit their data to CMS in real time.CROWNWeb initial releases were in phases that gave participating facilities an opportunity to work with CMS to help improve system usability.  The CROWNWeb National Release occurred on June 14, 2012.  Data is submitted into CROWNWeb via two methods: approximately 20% of the facilities manage data entry at the facility level by manually entering their information through the User Interface. The remaining 80% have their data uploaded on their behalf by authorized submitters through the Electronic Data Interface (EDI). Furthermore, data uploaded through the EDI is submitted through a process known as Batch for approved Batch Submitting Organizations (BSOs), and a method using the NwHIN by the National Renal Administrators Association (NRAA) Health Information Exchange (HIE). Core Function: * The Consolidated Renal Operations in a Web enabled Network (CROWNWeb) is an Internet, roles based application that allows dialysis facilities to electronically transmit patient, clinical and facility data.* CROWNWeb enables the sharing of data among CMS, ESRD facilities, ESRD Network Organizations, and the public via the Dialysis Facility Compare/Reporting websites.* CROWNWeb is a web based data collection system that is mandated by CMS to enable dialysis facilities to meet Section 494.180(h) of the 2008 updated Conditions for Coverage for ESRD Dialysis Facilities", "version": "25", "acronym": "EQRS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Value-Based Incentives and Quality Reporting", "systemMaintainerOrg": "Information Systems Group", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2021-03-18", "atoExpirationDate": "2024-03-18"}, {"id": "{959DAB5D-9D57-4b66-B466-2847EFA5B29C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{959DAB5D-9D57-4b66-B466-2847EFA5B29C}", "uuid": "CF8C7B62-1265-4F38-86D4-11B37C022842", "name": "Financial Information and Vouchering System Next Generation", "description": "The FIVS application is the current program management tool that provides the ability to submit budgets and invoices, define and manage the projects, make payment justifications and submit administrative suspension notices. This system also allows review of, generate reports about, and maintain audit trails of the entire process, and it provides CMS insight into QIO performance, specifically as it relates to program management and control.", "version": "25", "acronym": "FIVS NG", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Operations Group (CCSQ BOG)", "systemMaintainerOrg": "Division of Hospitals, ASC, and QIO Systems", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-01-14", "atoExpirationDate": "2025-01-14"}, {"id": "{8149CC91-1ABC-4ab3-8F91-BF683E17DD18}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8149CC91-1ABC-4ab3-8F91-BF683E17DD18}", "uuid": "13BF5192-0A7C-4AD9-BFB0-7716ADA0DB4B", "name": "Open Payments System", "description": "Open Payments is a statutory program enacted by the Patient Protection and Affordable Care Act (ACA) of 2009 (H.R. 3590, Section 6002). The legislation required Applicable Manufacturers (AMs) and applicable Group Purchasing Organizations (GPOs) to annually report payments and other transfers of value made to physicians and teaching hospitals (THs), as well as certain information regarding the ownership or investment interests held by physicians or their immediate family members to CMS. The SUPPORT for Patients and Communities Act recently expanded Open Payments to also include payments to Physician Assistants, Nurse Practitioners, Clinical Nurse Specialists, Certified Registered Nurse Anesthetists or Anesthesiologist Assistants, and Certified Nurse Nurse-Midwives. CMS is mandated to collect that data via an electronic solution and make the data available for view and download by general public on a public website.  The Open Payments System (OPS) provides development, maintenance and enhancements for the program's electronic reporting solution and supporting services such as user help desk, outreach, and communication.", "version": "25", "acronym": "OPS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Transparency Projects", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-08-01", "atoExpirationDate": "2026-07-31"}, {"id": "{A732B056-B3DF-4b0f-AB44-2625631BBDA7}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A732B056-B3DF-4b0f-AB44-2625631BBDA7}", "uuid": "F31E4DEE-64C5-43EB-A2A7-C32F8F5035D4", "name": "One Program Integrity", "description": "The One PI System provides modernized data analysis capability for CMS, Law Enforcement and its contractors, primarily to combat fraud, waste and abuse in Medicare (and soon, Medicaid). The One PI Portal provides a secure, centralized point of entry for users, and is accessible only through CMSNet. The system implements many basic features, including role based security to constrain who can access information. All users access the One PI system through the One PI Portal. The analytical capabilities provided include SAP BusinessObjects Business Intelligence, SAS Enterprise Business Intelligence, and Cotiviti STARSInformant, three commercially available tools used by many PI end users. Core Function: * The Medicare Integrity Program (MIP) provides the PI Group with funds to safeguard more than $400 billion in Medicare program payments. Using its authority under MIP, the CMS PI Group currently performs the following core business functions to safeguard *  Research and information sharing to meet the information needs of a wide array of users, including responding to law enforcement or Freedom of Information Act (FOIA) requests*  FWA investigation and development to begin the process of reducing FWA*  Audits in a variety of settings, such as regional Medicare Drug Integrity Contractor (MEDIC) plan audits and Hospital Cost Report audits, as another technique to identify FWA*  Medical reviews to determine whether services provided are medically reasonable and necessary and to educate providers*  Payment error rate determination to monitor and report the accuracy of claim payments.", "version": "25", "acronym": "One PI", "status": "", "belongsTo": "", "businessOwnerOrg": "Data Analytics and Systems Group", "systemMaintainerOrg": "Division of Investigative Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-07-02", "atoExpirationDate": "2024-07-02"}, {"id": "{AE737817-27A1-498e-BD33-CD84752B0830}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{AE737817-27A1-498e-BD33-CD84752B0830}", "uuid": "D489E75A-DB18-7700-9B37-72840F9619EB", "name": "Medicaid Module Data Lab", "description": "The MMDL system collects, tracks and house program data for Medicaid State Plan Eligibility, CHIP Eligibility, and Medicaid Premiums and Cost Sharing, and Alternative Benefit Programs Core Function: Collects and track program data for Medicaid: CHIP EligibilityAlternative Benefit PlansPremium and Cost Sharing", "version": "25", "acronym": "MMDL", "status": "", "belongsTo": "{09F10C4E-18D5-4472-851B-44403CB62F06}", "businessOwnerOrg": "Medicaid Benefits and Health Programs Group", "systemMaintainerOrg": "Division of Information Systems", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2023-02-09", "atoExpirationDate": "2026-02-08"}, {"id": "{830E2A6E-9C74-4db8-9F74-9A77B9C2B4E8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{830E2A6E-9C74-4db8-9F74-9A77B9C2B4E8}", "uuid": "DB8E60FE-21FA-4FFC-A3AC-02295B324D63", "name": "Recovery Audit Contractor Regions 2 and 3", "description": "The Recovery Audit Program’s mission is to identify and correct Medicare improper payments through the efficient detection and collection of overpayments made on claims of health care services provided to Medicare beneficiaries, and the identification of underpayments to providers so that the CMS can implement actions that will prevent future improper payments in Recovery in all 50 states.", "version": "25", "acronym": "RACs- 2 and 3", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Recovery Audit Operations", "systemMaintainerOrg": "Division of Recovery Audit Operations", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2023-04-05", "atoExpirationDate": "2026-04-04"}, {"id": "{A9440F59-3F8C-4910-92F7-188350A58C86}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{A9440F59-3F8C-4910-92F7-188350A58C86}", "uuid": "54D4E6FC-9229-4391-B0DE-69CEED6452D2", "name": "Relationships, Events, Contacts, and Outreach Network", "description": "RECON tracks CMS interaction with partners and its outreach events to: determine effectiveness of messages delivered to beneficiaries; identify communications gaps for initiatives; and link data with local partner information. It also has contact management capability providing a historical record and context for partner relations. Core Function: * Determine effectiveness of messages delivered to beneficiaries* Identify communications gaps for initiatives and link data with local partner information.  * Contact management capability providing a historical record and context for partner relations. Salesforce.com provides a platform that could be rapidly deployed for prototyping purposes, and proved to be flexible enough to grow in real time with CMS.", "version": "25", "acronym": "RECON", "status": "", "belongsTo": "", "businessOwnerOrg": "Partner Relations Group", "systemMaintainerOrg": "Partner Relations Group", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2021-10-07", "atoExpirationDate": "2024-10-07"}, {"id": "{7E3F5590-D703-442c-A5B7-DA3246A564EA}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{7E3F5590-D703-442c-A5B7-DA3246A564EA}", "uuid": "8DB5BBAB-08D9-473F-BE01-A5AF0DCC30C0", "name": "MicroStrategy", "description": "The purpose of Enterprise MicroStrategy BI Application is to provide CMS with an Enterprise Solution, which will address these three key business areas: BI Analytical Solutions/Policy Management, Program Management, and Operational Management for all divisions within CMS. MicroStrategy will provide BI reports to aid CMS in detecting fraud and abuse, analyze trends in enrollment, claims and eligibility, and analyze the effectiveness of the Medicare Modernization Act program by allowing the CMS employees and managers access the BI reports, data, and Medicare Modernization Act program indicators.", "version": "25", "acronym": "MSTR", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-03-18", "atoExpirationDate": "2025-03-17"}, {"id": "{59DAB032-AE3B-47dd-9662-257C0B2F9592}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{59DAB032-AE3B-47dd-9662-257C0B2F9592}", "uuid": "08ED4CE1-926B-46B7-9575-A95150BB7DA8", "name": "CMS Connect", "description": "The ServiceNow Service Automation Government Cloud Suite is a suite of natively integrated applications designed to support IT service automation, resource management and shared support services. The first application built on this platform processes helpdesk requests submitted by CMS Employees and Contractors into the CMS ServiceNow Helpdesk ITSM System. This application also allows multiple service desks within the agency to communicate and transfer tickets without an impact to business operations.", "version": "25", "acronym": "CCN", "status": "", "belongsTo": "", "businessOwnerOrg": "Infrastructure and User Services Group", "systemMaintainerOrg": "Infrastructure and User Services Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-07-16", "atoExpirationDate": "2024-07-16"}, {"id": "{87F34E4B-27A7-4353-997B-B997D0422897}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{87F34E4B-27A7-4353-997B-B997D0422897}", "uuid": "2F7A1ED2-3F1D-4BC9-93A6-0A4E5B5848B4", "name": "Risk Adjustment Suite of Systems", "description": "The Risk Adjustment Suite of System (RASS) supports CMS processes that apply risk adjustments to payments to Medicare Advantage and Part D plans. This system was implemented to receive, process, store risk adjustment data using the CMS HCC, ESRD, Part D models developed by CMS to calculate risk scores which would be used by MARx to compute plan payments.", "version": "25", "acronym": "RASS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Encounter Data and Risk Adjustment Operations", "systemMaintainerOrg": "Division of Medicare Systems Support", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-03-06", "atoExpirationDate": "2024-03-05"}, {"id": "{766A5DD3-779C-40e6-9FB0-BE21D9622E92}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{766A5DD3-779C-40e6-9FB0-BE21D9622E92}", "uuid": "DAC84A01-02DD-4152-9479-D570B6E9D904", "name": "Informatica BI", "description": "The purpose of Informatica Power Center applications is to provide CMS with an Enterprise Solution, which will address large volume complex Enterprise Data Warehouse environments to support the ETL, Transform, Load (ETL) processes necessary to integrate data across the enterprise.", "version": "25", "acronym": "InformatBI", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enterprise Information Management Services", "systemMaintainerOrg": "Division of Enterprise Information Management Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-04-11", "atoExpirationDate": "2025-04-11"}, {"id": "{9BEA95CC-30BC-4f8f-B3FE-CF100EA94E88}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{9BEA95CC-30BC-4f8f-B3FE-CF100EA94E88}", "uuid": "E879AA7F-925C-4E11-ACF3-508A7AE6CF4C", "name": "Quality Payment Program", "description": "Quality Payment Program (QPP) serves as the front end gateway for all Medicare providers eligible to participate in Merit Based Incentive Payment System (MIPS) or Alternative Payment Models (APMs) to avoid MIPS Payment Adjustment on Medicare claims starting calendar year 2019. MIPS/APM Front end system maintains following data: MIPS/APM Eligibility, APM/APE/Provider Relationships, Qualifying APM Participant Status for each Eligible Professionals (EP), MIPS measure specifications and benchmarks, clinician submissions, final scores and Feedback Reports Core Function: *Receive measures *Score measures *Create a payment adjustment file for Medicare Administrative Contractors to apply payment adjustments", "version": "25", "acronym": "QPP", "status": "", "belongsTo": "", "businessOwnerOrg": "Quality Measurement and Value-Based Incentives Group", "systemMaintainerOrg": "Systems Implementation Staff", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-07-13", "atoExpirationDate": "2026-07-12"}, {"id": "{789E0508-6356-4f9b-8D91-9646677636B6}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{789E0508-6356-4f9b-8D91-9646677636B6}", "uuid": "BC59A9CF-B882-4C9D-BCB8-189C799D7BB7", "name": "Electronic Retro Processing Transmission", "description": "A web-based portal that will allow Medicare Parts C and D plan sponsors to submit all necessary documentation through the web portal which will expedite the processing of retroactive transactions and hence streamline plan sponsors ability to coordinate benefits. Core Function: The Electronic Retroactive Processing Transaction will improve data transmission efficiencies between the Managed Care and Prescription Drug Plan/Sponsors and the Retroactive Processing Contractor (RPC).", "version": "25", "acronym": "eRPT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Medicare Enrollment Coordination", "systemMaintainerOrg": "Division of Data Enterprise Services", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-11-01", "atoExpirationDate": "2026-10-31"}, {"id": "{576D2C59-B9CB-4ccd-85E1-BE6DCB6110D0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{576D2C59-B9CB-4ccd-85E1-BE6DCB6110D0}", "uuid": "F8A55491-C71A-442B-8EC9-9804A71FE912", "name": "Strategic Work Information Folder Transfer System", "description": "The Strategic Work Information Folder (SWIFT) is an intranet-based document management, workflow and tracking system which supports the executive \ncorrespondence process, the CMS FOIA process, Reports to Congress, Executive Paperless Approvals, Litigation Holds, the CMS regulation drafting and \nclearance process and Digital Mail Scanning & Delivery. \nThe SWIFT Correspondence application is used by OSORA for the management, control, and tasking of CMS's executive correspondence process. OSORA works \nclosely with CMS's Central Office, the Regional Offices and the Office of the Secretary (OS) to coordinate and clear correspondence for the signature of \nthe CMS Administrator and DHHS Secretary to members of Congress, state officials and external stakeholders. Desk officers are assigned to each of CMS's \noffice's to ensure that CMS's executive level correspondence is processed in an expeditious and accurate manner utilizing the SWIFT Correspondence \napplication. \nThe SWIFT FOIA application maintains a log of all FOIA requests; supporting documentation received from requestors; generates reports and \nhandles the tasking functions between the Central Office, Regional Offices and MAC's. The data captured allows CMS to generate the monthly and annual \nstatistics as designated by the FOIA and the Annual FOIA Report, which CMS provides to the United States Department of Justice. The Privacy Request Portal \nmanages the automatic vetting & submission of Medicare beneficiary Privacy Act requests from the public or parties acting on their behalf & it is transferred\ninto the SWIFT FOIA Application for processing. \nThe Litigation Holds application manages the digital approval, notification and tracking of requirements to hold documents for ongoing litigation throughout\nCMS. \nThe SWIFT Regulation Management System tracks and manages the regulation drafting and clearance process within the agency. \nThe Digital Mail System manages the delivery of digitized mail and tracking of paper-based accountable mail through the CMS Central Office and the ten \nRegional Offices and tracking of paper-based accountable mail in the Central Office. \nThe SWIFT system is integrated with CMS SharePoint. SWIFT coordinators can assemble teams of subject matter experts around documents hosted in SharePoint, \nallowing for increased collaboration across CMS.", "version": "25", "acronym": "SWIFT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Records and Information Systems", "systemMaintainerOrg": "Division of Records and Information Systems", "state": "Active", "businessOwnerOrgComp": "OSORA", "systemMaintainerOrgComp": "OSORA", "atoEffectiveDate": "2023-03-20", "atoExpirationDate": "2026-03-19"}, {"id": "{69C0D948-BCC5-4984-8187-253FEA853089}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{69C0D948-BCC5-4984-8187-253FEA853089}", "uuid": "552A761C-51BE-4821-A129-12FA16356829", "name": "National Plan and Provider Enumeration System", "description": "NPPES serves as the national system designed to assign unique identifiers to health care providers who apply for the National Provider Identifier (NPI). NPPES contains information that is used to uniquely identify the health care provider. See CMS 10114 for the information collected. Core Function: Assigns National Provider Identification (NPI) numbers to providers.  When an NPI number is assigned, the provider information is compared to various sources (such as the SSA Death Master File) to ensure that this is a valid provider.", "version": "25", "acronym": "NPPES", "status": "", "belongsTo": "", "businessOwnerOrg": "Provider Enrollment and Oversight Group", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2022-12-08", "atoExpirationDate": "2025-12-07"}, {"id": "{C7148236-0132-422d-A3AA-1EA094885307}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{C7148236-0132-422d-A3AA-1EA094885307}", "uuid": "0AD77CB6-372D-444C-83B4-C2479B21983C", "name": "Marketplace Outreach Data System", "description": "This system is used internally by CMS contractors and staff to create outbound lists for targeted communications from the MIDAS/FFM consumer record including email, SMS and autodial. The data on the impact of these communications is then fed into the system to help determine the effectiveness of each type of messaging, timing, etc. to shape more efficient campaigns. Core Function: MODS supports targeted outreach to consumers who have expressed interest in enrolling in the Federally Facilitated Marketplace or begun the process of enrolling.* It receives data from MIDAS, GovDelivery, and ad hoc data sources, and provides a workspace for analysts to produce lists for outreach via postal mail, email, SMS, and outbound phone calls.* It also provides a platform for analysts to conduct ad hoc analytics about the reach and effectiveness of outreach activities.", "version": "25", "acronym": "MODS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Digital Marketing", "systemMaintainerOrg": "Division of Digital Marketing", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2023-09-22", "atoExpirationDate": "2026-09-21"}, {"id": "{5171E0AD-C192-4233-BEA8-C3BC551D74B6}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5171E0AD-C192-4233-BEA8-C3BC551D74B6}", "uuid": "1565e2f5dbb30c10512770131f961960", "name": "Common Electronic Data Interchange", "description": "The CEDI (Common Electronic Data Interchange) is a Contractor Owned System that provides a single, electronic front end solution for all Medicare DME suppliers. CEDI works closely with software vendors, billing services, clearinghouses, and Trading Partners (submitters) for their Medicare DME electronic transactions as well as online entry via claim portal.\r\nAll DME MAC electronic claims (i.e. those submitted as X12 837P and NCPDP D.0 and those entered via CCP) as well as X12 276 transactions are collected by CEDI. CEDI also returns all DME MAC electronic front end acknowledgements/reports, X12 835 electronic remittance advices and X12 277 claim status response transactions.", "version": "25", "acronym": "CEDI", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Contractor Management Group", "systemMaintainerOrg": "Division of MAC Systems Security and Operations Oversight", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2021-04-08", "atoExpirationDate": "2024-04-08"}, {"id": "{8AEAE4F9-8657-4189-A904-1EC85D812A63}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8AEAE4F9-8657-4189-A904-1EC85D812A63}", "uuid": "E5D6C19E-B66F-4365-88CF-348D2A18DC05", "name": "Multidimensional Insurance Data Analytics System", "description": "The Multidimensional Insurance Data Analytics System (MIDAS) acts as the perpetual central repository for capturing, aggregating, and analyzing information on health insurance coverage. Current and historical data will be used to monitor, forecast, trend, analyze and report on the new information that the Affordable Care Act authorizes federal agencies to collect. An integral piece of this program is the business intelligence analytics which uses technologies, processes, and applications to analyze mostly internal, structured data, and aims to support better business decision making. MIDAS provides the following functions:  Centralizes and consolidates business logic into a metadata repository required to report and manage performance of the Affordable Care Act activities under CCIIO Integrates data from multiple operational source systems into a single, web based information data store  Provides access to standardized reporting, ad hoc queries, and data visualization  Provides reporting on the data collected and maintained  Provides robust analytic capabilities supporting trending and prediction from the data collected and maintained. Core Function: * Centralizes and consolidates business logic into a metadata repository required to report and manage performance of the Affordable Care Act activities under CCIIO * Integrates data from multiple operational source systems into a single, web based information data store* Provides access to standardized reporting, ad hoc queries, and data visualization* Provides reporting on the data collected and maintained* Provides robust analytic capabilities supporting trending and prediction from the data collected and maintained.", "version": "25", "acronym": "MIDAS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of HUB Data and State Support", "systemMaintainerOrg": "Division of HUB Data and State Support", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-09-02", "atoExpirationDate": "2024-09-02"}, {"id": "{49A82634-258A-4d62-8EAC-38E40D3514E1}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{49A82634-258A-4d62-8EAC-38E40D3514E1}", "uuid": "5BB2BA49-E134-4F65-A2BC-0075330836B9", "name": "Beneficiary Claims Data API", "description": "BCDA: The Beneficiary Claims Data Application Programming Interface (BCDA) enables CMS Alternative Payment Model participants to retrieve Medicare Part A, Part B, and Part D claims data for their prospectively assigned or assignable beneficiaries. In the production environment, the API provides data parallel to Claim and Claim Line Feed (CCLF) files, currently provided monthly to CMS Alternative Payment Model participants by CMS. This includes Medicare claims data for instances in which beneficiaries receive care outside of the model participant, allowing a full picture of patient care. BCDA currently supports the Shared Savings Program and Global and Professional Direct Contracting models.\r\nAB2D: In response to the Bipartisan Budget Act of 2018 and Final Rule, CMS has developed the FHIR based Claims Data to Part D Sponsors Application Programming Interface otherwise known as the AB2D API. The AB2D API securely provides stand-alone PDP sponsors with bulk Medicare Parts A and B claims data for their active enrollees. PDP sponsors will be able to utilize this claims data to promote the appropriate use of medications and to improve health outcomes for their beneficiaries.\r\nDPC: Data at the Point of Care provides FHIR-formatted bulk data files to fee-for-service providers for their active patients as needed for treatment purposes under HIPAA. With DPC, providers identify their own rosters of patients to track, and no action is required from the beneficiary to authorize sharing of data. Data is shared between covered entities for treatment purposes as defined under HIPAA.", "version": "25", "acronym": "BCDA", "status": "", "belongsTo": "", "businessOwnerOrg": "Data and Analytics Strategy Group", "systemMaintainerOrg": "Data and Analytics Strategy Group", "state": "Active", "businessOwnerOrgComp": "OEDA", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2022-09-08", "atoExpirationDate": "2025-09-08"}, {"id": "{5BB9B6F9-3247-4077-81A7-D4C676708B21}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5BB9B6F9-3247-4077-81A7-D4C676708B21}", "uuid": "D3D32F38-EC2F-45E9-9C63-44A7A8104EF2", "name": "CMS.gov", "description": "CMS.gov provides the public and professionals the ability to access information regarding the CMS programs. CMS.gov website mission is to provide clear, accurate, and timely information about CMS programs to the entire health care community to improve quality and efficiency in an evolving health care system. CMS.gov website is a combination of static content and general content applications.  Core Function: * Information dissemination", "version": "25", "acronym": "cms.hhs.gov", "status": "", "belongsTo": "", "businessOwnerOrg": "Web and Emerging Technologies Group", "systemMaintainerOrg": "Web and Emerging Technologies Group", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{71932742-AD52-40d3-A317-4EA155B9B0FA}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{71932742-AD52-40d3-A317-4EA155B9B0FA}", "uuid": "74FA5AD1-F19C-4D3A-B71C-B09C1D4C2F65", "name": "Durable Medical Equipment Prosthetics, Orthotics and Supplies Bidding System", "description": "DBidS is a web based application that allows Medicare accredited DMEPOS suppliers to submit bids for selected DMEPOS items and related services within certain geographic areas known as Competitive Bidding Areas (CBAs). The bidding window is for a 60 day period, and those suppliers that do not participate or are not awarded a contract can not bill Medicare. Core Function: * Accepts and manages bids from Durable Medical Equipment suppliers who offer to provide goods within specific product categories in exchange for the amount they propose.", "version": "25", "acronym": "DBidS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Provider Communications Technology", "systemMaintainerOrg": "Division of Mid-Tier Applications Management", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-07-01", "atoExpirationDate": "2024-07-01"}, {"id": "{3E04EEAC-E2BA-4cfb-BEA5-5E088090BD93}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3E04EEAC-E2BA-4cfb-BEA5-5E088090BD93}", "uuid": "3B302570-F053-42F0-8123-5B193893FFEE", "name": "Virtual Audit Management System", "description": "The Virtual Audit Management System (VAMS) is a system used by CMS/CCIIO to provision and maintain a leading COTS product for the Affordable Care Act (ACA)/CMS No Surprises & Transparency Act System (NST) auditing of healthcare entities. The auditing COTS product being utilized by CMS is known as TeamMate Plus, from the Wolters Kluwer organization.\nServers are built/updated based on the windows OS gold-images provided by the Cloud Navigator Services (CNS) team. TeamMate+ software/application is installed and configured within the web server under IIS (Internet Information Services).", "version": "25", "acronym": "VAMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Marketplace IT Operations", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-03-11", "atoExpirationDate": "2025-03-11"}, {"id": "{3EC2D6CA-2C44-4f95-AA58-878E6FCB2137}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3EC2D6CA-2C44-4f95-AA58-878E6FCB2137}", "uuid": "E818119D-B7E9-4531-BFB5-DCFD1A4ECB8D", "name": "Health Care Cost Report Information System", "description": "The Healthcare Cost Report Information System (HCRIS) collects Medicare cost report data for Hospitals, Skilled Nursing Facilities, Home Health Agencies, Renal Dialysis Facilities, Hospices, Rural Health Clinics, Federally Qualified Health Centers, and Community Mental Health Centers.  This data is collected from the Medicare Administrative Contractors.  The cost report data is analyzed by CMS internal offices and also by the  public. HCRIS creates SAS datasets and provides database access via SAS EBI to internal users. HCRIS creates public use files available on the CMS website for consumption by business and academic users. HCRIS provides data files to the Medicare Payment Advisory Commission. Core Function: *Aggregates public cost report data from FIs/MACs*Collects data from Medicare cost reporting forms *Creates public use files on a quarterly basis*Provides an ad hoc database query capability to CMS internal users*Creates a standard SAS dataset for consumption by CMS internal users*HCRIS creates customized data reports for internal and external requestors Provides annual datasets to MedPAC.", "version": "25", "acronym": "HCRIS", "status": "", "belongsTo": "", "businessOwnerOrg": "National Health Statistics Group", "systemMaintainerOrg": "Division of Mid-Tier Applications Management", "state": "Active", "businessOwnerOrgComp": "OACT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2024-02-01", "atoExpirationDate": "2027-01-31"}, {"id": "{3F585533-9CF2-4d85-AC58-BE91AE68303C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{3F585533-9CF2-4d85-AC58-BE91AE68303C}", "uuid": "03e40349db94805085c976708c961905", "name": "Easy Access to System Information", "description": "EASi is a resource channel for CMS internal stakeholders (CIO, Business Owners, System Owners, or Navigators) to access CMS systems information on a single platform. The goal is to help stakeholders easily find information at a high level and then if needed, provide other channels or dashboards that have detailed features", "version": "25", "acronym": "EASi", "status": "", "belongsTo": "", "businessOwnerOrg": "Office of Information Technology", "systemMaintainerOrg": "IT Capital Planning Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-04-14", "atoExpirationDate": "2026-04-13"}, {"id": "{623D4757-312B-48be-8904-86CAD72EE04B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{623D4757-312B-48be-8904-86CAD72EE04B}", "uuid": "D69B06AB-2431-4E9B-A823-50E2248D3BED", "name": "Enterprise Content Management", "description": "CMS' use of the existing ECM infrastructure facilitates a strategic, enterprise view of unstructured data. The ECM infrastructure also replaces expensive and time-consuming traditional content management processes and production technologies. CMS' adoption of the ECM solution makes content easier to find and access, and provides improved support to CMS users and business partners. By capturing content electronically, CMS eliminates reliance on physically deteriorating media, and ensures it can store and manage this content indefinitely. Adoption of the ECM infrastructure also helps CMS comply with legislated mandates and executive initiatives to reduce paper and manual processes. It also places CMS in a position to easily facilitate records management and compliance functionality, and to support structured [Integrated Data Repository (IDR)] and unstructured data integration in alignment with Department of Health and Human Services (HHS) long-range goals—namely, to transform the delivery of health care and to provide appropriate privacy and security protections for Protected Health Information (PHI) and Personal Health Records (PHR). CMS implemented the IBM Content Manager (CM) Enterprise Infrastructure to facilitate the Agency's life-cycle management of unstructured data", "version": "25", "acronym": "ECM", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Infrastructure and User Services Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2021-04-16", "atoExpirationDate": "2024-04-16"}, {"id": "{411CFDA0-55BC-4ddd-891A-76B3692717C3}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{411CFDA0-55BC-4ddd-891A-76B3692717C3}", "uuid": "75A3E5D8-D1C3-427E-A388-2242693FE2D1", "name": "Internet Quality Improvement and Evaluation System", "description": "iQIES will be a key source of quality data for CMS. It is CMS' major tracking, analysis and data repository system for Medicare, Medicaid and the Clinical Laboratory Improvement Amendments of 1974 (CLIA) provider quality of care. It collects and validates data on provider and beneficiary specific outcomes of care and performance for use in improving the quality and cost effectiveness of services provided by the Medicare, Medicaid, and CLIA programs.  The iQIES application will be used by our providers to submit post acute care assessments and staffing, census, and employee records.  Assessment, survey, and certification data will be available for reporting to providers, State Agencies, CMS Locations, and other iQIES users.  The system will also collect, organize, track, analyze and maintain data on demographics, certification, surveys, deficiencies, complaints, enforcement, and accreditation. It will be the primary tool used by these CMS and State Agencies to ensure healthcare providers/suppliers are in compliance with CMS Health and Life-safety standards. Core Function:  iQIES supports collection, analysis, and reporting of provider and beneficiary specific outcomes of care and performance data across delivery sites for use in improving the quality and cost effectiveness of services provided.  It provides software for state and federal surveyors to schedule surveys, collect and track survey results, complaint investigations and enforcement activities.  iQIES provides software for collecting assessment data on residents/patients and submitting data to CMS.", "version": "25", "acronym": "iQIES", "status": "", "belongsTo": "", "businessOwnerOrg": "Quality, Safety, and Oversight Group", "systemMaintainerOrg": "Division of Quality Systems for Assessments and Surveys", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2022-08-29", "atoExpirationDate": "2025-08-29"}, {"id": "{310AAAE1-9459-4ffb-A1F9-7E839757DE31}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{310AAAE1-9459-4ffb-A1F9-7E839757DE31}", "uuid": "B56BC07D-49BA-4E09-B0BC-304875DBFD46", "name": "InsureKidsNow.gov", "description": "InsureKidsNow.Gov is a White House Initiative (Authorized in 1997) managed by CMCS with plays a critical role in sharing information related to Medicaid and CHIP health coverage programs for children in each state. The site also fulfills our statutory obligation under CHIPRA to provide an online directory of dentists who accept Medicaid and CHIP in each state. Core Function: * Education and Outreach for Medicaid and CHIP* Supports legal requirements for dental information in Medicaid and CHIP", "version": "25", "acronym": "InsureKidsNow", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Communications and Outreach", "systemMaintainerOrg": "Division of Communications and Outreach", "state": "Active", "businessOwnerOrgComp": "CMCS", "systemMaintainerOrgComp": "CMCS", "atoEffectiveDate": "2023-04-10", "atoExpirationDate": "2026-04-09"}, {"id": "{5163E426-6D8B-42a6-8F4D-BA2E516FB331}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{5163E426-6D8B-42a6-8F4D-BA2E516FB331}", "uuid": "8C14397C-2458-4933-8B26-23D7127E1A7B", "name": "Hospital Quality Reporting", "description": "The Hospital Quality Reporting System is a quality reporting program where participating hospitals provide CMS with data to help consumers make more informed decisions about their health care. The data is available at www.hospitalcompare.hhs.gov and www.data.medicare.gov. Through legislation, CMS is authorized to pay hospitals a higher annual update to their payment rates if they successfully report designated quality measures. The program began with the Inpatient Quality Reporting (IQR) Program and now represents several health care provider types (no longer just hospitals). The Hospital IQR Program was developed as a result of the Medicare Prescription Drug, Improvement and Modernization Act (MMA) of 2003. Section 5001(a) of Pub. 109 171 of the Deficit Reduction Act (DRA) of 2005 provided new requirements for the Hospital IQR Program, which built on the voluntary Hospital Quality Initiative. The Hospital IQR Program requires sub section (d) hospitals to submit data for specific quality measures for health conditions common among people with Medicare, and which typically result in hospitalization. If they successfully participate in the program, they are eligible to receive their full Medicare reimbursement (of which CMS withholds a percentage until they successfully meet the annual criteria). Core Function: The Hospital Quality Reporting Program currently supports quality programs for the following areas of care: Inpatient Hospitals, Outpatient Hospitals, Ambulatory Surgical Centers (ASCs), Inpatient Psychiatric Facilities (IPFs),  Prospective Payment System Exempt (PPS Exempt) Cancer Hospitals.", "version": "25", "acronym": "HQR", "status": "", "belongsTo": "", "businessOwnerOrg": "Information Systems Group", "systemMaintainerOrg": "Division of Quality Systems and Operations Support", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2021-06-10", "atoExpirationDate": "2024-06-10"}, {"id": "{318F7724-245B-4f06-B1D2-CC5F83A50C45}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{318F7724-245B-4f06-B1D2-CC5F83A50C45}", "uuid": "16540DBF-F41C-403F-9270-76F512E2BEE3", "name": "Registration for Technical Assistance Portal", "description": "REGTAP serves as a centralized information portal for the Centers for Medicare & Medicaid Services (CMS) resources and training related to the Affordable Care Act (ACA) Health Insurance Marketplaces, the Consolidated Appropriations Act, 2021 (CAA) including the No Surprises Act (NSA) and other CMS policies. Registered users can access a library of resources, search Frequently Asked Questions (FAQs), view Computer Based Trainings (CBTs), submit inquires and register for training events.", "version": "25", "acronym": "REGTAP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Financial Transfers and Operations", "systemMaintainerOrg": "Division of Program Management", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2023-11-16", "atoExpirationDate": "2026-11-15"}, {"id": "{42A575A8-0759-47a7-BE94-8F667CC0C8A8}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{42A575A8-0759-47a7-BE94-8F667CC0C8A8}", "uuid": "*************-4143-A174-915C3FA1EAA1", "name": "Chronic Condition Data Warehouse", "description": "CCW is a research database of fee for service claims, enrollment, and assessments data. It was created in response to Section 723 of the Medicare Modernization Act to provide data for the study of chronic conditions. CCW data are used by numerous individuals including CMS employees, CMS contractors, other DHHS and Federal agencies, State agencies and privately funded researchers. Core Function: * Monthly, quarterly, annually data is loaded to the warehouse. Multiple data sources are mapped across the continuum utilizing beneficiary information.* Provides Beneficiary linked data (claims, assessments, etc.) as extracts to Internal CMS users and external researchers as well as provide direct access to approved users via the VRDC.", "version": "25", "acronym": "CCW", "status": "", "belongsTo": "", "businessOwnerOrg": "", "systemMaintainerOrg": "Research Data Development Group", "state": "Active", "businessOwnerOrgComp": "", "systemMaintainerOrgComp": "OEDA", "atoEffectiveDate": "2022-04-29", "atoExpirationDate": "2025-04-29"}, {"id": "{43A00F28-6DE9-4eaf-834A-0D131514F31D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{43A00F28-6DE9-4eaf-834A-0D131514F31D}", "uuid": "05F7963C-D898-4C5C-BCD4-1F81B9D8F819", "name": "Box Storage Solution", "description": "BSS provides a method for CMS users to transmit documents and files over the public internet with FIPS 199 approved encryption in transit and at rest for file synchronization and sharing in a collaborative way.", "version": "25", "acronym": "BSS", "status": "", "belongsTo": "", "businessOwnerOrg": "Infrastructure and User Services Group", "systemMaintainerOrg": "Infrastructure and User Services Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-03-04", "atoExpirationDate": "2025-03-03"}, {"id": "{8CB7597F-8947-4afe-AFBA-73A346861895}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{8CB7597F-8947-4afe-AFBA-73A346861895}", "uuid": "DBE68C3F-F1E8-4391-B83D-C002E0F2FF12", "name": "Medicare Advantage and Prescription Drug System", "description": "The Medicare Advantage (MA) & Prescription Drug (Part D) (MARx) system maintains enrollment, payment and premium data for Medicare beneficiaries participating in Medicare Part C and D Plans. The MARx system is the primary interface for MA and Prescription Drug Plans (PDP) and provides services to beneficiaries, Plans, states, and CMS. \n\nTransaction Processing: Plans submit batch files which contain enrollment, disenrollment, and various status update transactions to MARx. Using CMS business rules, MARx validates the transactions and accepts or rejects the submitted enrollments, dis-enrollments, or changes. Daily response files report the results of all transactions back to the plans. Notifications from other systems, such as the Medicare Enrollment and Premium Billing System (MEPBS), which report changes in beneficiary demographics, entitlement, health status, low income subsidy status, etc. may affect beneficiary enrollment and payment to the Plans. \n\nPayment Processing: MARx calculates monthly Medicare payments for each Plan and generates payment adjustments when changes in membership and/or beneficiary health status occur. Monthly reports detail enrollment and beneficiary level payment information for the plans and for CMS. \n\nPremium Processing: MARx receives and processes transactions impacting Part C and Part D premiums, Medicare Part B Reduction, and Medicare Part C/D Plan enrollment status. Beneficiaries may elect to have their premiums withheld from their Social Security Administration (SSA) or Railroad Retirement Board (RRB) checks. MARx exchanges data with partner agencies (SSA and RRB) in order to ensure monthly beneficiary premium collections are accurate, establish or terminate withholding, and track expected withholding amounts. Once the withholding agency completes processing each transaction, a response is sent back to MARx informing CMS of the withholding agency's acceptance or rejection of each of the individual premium withholding requests, cancellations, statuses, and amounts. The withholding agency's acceptance or rejection of the transactions is communicated back to the Plans. The Premium Withhold Accounting (PWA) functionality was implemented to reconcile the withheld premium amounts. Based on the reconciliation of the expected and actual premium amounts, the premium dollars to be included in a plan's payment are determined. \n\nUser Interface (UI): The MARx application includes online processing capabilities. The MARx User Interface (UI) provides Plans and CMS the ability to view and update beneficiary, enrollment, payment, and premium information. \n\nCore Functions: MARx supports over 1000 MA/PDP Plans, 50+ million beneficiaries, and generates over $48 billion/month in plan payments.  Responsible for the enrollment of beneficiaries into Medicare Advantage (Part C) and Part D plans; Calculates monthly premiums for Part D plans, Medicare Advantage plan providers and Part D plan providers; Generates the payment file used to pay plan providers; Provides withholding information to SSA and RRB so that the monthly premiums can be deducted from a beneficiary’s SSA or RRB check.", "version": "25", "acronym": "MARx", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Payment Operations", "systemMaintainerOrg": "Division of Medicare Systems Support", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-08-22", "atoExpirationDate": "2024-08-21"}, {"id": "{23A45CA3-4066-4515-8C21-666CE641007E}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{23A45CA3-4066-4515-8C21-666CE641007E}", "uuid": "D1B0E899-208B-40D0-A531-D148CDA4309E", "name": "State Exchange Resource Virtual System", "description": "The State Exchange Resource Virtual Information System (SERVIS) is a mechanism for CMS staff to track the status of State Exchanges with respect to their readiness to operate in accordance with the Affordable Care Act (ACA). \r\nSERVIS is a front-end web portal which allows state grantees to log in and see upcoming events for their state. SERVIS provides shared documents and guidance as well as online functionality to submit questions to CMS and receive responses. Established as part of the Affordable Care Act (ACA). Purpose is to communicate with States regarding the Standup and Maintenance of their State Exchanges.", "version": "25", "acronym": "SERVIS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of State Technical Assistance", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2022-02-04", "atoExpirationDate": "2025-02-04"}, {"id": "{28944193-550C-4951-9B9E-789AC59837CE}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{28944193-550C-4951-9B9E-789AC59837CE}", "uuid": "64A0C7C0-0517-492B-9CF7-BC6FAB9027A0", "name": "Advanced Provider Screening", "description": "APS validates Medicare provider data against external sources and monitors enrollment eligibility, in an effort to meet CMS/ CPI mission goals to prevent waste, fraud and abuse. Core Function: To perform automated screening on new and existing enrollments of providers/suppliers. To apply business rules assessments to identify ineligible providers and potential fraud waste, and abuse on a pre- and post enrollment basis, resulting in alerts and reports to designated CMS business partners.", "version": "25", "acronym": "APS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enrollment Systems", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-04-05", "atoExpirationDate": "2024-04-05"}, {"id": "{2996AE31-719C-418a-8FB5-7A8D94EEC328}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2996AE31-719C-418a-8FB5-7A8D94EEC328}", "uuid": "B544FF41-ED39-4A1F-9746-0936039449F6", "name": "Marketplace Learning Management System", "description": "To provide online training for FFM agents, brokers and assisters that support consumers' enrollment into qualified health plans (QHP) via the Marketplace.  The MLMS also hosts training and provides registration support for various groups within the Center for Program Integrity.   CMS CCIIO staff and end users (agents, brokers, navigators, and certified application counselors) will use the MLMS. Core Function: * Registration and training of Marketplace agents, brokers and assisters to support Marketplace consumers with choosing qualified health plans (QHPs) and program integrity contractors for CMS. This system also supports the training needs of the Center for Program Integrity's Division of Analytic & Sharing Group for unified case management users.", "version": "25", "acronym": "MLMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Consumer Support Group", "systemMaintainerOrg": "Marketplace Innovation and Technology Group", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2023-03-07", "atoExpirationDate": "2026-03-06"}, {"id": "{373C93AA-36BB-4330-85FB-32340BD73DDF}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{373C93AA-36BB-4330-85FB-32340BD73DDF}", "uuid": "DCA72BCC-AB0E-44F6-A75C-CD62F3274E9C", "name": "Payment Record Processing", "description": "Description: MBPRP was developed to create extract records from adjudicated Part A Medicare claims.  These extract records, called \"skeleton records,\" contain business and demographic data elements copied or derived from the adjudicated Medicare claims. Core Function: *Create records that contain a subset of the  Part A Medicare claim records for input to a statistical system that produces state and national statistics.", "version": "25", "acronym": "MBPRP", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Enterprise Architecture and Data Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-06-16", "atoExpirationDate": "2025-06-16"}, {"id": "{15A1127F-2777-4802-8BC4-F221B239B29B}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{15A1127F-2777-4802-8BC4-F221B239B29B}", "uuid": "0074BAC6-7C64-452F-9991-BF844D99196C", "name": "Automated Plan Payment System", "description": "The Automated Plan Payment System (APPS) calculates the monthly payment to Medicare Advantage, Prescription Drug, Demonstration and Cost-Based Plans, and facilitates the payments via Treasury through the HIGLAS system in OFM. APPS contains the banking information needed for OFM to send the payments to the plans through the EFT.  Core Function: * Computes monthly payments to Medicare Advantage, Prescription Drug, Demonstration and Cost plans based on data from HPMS, MARx and PWS. * Computes Annual Part D reconciliation and Coverage Gap Discount payments. * Produces a monthly payment file for the OFM HIGLAS. * Computes and collects sequestration amounts from plan payments.", "version": "25", "acronym": "APPS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Payment Operations", "systemMaintainerOrg": "Division of Payment Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2021-09-17", "atoExpirationDate": "2024-09-17"}, {"id": "{29C4C7F1-6AD2-43c3-A69B-BF6236EA8880}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{29C4C7F1-6AD2-43c3-A69B-BF6236EA8880}", "uuid": "469C6C3C-4117-4177-886B-380878E1935B", "name": "Medicare Fee-for-Service Data Collection System", "description": "This system facilitates pricing for Medicare Part B Drug claims the Clinical Laboratory Fee Schedule, and the Ambulance Fee Schedule.  There are three current different modules under this system.  One module is a web based, secure data input tool for drug manufacturers to report quarterly Medicare Part B drug ASP data, a data validation tool, and a backend database for published reports and analytics.  The other module is also a web based, secure data input tool for laboratories required to submit data on laboratory tests on the Clinical Laboratory Fee Schedule. The third module is an online survey that will collect data from Ambulance providers.  Core Function: * Data collection of Medicare Part B drug average sales price information from drug manufacturers* ASP data validation and crosswalking* Quarterly payment limit calculation and reporting of crosswalk and payment limit data* Data collection of Medicare Part B clinical laboratory private payor data from applicable clinical laboratories* CLFS data validation* CLFS fee schedule generation and reporting*Ambulance Fee Schedule", "version": "25", "acronym": "FFSDCS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Ambulatory Services", "systemMaintainerOrg": "Hospital and Ambulatory Policy Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2022-12-08", "atoExpirationDate": "2025-12-07"}, {"id": "{6356BFCE-F5B4-49b7-AB68-46FDED5E8C7C}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{6356BFCE-F5B4-49b7-AB68-46FDED5E8C7C}", "uuid": "D26ABD1F-0EA2-41BD-BB79-A04FD22A912D", "name": "Office of Hearings Case and Document Management System", "description": "The Office of Hearings Case and Document Management System (OHCDMS) is an application designed to provide case tracking capabilities to Office of Hearings (OH), beginning with the initiation of a case through its final disposition.  OHCDMS is a replacement for an existing OH application and is intended to be used by OH and their customers with the aim for achieving time savings on the part of OH and increased visibility on the part of OH customers.  Capabilities for the system are as follows: Electronic submission of appeal requests to the Provider Reimbursement Review Board; Electronic submission of reclassification applications to the Medicare Geographic Classification Review Board; Electronic submission of appeals to the CMS Hearing Officer; OH case management tools for the appeal and applications noted above, including notifications, reporting, and record management. Core Function: *Intake of new appeal and application requests through CMS portal*Storage of data and documents associated to each case*Issuance of acknowledgements and notices (both manually and system generated)*Case management, including maintenance of party contacts, staff assignments, deadlines and scheduling*Issuance of final decisions*Reporting", "version": "25", "acronym": "OH CDMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Systems and Case Management", "systemMaintainerOrg": "Division of Systems and Case Management", "state": "Active", "businessOwnerOrgComp": "OHI", "systemMaintainerOrgComp": "OHI", "atoEffectiveDate": "2021-05-07", "atoExpirationDate": "2024-05-08"}, {"id": "{39551066-3EFE-4d58-8E07-5E8C370ADFD1}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{39551066-3EFE-4d58-8E07-5E8C370ADFD1}", "uuid": "B24B90E1-3425-4B7A-9D15-5BE22ABCA926", "name": "Eligibility and Enrollment Medicare Online", "description": "ELMO is a Common User Interface for Medicare Beneficiary Demographics, Entitlement/Eligibility, Health Status, Utilization, Low Income Subsidy (LIS), Direct Billing, Third Party Billing, Enrollment, and Premium Information. ELMO will provide inquiry and update access to users based on their approved roles. The system is intended for use by CMS Central Office and Regional Office staff, CMS Contractors supporting Medicare business processes, external agencies such as Social Security Administration (SSA), Railroad Board (RRB), Office of Inspector General (OIG) and State Agencies and other CMS business partners requiring online access to Medicare Beneficiary information.ELMO will consolidate and replace the capabilities of several legacy user interfaces supported by the Medicare Enrolment and Premium Billing Systems (MEPBS) and Medicare Beneficiary Database Suite of Systems (MBDSS). ELMO is being designed for future integration of other Medicare Beneficiary information related user interfaces such as those supported by the Medicare Advantage and Prescription Drug System (MARx). Core Function: * Consolidated User interface for Medicare Beneficiary Demographic, Entitlement, Eligibility, Enrollment, Premium, Health Status, and Low Income Subsidy information", "version": "25", "acronym": "ELMO", "status": "", "belongsTo": "", "businessOwnerOrg": "Medicare Enrollment and Appeals Group", "systemMaintainerOrg": "Enterprise Systems Solutions Group", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-01-17", "atoExpirationDate": "2026-01-16"}, {"id": "{1CDA3AA5-DAB4-4d18-B01A-C36ACC1C19EB}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{1CDA3AA5-DAB4-4d18-B01A-C36ACC1C19EB}", "uuid": "889C239C-3352-44E3-9A51-CB6012591515", "name": "Information Technology Security and Privacy - Computer Based Training", "description": "The Federal Information Security Management Act (FISMA) of 2002 requires all users of Federal information systems to be exposed to security awareness materials at least annually. The ITSP CBT (hereafter referred to as the CBT/LMS) provides CMS with the means to meet this requirement by providing basic information security and privacy awareness training to CMS employees, contractors, students, guest researchers, visitors and others who may need access to CMS information systems and applications and have been issued a CMS User Id. There are currently approximately 46,000 users who are required to complete the CBT. Of this number approximately 2,000 users have newly issued User Ids each year and they are required to complete the CBT within three days of receipt of their account. The remaining users must complete the CBT in conjunction with annual recertification of their CMS User Ids which is required during the anniversary month in which their User ID was originally issued. In addition, due to the large amount of data in the database, the CBT application also provides reporting features and information for the CBT Administrator, 1st Approvers, CAAs, Business Owners, System Developers/Maintainers, ISSOs, and System Point of Contacts which help them to manage, track, and administer users, training events attendance, Job Codes and systems for which they are responsible. Core Function:", "version": "25", "acronym": "ITSP-CBT", "status": "", "belongsTo": "", "businessOwnerOrg": "Information Security and Privacy Group", "systemMaintainerOrg": "Information Security and Privacy Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-05-25", "atoExpirationDate": "2026-05-24"}, {"id": "{2BD804D8-9160-4e12-99B3-1436BB4D70B9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2BD804D8-9160-4e12-99B3-1436BB4D70B9}", "uuid": "A10A61E0-D7EF-45C8-BE07-850CF91D7E7A", "name": "Customer Support Front End System", "description": "The purpose of the CSFES is to edit and process Risk Adjustment Processing System (RAPS) files, Encounter Data (ED) files, Prescription Drug Event (PDE) files, and Medicare Medicaid Plans (MMP) files and to also provide customer support services to the Medicare Advantage Plans. The files are submitted by MA and Part D plans for initial editing and validation of their diagnoses files. If the files are accepted, they get submitted for processing to RAPS, Encounter Data Processing System, or the Drug Data processing System. If files are determined to have errors, they are not processed and are sent back to the plans for correction. After each submission, the CSFES sends reports back to the plans summarizing, what was accepted or not accepted. Customer support services include responding to plan inquiries and providing answers to plans/submitters by telephone, written and e mail inquiries related to RAPS data, ED data, Prescription Drug Data (PDE) data, and recommending solutions to those inquiries. Customer and technical help desk services are provided by a consolidated Customer Support Center for all transactions and reports processed through the front end system.   Core Function: The core business functions that this system performs are:1.  Electronic Data Interchange (EDI)) gateway for:    Encounter files,    Medicare Medicaid Plan (MMP) files,    Prescription Drug Event (PDE) files,    Front End Risk Adjustment System files (RAPS)    2.  Customer Service help and technical support desk support.3.  Sends files to downstream systems (DDPS, EDPS Next Gen, RAPS/RAS, IDR). 4.  Returns reports to plans/submitters from downstream systems 5.  Creates IDR files for the MMP files.", "version": "25", "acronym": "CSFES", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Encounter Data and Risk Adjustment Operations", "systemMaintainerOrg": "Division of Encounter Data and Risk Adjustment Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2023-08-14", "atoExpirationDate": "2026-08-13"}, {"id": "{1EC4C234-32FC-452e-B7F1-5EE02D14C295}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{1EC4C234-32FC-452e-B7F1-5EE02D14C295}", "uuid": "e5cbb3511b232c50b98f62cfe54bcbd8", "name": "Application Programming Interface Gateway", "description": "The CPI API Gateway application aligns with the CPI goals to improve data sharing. It provides real-time access to the CPI Provider and Investigative Systems (PECOS, NPPES, esMD, OnePI, and UCM). The application detail view includes core information for the Law Enforcement and CMS data consumers to perform data analysis. It provides real time access to SOR data through APIs to support CPI's goal of data liberation with openness and collaboration. This capability will enable provider systems to accept data directly from other SOR systems. This is anticipated to improve provider enrollment efficiency across the CPI portfolio.", "version": "25", "acronym": "API Gateway", "status": "", "belongsTo": "", "businessOwnerOrg": "Data Analytics and Systems Group", "systemMaintainerOrg": "Division of Provider Systems Management", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2021-12-13", "atoExpirationDate": "2024-12-13"}, {"id": "{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{47D72BA4-9B65-42b2-B4F6-BC5F91E6C0C9}", "uuid": "EBF05C5C-E261-4BBB-B8D1-E51E9AF6D22C", "name": "Comprehensive Acquisition Management System", "description": "CAMS is an automated, web based, full procurement lifecycle tracking and reporting system powered by PRISM software. Used by CMS components to process requisitions, contracts and invoices electronically.  Provides the methods for reporting CMS contracting data to the Federal Procurement Data System (FPDS).  CAMS Production environment runs a nightly job which refreshes the database.  This database is used to generate management reports used directly or indirectly by OAGM, OFM, COO and many components as an essential tool in tracking the nearly $ 8+ billion in contract obligated funds each year. The PRISM software has been in use at CMS since 1992 and is a key piece of the Department's one HHS Acquisition Integration and Modernization initiative. Core Function: * Plans, organizes, coordinates and manages the activities required to maintain an agency wide acquisition program* Ensures the effective management of CMS acquisition resources* Supports cost/price analyses and evaluations required for the review, negotiation, award, administration, and closeout of contracts* Provides support for field audit capability during the pre award and closeout phases of contract activities", "version": "25", "acronym": "CAMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Data, Systems and Certification", "systemMaintainerOrg": "Division of Data, Systems and Certification", "state": "Active", "businessOwnerOrgComp": "OAGM", "systemMaintainerOrgComp": "OAGM", "atoEffectiveDate": "2022-05-27", "atoExpirationDate": "2025-05-28"}, {"id": "{2D0DC3CB-F6BF-4fbd-B0E1-1A5D3BD8E8FC}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2D0DC3CB-F6BF-4fbd-B0E1-1A5D3BD8E8FC}", "uuid": "B997D89E-E30F-4F79-944C-D330151FC6C8", "name": "Warehouse Librarian - CMS ServiceNow", "description": "The CMS warehouse maintains an inventory of publications, forms, manuals, and commodity items. Providers, intermediaries, advocacy groups, CMS employees, and other government agencies order these items on a daily basis, at no cost to them. Warehouse Librarian is a Commercial Off-The-Shelf (COTS) system that provides hardware and software support for the storage and retrieval of these items.\r\nThe WL system receives requests for stock from two sources - via CMS EFT (Electronic File Transfer) from CMS Product Ordering Website (POW), and via “hot picks”, which are high priority orders called in or e-mailed to the warehouse, on an ad hoc basis, by Inventory Specialists in the Office of Security, Facilities & Logistics Operations, Facilities and Logistics Management Group (OSFLO/FMLG).  Unlike standard orders, hot pick requests are entered directly into WL itself and do not originate in the POW system.  WL processes both types of orders and retrieves materials from carousel and bulk locations.", "version": "25", "acronym": "WL", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Printing and Mail", "systemMaintainerOrg": "Division of Printing and Mail", "state": "Active", "businessOwnerOrgComp": "OSFLO", "systemMaintainerOrgComp": "OSFLO", "atoEffectiveDate": "2023-09-18", "atoExpirationDate": "2024-03-16"}, {"id": "{2D93C5B3-E192-41d3-AA95-14B1801D8B5F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2D93C5B3-E192-41d3-AA95-14B1801D8B5F}", "uuid": "8CAB9394-1AAC-4309-B409-1B820C16830D", "name": "Healthcare.gov", "description": "The Health Insurance Marketplace includes, but is not limited to general web development, systems management support, project management, customer service/communication and support for various consumer tools and mobile technologies. Core Function: * Consumer enrollment* Consumer authentication* Outreach & education* Customer service", "version": "25", "acronym": "Healthcare.gov", "status": "", "belongsTo": "", "businessOwnerOrg": "Web and Emerging Technologies Group", "systemMaintainerOrg": "Web and Emerging Technologies Group", "state": "Active", "businessOwnerOrgComp": "OC", "systemMaintainerOrgComp": "OC", "atoEffectiveDate": "2023-08-07", "atoExpirationDate": "2026-08-06"}, {"id": "{20B7E029-1807-4174-919E-8BA2CA50570D}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{20B7E029-1807-4174-919E-8BA2CA50570D}", "uuid": "463EA776-F0A3-4A39-B236-52C1058B503A", "name": "Quality Service Center", "description": "QSC is a Customer Service Management system used for tracking, monitoring, recording and reporting of user inquires. QSC supports quality programs and IT systems managed and owed by CMS Center for Clinical Standards and Quality. Core Function: Customer service desk provide answer for technical and policy related inquries.", "version": "25", "acronym": "QSC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Quality Systems and Operations Support", "systemMaintainerOrg": "Division of Quality Systems and Operations Support", "state": "Active", "businessOwnerOrgComp": "CCSQ", "systemMaintainerOrgComp": "CCSQ", "atoEffectiveDate": "2023-03-22", "atoExpirationDate": "2026-03-21"}, {"id": "{0FDA1F1E-5F7C-425b-9549-817103577FF5}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0FDA1F1E-5F7C-425b-9549-817103577FF5}", "uuid": "55aede951b5ad05003b5ed39bc4bcb61", "name": "Administrative QIC", "description": "The Administrative Qualified Independent Contractor (AdQIC) provides administrative services in support of the Qualified Independent Contractors (QICs) that perform adjudication services in accordance with §1869 of the Social Security Act, as amended by both the Medicare, Medicaid, and State Children's Health Insurance Program (SCHIP) Benefits Improvement and Protection Act (BIPA) of 2000 and the Medicare Prescription Drug, Improvement and Modernization Act (MMA) of 2003.\r\nThe AdQICs website Q2A.com provides QICs with manual direction for appeals process, quick reference guides for usage of the Medicare Appeals system, and templates for correspondence.  The AdQIC also has an internal workflow management system, document imaging, scanning, and temporary storing.  The appeals system of record is the Medicare Appeals System (MAS).", "version": "25", "acronym": "CM-AdQIC", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Appeals Operations", "systemMaintainerOrg": "Division of Appeals Operations", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2021-12-21", "atoExpirationDate": "2024-12-21"}, {"id": "{546B8964-565A-4d1b-9A73-2B83348CB4A0}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{546B8964-565A-4d1b-9A73-2B83348CB4A0}", "uuid": "E53A3505-2ACF-4298-B012-0CF2C96001BE", "name": "Enterprise Electronic Change Information Management Portal", "description": "Enterprise ECHIMP automates the change management process for FFS and MAPD.  This system was developed in 2006 to automate the initiation, Point of Contact (POC) Review, Clearance and Issuance process for the Medicare Fee-for-Service (FFS) Change Requests (CRs) and Change Management process.  Several years later in 2013, ECHIMP was expanded to include the automation of the Technical Direction Letters (TDLs) issued to the FFS Medicare Administrative Contractors (MACs).  One year later, ECHIMP was further enhanced to automated the initiation and clearance process for the Award Fee Packages (AFPs) issued to the FFS MACs.  Last year, in 2021, ECHIMP was expanded to automate the initiation, review and approval process for the Quality Assurance and Surveillance Plans (QASPs) for the FFS MACs.  Finally, also in 2021, ECHIMP was enhanced to automate the Office of Strategic Operations and Regulatory Affairs, Issuance Records and Information Systems Group, Division of Issuance's data entry and tracking of Non-FFS Internet-Only Manuals.  This automation has streamlined the aforementioned processes and reduced the time to issue CRs, TDLs, AFPs and QASPs.", "version": "25", "acronym": "ECHIMP", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Change and Operations Management", "systemMaintainerOrg": "Medicare Contractor Management Group", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2023-05-02", "atoExpirationDate": "2026-05-01"}, {"id": "{190F0D00-35F9-4c6d-B9EF-C8999C792184}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{190F0D00-35F9-4c6d-B9EF-C8999C792184}", "uuid": "C56EA4EC-C565-438E-BEB9-D3DDC4EDAD38", "name": "COGNOS BI", "description": "Cognos is a Business Intelligence application that provides CMS with a wide range of BI functionality on a single Web services-based architecture. Also, the system delivers a single metadata layer and a single query engine, providing CMS with a single source all relevant data and a complete and consistent view of any business issue or driver.", "version": "25", "acronym": "COGNOS BI", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Enterprise Information Management Services", "systemMaintainerOrg": "Division of Enterprise Information Management Services", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-06-16", "atoExpirationDate": "2025-06-16"}, {"id": "{17A6ECD3-6618-4b98-B2F7-5857DE53C15A}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{17A6ECD3-6618-4b98-B2F7-5857DE53C15A}", "uuid": "fd3b3bf5dbbafb00f99cfd721f9619c0", "name": "Health Data Reporting", "description": "The HDR solution shall enable CMMI to health equity related data, other clinical data, utilization and cost data in an accepted file format from different types of APM participants (eligible clinicians, hospitals, post- acute care facilities and other health care provider entities), Electronic Health Records (EHRs), Health Information Exchanges (HIEs), and other third party data sources.", "version": "25", "acronym": "HDR", "status": "", "belongsTo": "", "businessOwnerOrg": "Business Services Group", "systemMaintainerOrg": "Division of Technology Solutions", "state": "Active", "businessOwnerOrgComp": "CMMI", "systemMaintainerOrgComp": "CMMI", "atoEffectiveDate": "2023-11-30", "atoExpirationDate": "2026-11-29"}, {"id": "{114ADCC8-8B0D-4daa-9669-C0DD7478C1B5}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{114ADCC8-8B0D-4daa-9669-C0DD7478C1B5}", "uuid": "19A78154-8AA9-498E-8AE9-29B9914989A9", "name": "Eligibility Appeals Case Management System", "description": "The Eligibility Appeals Case Management System (EACMS) is a critical components of the Marketplace Eligibility Appeals Operations Support (EAOS).  EACMS is the official data repository of EAOS and support the processing of Marketplace eligibility appeals received for the federal health insurance marketplace and potentially from State Based Exchanges (SBEs). \nEACMS, through various measures receives, stores, displays and processes appeal requests and supporting documentation submitted to the Market Place by consumers, Large Employers, and SHOP.  Activities performed in EACMS include: \n(1)    Manual and electronic receipt of exchange data extracts on a daily or ad-hoc basis from the CMS Federally Facilitated Marketplace (FFM).\n(2)    Appeals processing received from FFE and SBE\n(3)    Appeal reconciliation \n(4)    Appeal notices issued from the system to appellants \n(5)    Processing of appeal reports\nThe EACMS also leveraged to schedule hearings, store and displays hearing transcripts, Federal Hearing Officer (FHO) notes and hearing decisions.  A new  system component called the Appellant Portal (AP) was created in FY23 that provided front end interface for consumers to submit their appeals electronically.", "version": "25", "acronym": "EACMS", "status": "", "belongsTo": "", "businessOwnerOrg": "Marketplace Appeals Group", "systemMaintainerOrg": "Division of Technology and Operations", "state": "Active", "businessOwnerOrgComp": "OHI", "systemMaintainerOrgComp": "OHI", "atoEffectiveDate": "2023-08-08", "atoExpirationDate": "2026-08-07"}, {"id": "{04A24CDD-16F8-43b9-8D3F-AF2DA8577B93}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{04A24CDD-16F8-43b9-8D3F-AF2DA8577B93}", "uuid": "4b87e7221bcb6050f926a93ce54bcbbe", "name": "Marketplace Open Modernization and Optimization Technologies", "description": "Provide a multi-tenant, secure, resilient, scalable, and cost-effective Foundational Enterprise Software Factory (F-ESF) using OpenShift Container Platform that integrates with existing organization paradigms and technologies, in support of Marketplace application development. It is comprised of processes, documentation, and automation in order to facilitate the onboarding of Application Development Organizations (ADOs) onto the platform, leveraging industry-suggested practices. It includes a reference Trusted Software Supply Chain (TSSC) that provides an opinionated approach to a DevSecOps CI/CD platform. The Marketplace supports both non-sensitive and sensitive (PII, FTI) information related to consumers seeking Healthcare coverage.", "version": "25", "acronym": "MOMOT", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Marketplace IT Operations", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "CCIIO", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-10-01", "atoExpirationDate": "2024-10-01"}, {"id": "{13A4C5E0-B1C9-4502-AA98-C2415960D366}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{13A4C5E0-B1C9-4502-AA98-C2415960D366}", "uuid": "30FC9A36-B324-45BE-9E4C-C9593CC34A0F", "name": "Financial Management External Data Gathering Environment", "description": "Financial Management (FM) performs financial transaction with Issuers and provides support for risk mitigation programs (the three Rs – Risk Adjustments [RA], Reinsurance [RI], and Risk Corridors [RC]) for Issuers, Consumers, and State Insurance Actuaries. Functional capabilities include:\n•Collect financial Issuer data.\n•Perform SHOP and individual premium processing.\n•Support reconciliation.\n•Collect data to support risk adjustment program.\n•Calculate Issuers’ credits for risk-mitigation programs (reinsurance, risk corridors, and risk adjustments).\nThe External Data Gathering Environment (EDGE) Server is one of the functional areas of the CMS-FFM project.  EDGE runs processes to receive, validate, store, and report on Issuer medical, pharmaceutical, and supplemental claims and enrollee data.  This data will be used to evaluate and perform Risk Adjustment and Reinsurance calculations for Issuers and other stakeholders.", "version": "25", "acronym": "FM-EDGE", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Technology and Operations", "systemMaintainerOrg": "Division of Marketplace IT Operations", "state": "Active", "businessOwnerOrgComp": "OHI", "systemMaintainerOrgComp": "CCIIO", "atoEffectiveDate": "2021-10-07", "atoExpirationDate": "2024-10-07"}, {"id": "{2E43E494-5A9D-4600-A94A-A34350C3B3C2}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2E43E494-5A9D-4600-A94A-A34350C3B3C2}", "uuid": "95392439db248410296cfd0e0f961941", "name": "Human Resources Enterprise Systems", "description": "Human Resource Enterprise Systems (HRES) house several applications under its umbrella.  The modules are as follows:\r\nHRITS (formally NEIL) is a workflow tracking system that can be used to initiate and store a variety of HR action requests , it is used to request and manage hiring request, it serves as the Employee and Labor Relations Case Management system.\r\nSF-182 Training Form  accessible to all CMS employees for requesting external training. \r\nSF-2809 Benefits form is available to CMS employees with qualifying events that would allow them to adjust current benefits, as well as new employees to the Federal government, and \r\nSuccession Planning Module                                                                                                                                                                                                                                                       Three attended Notification Bots (Ui Path): WGI Bot, NTE Bot, Probation", "version": "25", "acronym": "HRES", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Workforce Systems", "systemMaintainerOrg": "Infrastructure and User Services Group", "state": "Active", "businessOwnerOrgComp": "OHC", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-04-20", "atoExpirationDate": "2026-04-19"}, {"id": "{22979E53-F437-4e57-8A70-248690FC9126}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{22979E53-F437-4e57-8A70-248690FC9126}", "uuid": "56B016BE-FD56-48AD-89A1-2755E0C875BA", "name": "Recovery Audit Contractor Region 4", "description": "The Recovery Audit Program’s mission is to identify and correct Medicare improper payments through the efficient detection and collection of overpayments made on claims of health care services provided to Medicare beneficiaries, and the identification of underpayments to providers so that the CMS can implement actions that will prevent future improper payments in Recovery in all 50 states.", "version": "25", "acronym": "RAC-4", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Recovery Audit Operations", "systemMaintainerOrg": "Division of Recovery Audit Operations", "state": "Active", "businessOwnerOrgComp": "CPI", "systemMaintainerOrgComp": "CPI", "atoEffectiveDate": "2024-01-25", "atoExpirationDate": "2027-01-24"}, {"id": "{243E608E-8CF1-443d-BF55-893147D2B561}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{243E608E-8CF1-443d-BF55-893147D2B561}", "uuid": "425F1012-4F49-4C37-AAD4-B08611650724", "name": "OCISO Systems Security Management", "description": "The Centers for Medicare & Medicaid Services (CMS) Chief Information Security Officer (CISO) developed the OCISO Systems Security Monitoring (OSSM) system to manage the security of CMS systems across the enterprise in accordance with the Office of Management and Budget (OMB) Federal Information Security Management Act (FISMA). The OSSM systems are located within the Baltimore Enterprise Data Center (EDC4) and within the CMS AWS cloud space. OSSM comprises several unique capabilities of security tools and operations across different sub-components: CCIC Security Engineering Center (OSN)•Supports all CCIC teams.  Focuses on design, implementation and support of enterprise security solutions• Creates, develops and deploys new security solutions and technologies• Responsible for deployment, configuration and tuning of CCIC enterprise security tools , CCIC Forensics and Analysis Team (FMAT)• Provides advanced analysis of host computers• Provides advanced analysis of malicious files / artifacts discovered during an investigation/incident• Provides analysis of memory in support of investigations/incidents• Extracts indicators-of-compromise (IOC's) and identifies additional hosts that can be compromised Cyber Threat and Intelligence (CTI)•Provide research and analysis on cyber attacks within CMS environment• Provide research and analysis on cyber attacks within the Healthcare Industry• Provide proactive research on cyber actors targeting US Government and/or Healthcare entities• Responsible for the sharing of cyber threat information to HHS and other CMS Data Centers and partners, CCIC Incident management Team (IMT)•Provides 24x7 incident management support for CMS enterprise and Marketplace systems• Escalation support for patch management issues for high profile vulnerabilities and data calls• Provides single point of communication for CMS leadership for security incidents/updates• Closes out privacy tickets, CCIC Vulnerability Assessment Team (VAT) • Proactively identifies enterprise level cyber risks as First Line of Defense • Increases Security posture by identifying risks to be mitigated • Governs the implementation and operation of four critical controls:•Hardware Asset management • Software Asset Management • Configuration Management • Vulnerability Management, Security Operations Center (ESOC/MSOC)•SOC analysts monitor network traffic and tools to detect, alert and respond to malicious activity • Will open security tickets and track through closure to ensure security events/incidents are addressed, investigated, remediated and closed out • Will create signatures and other methods to detect malicious activity CCIC Penetration Testing Team •Coordinates and conducts all Agency penetration testing on systems operated by and on behalf of CMS. •Utilizes a variety of CMS approved tools to conduct vulnerability assessments and penetration tests for all CMS FISMA systems.•Identify security deficiencies and determine the efficacy of security controls design and implementation.•Provide a basis for evaluating the effectiveness of proposed or implemented security measures;•Provide vulnerability to exploit mapping.•Provide penetration testing services in support of the CMS Continuous Diagnostic and Mitigation process.•Integrate penetration testing activities with other testing efforts, including but not limited to, vulnerability assessments, threat modeling, event detection evaluation, continuous monitoring tool verification, incident response, and incident reporting compliance.", "version": "25", "acronym": "OSSM", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Cyber Threat and Security Operations", "systemMaintainerOrg": "Information Security and Privacy Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-03-24", "atoExpirationDate": "2026-03-23"}, {"id": "{0489F75B-0706-4ac4-B54D-84E821AC6ACF}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0489F75B-0706-4ac4-B54D-84E821AC6ACF}", "uuid": "059e6626dbd76f0085c976708c9619a6", "name": "Identity Management", "description": "The Identity Management (IDM) system utilizes a cloud-based model with multiple products providing specialized services—Okta Identity as a Service (IDaaS), which includes Multi-Factor Authentication (MFA) services; Experian Remote Identity Proofing (RIDP) services; and Cloud Computing Services-Amazon Web Services/ Information Technology Operations (CCS-AWS/ITOps) Hub Hosting—and an IDM Continuous Integration/Continuous Delivery (CI/CD) contractor to provide oversight, guidance, and direction to ensure all IDM products and services are functioning as expected, and to make improvements and enhancements to IDM as the system matures and is maintained in an Agile-centric environment.  Overall, IDM will enable individuals (i.e., internal and external entities to CMS) to gain secure access to over four hundred (400) CMS business applications that consume one or more of the following enterprise identity management services:  authentication (i.e., login/registration and multi-factor authentication), authorization, role management, remote identity proofing (RIDP), reporting, help desk, user interface (UI), and lifecycle management.", "version": "25", "acronym": "IDM", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Systems Solutions Group", "systemMaintainerOrg": "Enterprise Systems Solutions Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2023-02-09", "atoExpirationDate": "2026-02-08"}, {"id": "{234A8695-9333-44f9-AFE4-0F27A6687087}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{234A8695-9333-44f9-AFE4-0F27A6687087}", "uuid": "78702945-61CF-45AF-BCEC-BF87C4CE7DED", "name": "Medicare Provider Analysis and Review System", "description": "Description: MEDPAR is a representation of a beneficiary's stay in an inpatient hospital or skilled nursing facility (SNF) facility. A stay record summarizes all services rendered to a beneficiary from the time of admission to a facility through discharge. MEDPAR is used to calculate the disproportionate share (DSH) fraction. MEDPAR is also used for rate setting and other research initiatives. MEDPAR is also used for litigations. Core Function: THE MEDPAR file is a representation of a beneficiary stay in an Inpatient hospital or Skilled Nursing Facility (SNF) which may include one or more final action claim.", "version": "25", "acronym": "MEDPAR", "status": "", "belongsTo": "", "businessOwnerOrg": "Enterprise Architecture and Data Group", "systemMaintainerOrg": "Enterprise Architecture and Data Group", "state": "Active", "businessOwnerOrgComp": "OIT", "systemMaintainerOrgComp": "OIT", "atoEffectiveDate": "2022-06-15", "atoExpirationDate": "2025-06-15"}, {"id": "{0A005FFD-12FF-49f8-A820-DCBF390BAFCC}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0A005FFD-12FF-49f8-A820-DCBF390BAFCC}", "uuid": "484ae915db4577000db67e560f961916", "name": "OFM ServiceNow", "description": "ServiceNow is the primary platform for tracking IT related requests for the Office of Financial Management (OFM) related to IT incidents, problems, change requests, assets, and other IT business service management data. ServiceNow is a single platform to automate business processes across the Enterprise. ServiceNow lets OFM consolidate fragmented tools and legacy systems while automating service management processes. ServiceNow applications are based on forms and workflow that run on ServiceNow Platform. ServiceNow forms are an interface to database tables and other data sources. ServiceNow IT Business Management allows OFM to track business correspondence and workloads in custom applications and track projects through the development and operations lifecycles. Workflow objects include client-side and server-side workflow that manipulate data, enforce business rules, etc.", "version": "25", "acronym": "OFM SNOW", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Program and Support Systems", "systemMaintainerOrg": "Division of Program and Support Systems", "state": "Active", "businessOwnerOrgComp": "OFM", "systemMaintainerOrgComp": "OFM", "atoEffectiveDate": "2023-04-12", "atoExpirationDate": "2026-04-11"}, {"id": "{0B20BF8D-E092-4149-BF50-78B95E74950F}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{0B20BF8D-E092-4149-BF50-78B95E74950F}", "uuid": "55D4EC76-4917-45EA-A7CF-1DEC4EF11500", "name": "Part D Transaction Facilitator", "description": "PDTransFac facilitates pharmacy transactions by forwarding data at point of sale. The system forwards to pharmacies Part A, B, and D eligibility information and forwards to Part D plans both: financial information for calculation of TrOOP balances; and, as necessary, TrOOP balances from other plans. Core Function: * Part A, B, D Eligibility: Enables the pharmacy to submit a real time transaction to determine if the patient is enrolled in Part A, B, or D* TrOOP Reporting: Report to Part D plans when a supplemental plan to Part D has impacted the out of pocket expenses of a Part D beneficiary* TrOOP Balance Transfer: Determines when a beneficiary has been enrolled into a new plan and requests TrOOP balance information from the old plan(s) and delivers it to the new plan", "version": "25", "acronym": "PDTransFac", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of Part D Policy", "systemMaintainerOrg": "Division of Part D Policy", "state": "Active", "businessOwnerOrgComp": "CM-(MMA)", "systemMaintainerOrgComp": "CM-(MMA)", "atoEffectiveDate": "2022-10-20", "atoExpirationDate": "2025-10-20"}, {"id": "{023BE457-783E-413e-99A2-FB52C55A1616}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{023BE457-783E-413e-99A2-FB52C55A1616}", "uuid": "D76A79FF-D36A-4DBB-A740-11025D2898F0", "name": "Administrative Simplification Enforcement and Testing Tool", "description": "ASETT supports the Health Insurance Portability and Accountability Act of 1996 (HIPAA) and the Affordable Care Act (ACA) processing of complaints for alleged violations of the transaction and code sets (TCS) and Unique Identifiers (UIs) regulations, Operating Rules, and other regulations to be determined by the Secretary. HIPAA covered entities are health plans, clearinghouses, healthcare providers, and their vendors who conduct any of the HIPAA adopted electronic standard transactions. ASETT system has the ability to test disputed health care and pharmacy transactions for compliance with the HIPAA/ACA standards and operating rules. ASETT provides the complainants with the ability to check complaint status and/or update their complaints via the ASETT Web site. Provides DNS with the ability to manage and maintain the overall complaint process. Individuals with access to the Internet may use ASETT. However, each user filing a complaint must first register with the Identity Management (IM) System. Upon successful registration, the user may enter information about HIPAA/ACA complaints and/or test a transaction for compliance. Only the original registrant who enters a complaint may subsequently view it. Staff in the National Standards Group (NSG) Admin, use ASETT to review and investigate complaints. The user's role in ASETT determines access and ability to update a complaint.The ASETT system currently is on the CMS Salesforce cloud platform. The system also is currently used to conduct HIPAA compliance review audits. HIPAA covered entities are randomly selected and audited for HIPAA compliance with the transactions, code sets, unique identifiers and operating rules. Contractor has developed an audit tracking tool for storing, monitoring , administering and reporting compliance review data. Core Function: Provide complainants with the ability to file, check the status of, and update their complaints, including the electronic submission of supporting documents* Provide ability to test transactions * Provide National Standards Group (NSG) staff the ability to manage the overall HIPAA enforcement complaint and compliance review audit process.", "version": "25", "acronym": "ASETT-SF", "status": "", "belongsTo": "", "businessOwnerOrg": "National Standards Group", "systemMaintainerOrg": "National Standards Group", "state": "Active", "businessOwnerOrgComp": "OBRHI", "systemMaintainerOrgComp": "OBRHI", "atoEffectiveDate": "2021-03-10", "atoExpirationDate": "2024-03-10"}, {"id": "{2F2AE2AE-4154-4049-AC6F-5DE4F649C538}", "nextVersionId": "", "previousVersionId": "", "ictObjectId": "{2F2AE2AE-4154-4049-AC6F-5DE4F649C538}", "uuid": "F31A4877-500C-4CE3-AEAB-3A42615A5C25", "name": "Production Performance Monitoring System", "description": "PULSE provides CMS with the to effectively manage, monitor and report on the effectiveness of the Medicare FFS contractors operational performance in the areas of claims processing and other related functions. Contains workload reporting capabilities that allow data to be used for estimating budgets, defining operational issues and comparisons of contractor performance Core Function: * PULSE reporting and analysis tools that are used for monitoring, managing and reporting on Medicare fee for service contractors.\r\n** The CMIS part of the system was decommissioned in June 2020.", "version": "25", "acronym": "PULSE", "status": "", "belongsTo": "", "businessOwnerOrg": "Division of MAC Strategy and Development", "systemMaintainerOrg": "Division of MAC Strategy and Development", "state": "Active", "businessOwnerOrgComp": "CM-(FFS)", "systemMaintainerOrgComp": "CM-(FFS)", "atoEffectiveDate": "2022-08-29", "atoExpirationDate": "2025-08-29"}], "count": 197}