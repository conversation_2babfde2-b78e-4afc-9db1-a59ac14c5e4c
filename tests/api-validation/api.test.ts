import { expect, describe, it } from 'vitest';
import newAtoFull from './new-ato-full.json';
import wmAtoFull from './wm-ato-full.json';
import newSystemSummaryByID from './new-system-summary-by-id-1.json';
import wmSystemSummaryByID from './wm-system-summary-by-id-1.json';
import newSystemSummeryFull from './new-system-summary-list-1.json';
import wmSystemSummeryFull from './wm-system-summary-list-1.json';
import newSystemSummeryVersion from './new-system-summary-list-2.json';
import wmSystemSummeryVersion from './wm-system-summary-list-2.json';
import newSystemSummeryIDs from './new-system-summary-list-3.json';
import wmSystemSummeryIDs from './wm-system-summary-list-3.json';
import newSystemSummeryStatus from './new-system-summary-list-4.json';
import wmSystemSummeryStatus from './wm-system-summary-list-4.json';

describe('New API Validation Tests', () => {
  // FIXME: Files temporary until integration tests are setup
  describe('Authority To Operate', () => {
    it('Should match full results', () => {
      expect(newAtoFull).toEqual(wmAtoFull);
    });
  });

  describe('System Summary By ID', () => {
    it('Should match full results', () => {
      expect(newSystemSummaryByID).toEqual(wmSystemSummaryByID);
    });
  });

  describe('System Summary List', () => {
    describe('List', () => {
      it('Should match full results', () => {
        expect(newSystemSummeryFull).toEqual(wmSystemSummeryFull);
      });

      it('Should match version 25 and includeInSurvey true', () => {
        expect(newSystemSummeryVersion).toEqual(wmSystemSummeryVersion);
      });

      it('Should match idsOnly true', () => {
        expect(newSystemSummeryIDs).toEqual(wmSystemSummeryIDs);
      });

      it('Should match status approved', () => {
        expect(newSystemSummeryStatus).toEqual(wmSystemSummeryStatus);
      });
    });
  });
});
