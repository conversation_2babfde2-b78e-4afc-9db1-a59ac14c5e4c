import {
  describe,
  vi,
  it,
  expect,
} from 'vitest';
import { get } from 'lodash';
import { Request, Response } from 'express';
import errorHandler from '../../src/outerceptors/error-handler';
import { testLogger } from '../test-utils';

describe('errorHandler', async () => {
  it('Should perform a GET request on "/" return with 500', async () => {
    const mockReq = {
      systemApp: {
        logger: testLogger,
      },
    } as unknown as Request;
    const mockRes = {} as unknown as Response;
    mockRes.status = vi.fn().mockReturnValue(mockRes);
    mockRes.json = vi.fn().mockReturnValue(mockRes);
    mockRes.send = vi.fn().mockReturnValue(mockRes);
    mockRes.setHeader = vi.fn().mockReturnValue(mockRes);
    const mockNext = vi.fn();
    const error = new Error('Testing the error handling system');

    errorHandler(error, mockReq, mockRes, mockNext);
    expect(mockRes.status).toHaveBeenCalledOnce();
    expect(mockRes.status).toHaveBeenCalledWith(500);
    expect(mockRes.send).toHaveBeenCalledOnce();
    expect(mockRes.send).toHaveBeenCalledWith({ error: 'Internal Server Error' });

    const errorLog = testLogger.error.mock.lastCall;
    expect(errorLog).not.toBeUndefined();
    expect(errorLog).toHaveLength(1);
    expect(get(errorLog, '[0]', [])).toBeInstanceOf(Error);
    expect(get(errorLog, '[0]', []).message).toEqual('Testing the error handling system');
  });

  it('Should call next with the error if the headers are already sent', () => {
    const mockReq = {} as Request;
    const mockRes = {
      headersSent: true,
    } as Response;
    const mockNext = vi.fn();
    const error = new Error('Testing the error handling system');
    errorHandler(error, mockReq, mockRes, mockNext);
    expect(mockNext).toHaveBeenCalledOnce();
    expect(mockNext).toHaveBeenCalledWith(error);
  });
});
