{"application": {"port": 3000, "corsWhitelist": [], "rootPath": "/", "cookie": {"prefix": "cms-api-base", "secure": true, "httpOnly": false}}, "seedDb": false, "logging": {"logNames": {"audit": "cms-audit", "server": "cms-server"}, "loggers": [{"name": "cms-server", "level": "info"}, {"name": "cms-audit", "level": "info"}]}, "secretStrings": ["CEDAR-DEV-LDAP-Connect-System-Account-2", "Sparx_API_Dev_User", "SparxDatabasesAccounts-DEV", "local/SAPCoreDatabase", "dev/SAPCoreSession"], "systems": {"auth": {"secretString": "CEDAR-DEV-LDAP-Connect-System-Account-2"}, "core": {"dataSource": "core", "dbSecretString": "local/SAPCoreDatabase"}, "session": {"dataSource": "core", "secretString": "dev/SAPCoreSession"}, "sparxea": {"dataSource": "sparxea", "dbSecretString": "SparxDatabasesAccounts-DEV", "apiSecretString": "Sparx_API_Dev_User"}, "cedarSupport": {"dataSource": "cedarSupport", "dbSecretString": "SparxDatabasesAccounts-DEV", "apiSecretString": "Sparx_API_Dev_User"}}}