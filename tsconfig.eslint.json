{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "noEmit": true,
    "allowJs": true,
    "checkJs": true,
    "types": [
      "node",
      "vitest/globals"
    ],
    "typeRoots": [
      "./src/types",
      "./node_modules/@types",
      "./node_modules"
    ],
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "tests/**/*.ts",
    "integration/**/*.ts",
    "eslint.config.js"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "coverage"
  ]
}
