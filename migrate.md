# Migration Instructions

In my environment, my projects are all located in:
  `~/projects/skyward/$repo_name`

If your projects are all in the same root directory, do this:
  `export $PROJECTS_ROOT="<CHANGE ME>"`


Set the environment variable with your preferred project root
directory if you have one.

## Prerequisites

* AWS access to `Cedar/Cedar Application Admin` in CloudTamer.
* Database query client (DBeaver, Datagrip, SQL Server Management Studio, etc.)
* API client utility (Insomnia, Postman, etc.)
* Cursor.

## Setup
### Repositories

* [cms-api-base](https://github.cms.gov/Cedar/cms-api-base/) (typescript)
  ```bash
  mkdir -p $PROJECTS_ROOT/cms-api-base
  cd $PROJECTS_ROOT/cms-api-base

  git clone https://github.cms.gov/Cedar/cms-api-base.git .
  git checkout main
  ```

* [webmethods-generator](https://github.cms.gov/Cedar/webmethods-generator/) (python)
  ```bash
  mkdir -p $PROJECTS_ROOT/webmethods-generator
  cd $PROJECTS_ROOT/webmethods-generator

  git clone https://github.cms.gov/Cedar/webmethods-generator.git .
  git checkout main
  ```

## Steps
### Find Endpoint Files
Run this command, which uses a glob to find person endpoint-related files
in Census Core V2.
```bash
for x in $(ls $PROJECTS_ROOT/webmethods-generator/output/typescript/**/resources/census-core-v2/*person*/**/*.ts); do \
  echo -ne "$x: "; git rev-list -1 HEAD $x; \
done
```

Prints (SHAs will likely be different):
```text
output/typescript/src/resources/census-core-v2/person/find.ts: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e
output/typescript/src/resources/census-core-v2/person/swagger/find.ts: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e
output/typescript/tests/resources/census-core-v2/person/find.test.ts: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e
```

### Create A New Branch in The API Project
```bash
cd $PROJECTS_ROOT/cms-api-base
git checkout -b seas-1234
```

### Copy Files To API Project
```bash
mkdir $PROJECTS_ROOT/cms-api-base/src/resources/census-core-v2/person
mkdir $PROJECTS_ROOT/cms-api-base/src/resources/census-core-v2/person/swagger
mkdir $PROJECTS_ROOT/cms-api-base/tests/resources/census-core-v2/person

cp $PROJECTS_ROOT/webmethods-generator/output/typescript/src/resources/census-core-v2/person/*.ts $PROJECTS_ROOT/cms-api-base/src/resources/census-core-v2/person/
cp $PROJECTS_ROOT/webmethods-generator/output/typescript/src/resources/census-core-v2/person/swagger/*.ts $PROJECTS_ROOT/cms-api-base/src/resources/census-core-v2/person/swagger/
cp $PROJECTS_ROOT/webmethods-generator/output/typescript/tests/resources/census-core-v2/person/*.ts $PROJECTS_ROOT/cms-api-base/tests/resources/census-core-v2/person/
```

### Add SHA Tags to The Top of Files
```typescript
// Generation SHA: f9b84b6a2d3cbb63d70e4002b7ceb6e27949433e

// ... rest of file untouched.
```

### Commit The Raw Generated Files
```bash
git add .
git commit
```

Template:
```text
SEAS-1234: $ticket_title

* Adding output from the last automatic generation.
```

### Begin Work
Open the files in Cursor.  You should get some immediate suggestions for fixes.

The initial set should be easy, but check the suggestions before tabbing through
them.  This will likely get the code to the point where the node server
won't have errors when loading the endpoint if it's having issues (this doesn't
always happen, some load on the first try, some don't).

At this point, you will probably need to figure out which database tables are
involved.  This is where the database client comes in.  You will probably need to:
* Double-check the reference PDF to make sure the tables / views / stored procedures
  are correct.
* Run some basic queries in the database client with the information
  in the explanation PDF and/or the TypeScript files.

  Example for looking at Census notes:
  ```sql
  -- Database: CEDAR_Support
  SELECT TOP 20 *
  FROM System_Census.SYSTEM_SURVEY_PAGE_NOTES
  ORDER BY SYSTEM_SURVEY_PAGE_NOTES.NOTES_CREATED_DATE DESC
  GO
  ```

It is helpful to now call the endpoint in the original webmethods environment
to inspect its output.  The goal is to duplicate this output exactly for the
same inputs.

Once the output in the endpoint matches webmethods, you can ask Cursor to
update the autogenerated unit tests and swagger file to match.
