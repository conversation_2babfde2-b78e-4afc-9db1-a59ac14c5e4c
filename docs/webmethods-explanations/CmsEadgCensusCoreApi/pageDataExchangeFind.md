# Webmethods Service Explanation: CmsEadgCensusCoreApi pageDataExchangeFind

## Service Overview

The `pageDataExchangeFind` service in the `CmsEadgCensusCoreApi` package is designed to retrieve comprehensive data exchange information for a specific IT system. Its primary business purpose is to provide a detailed view of how a given system interacts with other systems or stakeholders through data exchanges. This includes both inbound and outbound data flows, as well as the current status of these exchanges.

The service accepts a `systemId` as a mandatory input parameter, which identifies the IT system for which data exchange information is to be retrieved. An optional `version` parameter allows for filtering data exchanges by a specific version, though this parameter is currently passed through to an underlying service but not explicitly used for filtering within the provided flow. The service is expected to output a structured JSON object containing a list of data exchanges associated with the provided `systemId` and their respective statuses.

Key validation rules include:
*   The `systemId` input parameter is mandatory. If it is not provided, the service will return a "400 Bad Request" error.
*   Internal calls also validate a `direction` parameter (which is hardcoded to "both" within this service but is part of downstream service inputs); an invalid value for this internal parameter can also lead to a "400 Bad Request" error.

The service has predefined error handling for common scenarios, ensuring a consistent error response format.

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageDataExchangeFind`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow" services. These services are represented by XML files (`.flow` and `.ndf`) and define a sequence of steps, much like a flowchart or pseudocode.

*   **SEQUENCE**: This element represents a block of sequential operations. It's similar to a standard function body or a `try` block in other programming languages.
    *   `EXIT-ON="FAILURE"`: If any step within this sequence fails, the execution of the entire sequence stops, and control passes to an enclosing CATCH block or exits the service. This is analogous to an unhandled exception.
    *   `FORM="TRY"` or `FORM="CATCH"`: These specify whether a sequence is part of a `try` or `catch` block for error handling. A `TRY` block executes its steps, and if an error occurs, control immediately transfers to the associated `CATCH` block.
*   **BRANCH**: This element acts like a `switch` statement or an `if-else if-else` chain. It evaluates a specified pipeline variable (`SWITCH="/variableName"`) and executes the first child step (or sequence of steps) whose label matches the variable's value.
    *   `LABELEXPRESSIONS="true"`: This attribute enables more complex conditional logic for branches, allowing labels to be boolean expressions (e.g., `%variable% != $null`).
    *   `$null`: A special label in a `BRANCH` that matches when the switch variable is `null` (or empty string in some contexts).
    *   `$default`: A special label in a `BRANCH` that acts as the `else` or default case, executing if no other branch condition matches.
*   **MAP**: This is a powerful step used for data transformation and manipulation within the service's "pipeline" (the data context passed between steps).
    *   **MAPSET**: Assigns a literal value or a constant expression to a field in the pipeline.
    *   **MAPCOPY**: Copies the value from one field in the pipeline to another. This is crucial for transforming data from one structure to another or for moving data between different parts of the pipeline.
    *   **MAPDELETE**: Removes a field from the pipeline. This is important for cleanup and preventing unnecessary data from being carried forward.
    *   `MODE="INPUT"` / `MODE="OUTPUT"`: These modes are used when mapping data to or from a service being invoked. An `INPUT` map defines how data from the current service's pipeline is transformed and passed as input to the invoked service. An `OUTPUT` map defines how the output from the invoked service is transformed and brought back into the current service's pipeline.
*   **INVOKE**: This step calls another Webmethods service, which can be another Flow service, a Java service, or an Adapter service (like JDBC or SAP adapters). It's akin to calling a function or method in traditional programming.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods Flow services support structured error handling. A `SEQUENCE` with `FORM="TRY"` defines the block of code to monitor for errors. If an error (exception) occurs within this `TRY` block, execution immediately jumps to a `SEQUENCE` with `FORM="CATCH"` that is a sibling to the `TRY` block. The `pub.flow:getLastError` service is typically called in the `CATCH` block to retrieve details about the error, which can then be used to construct a meaningful error response.
*   **LOOP**: This step iterates over elements in an array or a list of documents within the pipeline. It functions similarly to a `for-each` loop in other languages.
    *   `IN-ARRAY`: Specifies the input array to iterate over.
    *   `OUT-ARRAY`: If specified, the output of each iteration is collected into this new array.

## Database Interactions

The service interacts with a Microsoft SQL Server database, specifically querying a view named `Sparx_System_DataExchange`. The database connection is managed by a JDBC Adapter connection named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.

*   **Database Connection Configuration**:
    *   Database Name: `Sparx_Support`
    *   Server Name: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   Port Number: `1433`
    *   User: `sparx_dbuser`
    *   Password: (retrieved from a secure password alias)
    *   Transaction Type: `NO_TRANSACTION` (indicating that this connection does not manage transactions, meaning each adapter call is an auto-committed transaction).

*   **Database Operations and SQL Query**:
    The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues`, which is a JDBC Adapter service performing a `SELECT` operation.
    The SQL query executed is:

    ```sql
    SELECT
        t1."Sender ID", t1."Sender Name", t1."Sparx Sender ID", t1."Sparx Sendor GUID",
        t1."Sender Type", t1."Connection Name", t1."Connector ID", t1."Connection GUID",
        t1."Receiver ID", t1."Receiver Name", t1."Sparx Receiver ID", t1."Sparx Receiver GUID",
        t1."Receiver Type", t1."Exchange Description", t1."Exchange Format", t1."Exchange Format Other",
        t1."Exchange Contains PHI", t1."Exchange Contains PII", t1."Exchange Frequency",
        t1."Exchange Includes Banking Data", t1."Exchange includes Beneficiary Address Data",
        t1."Exchange Supports Mailing to Beneficiaries", t1."IE Agreement",
        t1."Number of Records Exchanged", t1."Data Shared via API", t1."Connection Direction",
        t1."Object State", t1."Retire Date", t1."Address Data Editable",
        t1."Contains Health Disparity Data", t1."API Ownership", t1."Type of Data",
        t1."Type of Data ID", t1."Exchange Start Date", t1."Exchange End Date",
        t1."Exchange Version", t1."Beneficiary Address Purpose",
        t1."Exchange Connection Authenticated", t1."Exchange Contains CUI",
        t1."Exchange CUI Description", t1."Exchange CUI Type",
        t1."Exchange Network Protocol", t1."Exchange Network Protocol Other"
    FROM
        Sparx_System_DataExchange t1
    WHERE
        t1."Sparx Sendor GUID" = ? OR
        t1."Sparx Receiver GUID" = ? OR
        t1."Connection GUID" = ?
    ```

*   **Used Database Tables/Views/Stored Procedures**:
    *   **Views**: `Sparx_System_DataExchange` (within the `dbo` schema, based on connection configuration).

*   **Data Mapping between Service Inputs and Database Parameters**:
    The `systemId` input to `pageDataExchangeFind` is passed to the `exchangeFindList` service. This service then internally calls `findExchange`, which maps the `systemId` to the `Sparx Sendor GUID` and `Sparx Receiver GUID` parameters in the `getExchangeValues` JDBC adapter service call. The `Connection GUID` parameter in the adapter's WHERE clause is present but is not mapped to any input in the provided flow, meaning it will likely be passed as `null` to the SQL query. This implies that the current service primarily queries by sender or receiver GUIDs.

## External API Interactions

Based on the provided Webmethods flow files, there are no direct external API interactions (such as calls to other REST or SOAP web services) within the `pageDataExchangeFind` service or its directly invoked dependencies (`exchangeFindList`, `pageDataExchangeStatusFind`). All `INVOKE` statements observed refer to other internal Webmethods services or adapter services that interact with a database.

## Main Service Flow (`pageDataExchangeFind`)

The `pageDataExchangeFind` service orchestrates the retrieval and aggregation of data exchange information by following these steps:

1.  **Initial Validation**:
    *   The service first checks if the mandatory `systemId` input parameter is provided.
    *   If `systemId` is null or empty, it immediately sets an error response with HTTP status 400 (Bad Request), a message indicating that `systemId` must be provided, and then exits the service.

2.  **Retrieve Core Exchange Data**:
    *   It invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeFindList` service.
    *   The `systemId` from the request and a hardcoded `direction` of "both" are passed as inputs to this service. The optional `version` is also passed along.
    *   The output, `_generatedResponse` (of type `ExchangeFindResponse`), is copied to a pipeline variable named `Exchanges`, and then `_generatedResponse` is cleared from the pipeline.

3.  **Retrieve Exchange Status Data**:
    *   Concurrently or sequentially (depending on how `pageDataExchangeStatusFind` operates, but sequentially in this flow), it calls `cms.eadg.census.core.api.v02.systemCensus_.services:pageDataExchangeStatusFind`. This service is presumed to retrieve status information for data exchanges related to the `systemId`.
    *   The `systemId` and a hardcoded `direction` of "both" are again passed as inputs.
    *   The output (`_generatedResponse` of type `PageDataExchangeStatusFindResponse`) is copied to a pipeline variable named `Status`, and then `_generatedResponse` and the internal `direction` variable are cleared.

4.  **Process and Assemble Response**:
    *   A `BRANCH` step then checks for any pre-existing error messages in `SetResponse` (which might have been set by the invoked `exchangeFindList` service).
        *   **If no error (`SetResponse` is null)**:
            *   It proceeds to check if any data exchange statuses were found (i.e., `Status/count > 0`).
                *   **If statuses are found (`%Status/count% > 0`)**:
                    *   It invokes `cms.eadg.census.core.api.v02.systemCensus_.operations.pageDataExchangeFind:mapResponse`. This critical service is responsible for merging the `Exchanges` (detailed exchange data) and `Status` (status information) into the final `PageDataExchange` output structure, performing necessary data transformations and aggregations.
                    *   The resulting `PageDataExchange` object is then copied to `_generatedResponse`, which is the official output of the main service.
                *   **If no statuses are found (`$default` branch for Status count)**:
                    *   It constructs a default `_generatedResponse` of type `PageDataExchange`. This response includes the `systemId`, sets `pageName` to "DataExchange", `count` to `0`, and provides an empty `DataExchanges` array, indicating that no data exchanges were found for the given system.
        *   **If an error was already present (`SetResponse` is not null)**:
            *   The main service exits with a `FAILURE` signal, allowing its caller to handle the error based on the `SetResponse` object.

5.  **Cleanup**:
    *   Finally, regardless of the success or failure path (but before exiting due to a `FAILURE` signal), a `MAP` step performs cleanup by deleting various intermediate variables from the pipeline, such as `token`, `systemId` (from input), `Status`, `Exchanges`, and `version`, to keep the pipeline clean.

6.  **Error Handling**:
    *   The entire main flow is wrapped in a `TRY` block. If any unhandled error occurs during execution, control transfers to the `CATCH` block.
    *   In the `CATCH` block, `pub.flow:getLastError` retrieves the details of the exception.
    *   This error information is then passed to `cms.eadg.utils.api:handleError`, a utility service that constructs a standardized error response, typically setting the HTTP status to 500 (Internal Server Error) with a detailed message.
    *   Further cleanup of error-related variables is performed before the service concludes.

## Dependency Service Flows

The `pageDataExchangeFind` service relies on several other services to perform its function.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeFindList`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: This service is responsible for querying the `Sparx_System_DataExchange` database view to retrieve raw data exchange records based on `systemId` and `direction`. It acts as the primary data retrieval layer for exchange details.
    *   **Integration with Main Flow**: The `pageDataExchangeFind` service invokes this service early in its execution to get the core data exchange records.
    *   **Input/Output Contract**:
        *   **Input**: `systemId` (string, required), `direction` (string, required, typically "sender", "receiver", or "both"), `version` (string, optional).
        *   **Output**: `_generatedResponse` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeFindResponse`) containing `Exchanges` (array of `Exchange` records) and a `count`, or `SetResponse` in case of validation errors or other failures.
    *   **Specialized Processing**:
        *   Performs initial input validation for `systemId` and `direction`.
        *   Uses a `BRANCH` statement to call the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues` JDBC adapter service. The `systemId` is mapped to either `Sparx Sendor GUID`, `Sparx Receiver GUID`, or both, depending on the `direction` parameter.
        *   Iterates through the raw database results (`getExchangeValuesOutput/results`) obtained from the adapter. For each result row, it maps the flattened database columns into structured fields of the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeValues` document type. This initial mapping is primarily a one-to-one field copy from JDBC result set column names to Webmethods document type field names.
        *   Then, it invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindList:mapExchange` to transform and aggregate these `ExchangeValues` into the final `ExchangeFindResponse` format.
        *   Includes a `TRY/CATCH` block for error handling, leveraging `pub.flow:getLastError` and `cms.eadg.utils.api:handleError` for standardized error responses.

*   **`cms.eadg.census.core.api.v02.systemCensus_.services:pageDataExchangeStatusFind`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: (Based on naming and context, as its flow file is not provided) This service is expected to retrieve status-related information for data exchanges from a separate source, possibly a different database or system.
    *   **Integration with Main Flow**: `pageDataExchangeFind` calls this service to enrich the core data exchange records with their current operational or review statuses.
    *   **Input/Output Contract (presumed)**:
        *   **Input**: `systemId` (string), `direction` (string, optional).
        *   **Output**: `_generatedResponse` (type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchangeStatusFindResponse`) containing `ExchangeStatus` (array of `PageDataExchangeStatus` records) and a `count`, or `SetResponse` for errors.
    *   **Specialized Processing**: Would likely involve its own data retrieval logic (e.g., JDBC calls to a different table or external API calls) and mapping to the `PageDataExchangeStatus` document type.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindList:findExchange`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: This operation within `exchangeFindList` is responsible for executing the actual database query via the JDBC adapter and performing the first stage of data transformation from raw database results into the `ExchangeValues` document type.
    *   **Integration with `exchangeFindList`**: It is directly invoked by `exchangeFindList` as a sub-routine.
    *   **Input/Output Contract**:
        *   **Input**: `systemId`, `direction`, `version`.
        *   **Output**: `ExchangeFindResponse` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeFindResponse`).
    *   **Specialized Processing**:
        *   Initializes internal counters (`tempExchangeId`, `dataTypeIndex`, `exchangeIndex`, `count`) used for mapping logic.
        *   Determines which part of the WHERE clause to populate based on the `direction` input ("sender", "receiver", or "both").
        *   Calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues` (the JDBC adapter service).
        *   Crucially, it then processes the `getExchangeValuesOutput/results` by looping over them. Within this loop, it performs data type conversions (strings to dates/booleans) and string tokenization (delimited strings to arrays) as listed in the Data Structures section.
        *   After the loop, it invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindList:mapExchange` to aggregate the transformed records.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindList:mapExchange`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: This operation handles the complex aggregation and deduplication logic. The raw database query might return multiple rows for the "same" logical exchange if that exchange has multi-valued attributes (e.g., multiple "Type of Data" entries stored as separate rows linked by `exchangeId`). This service ensures that each unique exchange appears only once in the output, with its multi-valued attributes correctly grouped into arrays.
    *   **Integration with `findExchange`**: It is invoked by `findExchange` after the initial data retrieval and basic row-level mapping.
    *   **Input/Output Contract**:
        *   **Input**: `Exchange` (an array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeValues`).
        *   **Output**: `ExchangeFindResponse` (single record of type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeFindResponse`).
    *   **Specialized Processing**:
        *   It uses a `LOOP` and a `BRANCH` with `LABELEXPRESSIONS` to compare the `exchangeId` of the current record with `tempExchangeId` (the `exchangeId` of the previous record processed).
        *   If `exchangeId` is new (`%tempExchangeId% != %Exchange/exchangeId%`), it increments the overall `exchangeIndex` and `count`, maps all fields of the current `ExchangeValues` record into a new `Exchange` entry in the output `ExchangeFindResponse/Exchanges` array, and resets the `dataTypeIndex` (for nested arrays).
        *   If `exchangeId` is the same (`%tempExchangeId% == %Exchange/exchangeId%`), it only increments the `dataTypeIndex` and maps the "Type of Data ID" and "Type of Data Name" to the already existing `Exchange.typeOfData` array in the current `Exchange` object, effectively appending to the array. This is a common pattern for handling one-to-many relationships from a flattened SQL result set.
        *   It also ensures that certain arrays (like `typeOfData`, `connectionFrequency`, `businessPurposeOfAddress`) are initialized as empty arrays if no values are present, rather than being `null`.

*   **`cms.eadg.utils.api:handleError`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: A generic utility service for standardizing error responses across the API.
    *   **Integration**: Used in the `CATCH` blocks of `pageDataExchangeFind` and `exchangeFindList`.
    *   **Input/Output Contract**:
        *   **Input**: `lastError` (from `pub.flow:getLastError`), `SetResponse` (optional, can be pre-populated error details).
        *   **Output**: Modifies the `SetResponse` document in the pipeline, populating it with error code (e.g., 500), phrase, result, and the error message extracted from `lastError`.
    *   **Specialized Processing**: Formats error details into a standard `SetResponse` structure for consistent API error reporting.

*   **`cms.eadg.utils.api:setResponse`** (`TYPE=DEPENDENCY_FILE`)
    *   **Purpose**: A generic utility service to set the final HTTP response status code and body based on a standardized `SetResponse` document.
    *   **Integration**: Called at the very end of services that return an HTTP response (or within error handling flows).
    *   **Input/Output Contract**:
        *   **Input**: `SetResponse` (containing `responseCode`, `responsePhrase`, `result`, `message`, and `format` like "application/json" or "application/xml").
        *   **Output**: Sets the HTTP response and clears the input `SetResponse` and `responseString` from the pipeline.
    *   **Specialized Processing**:
        *   Maps `SetResponse` fields to a generic `Response` document.
        *   Branches based on the desired `format` (e.g., "application/json" or "application/xml").
        *   Uses `pub.json:documentToJSONString` or `pub.xml:documentToXMLString` to serialize the `Response` document into a string.
        *   Calls `pub.flow:setResponseCode` to set the HTTP status code and `pub.flow:setResponse2` to set the HTTP response body and content type.

## Data Structures and Types

The service heavily relies on Webmethods "Document Types" (recref), which are analogous to data classes or interfaces in object-oriented programming, defining the structure and types of data objects.

*   **Input Data Model (`pageDataExchangeFind` Service)**:
    *   `systemId`: `string`, **required**. This is the unique identifier for the system whose data exchanges are being queried.
    *   `version`: `string`, *optional*. Specifies a particular version of the data exchanges to retrieve.

*   **Output Data Model (`_generatedResponse` of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchange`)**:
    This is the main output structure, providing a consolidated view of data exchanges and their statuses.
    *   `systemId`: `string`. The ID of the system for which data exchanges were retrieved.
    *   `pageName`: `string`. A constant value "DataExchange" (or possibly dynamic, but hardcoded to "DataExchange" if no exchanges are found).
    *   `count`: `java.math.BigInteger`. The total number of unique data exchanges found for the system.
    *   `DataExchanges`: `Array` of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:DataExchanges` records. Each element in this array represents a single data exchange.

*   **Nested Data Structure: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:DataExchanges`**:
    This document type combines the core exchange details with its status and other flags.
    *   `direction`: `string`. The direction of the data exchange relative to the `systemId` (e.g., "receiver").
    *   `deleted`: `java.lang.Boolean`. Indicates if the exchange is logically deleted.
    *   `undeleted`: `java.lang.Boolean`. Indicates if the exchange was previously deleted and then restored.
    *   `updated`: `java.lang.Boolean`. Indicates if the exchange record has been updated.
    *   `Exchange`: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Exchange`. Contains the detailed information about the data exchange.
    *   `Status`: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchangeStatus`. Contains the status information for this specific exchange.

*   **Nested Data Structure: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Exchange`**:
    This document type (and its identical counterpart `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`) holds the detailed attributes of a data exchange. Many of these fields are mapped directly from database columns.
    *   `exchangeId`: `string`. Unique identifier for the exchange.
    *   `exchangeName`: `string`. Name of the data exchange.
    *   `exchangeDescription`: `string`. Description of the exchange.
    *   `exchangeVersion`: `string`. Version of the exchange.
    *   `exchangeState`: `string`. Current state of the exchange (e.g., "Active").
    *   `exchangeStartDate`: `java.util.Date`. Start date of the exchange.
    *   `exchangeEndDate`: `java.util.Date`. End date of the exchange.
    *   `exchangeRetiredDate`: `java.util.Date`. Retirement date of the exchange.
    *   `fromOwnerId`: `string`. ID of the sending system/owner.
    *   `fromOwnerName`: `string`. Name of the sending system/owner.
    *   `fromOwnerType`: `string`. Type of the sending owner (e.g., "application").
    *   `toOwnerId`: `string`. ID of the receiving system/owner.
    *   `toOwnerName`: `string`. Name of the receiving system/owner.
    *   `toOwnerType`: `string`. Type of the receiving owner.
    *   `connectionFrequency`: `Array` of `string`. How often data is exchanged (e.g., "Daily", "Weekly").
    *   `dataExchangeAgreement`: `string`. Indicates existence of data exchange agreement.
    *   `containsBeneficiaryAddress`: `java.lang.Boolean`. Indicates if beneficiary address data is exchanged.
    *   `businessPurposeOfAddress`: `Array` of `string`. Business purposes for exchanging address data.
    *   `isAddressEditable`: `java.lang.Boolean`. Indicates if address data is editable.
    *   `containsPii`: `java.lang.Boolean`. Indicates if Personally Identifiable Information (PII) is exchanged.
    *   `containsPhi`: `java.lang.Boolean`. Indicates if Protected Health Information (PHI) is exchanged.
    *   `containsHealthDisparityData`: `java.lang.Boolean`. Indicates if health disparity data is exchanged.
    *   `containsBankingData`: `java.lang.Boolean`. Indicates if banking data is exchanged.
    *   `isBeneficiaryMailingFile`: `java.lang.Boolean`. Indicates if it's a beneficiary mailing file.
    *   `sharedViaApi`: `java.lang.Boolean`. Indicates if data is shared via API.
    *   `apiOwnership`: `string`. Ownership of the API.
    *   `typeOfData`: `Array` of `record` (with `id`: `string`, `name`: `string`). Describes the types of data exchanged.
    *   `numOfRecords`: `string`. Number of records exchanged.
    *   `dataFormat`: `string`. Format of the data.
    *   `dataFormatOther`: `string`. Other data formats.
    *   `exchangeContainsCUI`: `java.lang.Boolean`. Indicates if Controlled Unclassified Information (CUI) is exchanged.
    *   `exchangeCUIDescription`: `string`. Description of CUI.
    *   `exchangeCUIType`: `Array` of `string`. Types of CUI.
    *   `exchangeConnectionAuthenticated`: `java.lang.Boolean`. Indicates if the connection is authenticated.
    *   `exchangeNetworkProtocol`: `Array` of `string`. Network protocols used.
    *   `exchangeNetworkProtocolOther`: `string`. Other network protocols.

*   **Nested Data Structure: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchangeStatus`**:
    This document type holds the status information for a data exchange related to a specific system/partner.
    *   `exchangeId`: `string`. The ID of the exchange.
    *   `systemId`: `string`. The ID of the system.
    *   `systemStatus`: `string`. Status from the system's perspective.
    *   `partnerId`: `string`. ID of the partner system.
    *   `partnerStatus`: `string`. Status from the partner's perspective.
    *   `reviewerStatus`: `string`. Status from a reviewer's perspective.
    *   `direction`: `string`. Direction of the exchange.
    *   `deleted`: `java.lang.Boolean`. Indicates if this status record is deleted.

*   **Helper Data Structure: `cms.eadg.utils.api.docs:SetResponse`**:
    A standardized document type for constructing API responses, especially error responses.
    *   `responseCode`: `string` (e.g., "200", "400", "500").
    *   `responsePhrase`: `string` (e.g., "OK", "Bad Request", "Internal Server Error").
    *   `result`: `string` ("success" or "error").
    *   `message`: `Array` of `string`. Detailed messages or error descriptions.
    *   `format`: `string` (e.g., "application/json", "application/xml").

*   **Source Database Column to Output Object Property Mapping**:
    This mapping primarily occurs within `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindList:mapExchange`, transforming data from the `Sparx_System_DataExchange` view rows into structured `Exchange` objects which are then embedded in the final `PageDataExchange` output.

    *   `"Connection GUID"`: `PageDataExchange.DataExchanges[].Exchange.exchangeId`
    *   `"Connection Name"`: `PageDataExchange.DataExchanges[].Exchange.exchangeName`
    *   `"Exchange Description"`: `PageDataExchange.DataExchanges[].Exchange.exchangeDescription`
    *   `"Exchange Version"`: `PageDataExchange.DataExchanges[].Exchange.exchangeVersion`
    *   `"Object State"`: `PageDataExchange.DataExchanges[].Exchange.exchangeState`
    *   `"Exchange Start Date"`: `PageDataExchange.DataExchanges[].Exchange.exchangeStartDate` (converted to `java.util.Date`)
    *   `"Exchange End Date"`: `PageDataExchange.DataExchanges[].Exchange.exchangeEndDate` (converted to `java.util.Date`)
    *   `"Retire Date"`: `PageDataExchange.DataExchanges[].Exchange.exchangeRetiredDate` (converted to `java.util.Date`)
    *   `"Sparx Sendor GUID"`: `PageDataExchange.DataExchanges[].Exchange.fromOwnerId`
    *   `"Sender Name"`: `PageDataExchange.DataExchanges[].Exchange.fromOwnerName`
    *   `"Sender Type"`: `PageDataExchange.DataExchanges[].Exchange.fromOwnerType`
    *   `"Sparx Receiver GUID"`: `PageDataExchange.DataExchanges[].Exchange.toOwnerId`
    *   `"Receiver Name"`: `PageDataExchange.DataExchanges[].Exchange.toOwnerName`
    *   `"Receiver Type"`: `PageDataExchange.DataExchanges[].Exchange.toOwnerType`
    *   `"Exchange Frequency"`: `PageDataExchange.DataExchanges[].Exchange.connectionFrequency` (tokenized string `|` into array)
    *   `"IE Agreement"`: `PageDataExchange.DataExchanges[].Exchange.dataExchangeAgreement`
    *   `"Exchange includes Beneficiary Address Data"`: `PageDataExchange.DataExchanges[].Exchange.containsBeneficiaryAddress` (boolean)
    *   `"Beneficiary Address Purpose"`: `PageDataExchange.DataExchanges[].Exchange.businessPurposeOfAddress` (tokenized string `|` into array)
    *   `"Address Data Editable"`: `PageDataExchange.DataExchanges[].Exchange.isAddressEditable` (boolean)
    *   `"Exchange Contains PII"`: `PageDataExchange.DataExchanges[].Exchange.containsPii` (boolean)
    *   `"Exchange Contains PHI"`: `PageDataExchange.DataExchanges[].Exchange.containsPhi` (boolean)
    *   `"Contains Health Disparity Data"`: `PageDataExchange.DataExchanges[].Exchange.containsHealthDisparityData` (boolean)
    *   `"Exchange Includes Banking Data"`: `PageDataExchange.DataExchanges[].Exchange.containsBankingData` (boolean)
    *   `"Exchange Supports Mailing to Beneficiaries"`: `PageDataExchange.DataExchanges[].Exchange.isBeneficiaryMailingFile` (boolean)
    *   `"Data Shared via API"`: `PageDataExchange.DataExchanges[].Exchange.sharedViaApi` (boolean)
    *   `"API Ownership"`: `PageDataExchange.DataExchanges[].Exchange.apiOwnership`
    *   `"Type of Data ID"`: `PageDataExchange.DataExchanges[].Exchange.typeOfData[].id` (nested object in array)
    *   `"Type of Data"`: `PageDataExchange.DataExchanges[].Exchange.typeOfData[].name` (nested object in array)
    *   `"Number of Records Exchanged"`: `PageDataExchange.DataExchanges[].Exchange.numOfRecords`
    *   `"Exchange Format"`: `PageDataExchange.DataExchanges[].Exchange.dataFormat`
    *   `"Exchange Format Other"`: `PageDataExchange.DataExchanges[].Exchange.dataFormatOther`
    *   `"Exchange Contains CUI"`: `PageDataExchange.DataExchanges[].Exchange.exchangeContainsCUI` (boolean)
    *   `"Exchange Connection Authenticated"`: `PageDataExchange.DataExchanges[].Exchange.exchangeConnectionAuthenticated` (boolean)
    *   `"Exchange CUI Description"`: `PageDataExchange.DataExchanges[].Exchange.exchangeCUIDescription`
    *   `"Exchange CUI Type"`: `PageDataExchange.DataExchanges[].Exchange.exchangeCUIType` (tokenized string `|` into array)
    *   `"Exchange Network Protocol"`: `PageDataExchange.DataExchanges[].Exchange.exchangeNetworkProtocol` (tokenized string `|` into array)
    *   `"Exchange Network Protocol Other"`: `PageDataExchange.DataExchanges[].Exchange.exchangeNetworkProtocolOther`

    Additionally, fields from `pageDataExchangeStatusFind`'s output (`PageDataExchangeStatusFindResponse.ExchangeStatus[]`) are mapped to `PageDataExchange.DataExchanges[].Status`:
    *   `ExchangeStatus.exchangeId`: `PageDataExchange.DataExchanges[].Status.exchangeId`
    *   `ExchangeStatus.systemId`: `PageDataExchange.DataExchanges[].Status.systemId`
    *   `ExchangeStatus.systemStatus`: `PageDataExchange.DataExchanges[].Status.systemStatus`
    *   `ExchangeStatus.partnerId`: `PageDataExchange.DataExchanges[].Status.partnerId`
    *   `ExchangeStatus.partnerStatus`: `PageDataExchange.DataExchanges[].Status.partnerStatus`
    *   `ExchangeStatus.reviewerStatus`: `PageDataExchange.DataExchanges[].Status.reviewerStatus`
    *   `ExchangeStatus.direction`: `PageDataExchange.DataExchanges[].Status.direction`
    *   `ExchangeStatus.deleted`: `PageDataExchange.DataExchanges[].Status.deleted`

    Note: `PageDataExchange.DataExchanges[].updated` and `PageDataExchange.DataExchanges[].undeleted` are defined in the `DataExchanges` document type but no explicit mapping for them from `PageDataExchangeStatusFindResponse` is visible in the provided flows. This suggests either they are always default values, or mapped by an unlisted transformation within `pageDataExchangeStatusFind`, or perhaps are meant to be derived from other logic.

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` blocks and utility services for standardized responses.

*   **Error Scenarios and HTTP Response Codes**:
    *   **400 Bad Request**:
        *   Triggered when mandatory input parameters are missing. In `pageDataExchangeFind`, this occurs if `systemId` is not provided.
        *   Also triggered by `exchangeFindList` if its `systemId` or `direction` inputs are missing, or if an invalid `direction` enumeration is provided to its internal `findExchange` operation.
        *   **Response**: `responseCode: "400"`, `responsePhrase: "Bad Request"`, `result: "error"`, `message` containing a specific error description (e.g., "'systemId' must be provided", "Please provide required parameters 'systemId' and 'direction'", "Please provide a valid enumeration for 'direction'"). The `format` is "application/json".
    *   **500 Internal Server Error**:
        *   This is the general fallback for any unhandled exceptions or system errors that occur during the execution of the main service flow or its critical dependencies.
        *   **Response**: `responseCode: "500"`, `responsePhrase: "Internal Server Error"`, `result: "error"`, `message` containing the detailed error information from the caught exception (e.g., `lastError.error`). The `format` is "application/json".
    *   **401 Unauthorized**:
        *   This response code is listed as a possible output in the service's `node.ndf` signature, but there's no explicit logic within the provided `flow.xml` files to set this code. It's highly probable that security policies (e.g., authentication, authorization) configured at the Webmethods Integration Server level, or in underlying services not explicitly detailed, would be responsible for generating this response code if API calls are not properly authenticated.
    *   **200 OK**:
        *   This is the success response. It's implicitly returned when the service completes without encountering any of the error conditions.
        *   If data exchanges are found, the `_generatedResponse` will contain the populated `PageDataExchange` object.
        *   If no data exchanges are found, the `_generatedResponse` will contain a `PageDataExchange` object with `count: 0` and an empty `DataExchanges` array, still signifying a successful (but empty) retrieval.

*   **Error Message Formats and Fallback Behaviors**:
    *   The `cms.eadg.utils.api:handleError` service plays a central role in standardizing error messages. It catches raw system errors from `pub.flow:getLastError` and transforms them into a clean `SetResponse` document type.
    *   The `cms.eadg.utils.api:setResponse` service then takes this `SetResponse` document and converts it into either JSON or XML format (defaulting to JSON) before setting the appropriate HTTP response code and body for the API client. This ensures all error messages conform to a consistent structure, making it easier for client applications to parse and handle.
    *   If no exchanges are found for a valid `systemId`, the service gracefully returns an empty result set (count 0) rather than an error, which is an expected "no data" scenario for a find operation.