# Webmethods Service Explanation: CmsEadgCensusCoreApi pageDataExchangeAdd

## Service Overview

The `pageDataExchangeAdd` service in the `CmsEadgCensusCoreApi` package is designed to manage data exchange information within a backend system, likely Alfabet, which then persists the data to a database. Its primary business purpose is to facilitate the addition or update of records detailing data exchanges between IT systems or external stakeholders.

The service accepts a single input parameter: `_generatedInput`, which is of document type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchange`. This input contains comprehensive details about one or more data exchange records, including system identifiers, metadata about the exchange itself, and associated status information.

Upon successful execution, the service is expected to:
*   Insert new data exchange records into the backend system/database if they are newly submitted.
*   Update existing data exchange records if they are identified as modified.
*   Record the status of each data exchange operation (e.g., system status, partner status).

The service returns a `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response` object. For successful operations, this response indicates "success" and provides a general confirmation message. In case of errors, it provides an "error" status with detailed messages and sets appropriate HTTP status codes (e.g., 400 for bad requests, 500 for internal server errors).

Key validation rules include checking for the presence of new or updated data exchange requests and ensuring that if a record refers to a class, its ID matches a configured global variable. The service also implements logic to detect and handle partial or complete failures during backend system interactions.

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageDataExchangeAdd`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow" to define service logic. Here are some key elements seen in this service:

*   **SEQUENCE**: Analogous to a block of code or a function body in traditional programming. Steps within a sequence execute in order. The `EXIT-ON="FAILURE"` attribute means if any step within the sequence encounters an error, the entire sequence stops, and control moves to the next sibling step. If a `SEQUENCE` has `FORM="TRY"`, it signifies the "try" block of an exception handling mechanism.
*   **BRANCH**: Similar to an `if-else if-else` or `switch` statement. It evaluates conditions (defined by `LABELEXPRESSIONS="true"` and `NAME` attributes) and executes only the first branch whose condition evaluates to true. If no conditions are met, the `$default` branch is executed.
*   **INVOKE**: Used to call or execute another Webmethods service, which could be a custom flow service, a Java service, or an adapter service. `VALIDATE-IN="$none"` and `VALIDATE-OUT="$none"` mean that the input and output data structures are not validated against predefined schemas, which can sometimes indicate a less strict data contract or reliance on internal validation.
*   **MAP**: A powerful data transformation step. It allows you to manipulate data within the Integration Server's "pipeline" (the in-memory data structure passed between steps).
    *   **MAPTARGET** and **MAPSOURCE**: These define the structure of the data you're mapping from and to.
    *   **MAPCOPY**: Copies data from one field in the pipeline to another.
    *   **MAPDELETE**: Removes a field from the pipeline. This is crucial for pipeline cleanup, ensuring only necessary data is passed between steps, which is good practice for memory management and clarity.
    *   **MAPSET**: Assigns a literal value or the result of an expression to a field. It can use pipeline variables (`VARIABLES="true"`) or global variables (`GLOBALVARIABLES="true"`).
    *   **MAPINVOKE**: Allows calling a sub-service directly within a Map step. This is often used for small, reusable transformations like formatting dates or converting boolean values to strings.
*   **Error Handling (TRY/CATCH Blocks)**: Webmethods supports structured exception handling. A `SEQUENCE FORM="TRY"` block contains the main business logic. If an error occurs anywhere within this block, execution immediately jumps to the paired `SEQUENCE FORM="CATCH"` block.
    *   `pub.flow:getLastError`: This built-in service is typically called within a `CATCH` block to retrieve detailed information about the error that occurred (e.g., error message, stack trace).
    *   `cms.eadg.utils.api:handleError`: A custom service called in the `CATCH` block to process the error information and format a standardized error response for the API consumer.
*   **Input Validation and Branching Logic**: The service uses conditional `BRANCH` statements to determine which operations (add or update) need to be performed based on the content of the incoming request. `EXIT FROM="$parent" SIGNAL="FAILURE"` is used to stop the current flow and propagate a failure signal up to the calling service, often indicating a validation failure or an unrecoverable error.

## Database Interactions

The `pageDataExchangeAdd` service interacts with a database primarily through specialized "Adapter Services," which are Webmethods components designed to connect to external systems like databases.

The primary database connection used, as indicated by `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`, targets a SQL Server database. The `IRTNODE_PROPERTY` in the `node.ndf` for this connection shows it uses `com.microsoft.sqlserver.jdbc.SQLServerDataSource` and connects to `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` on port `1433` with user `sparx_dbuser`. This connection is configured for `NO_TRANSACTION`, meaning transactions are handled outside of this specific connection resource.

The core database operations are performed by invoking the following JDBC Adapter Services:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:addExchangeSP`**: This service is responsible for inserting new data exchange records into the database. It directly calls the SQL Server stored procedure:
    *   **Stored Procedure**: `SP_Insert_SystemDataExchange_json`
    *   This stored procedure accepts JSON input (`@jsonInput`) and can return JSON output (`@jsonOutput`), indicating that the database is designed to process and return complex data structures directly as JSON strings. It also returns an integer value (`@RETURN_VALUE`) which likely signifies the success or failure of the operation (0 for success in this case).

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP`**: This service handles updating existing data exchange records. It invokes the SQL Server stored procedure:
    *   **Stored Procedure**: `SP_Update_SystemDataExchange_json`
    *   Similar to the insert procedure, it also works with JSON input (`@jsonInput`) and output (`@jsonOutput`) and provides an integer return value (`@RETURN_VALUE`).

While specific table or view names are not explicitly mentioned in the provided `node.ndf` files for the adapters, the names of the stored procedures (`SP_Insert_SystemDataExchange_json`, `SP_Update_SystemDataExchange_json`) strongly suggest that the underlying database contains tables or views related to "SystemDataExchange" where this JSON data is stored and managed.

Data mapping between service inputs and database parameters:
The service first transforms its input `PageDataExchange` into an `UpdateRequest` object, which is then serialized into a JSON string (`jsonString`). This `jsonString` is passed as the `@jsonInput` parameter to the respective `addExchangeSP` or `updateExchangeSP` stored procedure. The stored procedure's `@jsonOutput` is then read back by Webmethods and processed.

## External API Interactions

The primary external API interaction in this context is with "Alfabet," an enterprise architecture management tool. While the main service `pageDataExchangeAdd` doesn't directly call Alfabet services, its dependency services (`cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeAdd` and `exchangeUpdate`) do.

*   **External Service Calls**: The `deleteObjectsByRef` service (which is invoked during a partial failure rollback attempt) directly calls an external HTTP endpoint using `pub.client:http`. The URL for the Alfabet API is derived from a global variable `%alfabet.api.url%` concatenated with a specific path (`/v2/delete`).
*   **Request/Response Formats**: Communication with the Alfabet API is in JSON format, as indicated by the `Content-Type: application/json` header being set for HTTP requests.
*   **Authentication Mechanisms**: The `pub.client:http` service uses a `Bearer` token for authentication, pulling the `token` value from the Webmethods pipeline. This implies that the caller of this service or a preceding service would have handled token acquisition.
*   **Error Handling for External Calls**: The `deleteObjectsByRef` service includes robust error handling for its HTTP call. It checks the HTTP `status` code in the response header. If the status is `200` (success), it proceeds to parse the JSON response. For other status codes like `403` (Forbidden) or any `$default` (other errors), it maps a custom error response. Specifically, a `403` error from Alfabet is re-mapped to a `500` "Internal Server Error" within the `deleteObjectsByRef` service, indicating a potential configuration or permissions issue that should be treated as a system-level problem.

## Main Service Flow

The `pageDataExchangeAdd` service orchestrates a multi-step process for handling data exchange records, wrapped in a `TRY-CATCH` block for robust error handling:

1.  **Input Preparation (`INVOKE cms.eadg.census.core.api.v02.systemCensus_.operations.pageDataExchangeAdd:mapRequests`)**:
    *   The raw input `_generatedInput` (type `PageDataExchange`) is received.
    *   This sub-service maps fields from `_generatedInput` to create distinct `PageDataExchange` (for local use), `ExchangeStatusAddRequest`, `ExchangeAddRequest` (for new records), and `ExchangeUpdateRequest` (for existing records). This initial mapping and separation are crucial for routing data to the appropriate downstream services. The original `_generatedInput` is then deleted from the pipeline to optimize memory.

2.  **Conditional Data Addition (`BRANCH %ExchangeAddRequest% != $null`)**:
    *   The service checks if the `ExchangeAddRequest` object (containing new data exchanges) is present.
    *   If `ExchangeAddRequest` is not null, it `INVOKE`s `cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeAdd`. This sub-service handles the logic for adding new exchanges to Alfabet (which then interacts with the database via `SP_Insert_SystemDataExchange_json`).
    *   After the `exchangeAdd` call, the service performs a `BRANCH` check for `SetResponse != $null`. This is a general error check; if the `exchangeAdd` service (or a preceding step that sets an error response) has indicated an error, the main service immediately `EXIT`s with a `FAILURE` signal. This prevents further processing if a core part of the request (adding new data) has failed.

3.  **Conditional Data Update (`BRANCH %ExchangeUpdateRequest% != $null`)**:
    *   The service then checks if the `ExchangeUpdateRequest` object (containing existing data exchanges that need modification) is present.
    *   If `ExchangeUpdateRequest` is not null, it `INVOKE`s `cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeUpdate`. This sub-service manages the logic for updating existing exchanges in Alfabet (via `SP_Update_SystemDataExchange_json`).
    *   Similar to the add step, a `BRANCH` check for `SetResponse != $null` immediately exits the service on failure, indicating that a problem occurred during the update process.

4.  **Populate Status Records (`INVOKE cms.eadg.census.core.api.v02.systemCensus_.operations.pageDataExchangeAdd:addExchangeIdToNewStatusRecords`)**:
    *   This step is critical for maintaining consistency. It takes the GUIDs (Globally Unique Identifiers) that were assigned by the backend system (Alfabet) for newly added exchanges and populates the `exchangeId` field within the corresponding `PageDataExchangeStatusAddRequest` records. This ensures that the status records are correctly linked to the actual data exchange entries.

5.  **Record Data Exchange Status (`INVOKE cms.eadg.census.core.api.v02.systemCensus_.services:pageDataExchangeStatusAdd`)**:
    *   Finally, the service calls `cms.eadg.census.core.api.v02.systemCensus_.services:pageDataExchangeStatusAdd` to persist the status records (which now include the correct `exchangeId` for new entries). This records the outcome of the data exchange operation within the system's tracking mechanism.

6.  **Response Generation (`MAP`)**:
    *   If all preceding steps execute without setting an error response, the service concludes by mapping a successful response. It sets `_generatedResponse/result` to "success" and `_generatedResponse/message` to "Data exchanges updated successfully".
    *   Cleanup: Various temporary variables and input/output parameters are `MAPDELETE`d from the pipeline to keep it clean and reduce memory footprint.

7.  **Error Handling (`CATCH` Block)**:
    *   If any unhandled exception occurs in the main `TRY` block, control is transferred here.
    *   `pub.flow:getLastError` is invoked to retrieve detailed information about the error.
    *   `cms.eadg.utils.api:handleError` is then called to standardize the error response. This service populates a `SetResponse` document (with HTTP status code 500 "Internal Server Error" and error messages) and uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to send the HTTP error response back to the client.

## Dependency Service Flows

The main `pageDataExchangeAdd` service relies heavily on several smaller, specialized services to perform its functions. These dependencies encapsulate specific business logic or technical interactions, promoting reusability and modularity.

*   **`cms.eadg.census.core.api.v02.systemCensus_.operations.pageDataExchangeAdd:mapRequests`**:
    *   **Purpose**: This initial mapping service's role is to deconstruct the generic `PageDataExchange` input into more granular requests: `ExchangeAddRequest` (for new exchanges), `ExchangeUpdateRequest` (for updates), and `PageDataExchangeStatusAddRequest` (for status updates). It intelligently routes data based on the `deleted` and `updated` flags within the input's `DataExchanges` array.
    *   **Integration**: It's the very first business logic step, preparing the input for subsequent conditional processing.
    *   **Input/Output Contract**: Input is `_generatedInput` (type `PageDataExchange`). Output includes `ExchangeStatusAddRequest`, `ExchangeAddRequest`, and `ExchangeUpdateRequest` populated based on the input.
    *   **Specialized Processing**: This service performs a critical initial data segregation based on flags like `deleted` and `updated`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeAdd`**:
    *   **Purpose**: This service is responsible for adding new data exchange records to the core Alfabet system and its underlying database.
    *   **Integration**: It's invoked conditionally if `ExchangeAddRequest` is present in the main service's pipeline.
    *   **Input/Output Contract**: Takes `_generatedInput` (type `ExchangeAddRequest`) and returns `_generatedResponse` (type `Response`) or specific HTTP error structures (400, 401, 500).
    *   **Specialized Processing**:
        *   **`mapExchange` (sub-service)**: Transforms the generic `Exchange` objects into `UpdateRequest` format expected by the external Alfabet API, including mapping to Alfabet-specific properties (e.g., `cms_api_owner`). It also dynamically generates relations based on `fromOwnerId`, `toOwnerId`, and `typeOfData`. This transformation is where most of the complex field mapping for the external system occurs.
        *   **JSON Serialization**: Converts the mapped `UpdateRequest` object into a JSON string using `pub.json:documentToJSONString`.
        *   **Database Interaction (`addExchangeSP`)**: Invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:addExchangeSP` to execute the `SP_Insert_SystemDataExchange_json` stored procedure.
        *   **Response Parsing**: Parses the JSON output from the stored procedure into an `ExchangeAddGUIDS` object, extracting newly generated GUIDs.
        *   **Error Handling**: Has its own internal error handling and can populate a `SetResponse` object which, if set, can trigger an early exit in the main `pageDataExchangeAdd` service.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.services:exchangeUpdate`**:
    *   **Purpose**: Similar to `exchangeAdd`, but handles updates to existing data exchange records in Alfabet.
    *   **Integration**: Invoked conditionally if `ExchangeUpdateRequest` is present.
    *   **Input/Output Contract**: Takes `_generatedInput` (type `ExchangeUpdateRequest`) and returns `_generatedResponse` (type `Response`) or specific HTTP error structures.
    *   **Specialized Processing**: Mirrors `exchangeAdd`'s process: maps to `UpdateRequest`, serializes to JSON, and calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP` (which executes `SP_Update_SystemDataExchange_json`).

*   **`cms.eadg.census.core.api.v02.systemCensus_.operations.pageDataExchangeAdd:addExchangeIdToNewStatusRecords`**:
    *   **Purpose**: Links newly added exchange records with their corresponding status records by injecting the `exchangeId` (GUID) obtained from the Alfabet API response.
    *   **Integration**: Called after successful `exchangeAdd` operations but before the `pageDataExchangeStatusAdd` call.
    *   **Input/Output Contract**: Takes `AddResponse` (which contains GUIDs from new object creation) and `ExchangeStatusAddRequest` and updates the `exchangeId` field in the status records within `ExchangeStatusAddRequest`.

*   **`cms.eadg.census.core.api.v02.systemCensus_.services:pageDataExchangeStatusAdd`**:
    *   **Purpose**: Persists the data exchange status records to the database.
    *   **Integration**: The final data persistence step in the main flow, ensuring the status of each exchange is recorded.
    *   **Input/Output Contract**: Takes `_generatedInput` (type `PageDataExchangeStatusAddRequest`) and returns a `UpsertResponse`.

*   **`cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`**:
    *   **Purpose**: Implements a rollback mechanism. If a partial failure occurs (some records added, others failed), this service attempts to delete the successfully added objects from Alfabet to maintain data integrity.
    *   **Integration**: Called from the `mapResponse` service of `exchangeAdd/Update` if a partial failure condition is met.
    *   **Specialized Processing**: Transforms object references (`RefStr`) into a JSON request, calls the external Alfabet `/v2/delete` endpoint via HTTP client, handles authentication, and processes the HTTP response, including error re-mapping (e.g., 403 to 500).

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: Centralized error handling. It standardizes the API's error response structure.
    *   **Integration**: Called in the `CATCH` block of the main service flow or other sub-services.
    *   **Specialized Processing**: Retrieves the system error details using `pub.flow:getLastError`, maps it to a `SetResponse` document (including HTTP status code, phrase, and error messages), and uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to send the standardized HTTP error response to the client.

## Data Structures and Types

The service heavily relies on several custom Webmethods "Document Types" (recref in the XML) to define its input, internal processing, and output data models.

*   **Input Data Model**:
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchange`: The top-level input to `pageDataExchangeAdd`.
        *   `systemId` (string, required): Unique identifier of the system.
        *   `pageName` (string, optional): Should be "DataExchange" for this service.
        *   `count` (BigInteger, required): Number of data exchanges expected.
        *   `DataExchanges` (array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:DataExchanges`, optional): Contains the actual data exchange records.
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:DataExchanges`: Represents a single data exchange transaction.
        *   `direction` (string, optional): "receiver" or "sender".
        *   `deleted` (Boolean, optional): Flag indicating logical deletion.
        *   `updated` (Boolean, optional): Flag indicating an update.
        *   `Exchange` (`cms.eadg.census.core.api.v02.systemCensus_.docTypes:Exchange`, optional): Detailed exchange data.
        *   `Status` (`cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchangeStatus`, optional): Status data related to the exchange.

*   **Internal Data Models for Transformation and External Calls**:
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Exchange`: Details of a data exchange.
        *   `exchangeId` (string, optional): GUID for the exchange (required for updates).
        *   `exchangeName`, `exchangeDescription`, `exchangeVersion`, `exchangeState` (strings, optional).
        *   `exchangeStartDate`, `exchangeEndDate`, `exchangeRetiredDate` (Date objects, optional).
        *   `fromOwnerId`, `fromOwnerName`, `fromOwnerType` (strings, optional): Details of the source owner.
        *   `toOwnerId`, `toOwnerName`, `toOwnerType` (strings, optional): Details of the target owner.
        *   `connectionFrequency` (string array, optional): E.g., ["Daily", "Weekly"].
        *   `dataExchangeAgreement` (string, optional): Status of agreement.
        *   Boolean flags (`containsBeneficiaryAddress`, `isAddressEditable`, `containsPii`, `containsPhi`, `containsHealthDisparityData`, `isBeneficiaryMailingFile`, `sharedViaApi`, `exchangeContainsCUI`, `exchangeConnectionAuthenticated`) represented as `java.lang.Boolean` objects in Webmethods.
        *   `businessPurposeOfAddress` (string array, optional).
        *   `apiOwnership` (string, optional).
        *   `typeOfData` (record array `id`, `name`, optional): Details about data types.
        *   `numOfRecords` (string, optional), `dataFormat` (string, optional), `dataFormatOther` (string, optional).
        *   `exchangeCUIDescription` (string, optional), `exchangeCUIType` (string array, optional).
        *   `exchangeNetworkProtocol` (string array, optional), `exchangeNetworkProtocolOther` (string, optional).
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageDataExchangeStatus`: Tracks the workflow status of an exchange.
        *   `exchangeId` (string, required): Linked to the exchange.
        *   `systemId`, `systemStatus`, `partnerId`, `partnerStatus`, `reviewerStatus`, `direction` (strings, required/optional).
        *   `deleted` (Boolean, optional).
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:InformationFlow`: This internal representation (used within `mapExchange`) mirrors the structure expected by the backend Alfabet system's data model, often with `cms_` prefixes for custom attributes. It typically holds simple string representations of data.
    *   `cms.eadg.alfabet.api.v01.resources.update.docs:UpdateRequest`: The format for sending data to the external Alfabet update API, consisting of `Objects` (list of `cms.eadg.alfabet.api.v01.docs.types:Object`) and `Relations` (list of `cms.eadg.alfabet.api.v01.docs.types:Relations`).
    *   `cms.eadg.alfabet.api.v01.docs.types:Object`: A generic Alfabet object with `RefStr` (reference string/GUID), `ClassName` (e.g., "InformationFlow"), `Id` (internal GUID), `Values` (a generic container for various properties like `cms_adress_data_edits`).
    *   `cms.eadg.alfabet.api.v01.docs.types:Relations`: Represents relationships between objects in Alfabet using `FromRef`, `ToRef` (references to other objects), and `Property` (type of relationship).

*   **Output Data Models**:
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`: The standard success/error response of this API.
        *   `result` (string): "success" or "error".
        *   `message` (string array): Details or confirmation messages.
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:UpsertResponse`: Used internally by status services to report inserted/updated counts.
        *   `inserted` (BigInteger), `updated` (BigInteger).

*   **Data Transformation Logic**:
    *   **Boolean to String**: Webmethods `java.lang.Boolean` objects are frequently converted to "true" or "false" strings using sub-services like `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:convertBooleanToString` before being mapped to `InformationFlow` fields. In TypeScript, these would naturally map to `boolean` types, requiring explicit string conversion if a backend API expects string representations.
    *   **Date Formatting**: `java.util.Date` objects are formatted to "MM/dd/yyyy" strings for certain fields (`exchangeRetiredDate`) using services like `cms.eadg.utils.date:formatDateIf`. TypeScript `Date` objects would need similar formatting before serialization.
    *   **Array to Delimited String**: String arrays (`connectionFrequency`, `businessPurposeOfAddress`, `exchangeNetworkProtocol`, `exchangeCUIType`) are joined into a single pipe-separated string (e.g., "Daily|Weekly") using `pub.string:makeString`. This implies a need for array joining logic in TypeScript.
    *   **Derived Fields**: The `name` field in `InformationFlow` is a concatenation of multiple input fields (`fromOwnerName`, `toOwnerName`, `exchangeVersion`) performed by `createExchangeName` service.

## Data Mapping: Input to Backend Object Properties

This section details the primary mapping from the input `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Exchange` properties to the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:InformationFlow` properties that are ultimately transmitted to the external Alfabet system (which then interacts with the database via stored procedures). These `InformationFlow` properties conceptually represent the "source database columns" in the context of this project.

*   `Exchange.apiOwnership`: `cms_api_owner`
*   `Exchange.exchangeDescription`: `description`
*   `Exchange.dataFormat`: `cms_data_exch_format`
*   `Exchange.dataFormatOther`: `cms_data_exch_format_other`
*   `Exchange.numOfRecords`: `cms_data_exch_num_recs`
*   `Exchange.dataExchangeAgreement`: `cms_ie_agreement`
*   `Exchange.exchangeName`: `cms_exchange`
*   `Exchange.fromOwnerId`: `fromowner`
*   `Exchange.toOwnerId`: `toowner`
*   `Exchange.exchangeState`: `objectstate`
*   `Exchange.exchangeState`: `status`
*   `Exchange.exchangeVersion`: `version`
*   `Exchange.exchangeCUIDescription`: `cms_exchange_cui_description`
*   `Exchange.exchangeNetworkProtocolOther`: `cms_exchange_network_protocol_other`

**Mapped with Type Conversion/Transformation:**
*   `Exchange.isAddressEditable` (Boolean) : `cms_adress_data_edits` (String - "true"/"false")
*   `Exchange.businessPurposeOfAddress` (String Array) : `cms_beneficiary_address_pur` (String - pipe-separated)
*   `Exchange.sharedViaApi` (Boolean) : `cms_data_exch_api_data_share` (String - "true"/"false")
*   `Exchange.isBeneficiaryMailingFile` (Boolean) : `cms_data_exch_benef_mailing` (String - "true"/"false")
*   `Exchange.containsPii` (Boolean) : `cms_data_exchange_pii` (String - "true"/"false")
*   `Exchange.containsHealthDisparityData` (Boolean) : `cms_health_disparity_data` (String - "true"/"false")
*   `Exchange.containsBeneficiaryAddress` (Boolean) : `cms_data_exch_beneficiary_add` (String - "true"/"false")
*   `Exchange.containsPhi` (Boolean) : `cms_data_exchange_phi` (String - "true"/"false")
*   `Exchange.containsBankingData` (Boolean) : `cms_data_exchange_banking` (String - "true"/"false")
*   `Exchange.connectionFrequency` (String Array) : `cms_exchange_frequency` (String - pipe-separated)
*   `Exchange.exchangeRetiredDate` (Date) : `cms_exchange_retire_date` (String - "MM/dd/yyyy" format)
*   `Exchange.fromOwnerName`, `Exchange.toOwnerName`, `Exchange.exchangeVersion` : `name` (String - derived by concatenation in `createExchangeName`)
*   `Exchange.exchangeStartDate` (Date) : `startdate` (String)
*   `Exchange.exchangeEndDate` (Date) : `enddate` (String)
*   `Exchange.exchangeContainsCUI` (Boolean) : `cms_exchange_contains_cui` (String - "true"/"false")
*   `Exchange.exchangeConnectionAuthenticated` (Boolean) : `cms_exchange_connection_authenticated` (String - "true"/"false")
*   `Exchange.exchangeNetworkProtocol` (String Array) : `cms_exchange_network_protocol` (String - pipe-separated)
*   `Exchange.exchangeCUIType` (String Array) : `cms_exchange_cui_type` (String - pipe-separated)

**Relations Mapping (Conceptual "Columns" for Relationships):**
These relations link the main `InformationFlow` object (identified by its generated `Id`) to other objects in Alfabet.
*   `InformationFlow.Id`: `Relations[].FromId` (references the newly created/updated `InformationFlow` object).
*   `Exchange.fromOwnerId`: `Relations[].ToRef` with `Relations[].Property` set to "From" (indicating the source of the exchange).
*   `Exchange.toOwnerId`: `Relations[].ToRef` with `Relations[].Property` set to "To" (indicating the target of the exchange).
*   `Exchange.fromOwnerId`: `Relations[].ToRef` with `Relations[].Property` set to "FromOwner".
*   `Exchange.toOwnerId`: `Relations[].ToRef` with `Relations[].Property` set to "ToOwner".
*   `Exchange.typeOfData[].id`: `Relations[].ToRef` with `Relations[].Property` set to "cms_data_exch_type_of_data" (creating multiple relations if multiple types of data are present).

## Error Handling and Response Codes

The service implements a structured error handling strategy to gracefully manage various failure scenarios and provide informative responses to API consumers.

1.  **Global `TRY-CATCH` Block**: The entire main service flow is wrapped in a `SEQUENCE` element with `FORM="TRY"`. If any step within this `TRY` block encounters an error, execution immediately transfers to the corresponding `SEQUENCE` with `FORM="CATCH"`. This ensures that even unexpected runtime exceptions are caught and handled.

2.  **Retrieving Error Details**: Inside the `CATCH` block, the `pub.flow:getLastError` service is invoked. This built-in Webmethods service retrieves comprehensive details about the last error that occurred in the current flow, including the error message, exception type, and stack trace. This information is crucial for debugging and for generating detailed error messages.

3.  **Standardized Error Response (`cms.eadg.utils.api:handleError`)**:
    *   The retrieved error details are passed to the custom `cms.eadg.utils.api:handleError` service.
    *   This service is designed to create a standardized error response object (`cms.eadg.utils.api.docs:SetResponse`). By default, it sets the `responseCode` to "500" (Internal Server Error) and `responsePhrase` to "Internal Server Error", populating the `message` field with the actual error message from `getLastError`.
    *   It also sets the `result` to "error" and `format` to "application/json" (or "application/xml" if explicitly configured for XML responses).
    *   The `handleError` service uses `pub.flow:setResponseCode` to set the HTTP status code and reason phrase for the outgoing response and `pub.flow:setResponse2` to set the response body (serialized JSON or XML).

4.  **Specific Error Scenarios (within `mapResponse` of `exchangeAdd/Update` - though disabled in the main flow for direct propagation):**
    *   **Total Failure (`%responseCount% == 0`)**: If the external Alfabet API (or underlying database operations) reports that zero records were successfully processed for an add/update request (i.e., `responseCount` is 0), this is treated as a total failure. The service sets a `400` ("Bad Request") HTTP status code with the message "No updates made. Invalid request." This implies that the entire request was invalid or could not be processed.
    *   **Partial Failure (`%responseCount% != 0 && %requestTotalCount% != %responseCount%`)**: This scenario occurs if some records were successfully processed, but others failed. In this case, the service attempts a **rollback** by invoking `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`. This service attempts to delete the successfully added objects from Alfabet using their generated `RefStr` (GUIDs) to ensure data consistency. The service then returns a `400` ("Bad Request") HTTP status code with the message "Partial failure. Rollback was attempted."
    *   **Alfabet API Specific Errors (`deleteObjectsByRef`'s internal handling)**:
        *   If the Alfabet API returns a `403` HTTP status code (Forbidden), `deleteObjectsByRef` re-maps this to a `500` ("Internal Server Error"). This suggests that a permissions issue with the Alfabet API should be treated as a systemic failure rather than a client-side bad request.
        *   If a required global variable (like `alfabet.class.className.id`) is missing during validation, the `deleteObjectsByRef` service will `EXIT` with a `FAILURE` signal, indicating a configuration problem.

5.  **Exiting the Flow**: `EXIT FROM="$parent" SIGNAL="FAILURE"` statements are used throughout the flow (especially within conditional branches and error handling sequences) to immediately stop the current flow and propagate a failure signal up the call stack to the parent service. This is critical for controlling flow execution and ensuring that subsequent steps are not executed on invalid or erroneous data.

For TypeScript porting, this robust error handling strategy is a good pattern to emulate, focusing on centralized error processing, clear HTTP status codes, and informative error messages. The distinction between total and partial failures, and the attempt at a rollback, highlights a sophisticated approach to data integrity.