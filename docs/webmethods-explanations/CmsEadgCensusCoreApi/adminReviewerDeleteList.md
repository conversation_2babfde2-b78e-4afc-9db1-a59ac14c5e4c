# Webmethods Service Explanation: CmsEadgCensusCoreApi adminReviewerDeleteList

This document provides a comprehensive explanation of the Webmethods service `adminReviewerDeleteList`. This service is designed to remove one or more reviewer records from a backend system, likely a database, based on provided identifiers. It processes a list of reviewers, attempting to delete each one, and provides a consolidated response indicating success, partial success, or failure.

The service expects an array of reviewer objects as input, where each reviewer can be identified by either an `id` or a `userName`, along with an optional `type`. It performs validation to ensure the input is present and then iterates through each reviewer, attempting the deletion. The output summarizes the results of these deletion attempts, indicating which records were processed successfully and which encountered errors.

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `adminReviewerDeleteList`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

### Service Overview

The `adminReviewerDeleteList` service serves the business purpose of allowing administrators to remove one or more system reviewer profiles in a batch operation. These reviewers are described as CMS employees responsible for validating System Census Survey data.

The service accepts a single input parameter, `_generatedInput`, which is a document (a structured record) of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:ReviewerDeleteRequest`. This input document is expected to contain an array named `Reviewers`. Each element in this `Reviewers` array is a document of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Reviewer`. For each `Reviewer` object, the service notes state that either the `id` or `userName` field must be provided, while `type` is optional; other fields like `fullName` are ignored for this operation.

The expected output is `_generatedResponse`, a document of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`. This response includes a `result` field (e.g., "success", "error", "partial success") and a `message` array, which provides detailed feedback for each reviewer deletion attempt.

Key validation rules include:
*   The `Reviewers` array within the `_generatedInput` must not be null. If it is null, the service immediately returns a "Bad Request" (HTTP 400) response.
*   Although not explicitly coded in the provided flow XML as a hard stop, the underlying database operation (presumably within `deleteReviewer`) would implicitly validate if a record exists to be deleted (`deleteReviewersOutput/count`). If no record is found or multiple records are affected when only one is expected, an error message is generated for that specific reviewer's deletion attempt.

### Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow" services. These services are essentially executable sequences of steps. Understanding a few core elements is crucial:

*   **SEQUENCE**: Analogous to a block of code in traditional programming languages (e.g., `{ ... }` in C#/Java/TypeScript). Steps within a sequence are executed in order. A `SEQUENCE` can be configured with `EXIT-ON="FAILURE"` (stops the sequence if any step fails) or `EXIT-ON="SUCCESS"` (stops the sequence if any step succeeds). The `FORM="TRY"` attribute indicates the start of a `TRY` block for error handling.
*   **BRANCH**: Similar to a `switch` statement in traditional programming. It evaluates a specified variable or expression (the `SWITCH` attribute) and directs the flow to a `SEQUENCE` or `INVOKE` node whose `NAME` attribute matches the evaluated value. A `NAME="$null"` branch catches null values, and `NAME="$default"` acts as the `default` case if no other match is found. The `LABELEXPRESSIONS="true"` attribute means the branch names can be full expressions (e.g., `%variable% == "value"`).
*   **MAP**: This element is used for data transformation and manipulation. It's akin to assigning values to variables or transforming objects. It has a `MAPSOURCE` (where data comes from) and `MAPTARGET` (where data goes).
    *   **MAPSET**: Directly assigns a literal value or an expression's result to a field. This is like `variable = "value"` or `object.property = someExpression`.
    *   **MAPCOPY**: Copies data from one field to another. This is like `targetVariable = sourceVariable` or `targetObject.property = sourceObject.property`. It can also have a `CONDITION` attribute, allowing for conditional copying (e.g., `if (condition) { copyData(); }`).
    *   **MAPDELETE**: Removes a field or document from the pipeline (the in-memory data structure). This is like `delete object.property` or nullifying a variable to free memory.
*   **INVOKE**: Used to call another service. This is equivalent to calling a function or method in traditional programming. It takes `INPUT` and `OUTPUT` mappings to define how data flows into and out of the invoked service.
*   **Error Handling (TRY/CATCH)**: Webmethods Flow services support structured error handling using `SEQUENCE` elements. A `SEQUENCE` with `FORM="TRY"` defines a block where exceptions are caught by a subsequent `SEQUENCE` with `FORM="CATCH"`. When an error occurs within a `TRY` block, execution immediately jumps to the corresponding `CATCH` block.

### Database Interactions

This service primarily interacts with a database to perform deletion operations. The specific database operation is managed by the `cms.eadg.census.core.api.v02.systemCensus_.adapters.admin.reviewer.wrappers:deleteReviewer` service. While the direct SQL query or stored procedure details are not available in the provided XML, the naming convention `deleteReviewer` strongly suggests a database adapter service designed to delete reviewer records.

The service passes the following input parameters to the `deleteReviewer` adapter, which are then used as parameters for the underlying database operation:

*   `_generatedInput/Reviewers/id` (from the input `Reviewer` object) is mapped to `SYSTEM_SURVEY_REVIEWER_ID`. This likely corresponds to the primary key or unique identifier for the reviewer record in the database.
*   `_generatedInput/Reviewers/userName` (from the input `Reviewer` object) is mapped to `REVIEWER_USERNAME`. This would correspond to a username field in the reviewer table.
*   `_generatedInput/Reviewers/type` (from the input `Reviewer` object) is mapped to `REVIEWER_TYPE`. This would correspond to a type field, such as 'QA' or 'DA', in the reviewer table.

Given the adapter's name and the input parameters, it is highly probable that the `deleteReviewer` adapter executes a `DELETE` SQL statement against a database table. A reasonable inference for the table name, based on the context, would be something like `SYSTEM_SURVEY_REVIEWERS` or `REVIEWERS`. It's also possible it calls a stored procedure like `DeleteReviewerSP` or similar. Without the adapter's specific configuration, the exact table/view/procedure names cannot be confirmed from the provided files, but it will be a table or view related to `Reviewer` information.

**Inferred SQL Tables/Views/Stored Procedures:**
*   **Tables/Views:** `SYSTEM_SURVEY_REVIEWERS` or `REVIEWERS`
*   **Stored Procedures:** `DeleteReviewerSP` (or a similar procedure)

### External API Interactions

Based on the provided Webmethods files (`flow.xml` and `node.ndf` for the main service and its direct dependencies), there are no direct calls to external, third-party APIs. The service primarily orchestrates internal Webmethods services and database operations.

The `rec_ref` and `invoke` lists contain references to various Webmethods built-in services (e.g., `pub.flow:getLastError`, `pub.string:makeString`, `pub.json:documentToJSONString`, `pub.xml:documentToXMLString`, `pub.flow:setResponseCode`, `pub.flow:setResponse2`) and other internal utility services within the `cms.eadg.utils.api` and `cms.eadg.census.core.api.v02.systemCensus_.adapters` packages. The other `rec_ref` entries (`cms.eadg.alfabet.api.v01.resources.objects.byReport.docs:ObjectByReportResponse`, `cms.eadg.easi.api.v01.resources.system.docs.references:mission_essential_function`, `cms.eadg.easi.api.v01.resources.system.docs.references:software_product`, `cms.eadg.easi.api.v01.resources.system.docs.types:SystemDetail`) are data structure definitions (document types) that are *referenced* by the system (possibly for schema validation or by other services in the larger ecosystem), but are not actively used for data transformation or external calls *within the logic of this specific `adminReviewerDeleteList` service*.

### Main Service Flow

The `adminReviewerDeleteList` service executes a flow with the following steps:

1.  **Overall Error Handling (Outer TRY/CATCH)**: The entire service flow is wrapped in a `SEQUENCE` block with `FORM="TRY"`. If any unhandled error occurs within this main `TRY` block, execution jumps to the outer `CATCH` block.

2.  **Input Validation (Initial BRANCH)**:
    *   The flow starts with a `BRANCH` statement that checks the `/`\_generatedInput`/Reviewers` field.
    *   **Case: `$null` (Missing `Reviewers` array)**: If the `Reviewers` array is null (meaning the client did not provide any reviewers to delete), the service executes a `MAP` step to set a `SetResponse` document. This `SetResponse` document specifies:
        *   `responseCode`: "400"
        *   `responsePhrase`: "Bad Request"
        *   `result`: "error"
        *   `message`: ["Missing required object 'Reviewers'"]
        *   `format`: "application/json"
        *   After mapping, the service `EXIT`s with a `SIGNAL="FAILURE"`, causing an immediate error response to the client. This also implicitly triggers the `cms.eadg.utils.api:setResponse` which then sets the HTTP response code and body.

3.  **Cleanup (Initial MAP)**: If the `Reviewers` input is not null, a `MAP` step is executed to `MAPDELETE` any existing `SetResponse` document. This is a cleanup step to ensure a clean state before processing.

4.  **Process Each Reviewer (LOOP)**: The core logic of the service is enclosed within a `LOOP` statement that iterates over the `/_generatedInput/Reviewers` array. For each `Reviewer` object in the array, the following steps are performed:
    *   **Iteration-Level Error Handling (Inner TRY/CATCH)**: Each iteration of the loop is itself wrapped in a `SEQUENCE` block with `EXIT-ON="SUCCESS"` and contains an inner `SEQUENCE` with `FORM="TRY"` for error handling specific to that reviewer's deletion attempt.
        *   **Initialize Variables (MAP)**: A variable `na` is initialized to the string "null".
        *   **Map Message (MAP)**: The `id`, `userName`, and `type` fields from the current `Reviewer` object (`_generatedInput/Reviewers`) are conditionally copied into a temporary `message` array. If a field is null, the value "null" from the `na` variable is copied instead. The `na` variable is then deleted. This `message` array will be used to construct a human-readable string about the reviewer.
        *   **Format Message (INVOKE `pub.string:makeString`)**: The `pub.string:makeString` service is invoked to concatenate the elements of the `message` array into a single string, using " | " as a separator. The result is stored back into `message`. This formatted string is used in success/error messages.
        *   **Delete Reviewer (INVOKE `cms.eadg.census.core.api.v02.systemCensus_.adapters.admin.reviewer.wrappers:deleteReviewer`)**: This crucial step invokes the actual database adapter service to delete the reviewer. The `id`, `userName`, and `type` from the current `Reviewer` object are mapped as input to this adapter as `SYSTEM_SURVEY_REVIEWER_ID`, `REVIEWER_USERNAME`, and `REVIEWER_TYPE` respectively.
        *   **Check Deletion Result (Inner BRANCH)**: After the deletion attempt, a `BRANCH` evaluates the `deleteReviewersOutput/count` received from the adapter.
            *   **Case: `%deleteReviewersOutput/count% != 1` (Record Not Found/Multiple Affected)**: If the count of deleted records is not exactly 1 (meaning either 0 records were found/deleted or more than one, which would be unexpected for a unique ID/username combination), an `errorFlag` is set to "true". The `_generatedResponse/message` array (which accumulates messages for all deletion attempts) receives an error message: "Could not delete record: [reviewer details from `message`]". The `deleteReviewersOutput` from the adapter is cleaned up.
            *   **Case: `$default` (Successful Deletion)**: If the count *is* 1, a `successFlag` is set to "true". The `_generatedResponse/result` is set to "success", and `_generatedResponse/message` receives a success message: "Successfully deleted record: [reviewer details from `message`]". The `deleteReviewersOutput` is cleaned up.
        *   **Inner CATCH (for `deleteReviewer` errors)**: If any technical error occurs during the `deleteReviewer` invocation (e.g., database connection issue, SQL error), execution jumps here. `pub.flow:getLastError` retrieves the error details. An `errorFlag` is set to "true", and the `_generatedResponse/message` receives a detailed error message: "Could not delete ID: [reviewer ID] - [error message from `lastError`]".

5.  **Cleanup (LOOP MAP)**: After each loop iteration completes (whether by success or by catching an error for that specific reviewer), a `MAP` step performs cleanup by deleting the `message` field, preparing for the next iteration.

6.  **Final Response Status (Outer BRANCH)**: After all reviewers in the loop have been processed, a `BRANCH` evaluates the overall `errorFlag` and `successFlag` to determine the appropriate HTTP status code for the response:
    *   **Case: `%errorFlag% == "true" && %successFlag% != "true"` (All Failed or Initial Validation Failed)**: If `errorFlag` is true and `successFlag` is not true (meaning either no successful deletions, or the initial validation failed and stopped before any successful deletions), `pub.flow:setResponseCode` is invoked to set the HTTP status to `400 Bad Request`. The `_generatedResponse/result` is also explicitly set to "error".
    *   **Case: `%errorFlag% == "true" && %successFlag% == "true"` (Partial Success)**: If both `errorFlag` and `successFlag` are true, it indicates that some deletions succeeded and some failed. In this scenario, `pub.flow:setResponseCode` is invoked to set the HTTP status to `207 Multi-Status`. The `_generatedResponse/result` is set to "partial success".
    *   **Implicit Case (All Success)**: If `errorFlag` is false (meaning no errors occurred throughout the loop) and `successFlag` is true, this path is not explicitly handled by a named branch. The `setResponse` utility service (discussed below) will handle the 200 OK status by default, as no other error code is set.

7.  **Final Cleanup (Outer MAP)**: After determining the final HTTP status, a `MAP` step deletes the internal `_generatedInput`, `successFlag`, and `errorFlag` from the pipeline.

8.  **Main CATCH (for unhandled errors)**: If any unhandled exception occurs in the main `TRY` block (e.g., a critical system error), execution jumps to this `CATCH` block.
    *   `INVOKE pub.flow:getLastError`: Retrieves details of the unhandled exception.
    *   `INVOKE cms.eadg.utils.api:handleError`: This utility service is invoked to standardize the error response for critical failures.

### Dependency Service Flows

The main service relies on two important utility services from the `CmsEadgUtils` package: `handleError` and `setResponse`.

1.  **`cms.eadg.utils.api:handleError`**:
    This service is designed to centralize error response generation.
    *   It checks the presence of a `SetResponse` document in the pipeline.
    *   **If `SetResponse` is null**: This indicates a general or unexpected error (like an unhandled exception caught by an outer CATCH block in the calling service). It proceeds to invoke `cms.eadg.utils.api:setResponse` after populating `SetResponse` with default error details:
        *   `responseCode`: "500"
        *   `responsePhrase`: "Internal Server Error"
        *   `result`: "error"
        *   `message`: The actual error message from `lastError/error` (obtained from `pub.flow:getLastError`).
        *   `format`: "application/json"
    *   **If `SetResponse` exists (`$default` case)**: This implies the calling service (like `adminReviewerDeleteList`'s initial input validation or `loop` iterations) has already prepared a specific error or success response in `SetResponse`. In this case, `handleError` simply passes this existing `SetResponse` to `cms.eadg.utils.api:setResponse` for final processing.
    *   After invoking `setResponse`, it cleans up the `lastError` and `SetResponse` documents from its own pipeline.

2.  **`cms.eadg.utils.api:setResponse`**:
    This utility service is responsible for formatting the final HTTP response sent back to the client.
    *   It first maps the `result` and `message` from the input `SetResponse` document to a generic `Response` document (`cms.eadg.utils.api.docs:Response`).
    *   **Content Type Branching**: It then uses a `BRANCH` statement based on the `SetResponse/format` field to determine the output content type:
        *   **`application/json`**: If the format is JSON, it invokes `pub.json:documentToJSONString` to convert the `Response` document into a JSON string. This string is then mapped to `responseString`, which will become the HTTP response body.
        *   **`application/xml`**: If the format is XML, it first wraps the `Response` document within a `ResponseRooted` document (`cms.eadg.utils.api.docs:ResponseRooted`). It then invokes `pub.xml:documentToXMLString` to convert this `ResponseRooted` document into an XML string. This XML string is mapped to `responseString`.
    *   **Set HTTP Status Code**: After content serialization, `pub.flow:setResponseCode` is invoked. This service sets the HTTP response status code (e.g., 200, 400, 500, 207) and reason phrase (e.g., "OK", "Bad Request", "Internal Server Error", "Multi-Status") for the HTTP response, using values provided in `SetResponse/responseCode` and `SetResponse/responsePhrase`.
    *   **Set HTTP Response Body**: Finally, `pub.flow:setResponse2` is invoked. This service takes the `responseString` (the JSON or XML body) and the `contentType` (e.g., "application/json", "application/xml") and writes them to the HTTP response stream.
    *   The service concludes by cleaning up all temporary variables.

### Data Structures and Types

The service utilizes several Webmethods Document Types (`docType`s), which define the structure of data exchanged within the service and with the client. These are analogous to interfaces or classes in TypeScript.

*   **`cms.eadg.census.core.api.v02.systemCensus_.docTypes:ReviewerDeleteRequest`**: This is the primary input structure for the `adminReviewerDeleteList` service.
    *   It contains a single field: `Reviewers` (an array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Reviewer` objects). This field is `field_opt=true` meaning it's technically optional in the schema, but the service enforces its presence via an explicit null check.

*   **`cms.eadg.census.core.api.v02.systemCensus_.docTypes:Reviewer`**: This document type defines the structure of a single reviewer object.
    *   `id` (string, optional): Unique identifier for the reviewer.
    *   `userName` (string, optional): Username of the reviewer.
    *   `fullName` (string, optional): Full name of the reviewer (ignored by this service).
    *   `type` (string, optional): Type of reviewer (e.g., "QA", "DA").
    The service description states that "Either 'id' or 'userName' must be required," indicating a business rule that might be enforced at a higher level (e.g., API Gateway, client-side) or by the invoked `deleteReviewer` adapter.

*   **`cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`**: This is the main output structure for the `adminReviewerDeleteList` service.
    *   `result` (string, optional): Indicates the overall outcome (e.g., "success", "error", "partial success").
    *   `message` (array of strings, optional): Contains human-readable messages about the processing of each reviewer, including success or error details.

*   **`cms.eadg.utils.api.docs:SetResponse`**: An internal utility document type used by `cms.eadg.utils.api:handleError` and `cms.eadg.utils.api:setResponse` to standardize the HTTP response parameters.
    *   `responseCode` (string): The HTTP status code (e.g., "200", "400", "500").
    *   `responsePhrase` (string): The HTTP reason phrase (e.g., "OK", "Bad Request").
    *   `result` (string): The conceptual result (e.g., "success", "error").
    *   `message` (array of strings): General messages for the response body.
    *   `format` (string): The desired content type for the response body (e.g., "application/json", "application/xml").

*   **`cms.eadg.utils.api.docs:ResponseRooted`**: A simple wrapper document type used specifically when generating XML responses to provide a root element. It contains a single field, `Response`, which is a `recref` to `cms.eadg.utils.api.docs:Response`.

*   **`pub.event:exceptionInfo`**: A standard Webmethods document type used to hold detailed information about caught exceptions, including the error message.

Other document types like `ObjectByReportResponse`, `mission_essential_function`, `software_product`, and `SystemDetail` are defined within the Webmethods environment but are not directly instantiated or manipulated by this specific `adminReviewerDeleteList` service. They likely belong to other related APIs or data models.

**Detailed List of Source Database Column to Output Object Properties:**
(Note: Direct database columns are inferred from adapter inputs and the service's purpose, as the adapter's internal SQL is not provided.)

**Input to Database Adapter Parameters (via `cms.eadg.census.core.api.v02.systemCensus_.adapters.admin.reviewer.wrappers:deleteReviewer`):**
*   `_generatedInput/Reviewers/id`: `SYSTEM_SURVEY_REVIEWER_ID`
*   `_generatedInput/Reviewers/userName`: `REVIEWER_USERNAME`
*   `_generatedInput/Reviewers/type`: `REVIEWER_TYPE`

**Internal Pipeline Data to Output Object Properties (`_generatedResponse`):**
*   (Derived from internal logic and `successFlag`/`errorFlag`): `_generatedResponse/result` (values: "success", "error", "partial success")
*   (Concatenated from `id`, `userName`, `type` of each reviewer, or error messages): `_generatedResponse/message` (array of strings)

### Error Handling and Response Codes

The service employs a robust error handling strategy, differentiating between various failure modes and setting appropriate HTTP response codes:

1.  **Missing Required Input**: If the initial `_generatedInput/Reviewers` array is missing, the service immediately sets the HTTP status code to `400 Bad Request` and returns an error response with a specific message "Missing required object 'Reviewers'".

2.  **Per-Record Deletion Failures**:
    *   **Record Not Found or Unexpectedly Deleted Count**: After invoking the `deleteReviewer` adapter, if the `deleteReviewersOutput/count` is not exactly 1 (meaning the reviewer could not be found, or more than one record was affected, which is an error in a targeted delete), an `errorFlag` is set to "true". A message indicating "Could not delete record: [reviewer details]" is added to the `_generatedResponse/message` array for that specific failed deletion.
    *   **Technical Errors During Deletion**: If the `deleteReviewer` adapter invocation itself throws an exception (e.g., database down, malformed query from adapter), the inner `CATCH` block catches it. It retrieves the error details using `pub.flow:getLastError`, sets `errorFlag` to "true", and adds a detailed error message like "Could not delete ID: [reviewer ID] - [exception error message]" to `_generatedResponse/message`.

3.  **Overall Response Status**: After attempting to process all reviewers in the input list, the service consolidates the `errorFlag` and `successFlag` (which track if *any* errors or *any* successes occurred during the loop) to determine the final HTTP response code:
    *   **`400 Bad Request`**: If `errorFlag` is true and `successFlag` is not true. This covers scenarios where all deletion attempts failed, or if the initial input validation prevented any processing. The `_generatedResponse/result` is set to "error".
    *   **`207 Multi-Status`**: If both `errorFlag` and `successFlag` are true. This indicates that some reviewer deletions were successful, while others failed. The `_generatedResponse/result` is set to "partial success".
    *   **`200 OK` (Implied)**: If all deletions were successful (meaning `errorFlag` is false and `successFlag` is true), no explicit `setResponseCode` is called. The `cms.eadg.utils.api:setResponse` utility would then apply its default behavior, which for a "success" result is typically a 200 OK status. The `_generatedResponse/result` would be "success".

4.  **Unhandled System Errors**: A broad `CATCH` block at the outermost level handles any unexpected, unhandled exceptions that might occur during the service execution. In such cases, `pub.flow:getLastError` captures the system error, and `cms.eadg.utils.api:handleError` is invoked. This utility service defaults to setting an HTTP status of `500 Internal Server Error`, with a generic error message and the actual exception details.

The `cms.eadg.utils.api:setResponse` service is consistently used to serialize the `_generatedResponse` into either JSON (`application/json`) or XML (`application/xml`) format, as specified in the `SetResponse/format` field, before sending it back as the HTTP response body.