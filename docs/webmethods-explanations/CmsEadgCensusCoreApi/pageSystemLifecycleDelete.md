# Webmethods Service Explanation: CmsEadgCensusCoreApi pageSystemLifecycleDelete

This document provides a comprehensive explanation of the `pageSystemLifecycleDelete` Webmethods service, outlining its business purpose, technical implementation details, and interactions with external systems. It is designed for experienced software developers who may be new to the Webmethods platform, focusing on concepts and patterns relevant to porting the functionality to other languages like TypeScript.

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageSystemLifecycleDelete`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

---

### 1. Service Overview

The `pageSystemLifecycleDelete` service is designed to remove specific records related to system lifecycles within an external system, likely "Alfabet", by deleting both an "Enterprise Release" and a "Release Item" entry. This operation is critical for maintaining data integrity and removing obsolete lifecycle data.

The service expects the following input parameters:

*   `id`: This string represents the "Application RefStr" (Reference String) of the application associated with the records to be deleted. It serves as a primary identifier for the application within the system.
*   `enterpriseReleaseRefstr`: This string is the "Enterprise Release RefStr" of the specific enterprise release record targeted for deletion.
*   `version`: This string represents the "EnterpriseRelease version". While provided as input, it is immediately discarded and does not directly influence the core logic of this service.

Upon successful execution, the service is expected to delete two records in the external system and return a success message. In case of any failure, it returns a detailed error response, including an appropriate HTTP status code and an explanatory message. Key validation rules involve ensuring that both the expected "Enterprise Release" and "Release Item" records are found and successfully deleted. If none or only one of these records is deleted, the service flags it as an error.

---

### 2. Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm with "flow services" that are structured sequences of steps. Understanding these core elements is crucial for interpreting the service logic:

*   **SEQUENCE**: In Webmethods flow, a `SEQUENCE` is akin to a block of code or a function body in traditional programming. Steps within a sequence are executed in the order they appear. The `EXIT-ON="FAILURE"` attribute means that if any step within this sequence fails (e.g., throws an exception or explicitly exits with failure), the sequence itself terminates, and control passes to the next defined error handler (often a `CATCH` block). `FORM="TRY"` indicates that the sequence defines a "try" block, where errors will be handled by a subsequent "catch" block.
*   **BRANCH**: A `BRANCH` step functions similarly to a `switch` statement or a series of `if/else if/else` conditions. It evaluates an expression (defined by `SWITCH` or `LABELEXPRESSIONS="true"`) and executes only the sequence labeled with the matching condition. `$null` is a special label indicating a null or empty value. `LABELEXPRESSIONS="true"` allows for complex boolean expressions as branch conditions (e.g., `%count% == 0`).
*   **MAP**: A `MAP` step is used for data transformation and manipulation within the service's pipeline (the in-memory data structure passed between steps). It's comparable to an assignment block or data mapping logic in other languages.
    *   `MAPTARGET` and `MAPSOURCE` define the structure of the data pipeline before and after the mapping operation.
    *   `MAPSET`: Assigns a specific value to a field. It can reference global variables (e.g., `%alfabet.api.token.1%`) or other variables in the pipeline. `OVERWRITE="true"` ensures that any existing value in the target field is replaced.
    *   `MAPCOPY`: Copies the value from one field to another in the pipeline.
    *   `MAPDELETE`: Removes a field from the pipeline. This is often used for cleanup to prevent unnecessary data from being passed to subsequent steps or returned to the caller.
*   **INVOKE**: An `INVOKE` step is used to call another Webmethods service, acting like a function call in traditional programming. `VALIDATE-IN="$none"` and `VALIDATE-OUT="$none"` mean that Webmethods will not perform schema validation on the input and output data for that specific service call.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flow services support structured error handling using `TRY` and `CATCH` sequences. If an error occurs within a `TRY` block, execution immediately transfers to the corresponding `CATCH` block.
    *   `pub.flow:getLastError`: This built-in service retrieves details about the last error that occurred in the current flow execution, similar to obtaining an exception object.
    *   `EXIT FROM="$parent" SIGNAL="FAILURE"`: This instruction acts like throwing an exception. It terminates the current flow and propagates a failure signal up to the parent caller, optionally including a `FAILURE-MESSAGE`.

---

### 3. Database Interactions

Based on the provided Webmethods flow files for `pageSystemLifecycleDelete` and its direct dependencies, **no direct SQL database queries or stored procedure calls are performed.**

The service primarily interacts with an external system (referred to as "Alfabet") through API calls. The `deleteObjectsByRef` dependency service is responsible for communicating with this external system, which very likely has its own underlying database. However, the specific database tables, views, or stored procedures used by the "Alfabet" system are abstracted by its API and are not exposed or directly accessed by these Webmethods services.

Therefore, no database tables, views, or stored procedures can be listed from the provided information.

---

### 4. External API Interactions

The `pageSystemLifecycleDelete` service heavily relies on one key external API interaction handled by a dependency service:

*   **Service Called**: `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`
*   **Purpose**: This service acts as a proxy or wrapper to interact with an external "Alfabet API". Its primary function is to send a request to the Alfabet system to delete one or more objects identified by their reference strings.
*   **Request Format**: The `deleteObjectsByRef` service constructs a JSON payload conforming to the `ObjectByRefRequest` document type. This includes:
    *   `Refs`: An array of strings, where each string is a unique reference identifier (`RefStr`) of an object to be deleted in Alfabet.
    *   `CurrentProfile`: A string value set to "API User", indicating the user context for the deletion operation in Alfabet.
*   **Request Protocol**: The request is sent via an `HTTP POST` method to the Alfabet API endpoint (`/v2/delete`), with the `Content-Type` header set to `application/json`.
*   **Authentication Mechanism**: The request includes a `Bearer` token for authentication. This token is constructed by concatenating values from three global variables: `%alfabet.api.token.1%`, `%alfabet.api.token.2%`, and `%alfabet.api.token.3%`.
*   **Response Format**:
    *   **Success**: If the Alfabet API call is successful (HTTP 200 OK), the response is expected to be a JSON string, which is then parsed into a `ObjectByRefResponse` document type. This document includes a `Count` field indicating the number of objects successfully deleted.
    *   **Error**: If the Alfabet API returns an HTTP error status code (e.g., 4xx or 5xx), the service attempts to capture the status, message, and response body (converted to a string) into an internal `SetResponse` document.
*   **Error Handling for External Calls**:
    *   The `deleteObjectsByRef` service has internal error handling for the HTTP call. For example, an HTTP 403 (Forbidden) response from the external Alfabet API is specifically remapped to an HTTP 500 (Internal Server Error) before propagating the error. Other non-200 responses are also treated as failures, with their status codes and messages being captured for the response.

---

### 5. Main Service Flow

The `pageSystemLifecycleDelete` service executes the following steps in a sequential manner:

1.  **Initialize Variables**:
    *   The input `version` field is immediately removed from the pipeline as it's not used by the service's core logic.
    *   An authentication `token` is assembled by concatenating three global variables (`%alfabet.api.token.1%`, `%alfabet.api.token.2%`, `%alfabet.api.token.3%`). This token is essential for authenticating requests to the external Alfabet API.

2.  **Retrieve Release Item Reference String**:
    *   The service invokes the `cms.eadg.census.core.api.v02.systemCensus_.operations.pageSystemLifecycle:getReleaseItemRefstr` service.
    *   It passes the `id` (Application RefStr) and `enterpriseReleaseRefstr` as inputs to this sub-service.
    *   The sub-service is expected to return a `releaseItemRefstr`, which is the reference string for the "Release Item" associated with the provided inputs.
    *   After this call, the original `applicationRefstr` and `id` fields are removed from the pipeline for cleanup.

3.  **Prepare Objects for Deletion**:
    *   The `enterpriseReleaseRefstr` and the newly obtained `releaseItemRefstr` are copied into a new array named `refstrToDelete`. This array will contain the reference strings of the two specific objects that are intended for deletion in the external system.
    *   The `enterpriseReleaseRefstr` and `releaseItemRefstr` fields are then removed from the pipeline.

4.  **Execute External Deletion**:
    *   The service invokes `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`. This is the crucial step that initiates the actual deletion in the external Alfabet system.
    *   The assembled `token` and the `refstrToDelete` array (mapped to `ObjectByRefRequest/Refs`) are passed as inputs. A `CurrentProfile` of "API User" is also explicitly set for the request.
    *   Upon completion of this call, various intermediate fields (`ObjectByRefRequest`, `token`, `refstrToDelete`, `Response`) are removed to keep the pipeline clean.

5.  **Validate Deletion Count and Formulate Response**:
    *   The `Count` of deleted objects received from the `ObjectByRefResponse` (from the Alfabet API call) is converted to a string and stored in a `count` variable.
    *   A branching logic is applied based on this `count`:
        *   **`%count% == 0`**: If zero objects were deleted, it indicates that the "Role assignment(s) could not be found." A 400 Bad Request response is set, and the service exits with a `FAILURE` signal.
        *   **`%count% < 2`**: If less than two objects (i.e., only one, since count must be >= 0) were deleted, it implies that "One or more roles could not be deleted." A 400 Bad Request response is set, and the service exits with a `FAILURE` signal. This ensures that both the "Enterprise Release" and "Release Item" are deleted as a single logical unit.
        *   **`%count% == 2`**: If exactly two objects were deleted, it signifies a successful operation. A success response is constructed with the message "ReleaseItem and EnterpriseRelease successfully deleted".
        *   **`$default` (any other value for `count`)**: Catches unexpected counts, indicating an "unknown error." A 500 Internal Server Error response is set, and the service exits with `FAILURE`.

6.  **Catch-all Error Handling (Outer TRY-CATCH)**:
    *   If any unhandled error occurs during the execution of steps 1-5 (including errors from invoked sub-services that propagate a `FAILURE` signal), the flow transfers to the outer `CATCH` block.
    *   Inside the `CATCH` block, `pub.flow:getLastError` is invoked to retrieve details about the exception.
    *   `cms.eadg.utils.api:handleError` is then called. This utility service standardizes the error response, typically setting a 500 Internal Server Error unless a more specific error response was already prepared by a preceding step (e.g., within `deleteObjectsByRef`).
    *   Finally, unnecessary error-related fields are removed from the pipeline before the service terminates.

---

### 6. Dependency Service Flows

The `pageSystemLifecycleDelete` service relies on several other Webmethods services to perform its operations and manage responses:

*   **`cms.eadg.census.core.api.v02.systemCensus_.operations.pageSystemLifecycle:getReleaseItemRefstr`**
    *   **Purpose**: The primary purpose of this service is to derive or retrieve the `RefStr` (Reference String) for a "Release Item" based on the provided application and enterprise release identifiers.
    *   **Integration with Main Flow**: It is invoked early in the `pageSystemLifecycleDelete` flow to obtain one of the two crucial reference strings required for the subsequent deletion call to the external Alfabet API.
    *   **Input Contract**: Expects `applicationRefstr` (mapped from `id` in the main service) and `enterpriseReleaseRefstr`.
    *   **Output Contract**: Returns `releaseItemRefstr`.
    *   **Specialized Processing**: While the internal flow of `getReleaseItemRefstr` is not provided, it is assumed to contain business logic, potentially involving database lookups or complex calculations, to accurately identify the `releaseItemRefstr` from its inputs.

*   **`cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`**
    *   **Purpose**: This service acts as the direct interface to the external Alfabet API for deleting objects by their reference strings. It abstracts the complexities of HTTP communication, JSON serialization, and API authentication from the calling service.
    *   **Integration with Main Flow**: It is a critical component, performing the actual deletion operation on the external system.
    *   **Input Contract**: Requires a `token` for authentication, and an `ObjectByRefRequest` document containing an array of `Refs` (reference strings to delete) and a `CurrentProfile`. It also has optional inputs `validateClassName` and `className` for additional validation.
    *   **Output Contract**: Returns `ObjectByRefResponse` on success (including `Count` of deleted items) or a generic `Response` document on error.
    *   **Specialized Processing**:
        *   **Class Name Validation**: If `validateClassName` is set to "true" and `className` is provided, the service performs an important validation step. It dynamically constructs a global variable name (e.g., `alfabet.class.YourClassName.id`) and fetches its value using `cms.eadg.utils.globalVariables:getGlobalVariable`. It then iterates through each `Ref` in the input `Refs` array, tokenizes the `RefStr` by the hyphen (`-`) to extract the class ID (the part before the first hyphen). If this extracted class ID does not match the expected `classId` from the global variable, the service immediately exits with a `FAILURE` signal, preventing deletion of objects of an unexpected type.
        *   **JSON Serialization**: Converts the `ObjectByRefRequest` document into a JSON string payload.
        *   **HTTP Call**: Executes an `HTTP POST` request to the configured Alfabet API URL and path (`/v2/delete`). It includes the JSON payload in the request body and the `token` in the `Bearer` authentication header.
        *   **Response Handling**: Converts the raw byte stream response from the HTTP call into a string. It then inspects the HTTP status code:
            *   If the status is `200` (OK), it parses the response string from JSON back into an `ObjectByRefResponse` document.
            *   If the status is `403` (Forbidden), it explicitly maps this to an internal `500` status, signifying a server-side configuration issue or unexpected permission problem for the API user, rather than a client-side bad request.
            *   For any other non-200 HTTP status, it captures the status code and message into a `SetResponse` document and exits with `FAILURE`.
        *   **Internal Error Handling**: Contains its own `CATCH` block to handle unexpected errors during its execution. If no `SetResponse` was previously populated by an HTTP error, it defaults to setting a 500 Internal Server Error using `cms.eadg.utils.api:setResponse` and propagates the error.

*   **`cms.eadg.utils.globalVariables:getGlobalVariable`**
    *   **Purpose**: This is a generic utility service designed to retrieve values from Webmethods global variables. Global variables are used for configuration values that might change between environments (e.g., API URLs, tokens, specific IDs).
    *   **Integration with Main Flow**: It is used indirectly by `deleteObjectsByRef` for class name validation, retrieving the expected class ID from global settings.
    *   **Input Contract**: Requires `name` (the name of the global variable to retrieve) and an optional `default` value.
    *   **Output Contract**: Returns `value` (the string value of the global variable).

*   **`cms.eadg.utils.api:handleError`**
    *   **Purpose**: A standardized error handling utility service. It ensures that API responses conform to a consistent error format regardless of where the error originates in the application.
    *   **Integration with Main Flow**: It is invoked by the main service's `CATCH` block when an unhandled exception occurs or a `FAILURE` signal is propagated.
    *   **Input Contract**: Accepts a `SetResponse` document (if an error response was partially formed) and `lastError` (from `pub.flow:getLastError`).
    *   **Output Contract**: Populates the `SetResponse` document with standardized error details.
    *   **Specialized Processing**: If the `SetResponse` input is null (meaning no specific error response was set previously), it populates it with a 500 Internal Server Error and includes the `error` message from `lastError`. If `SetResponse` already contains details (e.g., from an HTTP error in a sub-service), it just reuses those.

*   **`cms.eadg.utils.api:setResponse`**
    *   **Purpose**: This utility service is responsible for formatting the final HTTP response sent back to the client. It handles content type negotiation (JSON vs. XML) and sets the HTTP status code.
    *   **Integration with Main Flow**: It is the last service invoked (indirectly, via `handleError` or directly from the success path) to prepare the outgoing API response.
    *   **Input Contract**: Takes a `SetResponse` document, which specifies the desired `responseCode`, `responsePhrase`, `result` (success/error), `message` (details), and `format` (e.g., "application/json", "application/xml").
    *   **Output Contract**: Sets the HTTP response code and response body for the current service invocation.
    *   **Specialized Processing**:
        *   **Data Mapping**: Copies `result` and `message` from `SetResponse` to a generic `Response` document.
        *   **Content Type Branching**: It branches based on the `format` specified in `SetResponse`:
            *   **`application/json`**: Uses `pub.json:documentToJSONString` to convert the `Response` document into a JSON string, which becomes the HTTP response body.
            *   **`application/xml`**: Wraps the `Response` document within a `ResponseRooted` document (to ensure a single root XML element), then uses `pub.xml:documentToXMLString` to convert it to an XML string for the response body.
        *   **HTTP Response Setting**: Calls `pub.flow:setResponseCode` to set the HTTP status code and reason phrase, and `pub.flow:setResponse2` to set the HTTP response body and content type.

---

### 7. Data Structures and Types

The service utilizes several document types to structure its inputs, outputs, and intermediate data:

*   **Input Data Model (`pageSystemLifecycleDelete`):**
    *   `id` (string): **Required.** Represents the unique identifier for the Application RefStr.
    *   `enterpriseReleaseRefstr` (string): **Required.** Represents the unique identifier for the Enterprise Release RefStr.
    *   `version` (string): **Optional.** Represents the Enterprise Release version. It is immediately discarded after input.

*   **Output Data Model (`pageSystemLifecycleDelete`):**
    *   `_generatedResponse` (Type: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`):
        *   `result` (string, optional): Indicates the overall outcome, typically "success" or "error".
        *   `message` (string[], optional): An array of human-readable messages providing more context about the operation's outcome.
    *   `400` (record, optional): This output signifies an HTTP 400 Bad Request error. It contains a nested `Response` document of the same type as `_generatedResponse`, but with error details.
    *   `401` (record, optional): Although not explicitly generated by this service, it's part of the output signature, suggesting potential propagation from downstream services. It would contain a nested `Response` document for an HTTP 401 Unauthorized error.
    *   `500` (record, optional): This output signifies an HTTP 500 Internal Server Error. It also contains a nested `Response` document with error details.

*   **Key Intermediate and Dependency Data Structures:**
    *   **`cms.eadg.alfabet.api.v01.resources.objects.byRef.docs:ObjectByRefRequest`**: Used to construct the request payload for the external Alfabet API's `deleteObjectsByRef` call.
        *   `Refs` (string[]): The array of reference strings to delete.
        *   `CurrentProfile` (string): Specifies the user profile under which the operation is performed in Alfabet (e.g., "API User").
    *   **`cms.eadg.alfabet.api.v01.resources.objects.byRef.docs:ObjectByRefResponse`**: The structure expected as a successful response from the external Alfabet API.
        *   `Objects` (record[]): An array of records representing the objects. Each record contains `ClassName`, `RefStr`, `Values`, etc.
        *   `RejectedObjects` (record[]): An array of records for objects that could not be processed.
        *   `Count` (object/long): Crucially, this field indicates the total number of objects affected by the operation.
    *   **`cms.eadg.utils.api.docs:SetResponse`**: A generic internal document type used to centralize and standardize the construction of API responses, especially for error scenarios.
        *   `responseCode` (string): The HTTP status code to be returned (e.g., "200", "400", "500").
        *   `responsePhrase` (string): The HTTP reason phrase (e.g., "OK", "Bad Request", "Internal Server Error").
        *   `result` (string): A custom status like "success" or "error".
        *   `message` (string[]): Detailed messages for the client.
        *   `format` (string): The desired content type of the response, "application/json" or "application/xml".
    *   **`pub.event:exceptionInfo`**: A built-in Webmethods document type that captures detailed information about an exception or error, including the `error` message and `stackTrace`.
    *   **`cms.eadg.utils.api.docs:Response`**: A simpler, generic response structure primarily used within utility services, containing `result` and `message` fields.
    *   **`cms.eadg.utils.api.docs:ResponseRooted`**: A wrapper document type (`Response` nested under `ResponseRooted`) used specifically when generating XML responses to ensure a proper root element.

*   **Field Validation Rules**:
    *   The `pageSystemLifecycleDelete` service implicitly requires `id` and `enterpriseReleaseRefstr` as inputs; their absence would lead to downstream errors.
    *   Within `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`, if `validateClassName` is enabled, there is a validation that checks if the class ID embedded in the input `RefStr` matches a pre-configured `className` obtained from global variables. This prevents unintended deletion of objects of the wrong type.

*   **Data Transformation Logic**:
    *   **`MAPDELETE`**: Used extensively to remove intermediate variables or input fields (`version`, `id`, `applicationRefstr`, internal API response objects, etc.) that are no longer needed, keeping the pipeline lean.
    *   **`MAPSET`**: Employed for setting fixed values (e.g., "API User" for `CurrentProfile`, HTTP methods like "post"), dynamically constructing URLs, and building the `SetResponse` document with status codes and messages. It also concatenates global variables to form the authentication token.
    *   **`MAPCOPY`**: Facilitates the transfer of data between different parts of the pipeline or different document types, such as mapping input `id` to `applicationRefstr` or populating `refstrToDelete` array.
    *   **`pub.json:documentToJSONString`**: Converts Webmethods `IData` documents (records) into JSON strings for external HTTP requests (e.g., `ObjectByRefRequest`).
    *   **`pub.string:bytesToString`**: Transforms the raw byte array received in an HTTP response body into a human-readable string.
    *   **`pub.json:jsonStringToDocument`**: Parses JSON strings received from external APIs into Webmethods `IData` documents for easier manipulation (e.g., `ObjectByRefResponse`).
    *   **`pub.string:objectToString`**: Converts the numerical `Count` from the Alfabet response (`java.lang.Long`) into a string for comparison in branching logic.
    *   **`pub.string:tokenize`**: Splits the `RefStr` string by a delimiter (hyphen in this case) to extract the class ID for validation purposes.
    *   **`pub.xml:documentToXMLString`**: Converts a Webmethods `IData` document into an XML string for the response body when XML format is requested.

---

### 8. Error Handling and Response Codes

The service employs a robust error handling strategy to provide clear and informative responses to the client, distinguishing between different failure scenarios.

*   **Main Service Error Scenarios**:
    *   **None Deleted (Count is 0)**:
        *   **Business Logic**: Occurs when the external Alfabet API reports that zero objects were deleted. This is considered a client-side error because the requested objects for deletion (Enterprise Release and Release Item) were not found.
        *   **HTTP Response**: Status Code `400` (Bad Request), Reason Phrase `Bad Request`.
        *   **Output**: `result: "error"`, `message: ["Role assignment(s) could not be found"]`.
    *   **Partial Deletion (Count is 1)**:
        *   **Business Logic**: Occurs if the external Alfabet API reports that only one of the two expected objects (either the Enterprise Release or the Release Item) was deleted. This indicates an inconsistent state or that one of the target objects was missing.
        *   **HTTP Response**: Status Code `400` (Bad Request), Reason Phrase `Bad Request`.
        *   **Output**: `result: "error"`, `message: ["One or more roles could not be deleted. Please re-pull role list."]`.
    *   **Unknown Count Error (Default Case)**:
        *   **Business Logic**: If the `Count` returned by the Alfabet API is not 0, 1, or 2 (which would be unexpected behavior).
        *   **HTTP Response**: Status Code `500` (Internal Server Error), Reason Phrase `Internal Server Error`.
        *   **Output**: `result: "error"`, `message: ["One or more roles could not be deleted. Please re-pull role list."]`.
    *   **Native Webmethods Error**:
        *   **Business Logic**: Catches any unhandled exceptions or propagated `FAILURE` signals from internal or external service calls that were not explicitly managed by preceding `BRANCH` statements. Examples include missing global variables (`"Global variable %classIdVariable% is missing"`) or other runtime errors.
        *   **HTTP Response**: Status Code `500` (Internal Server Error), Reason Phrase `Internal Server Error`.
        *   **Output**: `result: "error"`, `message: [error details from pub.flow:getLastError]`.

*   **External API Error Scenarios (handled by `deleteObjectsByRef` dependency)**:
    *   **Class ID Mismatch**:
        *   **Business Logic**: If the `validateClassName` flag is true and the class ID extracted from an input `RefStr` does not match the expected `classId` from global variables.
        *   **HTTP Response**: The `deleteObjectsByRef` service exits with `FAILURE`. This is caught by the main service's outer `CATCH` block.
        *   **Output**: Results in a `500 Internal Server Error` with a message like `"%ObjectByRefRequest/Refs% does not match expected class ID (%classId%) for class %className%"`.
    *   **External API HTTP 403 Forbidden**:
        *   **Business Logic**: The external Alfabet API returns an HTTP 403 status, indicating permission issues. The `deleteObjectsByRef` service explicitly re-maps this to a 500.
        *   **HTTP Response**: Status Code `500` (Internal Server Error), Reason Phrase (`Internal Server Error` set by `setResponse` or the original `statusMessage` from Alfabet).
        *   **Output**: `result: "error"`, `message: [original message from the external API]`.
    *   **Other External API HTTP Errors**:
        *   **Business Logic**: Any other non-200 HTTP status returned by the external Alfabet API.
        *   **HTTP Response**: The original HTTP Status Code and Reason Phrase from the Alfabet API are captured.
        *   **Output**: `result: "error"`, `message: [original response body string from the external API]`.

*   **HTTP Response Codes Used**:
    *   `200 OK`: For successful deletion of both Enterprise Release and Release Item.
    *   `400 Bad Request`: For client-side errors where the request is invalid or the target records for deletion are not found/partially found.
    *   `500 Internal Server Error`: For unexpected server-side errors, unhandled exceptions, or issues with external API communication (including re-mapped 403s).

*   **Error Message Formats**:
    *   All error responses are standardized, typically providing a `result` field set to "error" and a `message` field (which is an array of strings) containing one or more descriptive messages. The HTTP status code and reason phrase are also set appropriately to reflect the nature of the error.

*   **Fallback Behaviors**:
    *   The nested `TRY-CATCH` blocks ensure that errors are handled at different levels of granularity. Specific issues like external API HTTP errors are caught and re-formatted by `deleteObjectsByRef`. Any errors not specifically handled by inner blocks are caught by the outermost `CATCH` block in `pageSystemLifecycleDelete`, which then uses `cms.eadg.utils.api:handleError` to provide a generic `500 Internal Server Error` response, preventing unhandled exceptions from crashing the service or returning unformatted responses. This layered approach ensures a consistent and predictable error output for the API consumer.
    *   The mapping of `403` to `500` in `deleteObjectsByRef` is a specific fallback decision, treating external permission issues as internal server problems for the calling service.

---

**Source Database Column to Output Object Property Mapping:**

Based on the provided Webmethods flow definitions, this service does not directly query a source database or transform database columns into output JSON properties. Its primary function is to call an external API (Alfabet) to perform deletion. The inputs `id` and `enterpriseReleaseRefstr` are used as reference strings for the external API, rather than mapping to database columns in the traditional sense.

Therefore, there are no direct SQL table/view column mappings to JSON output object properties in this service. The service operates on "reference strings" (RefStrs) which are identifiers within the external "Alfabet" system.

*   `id` (Input): Used as `applicationRefstr` in `getReleaseItemRefstr`.
*   `enterpriseReleaseRefstr` (Input): Used as `enterpriseReleaseRefstr` in `getReleaseItemRefstr` and also directly mapped into `refstrToDelete`.
*   `releaseItemRefstr` (Derived/Output from `getReleaseItemRefstr`): Mapped into `refstrToDelete`.
*   `refstrToDelete` (Internal array): Contains the Alfabet `RefStr` values for deletion.
*   `ObjectByRefResponse/Count` (from external API): Converted to `count` (internal string variable) for validation.
*   `_generatedResponse/result`: Set to "success" or "error" based on deletion outcome.
*   `_generatedResponse/message`: Set to various informational or error messages based on business logic.