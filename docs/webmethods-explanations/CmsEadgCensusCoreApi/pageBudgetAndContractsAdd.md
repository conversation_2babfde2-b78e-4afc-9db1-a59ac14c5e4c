# Webmethods Service Explanation: CmsEadgCensusCoreApi pageBudgetAndContractsAdd

This document provides a comprehensive explanation of the Webmethods service `pageBudgetAndContractsAdd` within the `CmsEadgCensusCoreApi` package, detailing its purpose, internal logic, database interactions, and error handling mechanisms. The service is designed to manage budget and contract information for systems by facilitating the addition, update, and deletion of related records in an underlying database (Alfabet, via a Cedar Core API abstraction layer).

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageBudgetAndContractsAdd`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

## Service Overview

The `pageBudgetAndContractsAdd` service serves as a centralized endpoint for managing system-specific budget and contract data. Its primary business purpose is to provide a single API for creating, modifying, and removing budget and contract records associated with a given system ID.

The service expects a single input parameter: `_generatedInput`, which is of document type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageBudgetAndContracts`. This input document is expected to contain a `systemId` along with arrays of `Budgets` and `Contracts` that indicate which records should be added, updated, or deleted.

The expected output of the service is `_generatedResponse`, a document of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`. On successful execution, this response will indicate `success` with a corresponding message. In case of errors, the service will return appropriate HTTP status codes (e.g., 400 for bad requests, 401 for unauthorized, 500 for internal server errors) along with an error message.

A key validation rule at the beginning of the service is that `systemId` must be provided within the input, otherwise, the service will immediately fail with a "Please provide a list of budgets" message (which seems like a generic error message, not specific to `systemId` itself). Further validations are handled by downstream services and database stored procedures.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow" to define service logic. Here are some core elements encountered in this service:

*   **SEQUENCE**: Analogous to a block of code in traditional programming. Statements within a sequence are executed in order. A sequence can have `EXIT-ON="FAILURE"`, meaning if any step within it fails, the entire sequence (and potentially the flow) terminates. `FORM="TRY"` and `FORM="CATCH"` are used to implement `try-catch` error handling blocks, where the `CATCH` sequence executes if an error occurs in the `TRY` sequence.
*   **BRANCH**: Similar to a `switch` statement or a series of `if-else if-else` conditions. It evaluates an expression (`SWITCH` attribute) and executes a specific path (`NAME` attribute) based on the expression's value. `LABELEXPRESSIONS="true"` allows more complex boolean expressions for branching conditions, rather than just direct value matching.
*   **MAP**: This element is crucial for data transformation and manipulation within the pipeline. The "pipeline" is Webmethods' in-memory data structure (an `IData` object) that carries all variables throughout the flow's execution.
    *   **MAPSET**: Used to set a new value for an existing field or create a new field in the pipeline.
    *   **MAPCOPY**: Used to copy the value from one field in the pipeline to another.
    *   **MAPDELETE**: Used to remove a field from the pipeline. This is important for managing memory and ensuring data integrity, as unnecessary data is removed after processing.
*   **INVOKE**: Used to call another Webmethods service (a sub-flow, Java service, or adapter service). When a service is invoked, its input parameters are mapped from the current pipeline, and its output parameters are mapped back into the pipeline.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flows can define `TRY` and `CATCH` sequences. Operations within the `TRY` block are executed, and if an unhandled exception occurs, control is transferred to the `CATCH` block. The `pub.flow:getLastError` service is typically invoked in the `CATCH` block to retrieve detailed information about the error that occurred. The `cms.eadg.utils.api:handleError` service then processes this error information to generate a standardized error response.

## Database Interactions

The `pageBudgetAndContractsAdd` service orchestrates several database interactions through its dependency services, primarily involving stored procedures. All database interactions are routed through the `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans` JDBC adapter connection.

*   **Database Connection Configuration**:
    *   **Adapter Type**: JDBCAdapter
    *   **Connection Name**: `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`
    *   **Database Name**: `Sparx_Support`
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port Number**: `1433`
    *   **User**: `sparx_dbuser`
    *   **Transaction Type**: `NO_TRANSACTION` (meaning each database operation is committed independently, not as part of a larger transaction managed by Webmethods).

*   **Database Operations and Stored Procedures Called**:

    *   **Adding Budgets**:
        *   **Operation**: Insertion of budget records.
        *   **Stored Procedure**: `dbo.SP_Insert_SystemBudget_json` (called by `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:addBudgetInput`).
        *   **Data Mapping (Service Input to SP Parameter)**: The `UpdateRequest` document (containing `ProjectArch` objects and `Relations`) is first converted to a JSON string, which is then passed as the `@jsonInput` parameter to this stored procedure.
        *   **SP Output**: `@RETURN_VALUE` (integer indicating success/failure).

    *   **Updating Budgets**:
        *   **Operation**: Update of existing budget records.
        *   **Stored Procedure**: `dbo.SP_Update_SystemBudget_json` (called by `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:updateBudget`).
        *   **Data Mapping (Service Input to SP Parameter)**: Similar to adding budgets, the `UpdateRequest` document (containing `ProjectArch` objects and `Relations`) is converted to a JSON string and passed as `@jsonInput`.
        *   **SP Output**: `@RETURN_VALUE`.

    *   **Deleting Budgets**:
        *   **Operation**: Deletion of budget records.
        *   **Stored Procedure**: `dbo.SP_Delete_System_Contract_Full_Tbl_Reln` (called by `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:SP_Delete_Contract_List`).
        *   **Data Mapping (Service Input to SP Parameter)**: The budget IDs to be deleted are concatenated into a string list (`@GUID_List`) and passed to the stored procedure.
        *   **SP Output**: `@RETURN_VALUE`.

    *   **Adding Contracts**:
        *   **Operation**: Insertion of contract and related deliverable records.
        *   **Stored Procedure**: `SP_Insert_SystemContract_json` (called by `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:SP_Insert_Contracts`).
        *   **Data Mapping (Service Input to SP Parameter)**: The `UpdateRequest` document (containing `ContractDeliverable` objects and `Relations`) is converted to a JSON string and passed as `@jsonInput`. An `@jsonOutput` parameter is also used as an in/out parameter, expected to return new GUIDs.
        *   **SP Output**: `@RETURN_VALUE`, `@jsonOutput` (string).

    *   **Updating Contracts**:
        *   **Operation**: Update of existing contract and optionally deliverable records.
        *   **Stored Procedure**: `SP_Update_SystemContract_json` (called by `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:SP_Update_Contracts`).
        *   **Data Mapping (Service Input to SP Parameter)**: The `UpdateRequest` document (containing `ContractDeliverable` objects and `Relations`) is converted to a JSON string and passed as `@jsonInput`.
        *   **SP Output**: `@RETURN_VALUE`.

## External API Interactions

This service primarily interacts with internal Webmethods services and database adapters. There are no direct calls to external APIs visible in the provided flow or immediate dependencies. The "Alfabet" system mentioned in comments refers to an enterprise architecture management tool, which is likely accessed via the internal Cedar Core API, abstracting away its direct API interactions.

## Main Service Flow

The `pageBudgetAndContractsAdd` service executes its logic within a `TRY` block to ensure robust error handling.

1.  **Initial Validation**: The service first checks if the `systemId` field within the `_generatedInput` document is provided. If `systemId` is null, the service immediately exits with a "FAILURE" signal and a message "Please provide a list of budgets." (Note: this message seems generic and might not accurately reflect the specific `systemId` validation failing).

2.  **Request Mapping**: It then invokes the `cms.eadg.census.core.api.v02.systemCensus_.operations.pageBudgetAndContractsAdd:mapRequests` service. This crucial step takes the `_generatedInput` (of type `PageBudgetAndContracts`) and transforms its contents into more granular request types for adding, updating, and deleting budgets and contracts. The output of this mapping step includes:
    *   `BudgetAddRequest`
    *   `BudgetUpdateRequest`
    *   `budgetDeleteIds` (array of strings)
    *   `ContractAddRequest`
    *   `ContractUpdateRequest`
    *   `contractDeleteIds` (array of strings)
    These new documents and arrays are then available in the pipeline for subsequent processing.

3.  **Conditional Business Logic Execution**: The service then proceeds with a series of conditional `BRANCH` statements, executing specific operations only if the corresponding request documents or ID lists are not null. This allows for partial updates where only certain types of data (e.g., only budgets to be added, or only contracts to be deleted) are present in the input.

    *   **Add Budgets**: If `BudgetAddRequest` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetAdd` service is invoked. This service handles the creation of new budget records. An immediate error check (`%SetResponse% != $null`) follows; if the `budgetAdd` service indicates an error (by setting a `SetResponse` document in the pipeline), the main service will terminate early.

    *   **Update Budgets**: If `BudgetUpdateRequest` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetUpdate` service is invoked. This handles modifications to existing budget records. Similarly, an error check follows.

    *   **Delete Budgets**: If `budgetDeleteIds` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetDeleteList` service is invoked to remove specified budget records. This also includes an error check.

    *   **Add Contracts**: If `ContractAddRequest` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractAdd` service is invoked for creating new contract records. An error check is performed immediately after.

    *   **Update Contracts (Full)**: If `ContractUpdateRequest` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractUpdate` service is invoked. This service updates existing contract records, including associated deliverable information if the `budgetsOnly` flag is not set (it's not set here, so full update). An error check follows.

    *   **Update Contracts (Budgets Only)**: If `ContractUpdateRequestBudgetsOnly` is not null, the `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractUpdate` service is invoked again, but this time with an explicit `budgetsOnly` flag set to `true`. This indicates a specific scenario where only the budget portion of a contract needs to be updated, implying a more granular update operation. An error check follows.

4.  **Response Generation**: If all the above operations complete without triggering any `EXIT` statements or setting a `SetResponse` (indicating an error from a sub-service), the main service proceeds to map a successful response. The `_generatedResponse` document's `result` field is set to "success", and its `message` is set to "Budget and contracts updated successfully". All intermediate pipeline variables like `systemId` and `_generatedInput` are deleted.

5.  **Error Scenarios and Handling**: The entire flow is enclosed in a `TRY-CATCH` block. If any unhandled exception occurs during the execution of the `TRY` block (e.g., a service fails unexpectedly without setting a `SetResponse` document), control transfers to the `CATCH` block.
    *   The `pub.flow:getLastError` service retrieves detailed information about the exception.
    *   This error information is then passed to the `cms.eadg.utils.api:handleError` service, which standardizes the error response format. This typically involves setting the HTTP response code and a descriptive error message in the output pipeline, which is then sent back to the client.

## Dependency Service Flows

The main service relies heavily on several other Webmethods services to perform its tasks. These dependencies encapsulate specific business logic and data manipulation.

*   **`cms.eadg.census.core.api.v02.systemCensus_.operations.pageBudgetAndContractsAdd:mapRequests`**:
    *   **Purpose**: This service is responsible for preprocessing the incoming `PageBudgetAndContracts` document. It parses the embedded `Budgets` and `Contracts` arrays and categorizes them into separate lists for "add", "update", and "delete" operations based on their internal flags (`updated`, `deleted`).
    *   **Integration with Main Flow**: It's the first major step after initial validation, transforming the single input document into multiple, more specific request documents/arrays that drive the subsequent conditional execution paths in the main service.
    *   **Input/Output Contracts**: Takes `PageBudgetAndContracts` as input and produces `BudgetAddRequest`, `BudgetUpdateRequest`, `budgetDeleteIds`, `ContractAddRequest`, `ContractUpdateRequest`, `contractDeleteIds` in the pipeline.
    *   **Specialized Processing**: This mapping logic is critical for enabling the idempotent and granular update/delete functionality of the overall API.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetAdd:mapBudget`**:
    *   **Purpose**: Maps the generic `Budget` input documents into the specific `UpdateRequest` structure required by the underlying Alfabet system (represented as `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`). This includes creating `ProjectArch` objects and their associated `Relations`.
    *   **Integration with Main Flow**: Called by `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetAdd` as a preparatory step before sending data to the database adapter.
    *   **Input/Output Contracts**: Takes an array of `Budget` documents and outputs a single `UpdateRequest` document.
    *   **Specialized Processing**: Handles the transformation of simple budget data into complex nested objects and relationships required by the target system. It sets `ClassName` and generates internal IDs (`Object.Id`) for new objects based on the loop iteration.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetUpdate:mapBudget`**:
    *   **Purpose**: Similar to `mapBudget` for adds, but specifically tailored for updates. It maps `Budget` documents to the `UpdateRequest` format, ensuring existing references (`RefStr`) are correctly maintained. It explicitly checks for `fundingId` which is crucial for identifying existing budget records in Alfabet.
    *   **Integration with Main Flow**: Called by `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetUpdate` before interacting with the database adapter.
    *   **Input/Output Contracts**: Takes an array of `Budget` documents and outputs a `UpdateRequest` document.
    *   **Specialized Processing**: Ensures that update requests correctly reference existing records in the target system using fields like `RefStr`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:mapContract`**:
    *   **Purpose**: Maps incoming `Contract` documents into the `UpdateRequest` structure suitable for Alfabet, specifically focusing on creating `ContractDeliverable` objects and their relationships. It loops through individual contracts and calls `buildUpdateRequest` for each.
    *   **Integration with Main Flow**: Called by `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractAdd`.
    *   **Input/Output Contracts**: Takes an array of `Contract` documents and outputs a `UpdateRequest` document.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:buildUpdateRequest`**:
    *   **Purpose**: Builds a single `UpdateRequest` object for a given contract and its associated architecture elements. It populates `ContractDeliverable` details and sets up necessary `Relations`. It constructs a `name` for `ContractDeliverable` by concatenating `contractId` and `architectureElementId`.
    *   **Integration with Main Flow**: Called within a loop by `cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:mapContract`.
    *   **Input/Output Contracts**: Takes individual contract-related IDs (`contractId`, `architectureElementId`, `objectId`, `cms_application_delivery_org`) and appends transformed `Object` and `Relations` data to the `UpdateRequest` document being built.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractUpdate:mapContract`**:
    *   **Purpose**: Similar to `mapContract` for adds, but designed for updating existing contracts and deliverables. It maps `Contract` documents to the `UpdateRequest` format, including the logic for identifying `ContractDeliverable` objects by their `RefStr`.
    *   **Integration with Main Flow**: Called by `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractUpdate`.
    *   **Input/Output Contracts**: Takes an array of `Contract` documents and outputs a `UpdateRequest` document.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.utils:mapDeleteResponse`**:
    *   **Purpose**: Standardizes the response after delete operations (for both budgets and contracts). It compares the `expectedCount` (number of items requested for deletion) with the `count` (number of successfully deleted items) and generates an appropriate success or error response message and HTTP status code (400 if partial or no deletion, 200 if all deleted).
    *   **Integration with Main Flow**: Called by `budgetDeleteList` and `contractDeleteList` after their deletion loops.
    *   **Input/Output Contracts**: Takes `expectedCount` and `count` (succeeded) as input, and outputs a `_generatedResponse` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`) which includes a `result` and `message`.

*   **Adapter Services (e.g., `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:addBudgetInput`, `SP_Insert_Contracts`)**:
    *   **Purpose**: These are direct interfaces to the database. They encapsulate the call to specific stored procedures and handle the conversion of Webmethods documents to database-friendly formats (like JSON strings for input to stored procedures) and vice-versa.
    *   **Integration with Main Flow**: Invoked by the `cedarCore_.services` (e.g., `budgetAdd`, `contractAdd`) after data transformation.
    *   **Input/Output Contracts**: Typically take a JSON string representing the data to be inserted/updated and return an integer indicating the success or failure of the stored procedure execution, sometimes with additional JSON output.

*   **Utility Services (e.g., `pub.json:documentToJSONString`, `pub.json:jsonStringToDocument`, `pub.string:makeString`, `pub.flow:getLastError`, `pub.flow:setResponse2`, `pub.flow:setResponseCode`, `cms.eadg.utils.api:handleError`, `cms.eadg.utils.api:setResponse`)**:
    *   **Purpose**: These are standard or custom utility services used for common tasks like JSON/XML conversion, string manipulation, and generic HTTP response handling.
    *   **Integration with Main Flow**: Integrated throughout the flow to prepare data for database interaction or for setting HTTP responses. `cms.eadg.utils.api:handleError` is the central error handling mechanism that standardizes error messages and response codes.

## Data Structures and Types

The service heavily relies on Webmethods "Document Types" (recrefs) which are schema definitions for complex data structures. These are analogous to TypeScript interfaces or classes.

*   **Input Data Model (`_generatedInput`): `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageBudgetAndContracts`**
    *   `systemId`: string, optional. Represents the ID of the system for which budget/contract data is being managed.
    *   `pageName`: string, optional.
    *   `count`: BigInteger, optional.
    *   `Budgets`: Array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Budget`. Each `Budget` document defines a budget record.
        *   `id`: string, optional. Internal ID of the OFM budget.
        *   `projectId`: string, required. OFM budget project ID.
        *   `projectTitle`: string, optional. Project Title.
        *   `fundingId`: string, optional. Cross-reference ID between budget project and application.
        *   `funding`: string, optional. Description of budget allocation.
        *   `deleted`: boolean, optional. Flag indicating user-initiated deletion.
        *   `updated`: boolean, optional. Flag indicating user-initiated add or update.
    *   `Contracts`: Array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Contract`. Each `Contract` document defines a contract record.
        *   `id`: string, required.
        *   `contractDeliverableId`: string, optional.
        *   `parentAwardId`: string, required. Parent contract number.
        *   `awardId`: string, required. Contract number.
        *   `contractADO`: string, optional (Yes/No). Is ADO Parent Contract.
        *   `description`: string, optional. Contract description.
        *   `deleted`: boolean, optional. Flag indicating user-initiated deletion.
        *   `updated`: boolean, optional. Flag indicating user-initiated add or update.
        *   `systemId`: string, optional. System which this contract funds.
        *   `POPStartDate`: string, optional.
        *   `POPEndDate`: string, optional.
        *   `contractName`: string, optional.

*   **Internal Data Transformation Models (`UpdateRequest` in Cedar Core API):**
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`: This is a generic request format used to communicate with the underlying Alfabet system. It contains:
        *   `CurrentProfile`: string. Hardcoded to "API User".
        *   `Objects`: Array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object`. These represent the entities (Budgets as `ProjectArch`, Contracts as `ContractDeliverable`) to be added/updated in Alfabet.
            *   `ClassName`: string. Hardcoded (e.g., "ProjectArch", "ContractDeliverable").
            *   `Id`: string. Internal unique ID for the object within the request.
            *   `RefStr`: string. Reference string for existing objects (e.g., `fundingId`, `contractDeliverableId`).
            *   `Values`: Document. Contains the actual data fields of the object (e.g., `ProjectArch` or `ContractDeliverable` specific fields).
        *   `Relations`: Array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations`. These define associations between objects in Alfabet.
            *   `FromId`: string. ID of the source object in the relationship.
            *   `Property`: string. The type of relationship (e.g., "project", "object", "architectureelement", "contract").
            *   `ToRef`: string. Reference string or ID of the target object in the relationship.

*   **Output Data Model (`_generatedResponse`): `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`**
    *   `result`: string, optional. Typically "success" or "error".
    *   `message`: Array of strings, optional. Contains informational or error messages.

*   **Data Transformation Logic**: The services `mapRequests`, `mapBudget`, `mapContract`, and `buildUpdateRequest` are central to transforming the incoming simpler `Budget` and `Contract` documents into the complex, nested `UpdateRequest` structure that the underlying Alfabet system (via stored procedures) expects. This involves:
    *   **Categorization**: Separating additions, updates, and deletions based on `updated` and `deleted` flags.
    *   **Field Mapping**: Direct copying of fields (e.g., `Budget.funding` to `ProjectArch.cms_funding`).
    *   **Concatenation**: Combining fields to form new values (e.g., `contractId` and `architectureElementId` to form `ContractDeliverable.name`).
    *   **Hardcoding**: Setting fixed values for certain fields (e.g., `CurrentProfile`, `ClassName`, `Property`).
    *   **ID Generation/Referencing**: Using internal `$iteration` as a temporary ID for new objects, and external IDs (`fundingId`, `contractDeliverableId`) as `RefStr` for existing objects to be updated.
    *   **Relational Mapping**: Creating explicit `Relations` documents to define how different entities in Alfabet are linked.

For TypeScript porting, these nested document structures will translate directly to interfaces or types, and the mapping logic within `MAP` and `LOOP` blocks will translate to object transformation functions and array processing (e.g., `map`, `filter`, `reduce`).

## Error Handling and Response Codes

The service employs a structured error handling strategy to provide consistent feedback.

*   **Error Scenarios Covered**:
    *   **Missing Required Input**: If `systemId` or the main `Budgets` / `Contracts` lists are missing from the `_generatedInput`, the service explicitly exits with a `FAILURE` signal and a `400 Bad Request` message.
    *   **Missing GUID for Update**: If `Budget/fundingId` is null during a budget update operation, `budgetUpdate:mapBudget` specifically exits with a `FAILURE` signal and a "System Budget Connection GUID is Null" message.
    *   **Database Operation Failure**: If any of the underlying stored procedure calls return a non-zero `@RETURN_VALUE`, it signifies a database error.
    *   **Partial/No Deletion**: For delete operations, if the number of successfully deleted items (`count`) is less than the expected number (`expectedCount`), `mapDeleteResponse` will generate a `400 Bad Request` with an appropriate message. If no items are found for deletion (count is 0), it also results in a `400 Bad Request`.
    *   **Unhandled Exceptions**: Any other unexpected errors or exceptions that occur within the `TRY` block are caught by the `CATCH` block.

*   **HTTP Response Codes Used**:
    *   **200 OK (Success)**: Returned when all requested operations (adds, updates, deletes) complete successfully. The `_generatedResponse/result` will be "success".
    *   **400 Bad Request**: Returned for client-side errors, such as missing required input data, or if specific records for update/delete cannot be found or are partially processed.
    *   **401 Unauthorized**: While not explicitly set in the provided `flow.xml`, this code is often managed by API Gateway or security policies upstream or within `cms.eadg.utils.api:handleError` if an authentication failure occurs.
    *   **404 Not Found**: For delete operations, if no objects are found corresponding to the provided IDs, the `mapDeleteResponse` utility sets a 400 (Bad Request) but the message "Object(s) could not be found" is typical of a 404 scenario.
    *   **500 Internal Server Error**: The default response for any unhandled server-side errors or unexpected exceptions. This is handled generically by `cms.eadg.utils.api:handleError`.

*   **Error Message Formats**:
    *   Success messages typically provide a concise confirmation, e.g., "Budget and contracts updated successfully" or "X object(s) successfully deleted".
    *   Error messages provide more detail about the specific issue, e.g., "Please provide a list of budgets.", "System Budget Connection GUID is Null", "Object(s) could not be found", or "One or more objects could not be deleted. Please re-pull object list."

*   **Fallback Behaviors**: The `TRY-CATCH` block and the `cms.eadg.utils.api:handleError` service ensure that even in unforeseen error scenarios, a standardized error response is returned to the client, preventing the service from crashing or returning obscure system errors. The `handleError` service acts as a central error response formatter.