# Webmethods Service Explanation: CmsEadgCensusCoreApi pageBudgetAndContractsFind

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageBudgetAndContractsFind`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

## Service Overview

The `pageBudgetAndContractsFind` service is designed to retrieve consolidated budget and contract information associated with a specific system. Its primary business purpose is to provide a unified view of financial and contractual data for a given system, facilitating reporting and analysis.

The service accepts a single input parameter:

*   **`systemId` (string)**: This required parameter represents the unique identifier of the system for which budget and contract information is to be retrieved.

The expected output of the service is a JSON object (or XML, depending on content type negotiation) representing `PageBudgetAndContracts` document type. This object includes the `systemId` used in the query, a total `count` of both budgets and contracts found, and two arrays: `Budgets` (containing details of associated budgets) and `Contracts` (containing details of associated contracts).

A key validation rule is that the `systemId` input parameter must be provided. If it is missing, the service immediately returns a "Bad Request" error. The service also includes a comprehensive error handling mechanism to catch and respond to unexpected issues during its execution.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Flow services use a visual programming paradigm to define business logic. Understanding a few core concepts is crucial for interpreting these services:

*   **`SEQUENCE`**: Analogous to a block of code in traditional programming (e.g., `{ ... }` in Java/TypeScript). Steps inside a `SEQUENCE` execute in order. They can also define `TRY` or `CATCH` blocks for error handling.
*   **`BRANCH`**: Similar to a `switch` statement or an `if/else if/else` block. A `BRANCH` evaluates an expression (or the value of a specific field) and directs the flow to a specific sequence of steps (called "branches") based on the outcome. The `SWITCH` attribute specifies the field to evaluate. Branches can be named (`NAME` attribute) or implicitly handled by a `$null` or `$default` branch.
*   **`MAP`**: A fundamental element for data transformation. It allows you to move, transform, and manipulate data within the service's pipeline (the in-memory data structure representing variables).
    *   **`MAPTARGET` and `MAPSOURCE`**: Define the structure of the data pipeline before and after the map step, helping to visualize data flow.
    *   **`MAPCOPY`**: Copies data from a source field to a target field. This is like assigning a variable (`target = source`).
    *   **`MAPSET`**: Sets a literal value or an expression result to a target field. This is like initializing a variable (`target = "someValue"`).
    *   **`MAPDELETE`**: Removes a field from the pipeline. This is useful for cleaning up intermediate data and optimizing memory usage.
*   **`INVOKE`**: Represents a call to another Webmethods service or an adapter service (which interacts with external systems like databases). This is similar to calling a function or method in traditional programming.
    *   **`VALIDATE-IN` and `VALIDATE-OUT`**: Define how input and output data structures are validated against their definitions. `$none` means no validation.
    *   **`MAP MODE="INPUT"` and `MAP MODE="OUTPUT"`**: Nested `MAP` elements within an `INVOKE` step define how data is mapped from the current service's pipeline to the invoked service's input, and from the invoked service's output back to the current service's pipeline, respectively.
*   **Error Handling (`TRY`/`CATCH` blocks)**: Webmethods flow services support structured error handling using `SEQUENCE` elements configured as `FORM="TRY"` and `FORM="CATCH"`. If an error occurs within a `TRY` block, control immediately transfers to the corresponding `CATCH` block. This is analogous to `try { ... } catch (e) { ... }` blocks in many programming languages.
*   **`LOOP`**: Iterates over an array of documents (records). This is similar to a `for-each` loop. `IN-ARRAY` specifies the input array, and `OUT-ARRAY` specifies the target array where processed items are placed.

## Database Interactions

This Webmethods service primarily interacts with a Microsoft SQL Server database named `Sparx_Support` via JDBC adapters. The connection details indicate it connects to `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` on port `1433` using the user `sparx_dbuser`. It operates in `NO_TRANSACTION` mode, meaning each database operation is auto-committed.

The service itself does not directly embed SQL queries; instead, it invokes pre-configured "adapter services" that encapsulate the SQL logic. These adapters fetch data from specific tables or views.

The following SQL **tables** and **views** are utilized by the underlying adapter services:

*   `CEDAR_API.Sparx_System_Contract_Full_Tbl` (Table)
*   `dbo.Sparx_Contract` (View)
*   `dbo.Sparx_System_BudgetProject` (View)
*   `dbo.Sparx_BudgetProject` (View)

There are no explicit stored procedures called directly by these adapters; they all appear to be `SELECT` operations.

Data mapping between service inputs and database parameters occurs within the `MAP MODE="INPUT"` sections of the `INVOKE` statements for the adapter services:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetails`**:
    *   SQL: Selects from `dbo.Sparx_System_BudgetProject`.
    *   Parameters: `t1."Sparx System GUID"`, `t1."OFM Project ID"`, `t1."Budget ProjectName"`, `t1."System ID"`.
    *   These are mapped from `systemId`, `projectId`, `projectTitle` input parameters of `budgetFind` service.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetailsAll`**:
    *   SQL: Selects all from `dbo.Sparx_System_BudgetProject` with no WHERE clause.
    *   No input parameters.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetailsIdsOnly`**:
    *   SQL: Selects from `dbo.Sparx_BudgetProject`.
    *   Parameters: `t1."Budget Project Name"`, `t1."Project ID"`.
    *   These are mapped from `projectTitle` and `projectId` input parameters of `budgetFind` service.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContracts`**:
    *   SQL: Selects from `CEDAR_API.Sparx_System_Contract_Full_Tbl`.
    *   Parameter: `t1."Sparx System GUID"`.
    *   This is mapped from `systemId` input parameter of `contractFind` service.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsAll`**:
    *   SQL: Selects all from `CEDAR_API.Sparx_System_Contract_Full_Tbl` with no WHERE clause.
    *   No input parameters.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsByKeywords`**:
    *   SQL: Selects from `dbo.Sparx_Contract`.
    *   Parameter: `t1."Contract Name" LIKE ?`.
    *   This is mapped from `keyword` input parameter of `contractFind` service after being formatted with wildcard characters.

## External API Interactions

Based on the provided Webmethods files, the `pageBudgetAndContractsFind` service does not directly interact with any external APIs. Its data retrieval is confined to internal database queries managed through JDBC adapters. The references to `cms.eadg.alfabet.api.v01.resources.objects.byReport.docs:ObjectByReportResponse` and `cms.eadg.easi.api.v01.resources.system.docs.types:SystemDetail` appear to be document type definitions from other packages, but are not invoked as external API calls within the provided flow.

## Main Service Flow

The main service, `pageBudgetAndContractsFind`, orchestrates the retrieval and aggregation of budget and contract data. The flow is structured within a `TRY` block to ensure robust error handling.

1.  **Input Validation**:
    *   The service first checks if the `systemId` input is provided using a `BRANCH` statement.
    *   If `systemId` is null, a "400 Bad Request" response is configured using `MAPSET` operations, including a specific error message "System ID must be provided". The service then `EXIT`s with a `FAILURE` signal.

2.  **Budget Data Retrieval**:
    *   If `systemId` is valid, the service `INVOKE`s the `cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetFind` service. This dependency service is responsible for querying the database for budget information related to the provided `systemId` (or other criteria, though `pageBudgetAndContractsFind` only passes `systemId`).
    *   The output of `budgetFind` (`_generatedResponse` of type `BudgetFindResponse`) is then mapped to `budgetsFindResponse` in the current service's pipeline. Intermediate variables like `_generatedResponse` from the invoked service and `SetResponse` (if any from `budgetFind`'s internal error handling) are cleaned up using `MAPDELETE`.

3.  **Contract Data Retrieval**:
    *   Following the budget retrieval, the service `INVOKE`s the `cms.eadg.cedar.core.api.v2.cedarCore_.services:contractFind` service. This dependency queries the database for contract information.
    *   Similarly, the output of `contractFind` (`_generatedResponse` of type `ContractFindResponse`) is mapped to `contractsFindResponse` in the pipeline, and intermediate variables are deleted.

4.  **Budget Data Processing and Aggregation**:
    *   A `BRANCH` statement checks if `budgetsFindResponse` exists (i.e., if any budgets were found).
        *   If `budgetsFindResponse` is `$null`: The `_generatedResponse` for the main service (of type `PageBudgetAndContracts`) has its `systemId` set from the input, and its `count` is initialized to `0`. The `budgetsFindResponse` itself is then deleted.
        *   If `budgetsFindResponse` is **not** null (`$default` branch):
            *   The `systemId` is copied to the main service's `_generatedResponse`.
            *   The `Budgets` array from `budgetsFindResponse` is copied to the `Budgets` array within the main service's `_generatedResponse`.
            *   The original `budgetsFindResponse` is deleted.
            *   Crucially, the `count` from `budgetsFindResponse` is copied to `_generatedResponse/count`.

5.  **Contract Data Processing and Aggregation**:
    *   Another `BRANCH` statement checks if `contractsFindResponse` exists.
        *   If `contractsFindResponse` is `$null`: The `contractsFindResponse` and input `systemId` (which might have been left on the pipeline, though already copied to output) are deleted.
        *   If `contractsFindResponse` is **not** null (`$default` branch):
            *   The `Contracts` array from `contractsFindResponse` is copied to the `Contracts` array within the main service's `_generatedResponse`.
            *   The original `contractsFindResponse` and input `systemId` are deleted.
            *   A `pub.math:addObjects` service is invoked to sum the `count` from `_generatedResponse` (which holds the budget count) and `contractsFindResponse/count`. The result updates the `_generatedResponse/count`, providing a total count of budgets and contracts.

6.  **Response Generation**:
    *   A final `MAP` step is included, though it contains no operations, indicating a point where further mapping or cleanup might occur if needed.

7.  **Error Scenarios and Handling (`CATCH` block)**:
    *   If any step within the main `TRY` block fails, control transfers to the `CATCH` block.
    *   `pub.flow:getLastError` is invoked to retrieve detailed information about the error that occurred.
    *   `cms.eadg.utils.api:handleError` is then invoked. This is a generic error handling service that formats the error into a standardized API response (e.g., HTTP 500 Internal Server Error) and prepares it for the client. The `lastError` information is passed to this handler.

## Dependency Service Flows

The `pageBudgetAndContractsFind` service relies on two main dependency services within the `cms.eadg.cedar.core.api.v2.cedarCore_` package: `budgetFind` and `contractFind`. Both implement robust data retrieval and mapping logic.

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.services:budgetFind`**:
    *   **Purpose**: This service is designed to find budget details based on various criteria such as `systemId`, `projectTitle`, or `projectId`. It can also return only IDs if requested (`idsOnly` flag).
    *   **Integration with Main Flow**: `pageBudgetAndContractsFind` invokes this service early in its flow, passing the `systemId` to fetch relevant budget data.
    *   **Input/Output Contracts**:
        *   **Inputs**: `systemId`, `projectTitle`, `projectId`, `idsOnly` (boolean). It also implicitly uses a global variable `cedar.api.budget.year` for the `FiscalYear` field.
        *   **Output**: `_generatedResponse` of type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BudgetFindResponse`, containing `count` and an array of `Budgets`. It can also return 400, 401, or 500 error responses (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`).
    *   **Specialized Processing**:
        *   Initializes `ReportArgs/year` from a global variable.
        *   Uses `pub.string:concat` within `cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetFind:toSqlLike` to add '%' wildcards to `projectTitle` and `projectId` for SQL `LIKE` queries if `idsOnly` is true.
        *   Branches its data retrieval based on the `idsOnly` flag and the presence of `projectId`, `projectTitle`, or `systemId`. It calls different JDBC adapters accordingly:
            *   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetailsIdsOnly` (for `idsOnly` requests).
            *   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetails` (for specific searches by `systemId`, `projectId`, or `projectTitle`).
            *   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:getBudgetDetailsAll` (if no specific search criteria are provided).
        *   Maps the raw adapter results into the structured `Budget` document type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Budget`) within a `LOOP`.
        *   Includes its own error handling, calling `cms.eadg.utils.api:handleError`.

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.services:contractFind`**:
    *   **Purpose**: This service is responsible for finding contract details based on criteria like `systemId` or a generic `keyword`.
    *   **Integration with Main Flow**: `pageBudgetAndContractsFind` invokes this service after `budgetFind`, passing the `systemId` to gather relevant contract data.
    *   **Input/Output Contracts**:
        *   **Inputs**: `systemId`, `keyword`, `POPStartDate`, `POPEndDate`, `contractName`.
        *   **Output**: `_generatedResponse` of type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractFindResponse`, containing `count` and an array of `Contracts`. It can also return 400, 401, or 500 error responses.
    *   **Specialized Processing**:
        *   Initializes an empty `Contracts` array.
        *   Branches data retrieval based on whether a `keyword` or `systemId` is provided.
        *   If `keyword` is present, it's wrapped with '%' for a SQL LIKE search before calling `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsByKeywords`.
        *   If no `keyword` but a `systemId` is present, it calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContracts`.
        *   If neither `keyword` nor `systemId` is present, it calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsAll`.
        *   Maps the raw adapter results into the structured `Contract` document type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract`) within a `LOOP`.
        *   Includes its own error handling, calling `cms.eadg.utils.api:handleError`.

## Data Structures and Types

The service uses several Webmethods "Document Types" which are schema definitions for structured data, analogous to TypeScript interfaces or classes.

*   **Input Model**:
    *   `systemId` (string): The sole direct input parameter for the `pageBudgetAndContractsFind` service. It is a required field.
*   **Output Model**:
    *   `_generatedResponse` (type: `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageBudgetAndContracts`): This is the main output structure.
        *   `systemId` (string): Copied from the input.
        *   `pageName` (string): Not explicitly populated by this service.
        *   `count` (BigInteger): Total number of budget and contract entries found.
        *   `Budgets` (array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Budget`):
            *   `id` (string): `Sparx BudgetProject GUID` from DB.
            *   `projectId` (string): `OFM Project ID` or `Project ID` from DB.
            *   `projectTitle` (string): `Budget ProjectName` from DB.
            *   `fundingId` (string): `Connection GUID` from DB.
            *   `funding` (string): `Funding` from DB.
            *   `FiscalYear` (string): Value from Webmethods global variable `cedar.api.budget.year`.
            *   `deleted` (Boolean), `updated` (Boolean): Not explicitly mapped in `budgetFind`'s loop, implies they would be null or default unless source adapter provides them.
        *   `Contracts` (array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Contract`):
            *   `id` (string): `Sparx Contract GUID` from DB.
            *   `contractDeliverableId` (string): `Connection GUID` from DB.
            *   `parentAwardId` (string): `Contract Number` from DB.
            *   `awardId` (string): `Order Number` from DB.
            *   `contractADO` (string): `IsDeliveryOrg` from DB.
            *   `description` (string): `Contract Full Name` from DB.
            *   `systemId` (string): `Sparx System GUID` from DB.
            *   `POPStartDate` (string): `POP Start Date` from DB.
            *   `POPEndDate` (string): `POP End Date` from DB.
            *   `contractName` (string): `Contract Name` from DB.
            *   `deleted` (Boolean), `updated` (Boolean): Not explicitly mapped in `contractFind`'s loop, implies they would be null or default unless source adapter provides them.

The primary data transformation logic involves mapping database column names (often containing spaces and special characters) to camelCase or similar conventions for the output JSON properties. This is achieved through `MAPCOPY` operations in the various `INVOKE` steps and within `LOOP`s for array processing. For example, `getContractsOutput.results."Sparx Contract GUID"` maps to `Contracts.id`.

## Error Handling and Response Codes

The service employs a structured error handling strategy to provide informative responses in case of failures.

*   **Direct Input Validation Error (HTTP 400 Bad Request)**:
    *   If the mandatory `systemId` input is missing, the service immediately sets the HTTP response code to `400` with the phrase "Bad Request". The response body's `result` field is "error" and the `message` array contains "System ID must be provided". This happens before any database or sub-service calls, ensuring fast feedback for invalid requests.
*   **General Exception Handling (HTTP 500 Internal Server Error)**:
    *   The entire main flow is wrapped in a `TRY` block. Any unhandled exception during the execution of the main service flow (including issues in invoked sub-services or database adapters) will trigger the `CATCH` block.
    *   Within the `CATCH` block, `pub.flow:getLastError` is called to capture details of the exception.
    *   This exception information is then passed to a generic error handling service, `cms.eadg.utils.api:handleError`.
    *   `cms.eadg.utils.api:handleError` (and its sub-service `cms.eadg.utils.api:setResponse`) is responsible for:
        *   Setting the HTTP response code to `500` ("Internal Server Error") by default, unless a more specific error response has already been populated in the `SetResponse` document (e.g., from a sub-service).
        *   Setting the response body's `result` field to "error" and its `message` array to the captured error details (`lastError/error`).
        *   Configuring the `Content-Type` header based on the requested `format` (e.g., `application/json` or `application/xml`) using `pub.flow:setResponse2`.
    *   This centralized error handling ensures consistent error response formats across multiple APIs.

In essence, the service prioritizes explicit validation for common request issues (like missing required parameters) and falls back to a generic internal server error for unexpected runtime exceptions, providing a clear distinction between client-side and server-side problems.