# Webmethods Service Explanation: CmsEadgCensusCoreApi pageSystemsListFind

This document provides a detailed explanation of the Webmethods service `pageSystemsListFind` within the `CmsEadgCensusCoreApi` package. This service is designed to retrieve a comprehensive list of systems that have participated in a "System Census." It aims to provide high-level information for each system, including contact points and various operational attributes.

The service accepts optional input parameters to filter the results. Upon successful execution, it returns a structured JSON (or XML) response containing the list of systems and a total count. In case of errors, it provides standardized error messages and appropriate HTTP status codes.

*   Package Name: `CmsEadgCensusCoreApi`
*   Service Name: `pageSystemsListFind`
*   Service Package Path: `CmsEadgCensusCoreApi/ns/cms/eadg/census/core/api/v02/systemCensus_`

## Service Overview

The `pageSystemsListFind` service has the business purpose of querying and returning data related to registered systems, specifically those captured during a "System Census." It serves as a read-only endpoint, providing an aggregated view of system information.

The service accepts the following input parameters:

*   `version`: An optional string that likely specifies a particular version of system data to retrieve. The service does not directly use this field but instead deletes it from the pipeline, suggesting it might be an unused or legacy input, or handled by a layer above this service.
*   `includeInSurvey`: An optional boolean object. When set to `true`, this parameter filters the results to include only systems eligible for the system census survey. This boolean is converted to a string before being passed to the underlying data retrieval mechanism.

The expected output is a structured response object named `_generatedResponse` conforming to the `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageSystemsList` document type. This output includes:

*   `pageName`: A static string value "SystemsList".
*   `count`: The total number of systems returned in the `SystemsList`.
*   `SystemsList`: An array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:SystemsList` records, each representing a system with its detailed attributes.

There are no direct side effects of this service, as its primary function is data retrieval. Key validation rules primarily involve the conversion of the `includeInSurvey` boolean input to a string format suitable for downstream processing. Explicit input validation steps beyond type conversion are not visible within this flow's direct configuration.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm called "Flow" to define service logic. Here are some key elements you'll encounter:

*   **SEQUENCE**: Analogous to a block of code in traditional programming (like `{...}` in JavaScript/TypeScript). Statements within a SEQUENCE execute in order from top to bottom. If any step within a SEQUENCE fails, the entire SEQUENCE fails, and control is typically transferred to an error-handling block. A `TRY` SEQUENCE indicates the start of a "try-catch" block, where subsequent steps are the "try" part, and a `CATCH` SEQUENCE is the "catch" part.
*   **BRANCH**: Similar to a `switch` statement in many programming languages. It evaluates a specified variable or expression (the `SWITCH` condition) and executes a specific path (SEQUENCE) based on the evaluated value. A `$null` branch handles cases where the switch variable is null or empty, and a `$default` branch handles all other cases not explicitly matched.
*   **MAP**: This is a powerful data transformation step. It allows you to define how data elements from the "MAPSOURCE" (the current pipeline, which holds all variables) are moved, copied, or transformed to the "MAPTARGET" (the desired output structure or new variables).
    *   **MAPSET**: Used to set a literal value to a variable in the pipeline, or to dynamically set a value based on a complex expression.
    *   **MAPCOPY**: Used to copy a value from one variable in the pipeline to another. This is equivalent to `target = source;`.
    *   **MAPDELETE**: Used to remove a variable from the pipeline. This is crucial for pipeline cleanup, ensuring that sensitive data or large intermediate results are not carried forward unnecessarily, optimizing memory usage.
*   **INVOKE**: This element calls another Webmethods service (a "flow service," "Java service," or "adapter service"). It's like calling a function or method in traditional programming. `VALIDATE-IN` and `VALIDATE-OUT` control whether the input and output documents of the invoked service are validated against their defined schemas.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flows use `SEQUENCE` elements with `FORM="TRY"` and `FORM="CATCH"` to implement try-catch error handling. If an error occurs within the `TRY` block, execution immediately jumps to the associated `CATCH` block. The `pub.flow:getLastError` service is commonly invoked within the `CATCH` block to retrieve details about the error, such as the error message and stack trace. This allows for centralized and standardized error response generation.

## Database Interactions

The core database interaction for this service is handled by an invoked adapter service:

*   **`cms.eadg.census.core.api.v02.systemCensus_.adapters.pageSystemsList:getSystemCensusList`**: This service is responsible for fetching system census data from the database. Based on common Webmethods adapter patterns and the service's name, it is highly likely to invoke a stored procedure or query a database view or table directly.

    *   **Database Operation**: Data retrieval (SELECT).
    *   **Database Connection**: The specific database connection is not detailed in the provided XML files but would be configured within the Webmethods Integration Server for the `CmsEadgCensusCoreApi` package, likely through a JDBC adapter connection.
    *   **SQL Queries or Stored Procedures**: The exact SQL or stored procedure name is not available in the provided `flow.xml` for `pageSystemsListFind` or its `node.ndf`. However, the service `getSystemCensusList` strongly implies a query that fetches a list of system records. Given the output structure, it's safe to assume a stored procedure or view like `GET_SYSTEM_CENSUS_LIST` or a similar naming convention is used.
    *   **Data Mapping between Service Inputs and Database Parameters**:
        *   The `includeInSurvey` boolean input is converted to a string `strIncludeInSurvey` using `cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString` before being passed to `getSystemCensusList`. This indicates that the underlying database query expects a string parameter (e.g., "true" or "false") for this filter.
        *   The `version` input is entirely removed from the pipeline before `getSystemCensusList` is called, meaning it is not passed to this specific database operation.

## External API Interactions

Based on the provided Webmethods files, this service does not directly interact with external third-party APIs (e.g., calling a REST endpoint outside of the immediate Webmethods ecosystem). All invoked services are internal to the `CmsEadg` and `pub` packages, which represent common utilities or other internal services within the application's domain.

For instance:

*   `cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString`: This is an internal utility service for data type conversion.
*   `cms.eadg.census.core.api.v02.systemCensus_.adapters.pageSystemsList:getSystemCensusList`: This is an adapter service, which serves as a wrapper for database interactions.
*   `cms.eadg.census.core.api.v02.systemCensus_.adapters.utils:obfuscateArtError`: Another internal utility for error handling.
*   `cms.eadg.utils.api:handleError` and `cms.eadg.utils.api:setResponse`: These are generic utility services for standardizing API responses and error messages across the application.
*   `pub.json:jsonStringToDocument`, `pub.list:sizeOfList`, `pub.xml:documentToXMLString`, `pub.flow:getLastError`, `pub.flow:setResponseCode`, `pub.flow:setResponse2`: These are built-in Webmethods services for common tasks like JSON/XML parsing/generation, list manipulation, and flow control.

Therefore, no direct external API calls are made from `pageSystemsListFind`.

## Main Service Flow

The `pageSystemsListFind` service execution follows a structured `TRY-CATCH` block, ensuring robust error handling.

1.  **Input Preparation (within TRY block - first MAP step)**:
    *   The service receives `version` and `includeInSurvey` as inputs.
    *   It invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString`. This service takes the `includeInSurvey` (boolean object) and converts it into a string `strIncludeInSurvey`.
    *   Immediately after, the original `includeInSurvey` and `version` variables are deleted from the pipeline using `MAPDELETE`. This cleans up the pipeline and ensures that only the `strIncludeInSurvey` (if `includeInSurvey` was provided) and other necessary variables are passed to subsequent steps.

2.  **Data Retrieval (INVOKE `getSystemCensusList`)**:
    *   The service then invokes `cms.eadg.census.core.api.v02.systemCensus_.adapters.pageSystemsList:getSystemCensusList`. This is the primary call to retrieve the system census data.
    *   This adapter service is expected to return two main outputs: `@RETURN_VALUE` (an integer, typically `0` for success) and `@Outputjson` (a string containing the raw JSON data fetched from the database).

3.  **Success Path (BRANCH on `@RETURN_VALUE` = 0)**:
    *   If `@RETURN_VALUE` is `0` (indicating successful data retrieval):
        *   **JSON to Document Conversion**: `pub.json:jsonStringToDocument` is invoked to parse the `@Outputjson` string into a structured Webmethods document (IData object). The parsed document is expected to contain a list of records under a `ResultSet` field.
        *   **Pipeline Cleanup**: Intermediate `jsonString`, `getStakeHolderSPOutput` (likely from a broader shared pipeline context), and `getSystemSummaryListSPOutput` are deleted to manage memory.
        *   **Count Calculation**: `pub.list:sizeOfList` is called, taking the `document/ResultSet` (the list of systems) as input to determine the total number of systems. The result is stored in the `size` variable.
        *   **Pipeline Cleanup**: The temporary `fromList` variable used by `sizeOfList` is deleted.
        *   **Response Mapping (final MAP)**:
            *   The `size` variable (string) is converted to a `java.lang.Long` using `cms.eadg.utils.math:toNumberIf` and mapped to the `_generatedResponse/count` field.
            *   The `pageName` field of `_generatedResponse` is statically set to "SystemsList".
            *   The `document/ResultSet` (the array of system records) is directly mapped to the `_generatedResponse/SystemsList` field, transforming the raw database output into the desired API response format.
            *   Further pipeline cleanup occurs, deleting `loop`, `size`, `document`, `getSystemCensusListOutput`, and `strIncludeInSurvey`.

4.  **Error Handling (CATCH block)**:
    *   If any error occurs during the execution of the `TRY` block, control is transferred to the `CATCH` block.
    *   **Get Last Error**: `pub.flow:getLastError` is invoked to capture detailed information about the error that occurred, storing it in the `lastError` variable.
    *   **Obfuscate Error**: `cms.eadg.census.core.api.v02.systemCensus_.adapters.utils:obfuscateArtError` is called. This utility is likely designed to sanitize or generalize internal error messages, preventing sensitive information from being exposed in the API response.
    *   **Handle Error**: `cms.eadg.utils.api:handleError` is invoked to standardize the error response. This service (explained in the "Dependency Service Flows" section) sets appropriate HTTP status codes and formats a generic error message based on the `lastError` information.
    *   **Pipeline Cleanup**: The `lastError` variable is deleted.

## Dependency Service Flows

The `pageSystemsListFind` service relies on several other Webmethods services, each serving a specific purpose:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString`**:
    *   **Purpose**: This utility service converts a boolean value (`true`/`false`) into its string representation ("true"/"false").
    *   **Integration**: Used early in the main service flow to prepare the `includeInSurvey` input for the database adapter, which expects a string.
    *   **Input/Output**: Takes a boolean and returns a string.

*   **`cms.eadg.census.core.api.v02.systemCensus_.adapters.pageSystemsList:getSystemCensusList`**:
    *   **Purpose**: This adapter service is the primary component for database interaction. It queries the underlying database to retrieve system census data.
    *   **Integration**: It is invoked after input preparation and its output (`@RETURN_VALUE` and `@Outputjson`) drives the subsequent branching logic and response generation in the main flow.
    *   **SQL Tables/Views/Stored Procedures**: While the specific SQL is not available in the provided files, given the service name and the output fields (see Data Mapping below), it is almost certainly executing a stored procedure or querying a view or table specifically designed for system census data. Possible names include `SystemCensusTable`, `SystemCensusView`, or `GET_SYSTEM_CENSUS_DATA_SP`.
    *   **Input/Output**: Receives `strIncludeInSurvey` as input. Outputs an integer `@RETURN_VALUE` (e.g., `0` for success) and a string `@Outputjson` containing the raw JSON result from the database query.

*   **`pub.json:jsonStringToDocument`**:
    *   **Purpose**: A built-in Webmethods service that parses a JSON string into a Webmethods IData document structure. This allows the flow to manipulate the data as structured fields rather than raw text.
    *   **Integration**: Used after `getSystemCensusList` successfully returns its JSON string output to convert it into a manipulable document.
    *   **Input/Output**: Takes a `jsonString` and outputs a `document` (IData object).

*   **`pub.list:sizeOfList`**:
    *   **Purpose**: A built-in Webmethods service to determine the number of elements in a list (array).
    *   **Integration**: Used to count the number of system records returned from the database, which is then mapped to the `count` field of the final response.
    *   **Input/Output**: Takes a `fromList` (object array) and outputs a `size` (string).

*   **`cms.eadg.utils.math:toNumberIf`**:
    *   **Purpose**: A utility service that converts a string representation of a number into a specified numeric object type (e.g., `java.lang.Long`).
    *   **Integration**: Used to convert the `size` (string) obtained from `pub.list:sizeOfList` into a `Long` before mapping it to the `count` field in the final response.
    *   **Input/Output**: Takes a string `num` and a `convertAs` type, outputs a numeric object `num`.

*   **`pub.flow:getLastError`**:
    *   **Purpose**: A built-in Webmethods service that retrieves the details of the last error that occurred in the current flow execution.
    *   **Integration**: Always called at the beginning of the `CATCH` block to capture error context.
    *   **Input/Output**: No input. Outputs `lastError` (of type `pub.event:exceptionInfo`), containing error message, stack trace, etc.

*   **`cms.eadg.census.core.api.v02.systemCensus_.adapters.utils:obfuscateArtError`**:
    *   **Purpose**: A custom utility service, likely designed to process raw error information (`lastError`) and generate a more user-friendly or less revealing error message for API consumers. This prevents internal system details from leaking.
    *   **Integration**: Called in the `CATCH` block immediately after `pub.flow:getLastError`.
    *   **Input/Output**: Takes `lastError` and may generate a `SetResponse` document (though not explicitly shown in this snippet, it's inferred from the subsequent `handleError` call).

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A centralized error handling service. It standardizes the format and content of error responses for the entire API suite.
    *   **Integration**: Called in the `CATCH` block. It determines the appropriate HTTP status code and constructs a standardized error message.
    *   **Flow**:
        *   It uses a `BRANCH` on `SetResponse` (an optional input).
        *   If `SetResponse` is null (the `$null` branch), it sets a default `responseCode` to "500", `responsePhrase` to "Internal Server Error", `result` to "error", and populates `message` from `lastError/error`. The `format` is set to "application/json".
        *   If `SetResponse` is provided (the `$default` branch), it directly uses the values from the input `SetResponse`.
        *   Finally, it invokes `cms.eadg.utils.api:setResponse` to finalize the HTTP response.
    *   **Input/Output**: Takes `SetResponse` (optional) and `lastError`. Its output is implicitly consumed by `setResponse`.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is responsible for formatting the final HTTP response body and setting HTTP response headers (status code and content type).
    *   **Integration**: Invoked by `cms.eadg.utils.api:handleError` for error responses, and implicitly by other successful services (though not directly shown in the `pageSystemsListFind` success path, it's a standard pattern).
    *   **Flow**:
        *   Maps input `SetResponse` to a `Response` document.
        *   Uses a `BRANCH` on `SetResponse/format` to determine output format:
            *   For `application/json`: Converts the `Response` document to a JSON string using `pub.json:documentToJSONString`.
            *   For `application/xml`: Wraps the `Response` document within a `ResponseRooted` document, then converts it to an XML string using `pub.xml:documentToXMLString`.
        *   Sets the HTTP response code and phrase using `pub.flow:setResponseCode`.
        *   Sets the HTTP response body and content type using `pub.flow:setResponse2`.
    *   **Input/Output**: Takes a `SetResponse` document (containing `responseCode`, `responsePhrase`, `result`, `message`, `format`). Its outputs are directly sent as the HTTP response.

*   **`pub.flow:setResponseCode`**: A built-in Webmethods service to set the HTTP status code and reason phrase for the HTTP response.
*   **`pub.flow:setResponse2`**: A built-in Webmethods service to write the actual response body and set the Content-Type header for the HTTP response.
*   **`pub.xml:documentToXMLString`**: A built-in Webmethods service to convert an IData document into an XML string.

## Data Structures and Types

The service heavily relies on predefined document types (schemas) to structure its inputs and outputs.

*   **Input Data Model**:
    *   The service's input signature (`sig_in` in `node.ndf`) defines:
        *   `version` (string, optional): Used for system versions.
        *   `includeInSurvey` (object of type `java.lang.Boolean`, optional): Indicates whether to include systems eligible for survey.

*   **Output Data Model (Success)**:
    *   The primary successful output (`sig_out` in `node.ndf`) is `_generatedResponse` of type `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageSystemsList`.
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:PageSystemsList` contains:
        *   `pageName` (string): Fixed value "SystemsList".
        *   `count` (object of type `java.math.BigInteger`): Total number of systems returned.
        *   `SystemsList` (array of `cms.eadg.census.core.api.v02.systemCensus_.docTypes:SystemsList` records): The actual list of system data.
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:SystemsList` defines the structure for each system record:
        *   `id` (string)
        *   `nextVersionId` (string, optional)
        *   `previousVersionId` (string, optional)
        *   `name` (string)
        *   `description` (string, optional)
        *   `version` (string, optional)
        *   `acronym` (string, optional)
        *   `state` (string, optional)
        *   `status` (string, optional)
        *   `businessOwnerOrg` (string, optional)
        *   `businessOwnerOrgComp` (string, optional)
        *   `systemMaintainerOrg` (string, optional)
        *   `systemMaintainerOrgComp` (string, optional)
        *   `qaReviewerAssignmentId` (string, optional)
        *   `qaReviewerFirstName` (string, optional)
        *   `qaReviewerLastName` (string, optional)
        *   `qaReviewerUserName` (string, optional)
        *   `daReviewerAssignmentId` (string, optional)
        *   `daReviewerFirstName` (string, optional)
        *   `daReviewerLastName` (string, optional)
        *   `daReviewerUserName` (string, optional)
        *   `censusStatus` (string, optional)
        *   `percentComplete` (object of type `java.math.BigInteger`, optional)

*   **Output Data Model (Error)**:
    *   In case of errors, the service can return `400`, `401`, or `500` HTTP responses. These responses conform to `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response`.
    *   `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response` contains:
        *   `result` (string, optional): e.g., "error", "success".
        *   `message` (array of strings, optional): Detailed error messages.

*   **Data Transformation Logic**:
    *   Boolean to string: `includeInSurvey` (boolean) to `strIncludeInSurvey` (string).
    *   JSON string to structured document: `@Outputjson` (string) to `document` (IData document with `ResultSet` array).
    *   List size to number: `size` (string) to `_generatedResponse/count` (`java.math.BigInteger`).
    *   Document to JSON/XML string for final response.

### Detailed Source Database Column to Output Object Properties Mapping

The `cms.eadg.census.core.api.v02.systemCensus_.docTypes:SystemsList` defines the output structure. Assuming the `getSystemCensusList` adapter directly maps database column names to these properties (which is a common pattern for adapters), the mapping is as follows:

*   `ID`: `id`
*   `NEXT_VERSION_ID`: `nextVersionId`
*   `PREVIOUS_VERSION_ID`: `previousVersionId`
*   `NAME`: `name`
*   `DESCRIPTION`: `description`
*   `VERSION`: `version`
*   `ACRONYM`: `acronym`
*   `STATE`: `state`
*   `STATUS`: `status`
*   `BUSINESS_OWNER_ORG`: `businessOwnerOrg`
*   `BUSINESS_OWNER_ORG_COMP`: `businessOwnerOrgComp`
*   `SYSTEM_MAINTAINER_ORG`: `systemMaintainerOrg`
*   `SYSTEM_MAINTAINER_ORG_COMP`: `systemMaintainerOrgComp`
*   `QA_REVIEWER_ASSIGNMENT_ID`: `qaReviewerAssignmentId`
*   `QA_REVIEWER_FIRST_NAME`: `qaReviewerFirstName`
*   `QA_REVIEWER_LAST_NAME`: `qaReviewerLastName`
*   `QA_REVIEWER_USER_NAME`: `qaReviewerUserName`
*   `DA_REVIEWER_ASSIGNMENT_ID`: `daReviewerAssignmentId`
*   `DA_REVIEWER_FIRST_NAME`: `daReviewerFirstName`
*   `DA_REVIEWER_LAST_NAME`: `daReviewerLastName`
*   `DA_REVIEWER_USER_NAME`: `daReviewerUserName`
*   `CENSUS_STATUS`: `censusStatus`
*   `PERCENT_COMPLETE`: `percentComplete`

Note: The database column names are inferred from common SQL naming conventions (uppercase, underscores) and typical mappings to camelCase/PascalCase JSON properties, given the output property names. The actual database column names are not explicitly present in the provided Webmethods XML files.

## Error Handling and Response Codes

The service employs a robust error handling strategy using a `TRY-CATCH` block.

*   **Error Scenarios**: Any unhandled exception during the `TRY` block execution will trigger the `CATCH` block. This could include issues with database connectivity, SQL execution, JSON parsing, or data mapping errors.
*   **Error Details Capture**: `pub.flow:getLastError` is the first step in the `CATCH` block, capturing the error details (message, stack trace) into the `lastError` document.
*   **Error Obfuscation/Standardization**:
    *   `cms.eadg.census.core.api.v02.systemCensus_.adapters.utils:obfuscateArtError` is called to process the raw error. This indicates a policy to prevent internal system or exception details from being exposed directly to the API consumer. The result of this service (which might be a transformed `SetResponse` document) is then passed to `handleError`.
    *   `cms.eadg.utils.api:handleError` centralizes the generation of API error responses. If no specific error response structure is passed to it (e.g., from `obfuscateArtError`), it defaults to an "Internal Server Error" (HTTP 500).
*   **HTTP Response Codes**:
    *   **200 OK**: Implied for successful responses when the data is returned as `_generatedResponse`.
    *   **500 Internal Server Error**: This is the default HTTP response code set by `cms.eadg.utils.api:handleError` if an unexpected error occurs and no specific error response is provided to it. The `responsePhrase` will be "Internal Server Error" and the `message` will typically contain the sanitized error from `lastError`.
    *   **400 Bad Request / 401 Unauthorized**: The `node.ndf` for `pageSystemsListFind` includes `400` and `401` in its output signature, suggesting that `cms.eadg.utils.api:handleError` (or a service it invokes) is capable of setting these specific codes if conditions for a bad request or unauthorized access are met (e.g., input validation failures or authentication issues, though not explicitly coded in *this* service's visible logic).
*   **Error Message Formats**: Error messages are formatted into a standard `cms.eadg.census.core.api.v02.systemCensus_.docTypes:Response` document. This document contains a `result` field (e.g., "error") and a `message` field (an array of strings) detailing the issue. This response document is then converted to either JSON or XML based on the `format` specified by the `setResponse` utility.
*   **Fallback Behaviors**: The `TRY-CATCH` block ensures that even if an unexpected error occurs, a structured error response is always returned to the client, preventing the service from crashing or returning an unformatted error. This is a crucial pattern for API reliability.

When porting this service to TypeScript, the following patterns would translate differently:

*   **Flow Logic**: The visual flow service logic (SEQUENCE, BRANCH, MAP, INVOKE) would be translated into sequential function calls, conditional statements (`if/else` or `switch`), and object property assignments in TypeScript.
*   **Error Handling**: The `TRY-CATCH` blocks would directly map to TypeScript `try-catch` statements. The error objects (`lastError`) would become custom exception classes or structured error objects.
*   **Data Structures**: Webmethods Document Types (`.ndf` files) would become TypeScript interfaces or classes. `recref` (record reference) would typically map to nested or imported interfaces.
*   **Adapters**: Database adapter calls would become database access layer functions, potentially using an ORM or direct SQL queries. The JSON string output from the Webmethods adapter would be replaced by structured data from the TypeScript database client.
*   **Utilities**: Common `pub` and `cms.eadg.utils` services would be implemented as helper functions or modules in TypeScript, encapsulating logic like JSON parsing, type conversion, and standardized response formatting.