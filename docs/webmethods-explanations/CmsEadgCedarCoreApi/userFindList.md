# Webmethods Service Explanation: CmsEadgCedarCoreApi userFindList

This document provides a comprehensive explanation of the Webmethods service `userFindList` within the `CmsEadgCedarCoreApi` package. It is tailored for experienced software developers who are new to Webmethods, focusing on the service's purpose, operational flow, data interactions, and underlying Webmethods concepts.

The service's primary business purpose is to retrieve a list of user profiles from a "CEDAR application" (likely a master data system or an integration layer that aggregates user information from various sources). It allows filtering of users based on several criteria such as ID, username, first name, last name, phone number, and email address. The expected output is a structured JSON response containing the count of found users and a list of detailed user objects. There are key validation rules enforced on the input parameters to ensure proper data retrieval and prevent misuse.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `userFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `userFindList` service acts as an API endpoint for querying user information. It takes several input parameters to filter the user list and returns a structured response.

Input parameters:

*   `application` (string, required): Specifies the application the user belongs to. This is a mandatory filter.
*   `id` (string, optional): A user's unique identifier.
*   `userName` (string, optional): A user's username.
*   `firstName` (string, optional): A user's first name.
*   `lastName` (string, optional): A user's last name.
*   `phone` (string, optional): A user's phone number.
*   `email` (string, optional): A user's email address.

Expected outputs:

*   A JSON object representing a `UserFindResponse`, which includes:
    *   `count`: The total number of users found (a `BigInteger`).
    *   `Users`: A list of `User` objects, each containing: `id`, `application`, `userName`, `firstName`, `lastName`, `phone`, `email`, and `isDeleted` (boolean, though `isDeleted` is not mapped by this service).
*   In case of errors, a structured error response with an appropriate HTTP status code.

Key validation rules:

*   The `application` input parameter must be provided. If missing, a `400 Bad Request` error is returned.
*   At least one of the optional filter parameters (`id`, `userName`, `firstName`, `lastName`, `phone`, `email`) must be provided. If all are missing, a `400 Bad Request` error is returned.
*   Any provided filter parameter (excluding wildcards) must contain two or more characters. This helps prevent overly broad queries. If this rule is violated, a `400 Bad Request` error is returned.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods services are built using a visual programming language called "Flow" which represents business logic as a sequence of steps. Here are some key concepts encountered in this service:

*   **SEQUENCE**: Analogous to a block of code or a function body in traditional programming. Steps within a sequence are executed in order. A `SEQUENCE` can have an `EXIT-ON` attribute (e.g., `FAILURE`) which determines if the sequence should terminate prematurely if any step within it fails. A `FORM="TRY"` attribute indicates a try-catch block, where a subsequent `SEQUENCE` with `FORM="CATCH"` will handle exceptions.
*   **BRANCH**: Similar to a `switch` statement or `if-else if-else` structure. It evaluates an expression (specified by `SWITCH="/path/to/variable"`) and executes a specific `SEQUENCE` (or another flow step) based on the variable's value. `NAME` attributes on sequences within a branch define the matching values (e.g., `NAME="all"` or `NAME="$null"`). A `$default` sequence acts as the `else` block.
*   **MAP**: This element is crucial for data transformation within Webmethods services. It allows you to move, copy, set, or delete data fields within the service's current data pipeline (which is essentially a shared memory space for variables).
*   **INVOKE**: Used to call another Webmethods service, similar to calling a function or method in traditional programming. It specifies the service to call and often includes `MAP` steps nested within it to prepare inputs (`MAP MODE="INPUT"`) and process outputs (`MAP MODE="OUTPUT"`) for the invoked service.
*   **TRY/CATCH blocks**: Represented by `SEQUENCE` elements with `FORM="TRY"` and `FORM="CATCH"`. If an error occurs in the "TRY" block, execution immediately jumps to the "CATCH" block, allowing for centralized error handling. This is standard exception handling.
*   **MAPSET**: A specific type of `MAP` operation used to explicitly assign a static value or expression to a field in the pipeline.
*   **MAPCOPY**: A specific type of `MAP` operation used to copy the value of one field to another field in the pipeline.
*   **MAPDELETE**: A specific type of `MAP` operation used to remove a field from the pipeline. This is often used for "cleanup" to keep the pipeline tidy and prevent unnecessary data from being passed around.
*   **Input validation and branching logic**: The service extensively uses `BRANCH` and `INVOKE` (e.g., `validateInputs`) to enforce business rules and direct the flow based on input conditions. For example, it checks if required parameters are present and if optional parameters meet minimum length criteria after stripping wildcards.
*   **LOOP**: Analogous to a `for-each` loop in other languages. It iterates over elements in an array (`IN-ARRAY`) and executes nested steps for each element. It can optionally map the current element to a new array (`OUT-ARRAY`).

When porting to TypeScript, these Flow constructs would typically translate into sequential function calls, `if/switch` statements, explicit data object transformations (mapping fields from one interface to another), and try-catch blocks for error handling. The concept of the "pipeline" would be replaced by passing structured data objects (interfaces/types) between functions.

## Database Interactions

The `userFindList` service interacts with a SQL Server database, specifically using a JDBC adapter named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. This adapter connects to the `Sparx_Support` database on `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` via port `1433` using the `sparx_dbuser` credentials. The connection is configured for `NO_TRANSACTION` which means each database operation runs in its own transaction.

Two main database operations are performed:

1.  **Selecting from a View (`Sparx_Person`):**
    *   **Adapter Service:** `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Person:getPersonByID`
    *   **Database Table/View:** `dbo.Sparx_Person` (This is a VIEW, as indicated in `getPersonByID/node.ndf`).
    *   **Database Operation:** `SELECT`
    *   **SQL Query Details:** The adapter performs a `SELECT` query on the `Sparx_Person` view.
        *   **Selected Columns (from `t1` alias for `Sparx_Person`):**
            *   `"Person ID"`
            *   `"Sparx Person ID"`
            *   `"Sparx Person GUID"`
            *   `"User Name"`
            *   `"First Name"`
            *   `"Last Name"`
            *   `Email`
            *   `Phone`
        *   **WHERE Clause:** Filters records where `t1."Sparx Person GUID" = ?`.
    *   **Data Mapping (Service Inputs to Database Parameters):**
        *   `id` (service input): Mapped to the `?` parameter for `t1."Sparx Person GUID"` in the `WHERE` clause.
    *   **Data Mapping (Database Results to Service Output Fields):** The results of this query are returned as a list of records in `getPersonByIDOutput.results`. These are later mapped in the `mapSparxUser` service.

2.  **Executing a Stored Procedure (`SP_Get_UserList`):**
    *   **Adapter Service:** `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Users:getUsersSP`
    *   **Database Stored Procedure:** `SP_Get_UserList;1`
    *   **Database Operation:** `EXECUTE STORED PROCEDURE`
    *   **Stored Procedure Parameters:**
        *   `@username` (IN)
        *   `@firstname` (IN)
        *   `@lastname` (IN)
        *   `@phone` (IN)
        *   `@email` (IN)
        *   `@Outputjson` (INOUT): This parameter is expected to return the query results as a JSON string.
    *   **Data Mapping (Service Inputs to Database Parameters):**
        *   `userName` (service input): Mapped to `@username`.
        *   `firstName` (service input): Mapped to `@firstname`.
        *   `lastName` (service input): Mapped to `@lastname`.
        *   `phone` (service input): Mapped to `@phone`.
        *   `email` (service input): Mapped to `@email`.
    *   **Data Mapping (Database Results to Service Output Fields):** The `@Outputjson` parameter's string content is parsed into `getUsersSPOutputDocument` (a document type representing a list of objects, each containing `id` and `Values` with `username`, `firstName`, `lastName`, `phone`, `email`). The stored procedure's return value is available as `getUsersSPOutput.@RETURN_VALUE`.

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external REST or SOAP APIs. Its data retrieval is solely dependent on internal database interactions through JDBC adapters. The references to `cms.eadg.alfabet.api.v01.docs.types:Person`, `cms.eadg.alfabet.api.v01.resources.objects.byFilter.docs:ObjectByFilterResponse`, `cms.eadg.alfabet.api.v01.resources.objects.byRef.docs:ObjectByRefResponse`, and `cms.eadg.alfabet.api.v01.resources.objects.byReport.docs:ObjectByReportResponse` suggest that there might be other services within the Webmethods ecosystem that handle Alfabet API interactions. However, `userFindList` itself does not directly use them. They might be legacy artifacts in the pipeline definition or available for other services in the same package.

## Main Service Flow

The main service `userFindList` (`flow.xml`) defines the top-level orchestration:

1.  **Error Handling (TRY block):** The entire service logic is wrapped in a `SEQUENCE` with `FORM="TRY"`. This means any unhandled error within this sequence will be caught by the subsequent `CATCH` block.
2.  **Input Validation:** The first step is to `INVOKE` the `cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:validateInputs` service. This dependency service ensures that all mandatory inputs are present and that optional inputs adhere to specified length constraints (e.g., at least two non-wildcard characters for search parameters). If validation fails, `validateInputs` sets an error in the `SetResponse` document and exits, causing the main service's `TRY` block to fail.
3.  **Application-Specific Logic:** A `BRANCH` statement evaluates the `/application` input:
    *   **`all` or `alfabet` branches:** Both of these explicit branches `INVOKE` the `cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:findSparxUser` service. This means that regardless of whether the `application` is specified as "all" or "alfabet", the service proceeds with the same user lookup logic. This might indicate that "all" and "alfabet" effectively mean the same thing in the current implementation, or that "all" previously had a different lookup path.
    *   **`$default` branch:** If the `application` input is neither "all" nor "alfabet" (or is null, which is caught by `validateInputs`), this branch executes. It uses `MAPSET` operations to populate a `SetResponse` document with a `400 Bad Request` status and a message "Please specify a valid application." It then `EXIT`s the flow with a `FAILURE` signal, indicating an error.
4.  **Error Check from Sub-services:** After `findSparxUser` completes (or the `$default` application branch fails), another `BRANCH` evaluates the `/SetResponse` document.
    *   **`$null` branch:** This path is taken if no `SetResponse` document exists in the pipeline, implying that no error was explicitly set by the invoked `findSparxUser` service (or `validateInputs`). In this case, a `MAP` step performs a crucial data transformation: it deletes the intermediate `UserFindResponse` and `MAPCOPY` its contents to `_generatedResponse`. This renames the primary output document to its final expected name.
    *   **`$default` branch:** If a `SetResponse` document *is* present (meaning `findSparxUser` or `validateInputs` explicitly set an error), this branch executes. It immediately `EXIT`s the flow with a `FAILURE` signal, propagating the error that was already set.
5.  **Cleanup:** A `MAP` step performs cleanup by `MAPDELETE`ing various intermediate variables and input parameters from the pipeline (`SetResponse`, `application`, `id`, `userName`, etc.), leaving only the `_generatedResponse` for the final output.
6.  **Global Error Handling (CATCH block):** If any unhandled exception occurs during the `TRY` block, execution transfers to this `CATCH` block.
    *   `INVOKE` `pub.flow:getLastError` to retrieve details of the last error that occurred.
    *   `INVOKE` `cms.eadg.utils.api:handleError` to process the error. This generic error handling service will format the error response (usually as a `500 Internal Server Error`) and set appropriate HTTP response codes.

## Dependency Service Flows

The `userFindList` service relies on several key dependency services to perform its functions:

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:validateInputs`**
    *   **Purpose:** To pre-process and validate the incoming request parameters. This ensures that essential inputs are present and that optional filter parameters are not too short (to prevent large data retrievals).
    *   **Integration:** It's the very first logical step in the main service's `TRY` block. If validation fails, it sets an error response (`SetResponse`) and signals a failure, stopping the main service early.
    *   **Input/Output Contract:** Takes all input parameters of `userFindList` (`application`, `id`, `userName`, `firstName`, `lastName`, `phone`, `email`). On success, it produces no explicit output in the pipeline (other than keeping the inputs for subsequent steps). On failure, it populates the `SetResponse` document with error details.
    *   **Specialized Processing:**
        *   Checks if `application` is null, if so, sets a 400 error.
        *   Checks if *all* optional parameters are null, if so, sets a 400 error requiring at least one search parameter.
        *   For each non-null optional parameter, it removes any wildcard characters (`*`) and checks if the resulting string length is less than 2. If so, it sets a 400 error, enforcing a minimum search length. This step prevents potentially massive database queries triggered by single-character or wildcard-only inputs.

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:findSparxUser`**
    *   **Purpose:** This service is responsible for actually querying the user data from the database. It conditionally uses one of two JDBC adapters based on whether an `id` is provided.
    *   **Integration:** Called by the main service if initial input validation passes and the `application` is valid ("all" or "alfabet").
    *   **Input/Output Contract:** Takes `id`, `userName`, `firstName`, `lastName`, `phone`, `email` as input. Its primary output is the `UserFindResponse` document (or `SetResponse` if internal data retrieval fails).
    *   **Specialized Processing:**
        *   **If `id` is provided:** It calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Person:getPersonByID`. This adapter queries the `Sparx_Person` database view using the provided `id` (mapped to `"Person ID"`). The output of this query is available in `getPersonByIDOutput.results`.
        *   **If `id` is not provided:** It calls `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Users:getUsersSP`. This adapter executes the `SP_Get_UserList` stored procedure, passing `userName`, `firstName`, `lastName`, `phone`, and `email` as input parameters. This stored procedure returns a JSON string via its `@Outputjson` parameter, which is then converted into a Webmethods document type (`getUsersSPOutputDocument`) using `pub.json:jsonStringToDocument`.
        *   It then checks if any records were `Selected` (a flag from the adapters) or if the stored procedure returned a `0` (success code). If no data is found (0 records or `Selected` is false/0), it sets the `UserFindResponse` count to 0.
        *   Finally, it invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:mapSparxUser` to transform the raw database query results into the standardized `UserFindResponse` format.

3.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.userFindList:mapSparxUser`**
    *   **Purpose:** To map the raw data retrieved from the database (either from `getPersonByID` view or `getUsersSP` stored procedure JSON output) into the canonical `UserFindResponse` document type.
    *   **Integration:** Called by `findSparxUser` after data retrieval.
    *   **Input/Output Contract:** Takes either `getUsersSPOutputDocument` (parsed JSON from SP) or `getPersonByIDOutput` (direct query results) as input. It outputs the `UserFindResponse` document.
    *   **Specialized Processing:**
        *   It has conditional logic to handle results from either the stored procedure or the direct view query.
        *   For stored procedure results (`getUsersSPOutputDocument`), it loops through the `Objects` array and maps each item to a `User` object within the `UserFindResponse/Users` list. The `application` field for these users is hardcoded to "alfabet".
        *   For view query results (`getPersonByIDOutput`), it loops through the `results` array and maps each record to a `User` object. In this case, the `application` field is *not* explicitly mapped within this service, which could lead to it being null or inheriting a value from an earlier step in the pipeline (which it doesn't here, potentially making it null in the output). This is a potential area for inconsistency if `application` needs to be `alfabet` for this path as well.
        *   It also sets the `count` field of `UserFindResponse` based on the number of results from the successful data source.

4.  **`cms.eadg.utils.api:handleError`**
    *   **Purpose:** A generic utility service for standardizing error responses.
    *   **Integration:** Called by the main service's `CATCH` block to process exceptions, and also called by other services (like `validateInputs` or `findSparxUser`) if they detect and explicitly set an error.
    *   **Specialized Processing:**
        *   If no explicit error details (`SetResponse`) are provided, it defaults to a `500 Internal Server Error` and uses the system's `lastError` information for the error message.
        *   If error details are already in `SetResponse`, it simply passes them through.
        *   It then invokes `cms.eadg.utils.api:setResponse` to format the error message into JSON or XML and set the HTTP response code.

5.  **`cms.eadg.utils.api:setResponse`**
    *   **Purpose:** A utility service for setting the final HTTP response details.
    *   **Integration:** Called by `handleError` and potentially other services just before completion to finalize the response.
    *   **Specialized Processing:**
        *   Converts the internal `Response` document into either a JSON string (using `pub.json:documentToJSONString`) or an XML string (using `pub.xml:documentToXMLString`) based on the `format` specified in `SetResponse`.
        *   Invokes `pub.flow:setResponseCode` to set the HTTP status code and reason phrase.
        *   Invokes `pub.flow:setResponse2` to set the content type and the response body.

## Data Structures and Types

The service primarily uses and manipulates data through Webmethods "Document Types", which are strongly typed schema definitions. These are crucial for understanding the data model.

*   **Input Data Model (for `userFindList` service):**
    *   `application` (string, required): The application context (e.g., "alfabet").
    *   `id` (string, optional): User ID.
    *   `userName` (string, optional): User's login name.
    *   `firstName` (string, optional): User's first name.
    *   `lastName` (string, optional): User's last name.
    *   `phone` (string, optional): User's phone number.
    *   `email` (string, optional): User's email address.

*   **Output Data Model (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UserFindResponse`):**
    *   `count` (object, `java.math.BigInteger`): The total number of users found matching the criteria.
    *   `Users` (list of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:User`): A list of user detail objects.
        *   **`User` Document Type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:User`):**
            *   `id` (string): User's unique identifier.
            *   `application` (string): The application the user belongs to.
            *   `userName` (string, optional): User's username.
            *   `firstName` (string, optional): User's first name.
            *   `lastName` (string, optional): User's last name.
            *   `phone` (string, optional): User's phone number.
            *   `email` (string, optional): User's email address.
            *   `isDeleted` (object, `java.lang.Boolean`, optional): Indicates if the user is deleted (though not currently mapped by this service).

*   **Internal/Utility Document Types:**
    *   `cms.eadg.utils.api.docs:SetResponse`: Used internally to build and pass error/success response details between services before final formatting.
        *   `responseCode` (string): HTTP status code.
        *   `responsePhrase` (string): HTTP status phrase (e.g., "Bad Request").
        *   `result` (string): General result status (e.g., "success", "error").
        *   `message` (list of string): Detailed messages or error descriptions.
        *   `format` (string): Desired output format (e.g., "application/json", "application/xml").
    *   `cms.eadg.utils.api.docs:Response`: A simpler response structure, subset of `SetResponse`, used for the final response body.
        *   `result` (string)
        *   `message` (list of string)
    *   `pub.event:exceptionInfo`: Standard Webmethods document type for capturing exception details.

### Detailed Source Database Column to Output Object Property Mapping:

This section details how data from the source database (views and stored procedures) maps to the fields in the final output `User` object within the `UserFindResponse`.

When `id` is provided, leading to `getPersonByID` (using `dbo.Sparx_Person` VIEW):

*   `Sparx_Person."Sparx Person GUID"`: `id`
*   `Sparx_Person."User Name"`: `userName`
*   `Sparx_Person.Email`: `email`
*   `Sparx_Person.Phone`: `phone`
*   `Sparx_Person."Last Name"`: `lastName`
*   `Sparx_Person."First Name"`: `firstName`
*   *(Note: `application` is not explicitly mapped from the database in this path, which is a potential inconsistency. In the `mapSparxUser` service, for the `getPersonByIDOutput` branch, `application` is not set)*

When `id` is not provided, leading to `getUsersSP` (using `SP_Get_UserList` Stored Procedure):

*   `SP_Get_UserList` output JSON `id`: `id`
*   `SP_Get_UserList` output JSON `username`: `userName`
*   `SP_Get_UserList` output JSON `firstName`: `firstName`
*   `SP_Get_UserList` output JSON `lastName`: `lastName`
*   `SP_Get_UserList` output JSON `phone`: `phone`
*   `SP_Get_UserList` output JSON `email`: `email`
*   *(Note: `application` is hardcoded to "alfabet" for these results within the `mapSparxUser` service)*

## Error Handling and Response Codes

The service employs a robust error handling strategy, primarily centralized in its main flow's `TRY-CATCH` block and leveraging shared utility services.

*   **Validation Errors (400 Bad Request):**
    *   **Scenario:** Missing `application` parameter.
    *   **Scenario:** All optional filter parameters (`id`, `userName`, `firstName`, `lastName`, `phone`, `email`) are missing.
    *   **Scenario:** Any provided filter parameter (after stripping wildcards) has fewer than two characters.
    *   **Handling:** The `validateInputs` service detects these issues. It sets `SetResponse` with `responseCode="400"`, `responsePhrase="Bad Request"`, `result="error"`, and a specific `message`. It then exits with a `FAILURE` signal, which is caught by the main service's flow, propagating this structured error response.

*   **Internal Server Errors (500 Internal Server Error):**
    *   **Scenario:** Any unexpected exception occurs within the `TRY` block of the main service flow (e.g., a database connection issue, an unhandled null pointer during data processing, or an issue within an invoked service that doesn't explicitly set its own `SetResponse` error).
    *   **Handling:** The main service's `CATCH` block captures these exceptions. It uses `pub.flow:getLastError` to get generic exception details and then invokes `cms.eadg.utils.api:handleError`.
    *   The `handleError` service, if it doesn't find a pre-set `SetResponse` (meaning it's a truly unexpected error), sets `responseCode="500"`, `responsePhrase="Internal Server Error"`, `result="error"`, and uses the caught exception's message as the `message`.
    *   Finally, `cms.eadg.utils.api:setResponse` (a sub-service of `handleError`) is invoked to actually set the HTTP status code in the response header and serialize the `Response` document (with error details) into JSON or XML as the response body.

*   **HTTP Response Codes:**
    *   `200 OK`: Implicitly set upon successful execution where no explicit error is returned. The `setResponse` utility service will set this if `responseCode` is not explicitly set to something else.
    *   `400 Bad Request`: Used for various input validation failures as described above.
    *   `500 Internal Server Error`: Used for unexpected system errors or unhandled exceptions.

The error message format typically includes a `result` field (`"error"`), and a `message` array detailing the specific issues, along with the HTTP status code and phrase. This consistent error structure is beneficial for API consumers and simplifies client-side error handling.