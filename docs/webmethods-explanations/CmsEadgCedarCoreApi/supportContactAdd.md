# Webmethods Service Explanation: CmsEadgCedarCoreApi supportContactAdd

This document provides a comprehensive explanation of the Webmethods service `supportContactAdd`, designed for experienced software developers who are new to the Webmethods platform. The primary focus is to detail its functionality, underlying Webmethods concepts, database interactions, and data mappings to aid in porting the API to TypeScript.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `supportContactAdd`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `supportContactAdd` service is responsible for adding new support contact records or updating existing ones within the "Alfabet" system (likely an Enterprise Architecture or IT Asset Management platform) and its associated database. It acts as an integration point, taking contact information and either creating a new entry with a generated ID or updating an existing entry identified by a provided ID.

The service accepts a list of `SupportContact` objects as input. For each contact, it determines whether to perform an "add" or "update" operation based on the presence of a `SupportContact.id` field. If `id` is not provided, a new support contact is created in Alfabet, and its details are then persisted to a local database. If `id` is provided, the existing support contact's details are updated in the local database.

Input parameters consist of an array of `SupportContact` objects, each potentially containing `id`, `application`, `name`, `title`, `url`, `phone`, and `email` fields. The service's expected output is a `Response` object, indicating success or failure. On successful additions, the `message` field of the response will contain a list of the GUIDs (Globally Unique Identifiers) for the newly created support contacts. In case of errors, the `message` field will contain descriptive error messages, and appropriate HTTP response codes (e.g., 500 for internal server errors) will be set.

Key validation rules include a check to ensure that the input `SupportContacts` list is not empty or null. Individual `SupportContact` records are processed sequentially, and errors at any stage of processing a single record will halt the entire service execution for the batch.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm called "Flow Services." These services are constructed from a sequence of predefined steps (nodes) that execute in a specific order. Here are explanations of the key elements seen in this service:

*   **SEQUENCE**: Analogous to a block of code in traditional programming, a `SEQUENCE` executes its child nodes in the order they appear. The `EXIT-ON="FAILURE"` attribute indicates that if any step within the sequence encounters an error, the entire sequence will terminate prematurely. The `FORM="TRY"` attribute signifies the beginning of a try-block for error handling, similar to a `try { ... }` block in languages like Java or C#.
*   **BRANCH**: This element functions as a `switch` statement. It evaluates a specified input field and directs the flow to a particular sequence of steps (child nodes) based on the field's value. Special keywords like `$null` (for null or empty values) and `$default` (for any other value not explicitly matched) provide flexible branching logic.
*   **MAP**: A `MAP` node is used for data transformation and manipulation within the service's pipeline (the in-memory data structure that holds all input, output, and intermediate variables).
    *   **MAPSET**: This operation assigns a literal, hardcoded value to a specific field in the pipeline.
    *   **MAPCOPY**: This operation copies the value from one field in the pipeline to another. It can also include a `CONDITION` attribute, allowing the copy to occur only if a specified condition (e.g., field is not null) is met.
    *   **MAPDELETE**: This operation removes a field from the pipeline. This is crucial for pipeline management, preventing unnecessary data from persisting in memory and often for security by removing sensitive information.
*   **INVOKE**: An `INVOKE` node calls another callable service or function. This can be another Webmethods Flow Service, an Adapter Service (for database or external system integration), or a built-in service (like `pub.flow` services).
*   **Error Handling (TRY/CATCH blocks)**: Webmethods Flow Services support structured error handling using `TRY` and `CATCH` sequences. If an error occurs within a `TRY` block, control is transferred to the associated `CATCH` block.
    *   `pub.flow:getLastError`: This is a built-in Webmethods service that retrieves detailed information about the last error that occurred in the current flow execution. This information typically includes the error message, type, and stack trace.
    *   `cms.eadg.utils.api:handleError`: This is a custom utility service (a dependency) designed to standardize error responses. It takes the error information (often from `pub.flow:getLastError`), sets a generic HTTP status code (e.g., 500 Internal Server Error), and formats an error message for the service's output.
    *   `pub.flow:setResponseCode`: A built-in service used to set the HTTP status code for the response.
    *   `pub.flow:setResponse2`: A built-in service used to set the HTTP response body (content).

## Database Interactions

This service primarily interacts with a Microsoft SQL Server database named `Sparx_Support` via JDBC adapter services. These adapter services encapsulate calls to specific stored procedures within the database.

*   **Database Connection Details:**
    *   Connection Name: `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`
    *   Database Server: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   Database Port: `1433`
    *   Database Name: `Sparx_Support`
    *   Database User: `sparx_dbuser`
    *   Transaction Type: `NO_TRANSACTION` (indicating that individual stored procedure calls are not part of a larger, coordinated transaction managed by the Integration Server).

*   **SQL Stored Procedures Called:**

    1.  **`SP_Insert_SupportContact`**
        *   Used by: `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContact`
        *   Purpose: Inserts new support contact details into the database.
        *   Input Parameters and their Data Mapping from service input:
            *   `@ContactName` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/name`
            *   `@ContactEmail` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/email`
            *   `@ContactPhone` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/phone`
            *   `@ContactTitle` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/title`
            *   `@URLLink` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/url`
            *   `@GUID` (NVARCHAR): Mapped from `newSupportContactGuid` (this GUID is generated by the `addResource` external API call).
        *   Output: `@RETURN_VALUE` (INTEGER) – indicates the success or failure of the stored procedure execution (0 for success, non-zero for failure).

    2.  **`SP_Insert_SystemSupportContact_Reln`**
        *   Used by: `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContactRelationSP`
        *   Purpose: Establishes a relationship (connector entry) between a support contact and a system in the database.
        *   Schema: `dbo`
        *   Input Parameters and their Data Mapping from service input:
            *   `@systemId` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/application`
            *   `@SupportContactId` (NVARCHAR): Mapped from `newSupportContactGuid`
            *   `@Order` (NVARCHAR): This parameter is part of the stored procedure signature but no mapping is provided in the `flow.xml`. It implies it might be an optional parameter, or a default value is handled by the stored procedure itself, or it's an oversight.
        *   Output: `@RETURN_VALUE` (INTEGER) – indicates the success or failure of the stored procedure execution (0 for success, non-zero for failure).

    3.  **`SP_Update_SupportContact`**
        *   Used by: `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:updateSupportContactSP`
        *   Purpose: Updates existing support contact details in the database.
        *   Input Parameters and their Data Mapping from service input:
            *   `@ContactName` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/name`
            *   `@ContactEmail` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/email`
            *   `@ContactPhone` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/phone`
            *   `@ContactTitle` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/title`
            *   `@URLLink` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/url`
            *   `@GUID` (NVARCHAR): Mapped from `_generatedInput/SupportContacts/id` (the existing ID).
        *   Output: `@RETURN_VALUE` (INTEGER) – indicates the success or failure of the stored procedure execution (0 for success, non-zero for failure).

The direct database table names are not explicitly defined in the provided adapter NDF files; instead, the interaction is solely through the invocation of these stored procedures. These procedures would internally interact with tables such as `SupportContact` and `SystemSupportContact_Reln`.

## External API Interactions

The service interacts with an external API managed by the `cms.eadg.sparx.api.services` package, specifically `addResource` service, which is assumed to be the interface to the "Alfabet" system for creating new "resources" (in this case, "Support Contacts").

*   **External Service Called:** `cms.eadg.sparx.api.services:addResource`
*   **Purpose:** To create a new "Support Contact" object in the Alfabet system when a `SupportContact.id` is not provided in the input.
*   **Request Format / Data Mapping:**
    *   The `AddResourceRequest` document type is used.
    *   `AddResourceRequest/rdf:RDF/oslc_am:Resource/ss:stereotype/ss:stereotypename/ss:name` is hardcoded to "Support Contact". This defines the type of resource being added in Alfabet.
    *   `AddResourceRequest/rdf:RDF/oslc_am:Resource/dcterms:title` is mapped conditionally: it takes the value from `_generatedInput/SupportContacts/name` if available, otherwise it falls back to `_generatedInput/SupportContacts/title`. This acts as the primary display name for the resource in Alfabet.
*   **Response Format / Authentication:** The `addResource` service returns a `SetResponse` document type. The `message` field from this response (specifically `SetResponse/message[0]`) is critical as it contains the newly generated GUID for the resource created in Alfabet. Authentication mechanisms for `cms.eadg.sparx.api.services:addResource` are not visible in the provided files, but it would typically involve API keys, OAuth, or other secure methods managed by the `sparx.api.services` package itself.
*   **Error Handling for External Calls:** If the `addResource` service does not return a `responseCode` of "201" (HTTP Created status), the flow redirects to an error handling sequence. This sequence maps the details from the `SetResponse` (which would contain the error from the `addResource` call) to the main service's output and exits with a `FAILURE` signal.

## Main Service Flow

The main service flow is defined in `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/supportContactAdd/flow.xml`. It outlines the sequential steps, conditional logic, and error handling for processing support contact additions or updates.

1.  **Initialization:** The `loopCount` variable is initialized to "0". This variable appears to be used for array indexing.
2.  **Input Validation:** A `BRANCH` step checks if the `_generatedInput/SupportContacts` field is `$null`. If it is, the service immediately exits with a `FAILURE` signal, indicating that a list of support contacts must be provided.
3.  **Process Each Support Contact (LOOP):** The service enters a `LOOP` that iterates over each `SupportContact` record within the `_generatedInput/SupportContacts` array. The `$iteration` variable is implicitly available within the loop to track the current index.
4.  **Determine Add or Update (BRANCH):** Inside the loop, a `BRANCH` step examines the `id` field of the current `SupportContact`.
    *   **Add New Support Contact (`$null` id branch):**
        *   **Call Alfabet API:** The service `cms.eadg.sparx.api.services:addResource` is invoked to create a new "Support Contact" in Alfabet. As described in the "External API Interactions" section, the `ss:name` is set to "Support Contact", and `dcterms:title` is mapped from the input `name` or `title`.
        *   **Extract GUID:** The `message` field from the `addResource` service's `SetResponse` output (which is the new GUID) is extracted and stored in `newSupportContactGuid`. Several temporary fields related to the Alfabet API call are then deleted.
        *   **Check Alfabet API Response (BRANCH):** A `BRANCH` checks the `responseCode` from the Alfabet API call.
            *   **Successful Alfabet Creation (`201` branch):**
                *   **Insert into Local DB:** The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContact`. This calls `SP_Insert_SupportContact` to persist the support contact details (including the Alfabet-generated GUID) into the local database.
                *   **Check Local DB Insert Status (BRANCH):** A `BRANCH` checks the `@RETURN_VALUE` from `SP_Insert_SupportContact`.
                    *   **Successful Local DB Insert (`0` branch):**
                        *   **Insert Contact-System Relation:** The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContactRelationSP`. This calls `SP_Insert_SystemSupportContact_Reln` to link the new support contact GUID to the provided `application` ID in the database.
                        *   **Check Relation Insert Status (BRANCH):** A `BRANCH` checks the `@RETURN_VALUE` from `SP_Insert_SystemSupportContact_Reln`.
                            *   **Successful Relation Insert (`0` branch):** Processing for the current `SupportContact` is complete. The flow continues to the next iteration of the `LOOP`.
                            *   **Failed Relation Insert (`$default` branch):** If the stored procedure returns a non-zero value, an error mapping is performed, copying `responseCode` and `message` from the `SetResponse` (likely from the initial Alfabet call, which may be misleading if this is a new error) to the main `Response` object, and the entire flow exits with a `FAILURE` signal. This is a critical error scenario that stops all further processing.
                    *   **Failed Local DB Insert (`$default` branch):** If `SP_Insert_SupportContact` returns a non-zero value, a similar error mapping and `EXIT` with `FAILURE` occurs.
            *   **Failed Alfabet Creation (`$default` response code branch):** If the `addResource` service returns any `responseCode` other than "201", an error mapping is performed, copying `responseCode` and `message` from the `SetResponse` to the main `Response` object, and the entire flow exits with a `FAILURE` signal.
        *   **Collect GUIDs:** After successful processing of an "add" operation, the `newSupportContactGuid` is added to a `newSupportContactGuids` array, and temporary variables are deleted.
    *   **Update Existing Support Contact (`$default` id branch):**
        *   **Normalize Input:** If `title` or `name` fields are null, they are explicitly set to empty strings. This is likely to prevent null pointer exceptions or ensure proper parameter binding in the subsequent stored procedure call.
        *   **Update Local DB:** The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:updateSupportContactSP`. This calls `SP_Update_SupportContact` to update the support contact details in the local database using the provided `id` (GUID).
        *   **Check Local DB Update Status (BRANCH):** A `BRANCH` checks the `@RETURN_VALUE` from `SP_Update_SupportContact`.
            *   **Successful Local DB Update (`0` branch):** Processing for the current `SupportContact` is complete. The flow continues to the next iteration of the `LOOP`.
            *   **Failed Local DB Update (`$default` branch):** If the stored procedure returns a non-zero value, an error mapping is performed, copying `responseCode` and `message` from `SetResponse` to the main `Response` object, and the entire flow exits with a `FAILURE` signal.
5.  **Final Response Generation:** After the loop completes (meaning all support contacts were successfully added or updated without unhandled errors), a final `MAP` step constructs the main service output:
    *   `_generatedResponse/result` is set to "success".
    *   The `newSupportContactGuids` array (containing all generated GUIDs from add operations) is copied to `_generatedResponse/message`.
    *   All remaining temporary variables are deleted from the pipeline.
6.  **Global Error Catch (`CATCH` block):** If any unhandled error occurs within the main `TRY` block (e.g., unexpected data format, network issues, unmapped error codes), the `CATCH` block is executed:
    *   `pub.flow:getLastError` retrieves the details of the exception.
    *   `cms.eadg.utils.api:handleError` is invoked. This dependency service formats the error information into a standardized `SetResponse` document (setting `responseCode` to "500", `result` to "error", and `message` with the error details).
    *   The `lastError` variable is then deleted. The `cms.eadg.utils.api:handleError` service internally handles setting the actual HTTP response code and body for the client.

## Dependency Service Flows

The main `supportContactAdd` service relies on several other services, primarily for database interactions, external API calls, and common utility functions.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContact`**: This is a JDBC Adapter Service. Its purpose is to insert new support contact records into the underlying database by executing the `SP_Insert_SupportContact` stored procedure. It takes various contact details and a GUID as input and returns an integer status from the stored procedure.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:insertSupportContactRelationSP`**: Another JDBC Adapter Service. It's designed to create a relational entry between a support contact and a system. It executes the `SP_Insert_SystemSupportContact_Reln` stored procedure, requiring a system ID and the support contact ID (GUID). It returns an integer status.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:updateSupportContactSP`**: This JDBC Adapter Service handles updates to existing support contact records. It executes the `SP_Update_SupportContact` stored procedure, taking an existing GUID and updated contact details as input, and returns an integer status.
*   **`cms.eadg.sparx.api.services:addResource`**: This is an external API integration service. Its internal implementation is not provided, but its role in `supportContactAdd` is to create a new resource (a "Support Contact" entity) within the external Alfabet system. It's crucial for generating the GUID for new contacts.
*   **`cms.eadg.utils.api:handleError`**: This utility flow service (`CmsEadgUtils/ns/cms/eadg/utils/api/handleError/flow.xml`) provides a centralized error handling mechanism. When invoked, it retrieves the last error from the pipeline (`pub.flow:getLastError`), sets a generic HTTP 500 (Internal Server Error) response code and message using `cms.eadg.utils.api:setResponse`, and prepares the response for the calling client. This ensures consistent error formatting across different services.
*   **`cms.eadg.utils.api:setResponse`**: This utility flow service (`CmsEadgUtils/ns/cms/eadg/utils/api/setResponse/flow.xml`) is responsible for formatting the final HTTP response sent back to the client. It takes a structured `SetResponse` document (containing response code, phrase, result status, and messages) and transforms the content into either a JSON or XML string based on the `format` field in `SetResponse`. It then uses built-in services `pub.flow:setResponseCode` and `pub.flow:setResponse2` to physically set the HTTP headers and body. This service standardizes how service responses are delivered.

## Data Structures and Types

The service utilizes several document types (schemas) to define its input, output, and intermediate data.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SupportContactAddRequest` (Input Model):**
    *   Purpose: Defines the structure of the input request body for adding or updating support contacts.
    *   Fields:
        *   `SupportContacts` (record array): An array of `SupportContact` records. This is the primary input containing all contact information.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SupportContact` (Nested Input Model):**
    *   Purpose: Defines the individual support contact record structure within the `SupportContacts` array.
    *   Fields:
        *   `id` (string, optional): The unique identifier (GUID) for the support contact. Its presence determines if an operation is an "add" (null) or "update" (provided).
        *   `application` (string, optional): The ID of the application or system this support contact is associated with.
        *   `name` (string, optional): The full name of the support contact.
        *   `title` (string, optional): The professional title or role of the support contact.
        *   `url` (string, optional): A relevant URL for the support contact (e.g., internal profile link).
        *   `phone` (string, optional): The contact phone number.
        *   `email` (string, optional): The contact email address.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` (Output Model):**
    *   Purpose: Defines the standard success/error response structure returned by the service.
    *   Fields:
        *   `result` (string, optional): Indicates the overall status, typically "success" or "error". In error scenarios, it might also contain an HTTP status code (e.g., "500").
        *   `message` (string array, optional): Contains details about the operation. On success, it holds the GUIDs of newly created support contacts. On failure, it contains descriptive error messages.
*   **`cms.eadg.utils.api.docs:SetResponse` (Internal Utility Model):**
    *   Purpose: An internal data structure used by utility services (`setResponse`, `handleError`) to standardize the communication of response details (HTTP code, phrase, result status, and messages) before the final output transformation.
    *   Fields: `responseCode`, `responsePhrase`, `result`, `message`, `format`.
*   **`cms.eadg.sparx.api.resources.docs:AddResourceRequest` (External API Request Model):**
    *   Purpose: Defines the request structure for the `addResource` call to the Alfabet system.
    *   Key Fields (relevant to this service's mapping): Highly nested structure containing `rdf:RDF`, `oslc_am:Resource`, `ss:stereotype`, `ss:stereotypename`, `ss:name`, and `dcterms:title`.

## Error Handling and Response Codes

The service employs a multi-layered error handling strategy to provide informative feedback and maintain stability.

*   **Initial Input Validation:** The service performs a basic check to ensure the `SupportContacts` array in the input is not empty. If this validation fails, the service immediately terminates and signals a `FAILURE`, with a message like "Please provide a list of SupportContacts". This would typically result in a 500 HTTP status if caught by an API Gateway.
*   **External API Call Errors:** When invoking `cms.eadg.sparx.api.services:addResource` (for adding new contacts to Alfabet), the service checks the `responseCode` returned by this external service. If the code is anything other than `201` (HTTP Created), it indicates an issue with the Alfabet integration. In such cases, the service maps the error details (specifically `responseCode` and `message`) from the `SetResponse` returned by the `addResource` service to its own `_generatedResponse` output and exits with a `FAILURE` signal. This means the client will receive an error response reflecting the issue from the Alfabet integration.
*   **Database Stored Procedure Errors:** For both insert and update operations on the local database (via `SP_Insert_SupportContact`, `SP_Insert_SystemSupportContact_Reln`, and `SP_Update_SupportContact`), the service inspects the `@RETURN_VALUE` from the stored procedures. A non-zero return value signifies a database-level error. Similar to external API errors, the service maps a prepared error message (potentially reusing information from previous `SetResponse` or an internal error status) to the `_generatedResponse` and exits with a `FAILURE`.
*   **Global Catch-All Mechanism (TRY/CATCH):** A comprehensive `CATCH` block wraps the entire core logic of the service. This ensures that any unhandled exceptions or unexpected runtime errors are gracefully caught.
    *   When an error is caught, `pub.flow:getLastError` is called to retrieve the full exception details.
    *   These details are then passed to the `cms.eadg.utils.api:handleError` utility service. This service is designed to standardize the error response:
        *   It sets the HTTP response code to `500` (Internal Server Error).
        *   It sets the `result` field of the response to "error".
        *   It populates the `message` field of the response with the specific error description obtained from `lastError`.
    *   Finally, `pub.flow:setResponseCode` and `pub.flow:setResponse2` are used to apply these HTTP response details to the client. This ensures that even for unforeseen errors, the client receives a structured and informative error response.

### Detailed Database Column to Output Object Properties (for `_generatedResponse`)

This service is primarily an "add/update" operation, meaning its main function is to ingest data into databases and external systems, rather than query existing database columns to construct a complex output object (like the example `ID`, `NAME`, `System_Alias` structure). The output of this service is a simple status and a list of generated identifiers.

Therefore, the mapping from "source database columns to output object properties" for this service focuses on the **generated identifier** and **status information** that is communicated back to the client.

*   **`newSupportContactGuid` (Generated internally by `cms.eadg.sparx.api.services:addResource` and stored in DB via `SP_Insert_SupportContact`):** `_generatedResponse.message` (Each generated GUID becomes an element in the `message` array for successful 'add' operations).
*   **`"success"` (Literal string on successful completion):** `_generatedResponse.result`
*   **`SetResponse.responseCode` (Internal status from Alfabet API or internal error handling, e.g., "201", "500"):** `_generatedResponse.result` (Used in error/non-success scenarios).
*   **`SetResponse.message` (Internal message from Alfabet API or error details from `pub.flow:getLastError`):** `_generatedResponse.message` (Used in error/non-success scenarios).

The input fields (`name`, `title`, `url`, `phone`, `email`, `application`) are mapped *to* database stored procedure parameters for persistence, but they are *not* directly mapped back to `_generatedResponse` properties in this service's output. The output only confirms the operation's success and provides the newly created GUIDs.