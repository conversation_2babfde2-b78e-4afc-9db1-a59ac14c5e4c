# Webmethods Service Explanation: CmsEadgCedarCoreApi contractAdd

This document provides a detailed explanation of the `contractAdd` Webmethods service. This service is designed to add new contract information into the Alfabet system, an IT portfolio management tool, by processing a list of contract documents provided as input. It primarily transforms the input contract data into a specific JSON format required by a backend stored procedure, which then handles the persistence in the target system. The service is robustly built with error handling mechanisms to provide informative responses.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `contractAdd`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `contractAdd` service's core business purpose is to facilitate the creation of new contract entries within the Alfabet system. It acts as an integration layer, translating external contract data into the format expected by Alfabet's underlying database.

The service expects a single input parameter:

*   `_generatedInput`: A document reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractAddRequest`. This document contains an array of `Contracts`, where each `Contract` is a structured record representing a single contract to be added.

The expected output is a response indicating the success or failure of the operation:

*   `_generatedResponse`: A document reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`. On success, this response includes a "success" result and an array of GUIDs for the newly created objects in the Alfabet system. On failure, it provides an "error" result with a descriptive message.
*   Error responses are also explicitly defined for HTTP status codes 400 (Bad Request), 401 (Unauthorized), and 500 (Internal Server Error), which include a `Response` document detailing the error.

A key validation rule is that the input `_generatedInput/Contracts` array must not be null; otherwise, the service will immediately fail with an appropriate message.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm called "Flow Services" to define business logic. These services are composed of various "steps" that execute in a defined sequence. Here's how some common elements function:

*   **SEQUENCE**: Similar to a block of code or a function body in traditional programming. Steps within a sequence are executed in order. A sequence can have an `EXIT-ON` attribute (e.g., `FAILURE`) which dictates whether the sequence should stop executing if a step within it fails.
*   **BRANCH**: Analogous to a `switch` statement or a series of `if-else if` conditions. It evaluates an expression (specified by the `SWITCH` attribute) and executes a specific path (a "sequence" node) based on the expression's value. The `EXIT-ON` attribute here is common as well.
*   **MAP**: This is a powerful data transformation step. It allows developers to visually map data from input variables to output variables, or to manipulate existing variables.
    *   **MAPSET**: Used to set a literal value or the result of a variable concatenation to a target field. This is similar to direct variable assignment (`variable = "value"`).
    *   **MAPCOPY**: Used to copy the value of one variable to another. This is equivalent to `targetVariable = sourceVariable;`.
    *   **MAPDELETE**: Used to remove a variable from the pipeline (Webmethods' term for the current data context). This helps manage memory and keep the pipeline clean, similar to dereferencing or setting a variable to null after it's no longer needed.
*   **INVOKE**: Used to call another Webmethods service. This is comparable to calling a function or method from a library in traditional programming. Services can be internal (other flow services or Java services) or external (adapters to databases, APIs, etc.).
*   **TRY/CATCH Blocks**: Webmethods implements error handling using `SEQUENCE` blocks with `FORM="TRY"` and `FORM="CATCH"`. If an error occurs within a "TRY" sequence, execution immediately jumps to the corresponding "CATCH" sequence, allowing for graceful error recovery or custom error responses. This is conceptually the same as `try { ... } catch (Exception e) { ... }` in Java or JavaScript.

## Database Interactions

The `contractAdd` service interacts with a Microsoft SQL Server database using a JDBC adapter. The primary database operation is an insertion of data into the Alfabet system, which is orchestrated through a stored procedure.

The database connection used is:
*   `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`

This connection targets the `Sparx_Support` database instance, located at `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` on port `1433`, using the username `sparx_dbuser`. The `NoTrans` suffix indicates that this connection is configured to operate without explicit database transactions managed by Webmethods, meaning each SQL operation commits independently.

The specific SQL **stored procedure** called by the service is:
*   `SP_Insert_SystemContract_json;1`

This stored procedure expects JSON input and produces JSON output.

**Data mapping between service inputs and database parameters:**

The input to the stored procedure is largely derived from the initial `_generatedInput/Contracts` array, but it undergoes a significant transformation into a complex JSON structure before being passed.

*   The `SP_Insert_Contracts` adapter service takes a string input named `@jsonInput`, which is generated from the `UpdateRequest` document. This `UpdateRequest` is built from the `Contract` input.
*   The adapter also expects an `@jsonOutput` string parameter, which will receive the JSON response from the stored procedure.
*   Additionally, the `@RETURN_VALUE` (an integer) is captured from the stored procedure, indicating the success or failure of the database operation.

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external APIs beyond its primary database interaction via the JDBC adapter. All operations appear to be confined to internal data transformations and a database stored procedure call.

## Main Service Flow

The `contractAdd` service orchestrates a series of steps within a `TRY-CATCH` block to process and persist contract data:

1.  **Input Validation (BRANCH `/_generatedInput/Contracts`)**:
    *   The service first checks if the `Contracts` array within the `_generatedInput` document is null.
    *   If `Contracts` is null, it immediately `EXIT`s with a `FAILURE` signal and the message "Please provide a list of contracts." This prevents further execution with invalid input.

2.  **Initialize Update Request (`mapContract` -> Initialize variables MAP)**:
    *   Before processing individual contracts, an `UpdateRequest` document is initialized.
    *   The `CurrentProfile` field of `UpdateRequest` is set to the literal string "API User". This likely identifies the source of the update within the Alfabet system.

3.  **Process Each Contract (LOOP `/Contracts`)**:
    *   The service then enters a `LOOP` that iterates over each `Contract` document in the `Contracts` array provided in the `_generatedInput`. For each `Contract`:
        *   **Build Update Request for Current Contract (INVOKE `cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:buildUpdateRequest`)**:
            *   Inside the loop, the `buildUpdateRequest` dependency service is invoked. This service is responsible for transforming the current `Contract` data into a format consumable by the Alfabet system.
            *   It maps fields from the current `Contract` (e.g., `id`, `systemId`, `contractADO`) to temporary variables (`contractId`, `architectureElementId`, `cms_application_delivery_org`).
            *   Crucially, it uses the `$iteration` variable (which represents the current index in the loop) as an `objectId`. This `objectId` then becomes the `Id` for the `Object` within the `UpdateRequest` and `FromId` for the `Relations`.
            *   It initializes a `ContractDeliverable` document, populating its `architectureelement`, `cms_application_delivery_org`, and `contract` fields from the mapped `architectureElementId`, `cms_application_delivery_org`, and `contractId` respectively.
            *   A `name` field for the `ContractDeliverable` is dynamically generated by concatenating `contractId` and `architectureElementId` with a "|" separator (e.g., "Contract123|SystemXYZ").
            *   This `ContractDeliverable` is then mapped to the `Values` sub-document of a new `Object` document. The `ClassName` of this `Object` is hardcoded to "ContractDeliverable".
            *   Two `Relations` documents are created: one for "architectureelement" and one for "contract". Both use the `objectId` (loop index) as their `FromId` and the `architectureElementId` or `contractId` as their respective `ToRef`. Their `Property` fields are set to "architectureelement" and "contract".
            *   Finally, the `Object` and `Relations` documents are appended to the `Objects` and `Relations` arrays within the main `UpdateRequest` document. This process effectively converts each `Contract` from the input array into a set of structured `Object` and `Relation` records within a single `UpdateRequest`.

4.  **Convert Update Request to JSON (INVOKE `pub.json:documentToJSONString`)**:
    *   After the loop completes, the entire `UpdateRequest` document, which now contains all the transformed contract data as objects and relations, is converted into a JSON string using `pub.json:documentToJSONString`. This JSON string will be passed to the stored procedure.

5.  **Insert Contracts into Database (INVOKE `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:SP_Insert_Contracts`)**:
    *   The generated JSON string is then passed as `@jsonInput` to the `SP_Insert_Contracts` adapter service. This service executes the `SP_Insert_SystemContract_json;1` stored procedure in the database.
    *   The adapter captures the `@RETURN_VALUE` (an integer) and `@jsonOutput` (a JSON string) from the stored procedure.

6.  **Parse Database Response (INVOKE `pub.json:jsonStringToDocument`)**:
    *   The `@jsonOutput` string returned by the stored procedure is converted back into a Webmethods document (`ContractRefStr`). This document is expected to contain the GUIDs of the newly created objects.

7.  **Handle Stored Procedure Result (BRANCH `SP_Insert_ContractsOutput/@RETURN_VALUE`)**:
    *   A `BRANCH` statement evaluates the `@RETURN_VALUE` from the stored procedure call:
        *   **Success (0)**: If the return value is `0`, it indicates success. The `_generatedResponse/result` is set to "success", and the `_generatedResponse/message` array is populated with the `GUID` values from the `ContractRefStr` document (which came from `@jsonOutput`).
        *   **Failure ($default)**: If the return value is anything other than `0`, the service `EXIT`s with a `FAILURE` signal. The specific failure message is not explicitly set in this branch, but the `CATCH` block will handle it.

8.  **Clean Up**: Throughout the flow, `MAPDELETE` steps are used to remove intermediate variables (`Contracts`, `document`, `UpdateRequest`, `jsonString`, `SP_Insert_ContractsInput`, `SP_Insert_ContractsOutput`, `objectId`, `cms_application_delivery_org`, `ArchitectureElementRelation`, `ContractRelation`, `Object`, `Relations`, `ContractDeliverable`, `$iteration`) from the pipeline, optimizing memory usage.

## Dependency Service Flows

The main `contractAdd` service relies on several key dependency services:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:buildUpdateRequest`**:
    *   **Purpose**: This service is a crucial data transformation component. Its role is to take a single `Contract` record from the input array and transform it into the `Object` and `Relations` format required by the `UpdateRequest` structure, which is subsequently converted to JSON for the backend stored procedure. It also generates unique identifiers based on the loop iteration.
    *   **Integration with Main Flow**: It is invoked repeatedly within a loop in the main `contractAdd` service, once for each `Contract` in the input list. The outputs of this service (the transformed `Object` and `Relations` data) are appended to the main `UpdateRequest` document in the calling service.
    *   **Input/Output Contracts**: It takes `UpdateRequest` (to which it appends), `contractId`, `architectureElementId`, `objectId` (derived from loop iteration), and `cms_application_delivery_org`. It outputs the modified `UpdateRequest` and the generated `objectId`.
    *   **Specialized Processing**: This service performs string concatenation to generate a `name` field for the `ContractDeliverable` and handles the creation of the specific `Object` and `Relations` records required for insertion into the Alfabet system via the stored procedure.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.contractAdd:mapContract`**:
    *   **Purpose**: This service orchestrates the iterative mapping of multiple input `Contract` documents into a single `UpdateRequest` document. It initializes the `UpdateRequest` and then loops through the input `Contracts`, calling `buildUpdateRequest` for each one.
    *   **Integration with Main Flow**: The main `contractAdd` service invokes `mapContract` once at the beginning to handle the entire input `Contracts` array.
    *   **Input/Output Contracts**: It takes an array of `Contracts` and an optional `budgetsOnly` flag. Its primary output is the `UpdateRequest` document populated with all the transformed contract data.
    *   **Specialized Processing**: It sets a default `CurrentProfile` for the `UpdateRequest` ("API User") and manages the iterative calls to `buildUpdateRequest` to aggregate all contract data.

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This service provides a centralized mechanism for handling errors across various API services. It retrieves information about the last error that occurred and formats a consistent error response.
    *   **Integration with Main Flow**: It is called within the `CATCH` block of the main `contractAdd` service. When an unhandled exception occurs in the `TRY` block, execution jumps to the `CATCH` block, where `handleError` is invoked.
    *   **Input/Output Contracts**: It takes `lastError` (an `exceptionInfo` document from `pub.flow:getLastError`) and an optional `SetResponse` document (which can pre-define error details). It populates HTTP response codes and a structured error message.
    *   **Specialized Processing**: It attempts to set the HTTP response code (defaulting to 500) and constructs a standard error message from the exception information. It then calls `cms.eadg.utils.api:setResponse` to finalize the response format.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is responsible for formatting the final HTTP response, including setting the content type and the response body. It can format the response as JSON or XML.
    *   **Integration with Main Flow**: It is called by `handleError` to output error messages, and it would typically be called by the main service flow for successful responses as well, although in `contractAdd`, the successful response seems to be mapped directly to `_generatedResponse`.
    *   **Input/Output Contracts**: It takes a `SetResponse` document (containing `responseCode`, `responsePhrase`, `result`, `message`, `format`). It prepares the `responseString` (JSON or XML) and sets the HTTP response headers.

## Data Structures and Types

The service heavily utilizes several custom document types to structure its input, intermediate data, and output:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractAddRequest`**: The primary input document for the `contractAdd` service. It contains a single field:
    *   `Contracts`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract` documents. This field is required (validated by the initial `BRANCH` in `contractAdd`).

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract`**: Represents a single contract to be added to Alfabet. It includes fields such as:
    *   `id`: String (required) - Unique identifier for the contract.
    *   `contractDeliverableId`: String (optional)
    *   `ContractNumber`: String (optional)
    *   `IsDeliveryOrg`: String (optional)
    *   `OrderNumber`: String (optional)
    *   `ProductServiceDescription`: String (optional)
    *   `ProjectTitle`: String (optional)
    *   `ServiceProvided`: String (optional)
    *   `parentAwardId`: String (required) - Parent contract number.
    *   `contractADO`: String (optional) - "Is ADO Parent Contract, Yes/No".
    *   `awardId`: String (required) - Contract number.
    *   `description`: String (optional) - Contract description.
    *   `systemId`: String (optional) - System which this budget funds.
    *   `POPStartDate`: String (optional)
    *   `POPEndDate`: String (optional)
    *   `contractName`: String (optional)

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`**: An intermediary document that encapsulates all the data (objects and relations) to be sent to the Alfabet backend via the stored procedure.
    *   `CurrentProfile`: String - Indicates the user profile (set to "API User").
    *   `APICulture`: String (optional)
    *   `Objects`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object` documents.
    *   `Relations`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations` documents (optional).

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object`**: Represents a generic object within Alfabet.
    *   `RefStr`: String (optional)
    *   `ClassName`: String - The type of object (e.g., "ContractDeliverable").
    *   `Id`: String - Unique ID for this specific object in the request (derived from loop iteration in `buildUpdateRequest`).
    *   `Values`: A record containing the actual data fields for the object, dynamically populated from `ContractDeliverable`.
    *   `GenericAttributes`: An array of generic attribute records.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractDeliverable`**: A specific type of object within Alfabet, representing a contract deliverable. Its fields map to `Object/Values`.
    *   `architectureelement`: String (mapped from `Contract/systemId`)
    *   `cms_rest_last_updated_date`: String
    *   `cms_rest_updated_user`: String
    *   `cms_application_delivery_org`: String (mapped from `Contract/contractADO`)
    *   `contract`: String (mapped from `Contract/id`)
    *   `deliverydate`: String
    *   `description`: String
    *   `id`: String
    *   `name`: String (concatenation of `contractId` and `architectureElementId`)
    *   `samplerecordforusecases`: String
    *   `status`: String
    *   `stereotype`: String
    *   `unit`: String
    *   `usagetype`: String
    *   `volume`: String

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations`**: Defines relationships between objects in Alfabet.
    *   `FromRef`: String (optional) - Reference to the source object.
    *   `FromId`: String (optional) - ID of the source object (mapped from `objectId` / loop iteration).
    *   `Property`: String (optional) - The type of relationship (e.g., "architectureelement", "contract").
    *   `ToRef`: String (optional) - Reference to the target object (mapped from `Contract/systemId` or `Contract/id`).
    *   `ToId`: String (optional) - ID of the target object.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`**: The standard output response for this service.
    *   `result`: String - "success" or "error".
    *   `message`: String array - Contains messages, typically GUIDs of created objects on success, or error details on failure.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractRefStr`**: An internal document type used to parse the JSON output from the database stored procedure.
    *   `NewObjects/GUID`: An array of strings, holding the GUIDs of the newly created objects.

*   **`cms.eadg.utils.api.docs:SetResponse`**: An internal utility document type used by `handleError` and `setResponse` to prepare response details.
    *   `responseCode`: String - HTTP status code (e.g., "500").
    *   `responsePhrase`: String - HTTP reason phrase (e.g., "Internal Server Error").
    *   `result`: String - "success" or "error".
    *   `message`: String array - Detailed messages.
    *   `format`: String - Content type (e.g., "application/json", "application/xml").

**Data Transformation Logic**: The primary transformation involves taking a flat `Contract` structure and mapping it into a hierarchical `UpdateRequest` JSON, which is then fed to the database. The unique `Id` for each `Object` within the `UpdateRequest` is derived from the iteration count of the input `Contracts` array, linking the new objects to their respective relationships. This indicates a batch processing design.

## Error Handling and Response Codes

The `contractAdd` service implements a comprehensive error handling strategy using Webmethods' `TRY-CATCH` blocks, combined with custom utility services.

*   **Top-Level `TRY-CATCH`**: The entire core logic of the `contractAdd` service is enclosed within a `TRY` block. This ensures that any unhandled exception during the main execution flow is caught.
*   **Catch Block Execution**: If an error occurs in the `TRY` block, execution immediately transfers to the `CATCH` block.
    1.  **Get Last Error**: The `pub.flow:getLastError` service is invoked to retrieve detailed information about the exception that occurred, including the error message.
    2.  **Handle Error**: The `cms.eadg.utils.api:handleError` service is then called.
        *   This utility service prepares a standard error response. It typically sets the `responseCode` to `500` (Internal Server Error) and the `result` to "error".
        *   The actual error message from `pub.flow:getLastError` is mapped to the `message` array within the `SetResponse` document.
        *   It then calls `cms.eadg.utils.api:setResponse` to format the response and set the HTTP status code.
*   **Specific Validation Errors**:
    *   An explicit `BRANCH` at the beginning checks if the `_generatedInput/Contracts` array is null. If it is, the service immediately `EXIT`s with `SIGNAL="FAILURE"` and `FAILURE-MESSAGE="Please provide a list of contracts."`. This is a `400 Bad Request` scenario that would be caught by an API Gateway if implemented.
    *   The database stored procedure's return value (`@RETURN_VALUE`) is also explicitly checked. A `0` indicates success, while any other value (handled by the `$default` branch) causes the service to `EXIT` with `SIGNAL="FAILURE"`. This general failure would also be caught by the main `CATCH` block and reported as a 500 error.
*   **HTTP Response Codes**: The service is designed to return the following HTTP response codes:
    *   **200 OK**: On successful addition of contracts.
    *   **400 Bad Request**: If the input `Contracts` list is empty or null.
    *   **500 Internal Server Error**: For any other unhandled exceptions during processing or if the database stored procedure returns a non-zero value.
*   **Error Message Formats**: Error messages are structured within the `Response` document, containing a `result` (e.g., "error") and a `message` array with details. The `cms.eadg.utils.api:setResponse` service ensures this response is formatted either as JSON (`application/json`) or XML (`application/xml`) based on the `format` field in `SetResponse`.
*   **Fallback Behaviors**: The `handleError` service provides a consistent fallback for unexpected errors, ensuring that even in case of unforeseen issues, a structured error response is returned to the caller.

## Source Database Column to Output Object Property Mapping

This section details the mapping of the input `Contract` document fields to the properties of the `UpdateRequest` JSON object, which is then passed to the `SP_Insert_SystemContract_json` stored procedure. The final service response fields are derived from the output of this stored procedure.

*   `Contract/id`:
    *   `UpdateRequest/Objects[N]/Values/contract`
    *   `UpdateRequest/Relations[N]/ToRef` (for ContractRelation)
    *   Part of `UpdateRequest/Objects[N]/Values/name` (as `contractId`)
*   `Contract/systemId`:
    *   `UpdateRequest/Objects[N]/Values/architectureelement`
    *   `UpdateRequest/Relations[N]/ToRef` (for ArchitectureElementRelation)
    *   Part of `UpdateRequest/Objects[N]/Values/name` (as `architectureElementId`)
*   `Contract/contractADO`:
    *   `UpdateRequest/Objects[N]/Values/cms_application_delivery_org`
*   `$iteration` (Webmethods internal loop index for each contract in the input array):
    *   `UpdateRequest/Objects[N]/Id`
    *   `UpdateRequest/Relations[N]/FromId` (for both ArchitectureElementRelation and ContractRelation)

**Other `UpdateRequest` properties set by the service (not directly from `Contract` input):**

*   `UpdateRequest/CurrentProfile`: "API User" (Hardcoded literal)
*   `UpdateRequest/Objects[N]/ClassName`: "ContractDeliverable" (Hardcoded literal)
*   `UpdateRequest/Relations[N]/Property` (for ArchitectureElementRelation): "architectureelement" (Hardcoded literal)
*   `UpdateRequest/Relations[N]/Property` (for ContractRelation): "contract" (Hardcoded literal)
*   `UpdateRequest/Objects[N]/Values/name`: Result of concatenating `Contract/id` and `Contract/systemId` (e.g., `"${Contract/id}|${Contract/systemId}"`)

**Final Service Output `_generatedResponse` properties (derived from Stored Procedure Output):**

*   `SP_Insert_ContractsOutput/@RETURN_VALUE` (from `SP_Insert_SystemContract_json`):
    *   `_generatedResponse/result` (`"success"` if `0`, otherwise implies an `"error"`)
*   `SP_Insert_ContractsOutput/@jsonOutput` (JSON string output from `SP_Insert_SystemContract_json`, parsed into `ContractRefStr`):
    *   `_generatedResponse/message` (populated with an array of `ContractRefStr/NewObjects/GUID` values)