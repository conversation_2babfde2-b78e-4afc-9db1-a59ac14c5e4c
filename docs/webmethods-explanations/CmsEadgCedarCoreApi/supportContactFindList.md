# Webmethods Service Explanation: CmsEadgCedarCoreApi supportContactFindList

This document provides a detailed explanation of the `supportContactFindList` service within the `CmsEadgCedarCoreApi` package. It covers the service's purpose, its internal flow, how it interacts with databases, and its approach to error handling. The primary goal is to provide a comprehensive understanding for experienced software developers unfamiliar with Webmethods, with a focus on data mapping for a TypeScript porting project.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `supportContactFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `supportContactFindList` service is designed to retrieve a list of support contact records associated with a specific application. Its business purpose is to provide a programmatic interface to query support contact information stored in a database, typically for display in another application or system.

The service expects a single input parameter:

*   `application` (string): This is a required parameter representing the ID or Globally Unique Identifier (GUID) of the application for which support contacts are to be found.

The expected output is a JSON or XML response containing a count of the found support contacts and an array of `SupportContact` objects, each detailing an individual support contact. There are no apparent side effects as this service appears to be a read-only operation against a database.

A key validation rule is that the `application` input parameter must be provided. If it is missing, the service will return a "400 Bad Request" error.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods services are constructed using a visual programming paradigm known as "Flow services." These services orchestrate calls to other services and define data transformations. Here are some core concepts used in this service:

*   **SEQUENCE**: A fundamental building block representing a block of steps that are executed sequentially from top to bottom. It's akin to a standard function body or a block of code in other programming languages. A `SEQUENCE` can have an `EXIT-ON` property (e.g., `EXIT-ON="FAILURE"`) which means if any step within that sequence fails, the entire sequence (and potentially the parent flow) will terminate.
*   **BRANCH**: This element provides conditional logic, similar to `switch` statements or `if/else if/else` structures. It evaluates a specified variable or expression (the `SWITCH` attribute) and executes the first child `SEQUENCE` or `INVOKE` node whose `NAME` attribute matches the evaluated value. A special `NAME="$null"` case handles null or undefined values, and `NAME="$default"` acts as a fallback if no other conditions match, much like an `else` block.
*   **MAP**: This element is used for data manipulation within the "pipeline" (Webmethods' term for the shared memory space where data is passed between steps). It allows for transformation, creation, or deletion of variables.
    *   **MAPSET**: Sets a specific, hardcoded value to a field in the pipeline.
    *   **MAPCOPY**: Copies the value from one field to another field in the pipeline.
    *   **MAPDELETE**: Removes a field from the pipeline. This is often used for cleanup to prevent unnecessary data from being passed along or returned.
*   **INVOKE**: This step calls another Webmethods service, which can be a built-in service (like `pub.flow:getLastError`), another custom Flow service, or an Adapter service (for database or external system integration). `VALIDATE-IN` and `VALIDATE-OUT` specify whether input/output validation should occur for the invoked service.
*   **TRY/CATCH**: This forms the primary error handling mechanism. The `TRY` block contains the main business logic. If any unhandled error or exception occurs within the `TRY` block, control is transferred to the `CATCH` block. This is analogous to `try { ... } catch (e) { ... }` in many programming languages.
*   **LOOP**: Used to iterate over an array of data. The `IN-ARRAY` attribute specifies the input array, and `OUT-ARRAY` (if provided) specifies the target array where the results of each iteration's transformations will be placed. Within the loop, a `MAP` step is typically used to process each element.

## Database Interactions

The `supportContactFindList` service interacts with a Microsoft SQL Server database to retrieve support contact information.

The primary database operation is performed by the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:selectSupportContact` service, which is an Adapter Service. Adapter Services are wrappers around database queries or stored procedure calls.

The database connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. The configuration for this connection reveals the following details:

*   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
*   **Port Number**: `1433`
*   **Database Name**: `Sparx_Support`
*   **User**: `sparx_dbuser` (password is referenced as `password.cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`)
*   **Driver Class**: `com.microsoft.sqlserver.jdbc.SQLServerDataSource`
*   **Transaction Type**: `NO_TRANSACTION` (meaning operations using this connection are not part of a larger transaction that could be rolled back).

The database query performed by `selectSupportContact` is a `SELECT` statement against a **view** named `Sparx_System_SupportContact` within the `dbo` schema.

The SQL query, as inferred from the `IRTNODE_PROPERTY` in the adapter's `node.ndf` file, selects specific columns from this view:

```sql
SELECT
    t1."System ID",
    t1."System Name",
    t1."Sparx System ID",
    t1."Sparx System GUID",
    t1."Support Contact ID",
    t1."Support Contact Name",
    t1."Sparx Support Contact ID",
    t1."Sparx Support Contact GUID",
    t1."Support Contact Title",
    t1."Support Contact Email",
    t1."Support Contact Phone",
    t1."Support Contact URL",
    t1."CMS order",
    t1."Import Year"
FROM
    dbo.Sparx_System_SupportContact t1
WHERE
    t1."Sparx System GUID" = ?
```

The `?` in the `WHERE` clause is a placeholder for the input parameter `application` passed to the `supportContactFindList` service, which is then mapped to `selectSupportContactInput."Sparx System GUID"` before invoking the adapter.

**Data Mapping from Database Columns to Adapter Output (`selectSupportContactOutput.results`):**

The `selectSupportContact` adapter's output `results` array directly mirrors the selected database columns:

*   `"System ID"`: `"System ID"`
*   `"System Name"`: `"System Name"`
*   `"Sparx System ID"`: `"Sparx System ID"`
*   `"Sparx System GUID"`: `"Sparx System GUID"`
*   `"Support Contact ID"`: `"Support Contact ID"`
*   `"Support Contact Name"`: `"Support Contact Name"`
*   `"Sparx Support Contact ID"`: `"Sparx Support Contact ID"`
*   `"Sparx Support Contact GUID"`: `"Sparx Support Contact GUID"`
*   `"Support Contact Title"`: `"Support Contact Title"`
*   `"Support Contact Email"`: `"Support Contact Email"`
*   `"Support Contact Phone"`: `"Support Contact Phone"`
*   `"Support Contact URL"`: `"Support Contact URL"`
*   `"CMS order"`: `"CMS order"`
*   `"Import Year"`: `"Import Year"`

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external APIs. Its primary interactions are with an internal database via a JDBC adapter and other internal Webmethods utility services for error handling and response formatting.

## Main Service Flow

The `supportContactFindList` service's execution flow is defined in `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/supportContactFindList/flow.xml`. The service is wrapped in a `TRY` block for robust error handling.

1.  **Input Validation**:
    *   The flow begins with a `BRANCH` statement that checks the value of the `/application` input parameter.
    *   **Scenario 1: Missing `application` parameter** (`NAME="$null"` branch):
        *   A `MAP` step sets fields for a "Bad Request" error response (`responseCode=400`, `responsePhrase="Bad Request"`, `result="error"`, `format="application/json"`, and a specific `message`).
        *   An `EXIT FROM="$parent" SIGNAL="FAILURE"` step then forces the flow to exit the `TRY` block and proceed to the `CATCH` block. This ensures the pre-set 400 error is handled and returned.
    *   **Scenario 2: `application` parameter is present** (`NAME="$default"` branch):
        *   The service proceeds to invoke the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:selectSupportContact` adapter service.
            *   **Input Mapping**: The `application` input from the main service is mapped to the adapter's input parameter, specifically to `selectSupportContactInput."Sparx System GUID"`.
            *   **Output Mapping**: After the database query, the temporary `selectSupportContactInput` and `application` variables are deleted from the pipeline to keep it clean.

2.  **Database Query Result Handling**:
    *   Following the database invocation, another `BRANCH` step evaluates `/selectSupportContactOutput/Selected`. This `Selected` field indicates the number of records returned by the database query.
    *   **Scenario 2a: No records found** (`NAME="0"` branch):
        *   A `MAP` step sets the `count` field of the `_generatedResponse` (the final output document) to `0`.
        *   The `selectSupportContactOutput` variable is deleted.
    *   **Scenario 2b: Records found** (`NAME="$default"` branch):
        *   The `cms.eadg.cedar.core.api.v2.cedarCore_.operations.supportContactFindList:mapSupportContact` service is invoked. This is a crucial step for transforming the raw database output into the desired API response format.
            *   **Output Mapping**: The `SupportContactFindResponse` created by `mapSupportContact` is copied to the `_generatedResponse` variable, which is the official output of the `supportContactFindList` service. The intermediate `SupportContactFindResponse` and `selectSupportContactOutput` variables are then deleted.

3.  **Error Scenario and Handling**:
    *   **CATCH Block**: If any unhandled error occurs during the execution of the `TRY` block (e.g., database connection failure, unexpected data format), control transfers here.
        *   `pub.flow:getLastError` is invoked to retrieve details about the exception.
        *   `cms.eadg.utils.api:handleError` is then called. This utility service standardizes error responses.
        *   **Output Mapping**: Finally, `lastError` and `SetResponse` (if present from the 400 error path) are deleted from the pipeline.

## Dependency Service Flows

The main `supportContactFindList` service relies on several other services to perform its tasks.

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:selectSupportContact`** (TYPE=DEPENDENCY_FILE)
    *   **Purpose**: This is a JDBC Adapter service responsible for executing the SQL `SELECT` query against the `Sparx_Support` database's `Sparx_System_SupportContact` view. It acts as the direct interface to the database.
    *   **Integration**: It's invoked early in the main service flow to fetch the raw support contact data based on the provided `application` ID (mapped to `"Sparx System GUID"`).
    *   **Input/Output Contract**:
        *   **Input**: `selectSupportContactInput."Sparx System GUID"` (string).
        *   **Output**: `selectSupportContactOutput` document, which contains `Selected` (an integer count of rows returned) and `results` (an array of records, each representing a row from the database query).

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.supportContactFindList:mapSupportContact`** (TYPE=DEPENDENCY_FILE)
    *   **Purpose**: This Flow service is dedicated to transforming the raw database output received from `selectSupportContact` into the structured `SupportContactFindResponse` document type expected by the API consumers. It ensures the output adheres to the API's defined data model, irrespective of the underlying database schema.
    *   **Integration**: It is invoked by the main service only when `selectSupportContact` returns one or more records.
    *   **Input/Output Contract**:
        *   **Input**: `selectSupportContactOutput` document (containing `Selected` and `results`).
        *   **Output**: `SupportContactFindResponse` document.
    *   **Specialized Processing**:
        *   It first maps the `selectSupportContactOutput.Selected` (the count of records from the database) directly to `SupportContactFindResponse.count`.
        *   It then uses a `LOOP` construct to iterate through each record in the `selectSupportContactOutput.results` array. For each record, it applies data transformations using `MAPCOPY` operations:
            *   Copies database column `Sparx System GUID` to `SupportContactFindResponse.SupportContacts[].application`.
            *   Copies database column `Support Contact Title` to `SupportContactFindResponse.SupportContacts[].title`.
            *   Copies database column `Support Contact Email` to `SupportContactFindResponse.SupportContacts[].email`.
            *   Copies database column `Support Contact Phone` to `SupportContactFindResponse.SupportContacts[].phone`.
            *   Copies database column `Support Contact URL` to `SupportContactFindResponse.SupportContacts[].url`.
            *   Copies database column `Sparx Support Contact GUID` to `SupportContactFindResponse.SupportContacts[].id`.
            *   Copies database column `Support Contact Name` to `SupportContactFindResponse.SupportContacts[].name`.
        *   After the loop, it performs cleanup by deleting the `selectSupportContactOutput` variable.

3.  **`cms.eadg.utils.api:handleError`** (TYPE=DEPENDENCY_FILE)
    *   **Purpose**: This Flow service provides a centralized mechanism for handling and formatting API error responses. It decides what HTTP status code, phrase, and message to send back to the client based on the error context.
    *   **Integration**: It's invoked from the `CATCH` block of the main `supportContactFindList` service when any exception occurs, or when an explicit error (like the 400 Bad Request) is configured.
    *   **Input/Output Contract**:
        *   **Input**: `SetResponse` (an optional document defining specific response details for controlled errors) and `lastError` (a document containing system exception details, typically from `pub.flow:getLastError`).
        *   **Output**: Cleans up various pipeline variables (`lastError`, `SetResponse`, `ObjectByReportResponse`, `SystemDetail`). The actual HTTP response is set by its sub-services.
    *   **Specialized Processing**: It contains a `BRANCH` based on whether `SetResponse` is available.
        *   If `SetResponse` exists (indicating a pre-defined error, e.g., the 400 validation error), it calls `cms.eadg.utils.api:setResponse` using the pre-configured `SetResponse` document.
        *   If `SetResponse` is `$null` (indicating an unexpected system error), it defaults to setting a 500 Internal Server Error using hardcoded values, mapping the actual exception message from `lastError` into the response, and then calls `cms.eadg.utils.api:setResponse`.

4.  **`cms.eadg.utils.api:setResponse`** (TYPE=DEPENDENCY_FILE)
    *   **Purpose**: This Flow service is a low-level utility called by `handleError` to construct the final HTTP response payload (JSON or XML) and set the HTTP status code and content type.
    *   **Integration**: It is always invoked by `handleError` to finalize the HTTP response to the client.
    *   **Input/Output Contract**:
        *   **Input**: `SetResponse` document (containing `responseCode`, `responsePhrase`, `result`, `message`, `format`).
        *   **Output**: Cleans up all input variables.
    *   **Specialized Processing**:
        *   It maps the `SetResponse` details to a generic `Response` document (or `ResponseRooted` for XML).
        *   It then branches based on the desired `format` (`application/json` or `application/xml`):
            *   For `application/json`, it invokes `pub.json:documentToJSONString` to convert the `Response` document to a JSON string.
            *   For `application/xml`, it invokes `pub.xml:documentToXMLString` to convert the `ResponseRooted` document to an XML string.
        *   Finally, it calls `pub.flow:setResponseCode` to set the HTTP status code and reason phrase, and `pub.flow:setResponse2` to write the JSON/XML string as the HTTP response body with the correct `Content-Type` header.

## Data Structures and Types

Webmethods defines "Document Types" to represent data structures, similar to DTOs or interfaces in object-oriented programming.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SupportContactFindResponse`**:
    *   This is the primary output structure for the `supportContactFindList` API.
    *   It contains:
        *   `count` (object, mapped to `java.math.BigInteger`): An integer representing the total number of support contact records found.
        *   `SupportContacts` (array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SupportContact`): An array, where each element is a detailed support contact record.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SupportContact`**:
    *   This document type defines the structure of a single support contact record within the `SupportContacts` array. All its fields are optional and nillable.
    *   It contains:
        *   `id` (string): Unique identifier for the support contact.
        *   `application` (string): The application ID this contact is associated with.
        *   `name` (string): The name of the support contact.
        *   `title` (string): The title or role of the support contact.
        *   `url` (string): A URL related to the support contact.
        *   `phone` (string): The phone number of the support contact.
        *   `email` (string): The email address of the support contact.

*   **`cms.eadg.utils.api.docs:Response`**:
    *   A generic document type used for API responses, particularly for conveying status or error messages.
    *   It contains:
        *   `result` (string): Indicates the outcome (e.g., "success", "error").
        *   `message` (array of strings): Contains descriptive messages, typically for errors or warnings.

*   **`cms.eadg.utils.api.docs:SetResponse`**:
    *   An internal document type used by the `cms.eadg.utils.api:handleError` and `cms.eadg.utils.api:setResponse` services to manage HTTP response details.
    *   It contains:
        *   `responseCode` (string): The HTTP status code (e.g., "200", "400", "500").
        *   `responsePhrase` (string): The HTTP reason phrase (e.g., "OK", "Bad Request", "Internal Server Error").
        *   `result` (string): The logical result (e.g., "success", "error").
        *   `message` (array of strings): Additional messages.
        *   `format` (string): The desired content type for the response body (e.g., "application/json", "application/xml").

## Error Handling and Response Codes

The service implements a structured error handling strategy to provide consistent responses to API consumers.

*   **Input Validation Error (400 Bad Request)**:
    *   **Scenario**: The `application` input parameter is missing.
    *   **Handling**: The main service flow explicitly checks for this `null` condition.
    *   **Response**:
        *   HTTP Status Code: `400`
        *   HTTP Reason Phrase: `Bad Request`
        *   Response Body (`result`): `error`
        *   Response Body (`message`): `["Please provide required parameter(s) 'application'"]`
        *   Content-Type Header: `application/json` (hardcoded for this specific error)

*   **General System Error (500 Internal Server Error)**:
    *   **Scenario**: Any unexpected exception occurs within the main `TRY` block (e.g., database connection issues, unexpected data from the database, unhandled internal errors).
    *   **Handling**: The `CATCH` block is activated. It calls `pub.flow:getLastError` to capture the exception details and then delegates to the `cms.eadg.utils.api:handleError` service.
    *   **Response**:
        *   HTTP Status Code: `500` (default for unhandled exceptions in `handleError`)
        *   HTTP Reason Phrase: `Internal Server Error` (default in `handleError`)
        *   Response Body (`result`): `error`
        *   Response Body (`message`): An array containing the technical error message from the captured exception (`lastError.error`).
        *   Content-Type Header: `application/json` (default format chosen by `handleError` if not explicitly overridden).

**TypeScript Porting Considerations**:

*   **Error Handling**: The Webmethods error handling flow, especially `handleError` and `setResponse`, demonstrates a pattern of centralizing error response generation. In TypeScript, this would translate to a global error handling middleware or a common error utility function that formats errors into a consistent JSON (or other) structure and sets appropriate HTTP status codes. You'll need to define custom error classes or interfaces for different error types (e.g., `BadRequestError`, `InternalServerError`) to maintain clarity.
*   **Data Models**: The Webmethods Document Types (`SupportContactFindResponse`, `SupportContact`, `Response`, `SetResponse`) are direct candidates for TypeScript interfaces or types. Pay close attention to optional (`field_opt`) and nillable fields, which translate to `?` and `| null` in TypeScript types respectively.
*   **Data Mapping**: The `mapSupportContact` service provides a clear blueprint for data transformation logic. This can be implemented as a simple mapping function in TypeScript, taking the raw database output and transforming it into the desired `SupportContactFindResponse` type. The explicit renaming of fields (e.g., `"Sparx System GUID"` to `application`) will be crucial to capture correctly.
*   **Database Interaction**: The SQL query and its input/output parameters are well-defined. In TypeScript, this would involve using an ORM (Object-Relational Mapper) or a raw SQL query builder, where the `application` input is safely parameterized into the query for the `Sparx_System_SupportContact` view.
*   **Conditional Logic**: The `BRANCH` statements translate directly to `if/else if/else` structures in TypeScript. The initial validation for the `application` parameter would be an early `if` check.
*   **Pipeline Analogy**: The "pipeline" in Webmethods is essentially the function's scope or arguments object. Variables are added, copied, or deleted from this object. In TypeScript, you'd manage data flow through function parameters, return values, and local variables. Explicitly `MAPDELETE` operations in Webmethods suggest a need to clean up intermediate data; in TypeScript, this is usually handled by variable scope and garbage collection, but it's a good practice to ensure you're only returning the necessary data.