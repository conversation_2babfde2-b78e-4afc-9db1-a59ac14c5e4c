# Webmethods Service Explanation: CmsEadgCedarCoreApi contractFind

This document provides a comprehensive explanation of the Webmethods service `contractFind`, detailing its business purpose, internal flow, data interactions, and error handling mechanisms. This service is designed to retrieve contract information from a backend database based on various search criteria.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `contractFind`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## 1. Service Overview

The `contractFind` service aims to provide a flexible API endpoint for searching and retrieving contracts. Its primary business purpose is to support applications needing to query contract data based on associated systems or general keywords.

The service accepts the following optional input parameters:

*   `systemId` (string, optional): The unique identifier of a system associated with contracts. If provided, the service attempts to find contracts specifically linked to this system.
*   `keyword` (string, optional): A general search term used to query contract names. If present, the service performs a broad keyword search.
*   `POPStartDate` (string, optional): A start date for the Period of Performance. This input is defined but not actively used in the provided flow logic.
*   `POPEndDate` (string, optional): An end date for the Period of Performance. This input is defined but not actively used in the provided flow logic.
*   `contractName` (string, optional): The name of a contract to search for. This input is defined but not actively used in the provided flow logic.

The expected output of the service is a JSON object conforming to the `ContractFindResponse` document type. This object includes a `count` representing the number of contracts found and an array of `Contracts` objects, each containing detailed information about a contract. In case of errors, the service returns a structured error response with appropriate HTTP status codes.

Key validation rules are primarily implicit through the branching logic: the service checks for the presence of `systemId` or `keyword` to determine which database query to execute. There are no explicit validation rules for data formats (e.g., date formats) defined within this service's logic.

## 2. Webmethods Concepts for Non-Webmethods Developers

Webmethods services are constructed using a visual programming paradigm known as "Flow services." These services combine various "steps" to define a business process. Here are some core concepts used in this service:

*   **SEQUENCE**: Analogous to a block of code in traditional programming, a `SEQUENCE` defines a series of steps to be executed in sequential order. A `SEQUENCE` can be configured with `TRY` and `CATCH` blocks to implement error handling, similar to `try-catch` blocks in languages like Java or C#. If an error occurs within the `TRY` block, execution immediately jumps to the `CATCH` block.
*   **BRANCH**: This element acts as a conditional statement, similar to `if-else` or `switch-case` constructs. It evaluates a specified variable (`SWITCH`) and directs the flow to different branches based on its value. `$null` is a special case indicating the variable is null or empty, and `$default` catches any values not explicitly handled by other branches.
*   **MAP**: The `MAP` step is crucial for data transformation and manipulation within the service's "pipeline" (the in-memory data structure holding all input, output, and intermediate variables).
    *   `MAPTARGET` and `MAPSOURCE`: These sections define the structure of the data expected before and after the mapping operation.
    *   `MAPSET`: Used to assign a literal or an expression's value to a specific field. It's like `variable = value;`.
    *   `MAPCOPY`: Copies the value from one field to another. It's like `targetVariable = sourceVariable;`.
    *   `MAPDELETE`: Removes a field from the service's pipeline, which is essential for cleaning up intermediate data and managing memory.
    *   `MAPINVOKE`: Allows invoking another service directly within a `MAP` step. This is typically used for simple, atomic operations like string manipulation (e.g., `pub.string:concat` for string concatenation) where the output directly feeds into another mapping operation.
*   **INVOKE**: This step is used to call other Webmethods services. These can be other flow services, adapter services (which interact with external systems like databases), or built-in services (e.g., `pub.flow:getLastError`). `VALIDATE-IN` and `VALIDATE-OUT` specify whether input/output documents should be validated against their schema definitions.

## 3. Database Interactions

The `contractFind` service interacts with a Microsoft SQL Server database using JDBC adapter services. These adapter services handle the connectivity, query execution, and result mapping.

The database connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. The configuration for this connection reveals the following details:

*   **Database Server**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
*   **Port**: `1433`
*   **Database Name**: `Sparx_Support`
*   **User**: `sparx_dbuser`
*   **Transaction Type**: `NO_TRANSACTION` (meaning each database operation is committed immediately, not part of a larger transaction).

The service performs `SELECT` operations on the following database **tables** and **views**:

*   **Tables**: `CEDAR_API.Sparx_System_Contract_Full_Tbl`
*   **Views**: `dbo.Sparx_Contract`

Here are the SQL queries executed by the invoked adapter services:

*   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContracts` (Used when `systemId` is provided):
    ```sql
    SELECT
        "Connection GUID",
        "Sparx System GUID",
        IsDeliveryOrg,
        "Sparx Contract ID",
        "Sparx Contract GUID",
        "Contract Name",
        "Award Type",
        "Contract Amount",
        "Contract Number",
        "Contract Pricing Type",
        "Contractor Name",
        "CS Last Name",
        Deleted,
        "End Date",
        "Order Number",
        "Original Start Date",
        "Parent Award Agency",
        "POP End Date",
        "POP Start Date",
        "Potential POP End Date",
        "Project Title",
        "Start Date",
        Status,
        "Ultimate POP End Date",
        "Service Provided",
        IsDeliveryOrg,
        "Product Service Description",
        "Contract Number"
    FROM CEDAR_API.Sparx_System_Contract_Full_Tbl t1
    WHERE t1."Sparx System GUID" = ?
    ```
    This query retrieves detailed contract information from `Sparx_System_Contract_Full_Tbl` where the "Sparx System GUID" matches the `systemId` input parameter passed to the service. Note that `IsDeliveryOrg` and `"Contract Number"` appear twice in the `SELECT` list, which might be a redundancy or an artifact of the adapter generation. For TypeScript porting, ensure uniqueness or intentional duplication.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsAll` (Used when neither `systemId` nor `keyword` is provided):
    ```sql
    SELECT
        "Connection GUID",
        "Sparx System GUID",
        IsDeliveryOrg,
        "Sparx Contract ID",
        "Sparx Contract GUID",
        "Contract Name",
        "Award Type",
        "Contract Amount",
        "Contract Number",
        "Contract Pricing Type",
        "Contract Type",
        "Contractor Name",
        Deleted,
        "End Date",
        "Obligated Amount",
        "Order Number",
        "Original Start Date",
        "Parent Award Agency",
        "POP End Date",
        "POP Start Date",
        "Potential POP End Date",
        "Start Date",
        Status,
        "Ultimate POP End Date",
        "Service Provided",
        IsDeliveryOrg,
        "Product Service Description",
        "Project Title"
    FROM CEDAR_API.Sparx_System_Contract_Full_Tbl t1
    ```
    This query retrieves all contracts from the `Sparx_System_Contract_Full_Tbl` table without any filtering. The service comment mentions returning contracts for "the current year" in this scenario, but the SQL query for `getContractsAll` does not implement such a filter. This is a discrepancy that should be noted for porting, as the current behavior deviates from the described business logic.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsByKeywords` (Used when `keyword` is provided):
    ```sql
    SELECT
        "Sparx Contract ID",
        "Sparx Contract GUID",
        "Contract Name",
        "Award Type",
        "Contract Amount",
        "Contract Number",
        "Contract Pricing Type",
        "Contract Type",
        "Contractor Name",
        Deleted,
        "End Date",
        "Obligated Amount",
        "Order Number",
        "Original Start Date",
        "POP End Date",
        "Product Service Description",
        "Security Privacy Clause",
        "Service Provided",
        "Legacy ID",
        "Contract ID",
        "Project Title",
        "POP Start Date",
        "Potential POP End Date",
        "Project Title",
        "Start Date",
        Status,
        "Ultimate POP End Date",
        "CO First Name",
        "CO Last Name",
        "COR First Name",
        "COR Last Name",
        "Creation Date",
        "Creation User",
        "CS First Name",
        "CS Last Name",
        "Funding Sub Agency",
        "Issuing Office",
        "Last Update",
        "Last Update User",
        "Major Program",
        "Mod Number",
        "NAICS Description",
        "Parent Award Agency"
    FROM dbo.Sparx_Contract t1
    WHERE t1."Contract Name" LIKE ?
    ```
    This query searches the `Sparx_Contract` view for records where the `"Contract Name"` column matches the provided `keyword` using a `LIKE` operator. The `keyword` is dynamically prepended and appended with '%' wildcards (`%keyword%`) to allow for substring matching. Note that `"Project Title"` appears twice in the `SELECT` list.

## 4. External API Interactions

Based on the provided Webmethods files, the `contractFind` service does not directly invoke any external APIs beyond its internal database adapters. The `pub.json:documentToJSONString` and `pub.xml:documentToXMLString` services are built-in utilities for data formatting, not external API calls.

## 5. Main Service Flow (`contractFind`)

The `contractFind` service's flow is orchestrated using a `TRY-CATCH` block to ensure robust error handling.

1.  **Initialize Variables**:
    *   The service begins by initializing an empty array named `Contracts`. This array will temporarily hold the results before they are mapped to the final `_generatedResponse` structure.

2.  **Conditional Database Query Execution (Branching Logic)**:
    *   The flow first checks if the `keyword` input parameter is null.
        *   **If `keyword` is null**: This branch proceeds to check the `systemId` parameter.
            *   **If `systemId` is null**: The service invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsAll` adapter. This adapter fetches all contract records from the `Sparx_System_Contract_Full_Tbl` table. After the invocation, the flow cleans up unnecessary input variables like `idsOnly` (though `idsOnly` is not explicitly used by the target adapter, it appears in the mapping context), `systemId`, and `keyword` from the pipeline.
            *   **If `systemId` is not null ( `$default` case for `systemId`)**: The service invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContracts` adapter. This adapter retrieves contracts associated with the specified `systemId` by querying `Sparx_System_Contract_Full_Tbl` filtered by `"Sparx System GUID"`. The `systemId` is mapped to the adapter's input parameter `getContractsInput."Sparx System GUID"`. After invocation, temporary input variables are cleaned up.
        *   **If `keyword` is not null ( `$default` case for `keyword`)**: This branch prepares the `keyword` for a `LIKE` search and executes a keyword-based query.
            *   Two `MAP` steps with `pub.string:concat` are used to wrap the `keyword` with wildcard characters (`%`). The first `MAP` prepends `%` (`%keyword`), and the second appends `%` (`%keyword%`). The result is stored in `keyword_search`.
            *   The original `keyword` variable is then deleted.
            *   The service invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContractsByKeywords` adapter, passing the `keyword_search` to its `"Contract Name"` input. This adapter queries the `Sparx_Contract` view.
            *   After the adapter call, several temporary variables related to the keyword search and system ID are deleted, and the results are copied into `getContractsOutput.results` to align with the output structure of the other adapter calls.

3.  **Process Query Results**:
    *   Following the database query, the flow branches again based on the `Selected` count from `getContractsOutput`. This `Selected` field typically indicates the number of records returned by the adapter.
        *   **If `Selected` is `0` (no contracts found)**: The service explicitly sets the `count` field of the `_generatedResponse` (which is of type `ContractFindResponse`) to `0`. The `getContractsOutput` variable is deleted.
        *   **If `Selected` is not `0` ( `$default` case, contracts found)**:
            *   A `LOOP` step iterates through each record in the `getContractsOutput/results` array.
            *   Inside the loop, a `MAP` step performs the crucial data transformation: it maps individual database column values from the `getContractsOutput.results` record to the corresponding fields of a `Contract` document type. This transformed `Contract` object is then added to the `Contracts` array in the pipeline.
            *   After the loop completes, another `MAP` step copies the populated `Contracts` array and the `Selected` count into the final `_generatedResponse` object, which is the service's output.

4.  **Cleanup**:
    *   A final `MAP` step is executed to remove all intermediate variables (`getContractsOutput`, the temporary `Contracts` array, `POPStartDate`, `POPEndDate`, `contractName`) from the pipeline, leaving only the final `_generatedResponse` for the client.

## 6. Dependency Service Flows

The main `contractFind` service relies on several other Webmethods services and components:

*   **JDBC Adapter Services (`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Contracts:getContracts`, `getContractsAll`, `getContractsByKeywords`)**: These services are the core data access layer. They encapsulate the SQL queries described in Section 3, connecting to the configured JDBC data source (`sparxSupportJdbcConnectionNoTrans`) and returning data from the database. Their role is to abstract the database interaction, providing a cleaner interface for the main service flow.
*   **Error Handling Utilities (`cms.eadg.utils.api:handleError`, `cms.eadg.utils.api:setResponse`)**:
    *   `cms.eadg.utils.api:handleError`: This service is a centralized error handler. When an error occurs in the `contractFind` service, `pub.flow:getLastError` is called to retrieve exception details, which are then passed to `handleError`. `handleError` then formats a generic error response, setting the HTTP status code (defaulting to `500 Internal Server Error` if no specific code is provided) and converting the error details into either JSON or XML format before setting the final HTTP response.
    *   `cms.eadg.utils.api:setResponse`: A utility service invoked by `handleError`. It takes a `SetResponse` document (containing response code, phrase, result, message, and format) and uses built-in Webmethods services (`pub.json:documentToJSONString` or `pub.xml:documentToXMLString`) to serialize the response into the specified format (JSON or XML). It then uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to set the HTTP response headers and body.
*   **Built-in Webmethods Services**:
    *   `pub.string:concat`: Used for string concatenation, specifically to add SQL wildcard characters (%) to the `keyword` for `LIKE` queries.
    *   `pub.flow:getLastError`: Retrieves information about the last error that occurred in the current flow. This is crucial for dynamic error reporting.
    *   `pub.flow:setResponseCode`: Sets the HTTP status code and reason phrase for the HTTP response.
    *   `pub.flow:setResponse2`: Sets the HTTP response content type and the actual response body (as a string or byte array).
    *   `pub.json:documentToJSONString`: Converts a Webmethods IData document into a JSON string.
    *   `pub.xml:documentToXMLString`: Converts a Webmethods IData document into an XML string.

## 7. Data Structures and Types

The service heavily relies on specific data structures, defined as "Document Types" in Webmethods. These are akin to data models or schemas in other programming environments.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract`: This document type defines the structure of a single contract object in the API's output. It includes fields such as `id`, `contractDeliverableId`, `parentAwardId`, `contractADO`, `awardId`, `description`, `systemId`, `POPStartDate`, `POPEndDate`, and `contractName`. Many fields are optional (`field_opt: true`) or nillable (`nillable: true`).
*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ContractFindResponse`: This is the top-level output document type for successful responses. It contains `count` (an integer representing the number of contracts found) and `Contracts` (an array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract` objects).
*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`: A generic document type used for error responses, containing `result` (e.g., "error") and a `message` array (for error details).
*   `cms.eadg.utils.api.docs:SetResponse`: A utility document type used internally by the error handling services to define the desired HTTP response code, reason phrase, result, message, and format (JSON or XML).
*   `pub.event:exceptionInfo`: A built-in document type used to capture detailed information about exceptions caught by Webmethods flow services.

### Detailed Source Database Column to Output Object Property Mapping

The critical task of mapping source database columns to output JSON object properties occurs primarily within the `LOOP` and `MAP` step (labeled "map Contract Values") in the main `contractFind` flow. The output fields conform to the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Contract` document type.

Here is a breakdown of the mapping from database columns (from `Sparx_System_Contract_Full_Tbl` or `Sparx_Contract` views/tables, depending on the query path) to the `Contract` output object properties:

*   `"Sparx Contract GUID"`: `id`
*   `"Connection GUID"`: `contractDeliverableId`
*   `"Contract Number"`: `ContractNumber`
*   `"Contract Number"`: `parentAwardId` (Note: The same database column is mapped to two different output fields.)
*   `IsDeliveryOrg`: `IsDeliveryOrg`
*   `IsDeliveryOrg`: `contractADO` (Note: The same database column is mapped to two different output fields.)
*   `"Order Number"`: `OrderNumber`
*   `"Order Number"`: `awardId` (Note: The same database column is mapped to two different output fields.)
*   `"Product Service Description"`: `ProductServiceDescription`
*   `"Project Title"`: `ProjectTitle`
*   `"Service Provided"`: `ServiceProvided`
*   `"Contract Full Name"` (from `Sparx_System_Contract_Full_Tbl`): `description`
*   `"Sparx System GUID"`: `systemId`
*   `"POP Start Date"`: `POPStartDate`
*   `"POP End Date"`: `POPEndDate`
*   `"Contract Name"`: `contractName`

It's important to note for TypeScript porting:
*   The `Cost` field is an intermediary field in the `MAP` step derived from `"Contract Amount"` but is not present in the final `Contract` document type, meaning it is dropped before the final response is generated.
*   The duplication of mappings from a single database column to multiple output fields (`"Contract Number"` -> `ContractNumber`, `parentAwardId`; `IsDeliveryOrg` -> `IsDeliveryOrg`, `contractADO`; `"Order Number"` -> `OrderNumber`, `awardId`) should be verified for intent. In TypeScript, this would typically involve explicit assignment for each desired field.

## 8. Error Handling and Response Codes

The `contractFind` service implements a centralized error handling strategy using a `TRY-CATCH` block and a dedicated error handling service.

*   **Error Catching**: The entire business logic of the `contractFind` service is encapsulated within a `SEQUENCE` with a `TRY` block. If any step within this `TRY` block encounters an error (e.g., database connection issue, null pointer exception), control immediately transfers to the `CATCH` block.
*   **Error Information Retrieval**: Inside the `CATCH` block, the `pub.flow:getLastError` built-in service is invoked. This service retrieves detailed information about the exception that occurred, including the error message and stack trace.
*   **Centralized Error Processing**: The retrieved error information is then passed to the `cms.eadg.utils.api:handleError` service.
    *   If the `handleError` service receives a pre-defined `SetResponse` document (which is not explicitly passed by `contractFind` in the provided flow, implying it might be used in other scenarios or for more granular error types), it will use those settings for the response.
    *   Otherwise, as in this service's case, `handleError` defaults to setting the HTTP status code to `500` (Internal Server Error) with a corresponding `reasonPhrase` of "Internal Server Error" and a `result` of "error". The actual error message from `pub.flow:getLastError` is included in the `message` field of the response.
    *   `handleError` then determines the response format (JSON by default, or XML if specified) and uses `pub.flow:setResponseCode` to set the HTTP status and `pub.flow:setResponse2` to set the response body with the formatted error message.
*   **Output Cleanup**: After the error is handled, the `lastError` and any specific HTTP error code documents (like `400`, `401`, `500` which are defined in the output signature but might be unused or cleaned up if not generated) are removed from the pipeline.

**Porting Considerations for Error Handling**:
For TypeScript, this pattern translates to a standard `try-catch` block where caught exceptions would be logged and then formatted into a standardized error response (e.g., using a dedicated error handling middleware or function) before sending the HTTP 500 status code and JSON payload to the client. If different HTTP status codes (e.g., 400 for bad input, 401 for unauthorized) were required, specific checks and error handling logic would need to be implemented *before* the generic catch-all `500` error, or the `handleError` service would need to be enhanced to dynamically determine the appropriate code based on the `lastError` content. In the current Webmethods flow, all caught errors within `contractFind` will ultimately result in a `500 Internal Server Error`.