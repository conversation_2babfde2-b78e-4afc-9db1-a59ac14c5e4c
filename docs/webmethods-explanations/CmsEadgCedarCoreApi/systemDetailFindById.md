# Webmethods Service Explanation: CmsEadgCedarCoreApi systemDetailFindById

This document provides a comprehensive explanation of the Webmethods service `systemDetailFindById`, its business purpose, technical implementation details, database interactions, and data mapping. This analysis is tailored for an experienced software developer who is new to Webmethods, with a focus on facilitating a migration to TypeScript.

The `systemDetailFindById` service is designed to retrieve detailed information about a specific system based on its unique identifier. Its primary business purpose is to provide a single endpoint for consumers to fetch comprehensive system attributes from the underlying data source, likely for display in applications or integration with other systems. The service expects a single input parameter, `id`, which represents the system's unique ID. Upon successful retrieval, it outputs a structured JSON object containing all relevant system details. In cases where the system is not found or an error occurs, the service returns an appropriate error response with a status code and message. Key validation logic primarily checks for the existence of the requested record.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `systemDetailFindById`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods services, particularly "Flow services," are visually programmed using a drag-and-drop interface, with the underlying logic represented in XML. Understanding a few core elements is crucial for comprehending their flow:

*   **SEQUENCE**: A `SEQUENCE` block represents a series of steps executed in order. It's akin to a standard function body or a `do-while` loop without the looping condition. A special type of `SEQUENCE` is the `TRY` block, which, when paired with a `CATCH` block, functions exactly like a `try-catch` block in traditional programming languages. If any step within the `TRY` sequence fails, execution immediately jumps to the associated `CATCH` block.
*   **BRANCH**: A `BRANCH` element enables conditional logic, similar to `if-else if-else` statements or `switch` statements. It evaluates an expression (often using `%variable% == value`) or a "switch" variable, and executes the first child `SEQUENCE` whose condition matches. A `$default` sequence acts as the `else` or default case.
*   **MAP**: A `MAP` step is used for data transformation and manipulation. It allows for copying, setting, and deleting data within the pipeline (the in-memory data structure passed between steps in a flow service).
    *   **MAPCOPY**: Copies a value from one field in the pipeline to another.
    *   **MAPSET**: Assigns a literal value or an expression result to a field in the pipeline.
    *   **MAPDELETE**: Removes a field from the pipeline. This is often used for cleanup to prevent unnecessary data from being passed between services or returned to the client.
*   **INVOKE**: An `INVOKE` step is used to call another Webmethods service. This promotes modularity and reusability. When invoking a service, input parameters are mapped to the invoked service's inputs, and outputs are mapped back to the calling service's pipeline.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flow services employ structured error handling similar to `try-catch` blocks. A `SEQUENCE` with `FORM="TRY"` defines the main logic. If an error occurs within this block, execution transfers to a `SEQUENCE` with `FORM="CATCH"`. The `pub.flow:getLastError` service is typically invoked in the `CATCH` block to retrieve details about the exception that occurred, which can then be used to construct a meaningful error response.

## Database Interactions

The core data retrieval for this service is handled by a JDBC adapter service.

*   **Database Operation**: The service performs a `SELECT` operation to retrieve a single record from a database view.
*   **Database Connection**: The database connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. The configuration for this connection reveals the following details:
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port Number**: `1433`
    *   **Database Name**: `Sparx_Support`
    *   **User**: `sparx_dbuser` (password is masked but indicated as configurable)
    *   **Driver**: `com.microsoft.sqlserver.jdbc.SQLServerDataSource` (indicating a Microsoft SQL Server database)
    *   **Transaction Type**: `NO_TRANSACTION`
*   **SQL Query**: The SQL query is defined within the `node.ndf` of the `getSystemSummaryAllFieldsById` adapter service.
    *   **Table/View Used**: `Sparx_EASi_System` (identified by `tableName` in the adapter configuration). This is a `VIEW`.
    *   **Query Type**: A `SELECT` query that retrieves all columns (`t1."System ID"`, `t1."Sparx System ID"`, etc.) from the `Sparx_EASi_System` view.
    *   **Where Clause**: `WHERE t1."Sparx System GUID" = ?`. This means the service filters records based on the "Sparx System GUID" column, which is provided as an input parameter.
*   **Data Mapping (Service Inputs to Database Parameters)**:
    *   The `id` input parameter to `systemDetailFindById` is directly mapped to the `getSystemSummaryAllFieldsInput."Sparx System GUID"` input of the `getSystemSummaryAllFieldsById` adapter service. This `Sparx System GUID` is then used as the parameter in the `WHERE` clause of the SQL query.

## External API Interactions

Based on the provided Webmethods files, this service does not directly interact with external APIs (third-party web services). Its primary data source is the internal SQL Server database accessed via a JDBC adapter. The utility services `pub.json:documentToJSONString` and `pub.xml:documentToXMLString` are used for formatting the final output as JSON or XML, but these are internal platform services, not external API calls.

## Main Service Flow

The `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/systemDetailFindById/flow.xml` defines the main execution logic:

1.  **TRY Block Execution**:
    *   **Database Call (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryAllFieldsById`)**:
        *   The `id` (System GUID) received as input to `systemDetailFindById` is mapped to the `"Sparx System GUID"` field of the input for the `getSystemSummaryAllFieldsById` adapter.
        *   This adapter executes a SQL `SELECT` query against the `Sparx_EASi_System` view to fetch all system details based on the provided GUID.
        *   After the database call, the `getSystemSummaryAllFieldsInput` data is deleted from the pipeline to clean up intermediate data.
    *   **Record Existence Check (`BRANCH %getSystemSummaryAllFieldsOutput/Selected% == 1`)**:
        *   A `BRANCH` statement evaluates the `Selected` field from the database query output. If `Selected` is `1` (indicating one record was found), the successful path is taken.
        *   **Success Path (`SEQUENCE NAME="%getSystemSummaryAllFieldsOutput/Selected% == 1"`)**:
            *   **Data Transformation (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemDetailFindById:mapSystemDetail`)**: This service is invoked to transform the raw data retrieved from the database into the standardized `SystemDetail` document type expected by the API. The `getSystemSummaryAllFieldsOutput` is implicitly passed to this mapping service.
            *   **Output Mapping (`MAP`)**: The `SystemDetail` object produced by `mapSystemDetail` is copied to the `_generatedResponse` field, which is the final output structure of the `systemDetailFindById` service. The intermediate `SystemDetail` object is then deleted from the pipeline.
        *   **Record Not Found Path (`SEQUENCE NAME="$default"`)**:
            *   If `Selected` is not `1` (meaning no record was found or multiple records were returned, though the query is designed for one), this path is executed.
            *   **Error Response Setup (`MAP`)**: This `MAP` step explicitly sets values for a `SetResponse` document:
                *   `responseCode`: "400" (Bad Request)
                *   `responsePhrase`: "Bad Request"
                *   `result`: "error"
                *   `message`: "Record not found"
                *   `format`: "application/json"
            *   **Exit Flow (`EXIT FROM="$parent" SIGNAL="FAILURE"`)**: This statement immediately stops the execution of the parent flow service and signals a failure, preventing any further processing and causing the service to return the configured error response.
    *   **Cleanup (`MAP`)**: Regardless of whether a record was found or not (unless an explicit `EXIT` occurs), this `MAP` step performs general cleanup by deleting various intermediate documents from the pipeline: `id`, `ObjectByReportResponse`, `Response`, `SetResponse`, and `getSystemSummaryAllFieldsOutput`. This helps manage memory and keeps the pipeline clean.
2.  **CATCH Block Execution (`SEQUENCE TIMEOUT="" EXIT-ON="FAILURE" FORM="CATCH"`)**:
    *   This block is executed if any unhandled error occurs within the `TRY` block.
    *   **Get Last Error (`INVOKE pub.flow:getLastError`)**: Retrieves the details of the exception that caused the flow to jump to the `CATCH` block.
    *   **Handle Error (`INVOKE cms.eadg.utils.api:handleError`)**: This utility service is called to process the error.
        *   It receives the `lastError` details.
        *   Its output is then cleaned up (the `lastError` document itself is deleted).

## Dependency Service Flows

The main `systemDetailFindById` service relies on several other services (dependencies) to perform its full functionality:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryAllFieldsById`**:
    *   **Purpose**: This is a database adapter service responsible for querying the `Sparx_EASi_System` view in the `Sparx_Support` database to retrieve all available fields for a specific system identified by its "Sparx System GUID". It acts as the direct interface to the database.
    *   **Integration**: It's the first major step in the `systemDetailFindById` service's `TRY` block, fetching the raw data that needs to be processed.
    *   **Input/Output Contract**:
        *   **Input**: A document named `getSystemSummaryAllFieldsInput` containing a field `"Sparx System GUID"` (string).
        *   **Output**: A document `getSystemSummaryAllFieldsOutput` which includes:
            *   `Selected`: A string indicating if a record was found (e.g., "1" for found).
            *   `results`: An array of records, where each record contains the raw data from the `Sparx_EASi_System` view's columns.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemDetailFindById:mapSystemDetail`**:
    *   **Purpose**: This service's sole purpose is to transform the flat `results` structure obtained from the `getSystemSummaryAllFieldsById` adapter into the more structured `SystemDetail` document type required for the API's output. It performs extensive field-level mapping and data type conversions.
    *   **Integration**: It is invoked after the successful retrieval of data from the database, acting as a data transformation layer.
    *   **Input/Output Contract**:
        *   **Input**: The `getSystemSummaryAllFieldsOutput` document from the database adapter call.
        *   **Output**: A `SystemDetail` document, which is the final structured output for the API.
    *   **Specialized Processing**: This service utilizes several utility services for data transformation:
        *   `cms.eadg.utils.date:dateTimeStringToObject`: Converts date strings (e.g., "MM/dd/yyyy") into Java `Date` objects for `movingToCloudDate`, `atoEffectiveDate`, `atoExpirationDate`, and `systemProductionDate`.
        *   `cms.eadg.utils.map:convertBoolean`: Converts string representations of boolean values (e.g., "Y"/"N", "True"/"False") into actual Java `Boolean` objects. This is used for fields like `storesBeneficiaryAddress`, `storesBankingData`, `isCmsOwned`, `editBeneficiaryInformation`, `agileUsed`, `businessArtifactsOnDemand`, `systemRequirementsOnDemand`, `systemDesignOnDemand`, `sourceCodeOnDemand`, `testPlanOnDemand`, `testScriptsOnDemand`, `testReportsOnDemand`, `omDocumentationOnDemand`, `hardCodedIpAddress`, `ecapParticipation`, `locallyStoredUserInformation`, `noMajorRefresh`, `noPersistentRecordsFlag`, `noPlannedMajorRefresh`, `recordsUnderLegalHold`, and `systemHasApiGateway`.
        *   `cms.eadg.utils.string:tokenize`: Splits delimited strings into arrays of strings. This is used for `beneficiaryAddressPurpose`, `beneficiaryAddressSource`, `beneficiaryInformation`, `multifactorAuthenticationMethod`, and `systemDataLocation`.
        *   `cms.eadg.utils.math:toNumberIf`: Converts a string to a number (specifically `BigInteger` for `ipEnabledAssetCount`) if the string is not null.
*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A generic error handling service designed to process exceptions that occur in other services. It standardizes the error response format.
    *   **Integration**: Called within the `CATCH` block of the main `systemDetailFindById` service when an unexpected error occurs.
    *   **Input/Output Contract**:
        *   **Input**: Optionally, a `SetResponse` document (if a specific error message/code needs to be overridden) and a `lastError` document (containing details of the exception from `pub.flow:getLastError`).
        *   **Output**: It configures the HTTP response codes and the response body, but does not add new documents to the pipeline for the calling service to process, as it directly sets the HTTP response.
    *   **Specialized Processing**: It checks if a `SetResponse` document is provided. If not, it defaults to a 500 Internal Server Error using information from `lastError`. If `SetResponse` is present, it uses its configured values. It then calls `cms.eadg.utils.api:setResponse` to finalize the response.
*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This is a crucial utility service responsible for setting the actual HTTP response code, reason phrase, and content type, and then serializing the response body into either JSON or XML format based on the `format` field in the `SetResponse` document.
    *   **Integration**: Invoked by `handleError` and also directly by `systemDetailFindById` in the "record not found" scenario.
    *   **Input/Output Contract**:
        *   **Input**: A `SetResponse` document containing `responseCode`, `responsePhrase`, `result`, `message` (array), and `format` (e.g., "application/json" or "application/xml").
        *   **Output**: No specific output document, as it directly manipulates the HTTP response.
    *   **Specialized Processing**:
        *   Uses a `BRANCH` on `SetResponse/format` to determine the output serialization:
            *   If `application/json`, it invokes `pub.json:documentToJSONString` to convert the `Response` document into a JSON string.
            *   If `application/xml`, it first maps the `Response` document into a `ResponseRooted` structure (which simply wraps the `Response` document in a root element) and then invokes `pub.xml:documentToXMLString` to convert it to an XML string.
        *   Finally, it calls `pub.flow:setResponseCode` to set the HTTP status code and `pub.flow:setResponse2` to send the serialized response body to the client.

## Data Structures and Types

The service uses several Webmethods "Document Types" (recrefs) to define its data models:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemDetail`**: This is the primary output data model for the API. It is a complex record containing other nested record types for better organization.
    *   `id`: string (from `"Sparx System GUID"` in DB)
    *   `nextVersionId`: string (from `nextVersionID` in DB) - Optional
    *   `previousVersionId`: string (from `previousVersionID` in DB) - Optional
    *   `ictObjectId`: string (from `"Sparx System GUID"` in DB)
    *   `uuid`: string (from `"CMS UUID"` in DB) - Optional
    *   `name`: string (from `"System Name"` in DB)
    *   `description`: string (from `Description` in DB) - Optional
    *   `version`: string (from `Version` in DB)
    *   `acronym`: string (from `Acronym` in DB) - Optional
    *   `state`: string (from `"Object State"` in DB) - Optional
    *   `status`: string (from `status` in DB) - Optional
    *   `belongsTo`: string (from `belongsTo` in DB) - Optional
    *   `businessOwnerOrg`: string (from `businessOwnerOrg` in DB) - Optional
    *   `businessOwnerOrgComp`: string (from `"Business Owner Organization Component"` in DB) - Optional
    *   `systemMaintainerOrg`: string (from `"System Maintainer Organization"` in DB) - Optional
    *   `systemMaintainerOrgComp`: string (from `"System Maintainer Organization Component"` in DB) - Optional
    *   `atoEffectiveDate`: `java.util.Date` (from `"ATO Effective Date"` in DB, converted from string) - Optional
    *   `atoExpirationDate`: `java.util.Date` (from `"ATO Expiration Date"` in DB, converted from string) - Optional
    *   `BusinessOwnerInformation`: Nested record type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BusinessOwnerInformation`)
    *   `DataCenterHosting`: Nested record type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DataCenterHosting`)
    *   `SystemMaintainerInformation`: Nested record type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemMaintainerInformation`)
    *   `SoftwareProductDetails`: Nested record type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SoftwareProductDetails`)
*   **`cms.eadg.alfabet.api.v01.docs.reports.cedar.core:SystemDetail`**: This is an intermediate document type used to hold the raw results directly fetched from the database before transformation. It mirrors the columns returned by the SQL query.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BusinessOwnerInformation`**:
    *   `isCmsOwned`: boolean (from `"Is CMS Owned"` in DB, converted from string) - Optional
    *   `storesBeneficiaryAddress`: boolean (from `"Beneficiary Address"` in DB, converted from string) - Optional
    *   `beneficiaryAddressPurpose`: string array (from `"Benefitiary Address Purpose"` in DB, tokenized) - Optional
    *   `beneficiaryAddressPurposeOther`: string (from `"Other Benefitiary Address Purpose"` in DB) - Optional
    *   `beneficiaryAddressSource`: string array (from `"Beneficiary Address Source"` in DB, tokenized) - Optional
    *   `beneficiaryAddressSourceOther`: string (from `"Beneficiary Address Source Other"` in DB) - Optional
    *   `storesBankingData`: boolean (from `"Banking Data"` in DB, converted from string) - Optional
    *   `costPerYear`: string (from `"Cost Per Year"` in DB) - Optional
    *   `numberOfFederalFte`: string (from `"Number of Federal FTE"` in DB) - Optional
    *   `numberOfContractorFte`: string (from `"Number of Contractor FTE"` in DB) - Optional
    *   `numberOfSupportedUsersPerMonth`: string (from `"Number of Supported Users Per Month"` in DB) - Optional
    *   `beneficiaryInformation`: string array (from `"Beneficiary Information"` in DB, tokenized) - Optional
    *   `editBeneficiaryInformation`: boolean (from `"Edit Beneficiary Information"` in DB, converted from string) - Optional
    *   `508UserInterface`: string (from `"System has UI"` in DB) - Optional
    *   `systemOwnership`: string (from `"System Ownership"` in DB) - Optional
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DataCenterHosting`**:
    *   `movingToCloud`: string (from `"Cloud Migration Plan"` in DB) - Optional
    *   `movingToCloudDate`: `java.util.Date` (from `"Cloud Migrated Date"` in DB, converted from string) - Optional
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SoftwareProductDetails`**:
    *   `apisDeveloped`: string (from `"API Developed"` in DB) - Optional
    *   `apiDataArea`: string array (from `"API Data Area"` in DB, tokenized) - Optional
    *   `apisAccessibility`: string (from `"API Accessibility"` in DB) - Optional
    *   `apiFHIRUse`: string (from `"Does the API use FHIR"` in DB) - Optional
    *   `apiFHIRUseOther`: string (from `"Does the API use FHIR Other"` in DB) - Optional
    *   `systemHasApiGateway`: boolean (from `"System has API Gateway"` in DB, converted from string) - Optional
    *   `aiPlan`: string (from `"Artificial Intelligence Plan"` in DB) - Optional
    *   `systemAiType`: string array (from `"Artificial Intelligence"` in DB, tokenized) - Optional
    *   `systemAiTypeOther`: string (from `"Artificial Intelligence - OTHER Description"` in DB) - Optional
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemMaintainerInformation`**:
    *   `systemCustomization`: string (from `"System Customization"` in DB) - Optional
    *   `frontendAccessType`: string (from `"Front End Access Type"` in DB) - Optional
    *   `netAccessibility`: string (from `"CMS System Access"` in DB) - Optional
    *   `ipEnabledAssetCount`: `java.math.BigInteger` (from `"IP Enabled Asset Count"` in DB, converted from string) - Optional
    *   `ip6EnabledAssetPercent`: string (from `"Percent IPV6"` in DB) - Optional
    *   `ip6TransitionPlan`: string (from `"IPV6 Transition Plan"` in DB) - Optional
    *   `hardCodedIpAddress`: boolean (from `"Hard Coded IP Address"` in DB, converted from string) - Optional
    *   `ecapParticipation`: boolean (from `"E-Cap Participation"` in DB, converted from string) - Optional
    *   `systemProductionDate`: `java.util.Date` (from `"Start Date"` in DB, converted from string) - Optional
    *   `devCompletionPercent`: string (from `"Development Complete Percent"` in DB) - Optional
    *   `devWorkDescription`: string (from `"Development Work Still Underway"` in DB) - Optional
    *   `agileUsed`: boolean (from `"Agile Methodology Use"` in DB, converted from string) - Optional
    *   `deploymentFrequency`: string (from `"Deployment Frequency"` in DB) - Optional
    *   `majorRefreshDate`: `java.util.Date` (from `"Next Major Tech Refresh Date"` in DB, converted from string) - Optional
    *   `plansToRetireReplace`: string (from `"Retire or Replace"` in DB) - Optional
    *   `yearToRetireReplace`: string (from `"Retire or Replace Date"` in DB) - Optional
    *   `quarterToRetireReplace`: string (from `"Planned Retirement Quarter"` in DB) - Optional
    *   `businessArtifactsOnDemand`: boolean (from `"Business Artifacts on Demand"` in DB, converted from string) - Optional
    *   `systemRequirementsOnDemand`: boolean (from `"Requirements on Demand"` in DB, converted from string) - Optional
    *   `systemDesignOnDemand`: boolean (from `"Design on Demand"` in DB, converted from string) - Optional
    *   `sourceCodeOnDemand`: boolean (from `"Source Code on Demand"` in DB, converted from string) - Optional
    *   `testPlanOnDemand`: boolean (from `"Test Plan on Demand"` in DB, converted from string) - Optional
    *   `testScriptsOnDemand`: boolean (from `"Test Script on Demand"` in DB, converted from string) - Optional
    *   `testReportsOnDemand`: boolean (from `"Test Reports on Demand"` in DB, converted from string) - Optional
    *   `omDocumentationOnDemand`: boolean (from `"Ops and Maintenance Plans on Demand"` in DB, converted from string) - Optional
    *   `recordsManagementBucket`: string array (from `"Business Artifact Location"` in DB, tokenized) - Optional
    *   `adHocAgileDeploymentFrequency`: string (from `"Deployment AdHoc Frequency"` in DB) - Optional
    *   `authoritativeDatasource`: string (from `"System Data Authoritative Source"` in DB) - Optional
    *   `dataAtRestEncryptionKeyManagement`: string (from `"Data At Rest Encryption Management"` in DB) - Optional
    *   `legalHoldCaseName`: string (from `"Legal Hold Case Name"` in DB) - Optional
    *   `locallyStoredUserInformation`: boolean (from `"Locally Stored User Info"` in DB, converted from string) - Optional
    *   `multifactorAuthenticationMethod`: string array (from `"MFA Method"` in DB, tokenized) - Optional
    *   `multifactorAuthenticationMethodOther`: string (from `"MFA Other"` in DB) - Optional
    *   `networkTrafficEncryptionKeyManagement`: string (from `"Network Traffic Encryption Management"` in DB) - Optional
    *   `noMajorRefresh`: boolean (from `"No Major Refresh Flag"` in DB, converted from string) - Optional
    *   `noPersistentRecordsFlag`: boolean (from `"No Persistent Records Flag"` in DB, converted from string) - Optional
    *   `noPlannedMajorRefresh`: boolean (from `"No Planned Major Refresh Flag"` in DB, converted from string) - Optional
    *   `recordsManagementDisposalLocation`: string (from `"Records Management Disposal Location"` in DB) - Optional
    *   `recordsManagementDisposalPlan`: string (from `"Records Management Disposal Plan"` in DB) - Optional
    *   `recordsUnderLegalHold`: boolean (from `"Records Under Legal Hold"` in DB, converted from string) - Optional
    *   `systemDataLocation`: string array (from `"System Data Location"` in DB, tokenized) - Optional
    *   `systemDataLocationNotes`: string (from `"System Data Location Notes"` in DB) - Optional
*   **`cms.eadg.utils.api.docs:Response`**: A generic response structure used for errors or success messages.
    *   `result`: string (e.g., "success", "error")
    *   `message`: string array (detailed messages)
*   **`cms.eadg.utils.api.docs:SetResponse`**: An internal document type used to configure the HTTP response properties.
    *   `responseCode`: string (HTTP status code, e.g., "200", "400", "500")
    *   `responsePhrase`: string (HTTP reason phrase, e.g., "OK", "Bad Request", "Internal Server Error")
    *   `result`: string (e.g., "success", "error")
    *   `message`: string array
    *   `format`: string (e.g., "application/json", "application/xml")
*   **`cms.eadg.utils.api.docs:ResponseRooted`**: A simple wrapper around `cms.eadg.utils.api.docs:Response` used specifically for XML serialization to provide a root element.

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` blocks and utility services:

*   **Success**: When a system is found, the service returns the `SystemDetail` object. While not explicitly shown setting a 200 OK, this is the default success code if no error occurs and no other code is explicitly set by `pub.flow:setResponseCode`.
*   **Record Not Found (HTTP 400 Bad Request)**:
    *   If the database query returns no records (i.e., `getSystemSummaryAllFieldsOutput/Selected` is not "1"), the service explicitly sets the HTTP response code to `400` with the phrase "Bad Request".
    *   The response body will be a JSON object (or XML if configured otherwise) with `result: "error"` and `message: ["Record not found"]`.
    *   Execution is terminated with `EXIT FROM="$parent" SIGNAL="FAILURE"`, indicating a client-side error.
*   **Internal Server Error (HTTP 500 Internal Server Error)**:
    *   Any unhandled exception during the execution of the `TRY` block (e.g., database connectivity issues, data transformation errors) will cause the flow to jump to the `CATCH` block.
    *   In the `CATCH` block, `pub.flow:getLastError` captures the exception details.
    *   The `cms.eadg.utils.api:handleError` service is invoked. By default, if no specific error `SetResponse` is provided to `handleError`, it will set the HTTP response code to `500` with the phrase "Internal Server Error".
    *   The response body will contain `result: "error"` and `message` providing the technical error details from the `lastError` object.
*   **Response Format**: The `cms.eadg.utils.api:setResponse` utility service handles setting the `Content-Type` header (either `application/json` or `application/xml`) and serializing the response body accordingly.

## Detailed Source Database Column to Output Object Property Mapping

This mapping is derived from `mapSystemDetail/flow.xml` and the `getSystemSummaryAllFieldsById/node.ndf` adapter details. Note that `results[]` indicates an array of records from the database query.

*   `results[].ID`: No direct mapping visible to the main `SystemDetail` or its sub-documents. Potentially unused or implicitly handled elsewhere.
*   `results[]."Sparx System GUID"`:
    *   `SystemDetail.id`
    *   `SystemDetail.ictObjectId`
*   `results[].nextVersionID`: `SystemDetail.nextVersionId`
*   `results[].previousVersionID`: `SystemDetail.previousVersionId`
*   `results[]."CMS UUID"`: `SystemDetail.uuid`
*   `results[]."System Name"`: `SystemDetail.name`
*   `results[].Description`: `SystemDetail.description`
*   `results[].Version`: `SystemDetail.version`
*   `results[].Acronym`: `SystemDetail.acronym`
*   `results[]."Object State"`: `SystemDetail.state`
*   `results[].status`: `SystemDetail.status`
*   `results[].belongsTo`: `SystemDetail.belongsTo`
*   `results[].businessOwnerOrg`: `SystemDetail.businessOwnerOrg`
*   `results[]."Business Owner Organization Component"`: `SystemDetail.businessOwnerOrgComp`
*   `results[]."System Maintainer Organization"`: `SystemDetail.systemMaintainerOrg`
*   `results[]."System Maintainer Organization Component"`: `SystemDetail.systemMaintainerOrgComp`
*   `results[]."Is CMS Owned"`: `SystemDetail.BusinessOwnerInformation.isCmsOwned` (string to boolean conversion)
*   `results[]."Beneficiary Address"`: `SystemDetail.BusinessOwnerInformation.storesBeneficiaryAddress` (string to boolean conversion)
*   `results[]."Benefitiary Address Purpose"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressPurpose` (tokenized string to string array conversion)
*   `results[]."Other Benefitiary Address Purpose"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressPurposeOther`
*   `results[]."Beneficiary Address Source"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressSource` (tokenized string to string array conversion)
*   `results[]."Beneficiary Address Source Other"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressSourceOther`
*   `results[]."Banking Data"`: `SystemDetail.BusinessOwnerInformation.storesBankingData` (string to boolean conversion)
*   `results[]."Cost Per Year"`: `SystemDetail.BusinessOwnerInformation.costPerYear`
*   `results[]."Number of Federal FTE"`: `SystemDetail.BusinessOwnerInformation.numberOfFederalFte`
*   `results[]."Number of Contractor FTE"`: `SystemDetail.BusinessOwnerInformation.numberOfContractorFte`
*   `results[]."Number of Supported Users Per Month"`: `SystemDetail.BusinessOwnerInformation.numberOfSupportedUsersPerMonth`
*   `results[]."Beneficiary Information"`: `SystemDetail.BusinessOwnerInformation.beneficiaryInformation` (tokenized string to string array conversion)
*   `results[]."Edit Beneficiary Information"`: `SystemDetail.BusinessOwnerInformation.editBeneficiaryInformation` (string to boolean conversion)
*   `results[]."System has UI"`: `SystemDetail.BusinessOwnerInformation.508UserInterface`
*   `results[]."System Ownership"`: `SystemDetail.BusinessOwnerInformation.systemOwnership`
*   `results[]."Cloud Migration Plan"`: `SystemDetail.DataCenterHosting.movingToCloud`
*   `results[]."Cloud Migrated Date"`: `SystemDetail.DataCenterHosting.movingToCloudDate` (string to date conversion)
*   `results[]."System Customization"`: `SystemDetail.SystemMaintainerInformation.systemCustomization`
*   `results[]."Front End Access Type"`: `SystemDetail.SystemMaintainerInformation.frontendAccessType`
*   `results[]."CMS System Access"`: `SystemDetail.SystemMaintainerInformation.netAccessibility`
*   `results[]."IP Enabled Asset Count"`: `SystemDetail.SystemMaintainerInformation.ipEnabledAssetCount` (string to BigInteger conversion)
*   `results[]."Percent IPV6"`: `SystemDetail.SystemMaintainerInformation.ip6EnabledAssetPercent`
*   `results[]."IPV6 Transition Plan"`: `SystemDetail.SystemMaintainerInformation.ip6TransitionPlan`
*   `results[]."Hard Coded IP Address"`: `SystemDetail.SystemMaintainerInformation.hardCodedIpAddress` (string to boolean conversion)
*   `results[]."E-Cap Participation"`: `SystemDetail.SystemMaintainerInformation.ecapParticipation` (string to boolean conversion)
*   `results[]."Start Date"`: `SystemDetail.SystemMaintainerInformation.systemProductionDate` (string to date conversion)
*   `results[]."Development Complete Percent"`: `SystemDetail.SystemMaintainerInformation.devCompletionPercent`
*   `results[]."Development Work Still Underway"`: `SystemDetail.SystemMaintainerInformation.devWorkDescription`
*   `results[]."Agile Methodology Use"`: `SystemDetail.SystemMaintainerInformation.agileUsed` (string to boolean conversion)
*   `results[]."Deployment Frequency"`: `SystemDetail.SystemMaintainerInformation.deploymentFrequency`
*   `results[]."Next Major Tech Refresh Date"`: `SystemDetail.SystemMaintainerInformation.majorRefreshDate` (string to date conversion)
*   `results[]."Retire or Replace"`: `SystemDetail.SystemMaintainerInformation.plansToRetireReplace`
*   `results[]."Retire or Replace Date"`: `SystemDetail.SystemMaintainerInformation.yearToRetireReplace`
*   `results[]."Planned Retirement Quarter"`: `SystemDetail.SystemMaintainerInformation.quarterToRetireReplace`
*   `results[]."Business Artifacts on Demand"`: `SystemDetail.SystemMaintainerInformation.businessArtifactsOnDemand` (string to boolean conversion)
*   `results[]."Test Script on Demand"`: `SystemDetail.SystemMaintainerInformation.testScriptsOnDemand` (string to boolean conversion)
*   `results[]."Test Reports on Demand"`: `SystemDetail.SystemMaintainerInformation.testReportsOnDemand` (string to boolean conversion)
*   `results[]."Test Plan on Demand"`: `SystemDetail.SystemMaintainerInformation.testPlanOnDemand` (string to boolean conversion)
*   `results[]."Source Code on Demand"`: `SystemDetail.SystemMaintainerInformation.sourceCodeOnDemand` (string to boolean conversion)
*   `results[]."Design on Demand"`: `SystemDetail.SystemMaintainerInformation.systemDesignOnDemand` (string to boolean conversion)
*   `results[]."Ops and Maintenance Plans on Demand"`: `SystemDetail.SystemMaintainerInformation.omDocumentationOnDemand` (string to boolean conversion)
*   `results[]."Requirements on Demand"`: `SystemDetail.SystemMaintainerInformation.systemRequirementsOnDemand` (string to boolean conversion)
*   `results[]."Business Artifact Location"`: `SystemDetail.SystemMaintainerInformation.recordsManagementBucket` (tokenized string to string array conversion)
*   `results[]."Deployment AdHoc Frequency"`: `SystemDetail.SystemMaintainerInformation.adHocAgileDeploymentFrequency`
*   `results[]."Locally Stored User Info"`: `SystemDetail.SystemMaintainerInformation.locallyStoredUserInformation` (string to boolean conversion)
*   `results[]."MFA Method"`: `SystemDetail.SystemMaintainerInformation.multifactorAuthenticationMethod` (tokenized string to string array conversion)
*   `results[]."MFA Other"`: `SystemDetail.SystemMaintainerInformation.multifactorAuthenticationMethodOther`
*   `results[]."Network Traffic Encryption Management"`: `SystemDetail.SystemMaintainerInformation.networkTrafficEncryptionKeyManagement`
*   `results[]."Data At Rest Encryption Management"`: `SystemDetail.SystemMaintainerInformation.dataAtRestEncryptionKeyManagement`
*   `results[]."No Persistent Records Flag"`: `SystemDetail.SystemMaintainerInformation.noPersistentRecordsFlag` (string to boolean conversion)
*   `results[]."Records Management Disposal Location"`: `SystemDetail.SystemMaintainerInformation.recordsManagementDisposalLocation`
*   `results[]."Records Management Disposal Plan"`: `SystemDetail.SystemMaintainerInformation.recordsManagementDisposalPlan`
*   `results[]."Records Under Legal Hold"`: `SystemDetail.SystemMaintainerInformation.recordsUnderLegalHold` (string to boolean conversion)
*   `results[]."Legal Hold Case Name"`: `SystemDetail.SystemMaintainerInformation.legalHoldCaseName`
*   `results[]."No Major Refresh Flag"`: `SystemDetail.SystemMaintainerInformation.noMajorRefresh` (string to boolean conversion)
*   `results[]."No Planned Major Refresh Flag"`: `SystemDetail.SystemMaintainerInformation.noPlannedMajorRefresh` (string to boolean conversion)
*   `results[]."API Developed"`: `SystemDetail.SoftwareProductDetails.apisDeveloped`
*   `results[]."API Data Area"`: `SystemDetail.SoftwareProductDetails.apiDataArea` (tokenized string to string array conversion)
*   `results[]."API Accessibility"`: `SystemDetail.SoftwareProductDetails.apisAccessibility`
*   `results[]."Does the API use FHIR"`: `SystemDetail.SoftwareProductDetails.apiFHIRUse`
*   `results[]."Does the API use FHIR Other"`: `SystemDetail.SoftwareProductDetails.apiFHIRUseOther`
*   `results[]."System has API Gateway"`: `SystemDetail.SoftwareProductDetails.systemHasApiGateway` (string to boolean conversion)
*   `results[]."Artificial Intelligence Plan"`: `SystemDetail.SoftwareProductDetails.aiPlan`
*   `results[]."Artificial Intelligence"`: `SystemDetail.SoftwareProductDetails.systemAiType` (tokenized string to string array conversion)
*   `results[]."Artificial Intelligence - OTHER Description"`: `SystemDetail.SoftwareProductDetails.systemAiTypeOther`
*   `results[]."ATO Effective Date"`: `SystemDetail.atoEffectiveDate` (string to date conversion, also exists at root level)
*   `results[]."ATO Expiration Date"`: `SystemDetail.atoExpirationDate` (string to date conversion, also exists at root level)
*   `results[]."System Data Authoritative Source"`: `SystemDetail.SystemMaintainerInformation.authoritativeDatasource`
*   `results[]."System Data Location"`: `SystemDetail.SystemMaintainerInformation.systemDataLocation` (tokenized string to string array conversion)
*   `results[]."System Data Location Notes"`: `SystemDetail.SystemMaintainerInformation.systemDataLocationNotes`

For the TypeScript porting project, special attention should be paid to:
*   Implementing equivalent data type conversions (string to boolean, string to Date, string to number/BigInt, and splitting delimited strings into arrays).
*   Replicating the conditional logic for handling `Selected` == 1 versus `$default` (record not found).
*   Translating the pipeline-based data flow and cleanup (MAPDELETE) into explicit variable declarations and scope management in TypeScript.
*   Designing robust error handling and response serialization mechanisms in TypeScript that mirror the Webmethods `handleError` and `setResponse` utilities, ensuring consistent API responses for different scenarios.
*   The use of nested document types in Webmethods (`SystemDetail` containing `BusinessOwnerInformation`, etc.) should translate directly into nested interfaces or classes in TypeScript to maintain a clean data model.
*   The `IRTNODE_PROPERTY` XML section in the adapter `node.ndf` files is invaluable for reconstructing the precise SQL query and understanding column types.
```markdown
# Webmethods Service Explanation: CmsEadgCedarCoreApi systemDetailFindById

This document provides a comprehensive explanation of the Webmethods service `systemDetailFindById`, its business purpose, technical implementation details, database interactions, and data mapping. This analysis is tailored for an experienced software developer who is new to Webmethods, with a focus on facilitating a migration to TypeScript.

The `systemDetailFindById` service is designed to retrieve detailed information about a specific system based on its unique identifier. Its primary business purpose is to provide a single endpoint for consumers to fetch comprehensive system attributes from the underlying data source, likely for display in applications or integration with other systems. The service expects a single input parameter, `id`, which represents the system's unique ID. Upon successful retrieval, it outputs a structured JSON object containing all relevant system details. In cases where the system is not found or an error occurs, the service returns an appropriate error response with a status code and message. Key validation logic primarily checks for the existence of the requested record.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `systemDetailFindById`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods services, particularly "Flow services," are visually programmed using a drag-and-drop interface, with the underlying logic represented in XML. Understanding a few core elements is crucial for comprehending their flow:

*   **SEQUENCE**: A `SEQUENCE` block represents a series of steps executed in order. It's akin to a standard function body or a `do-while` loop without the looping condition. A special type of `SEQUENCE` is the `TRY` block, which, when paired with a `CATCH` block, functions exactly like a `try-catch` block in traditional programming languages. If any step within the `TRY` sequence fails, execution immediately jumps to the associated `CATCH` block.

*   **BRANCH**: A `BRANCH` element enables conditional logic, similar to `if-else if-else` statements or `switch` statements. It evaluates an expression (often using `%variable% == value`) or a "switch" variable, and executes the first child `SEQUENCE` whose condition matches. A `$default` sequence acts as the `else` or default case.

*   **MAP**: A `MAP` step is used for data transformation and manipulation. It allows for copying, setting, and deleting data within the pipeline (the in-memory data structure passed between steps in a flow service).
    *   **MAPCOPY**: Copies a value from one field in the pipeline to another.
    *   **MAPSET**: Assigns a literal value or an expression result to a field in the pipeline.
    *   **MAPDELETE**: Removes a field from the pipeline. This is often used for cleanup to prevent unnecessary data from being passed between services or returned to the client.

*   **INVOKE**: An `INVOKE` step is used to call another Webmethods service. This promotes modularity and reusability. When invoking a service, input parameters are mapped to the invoked service's inputs, and outputs are mapped back to the calling service's pipeline.

*   **Error Handling (TRY/CATCH blocks)**: Webmethods flow services employ structured error handling similar to `try-catch` blocks. A `SEQUENCE` with `FORM="TRY"` defines the main logic. If an error occurs within this block, execution transfers to a `SEQUENCE` with `FORM="CATCH"`. The `pub.flow:getLastError` service is typically invoked in the `CATCH` block to retrieve details about the exception that occurred, which can then be used to construct a meaningful error response.

## Database Interactions

The core data retrieval for this service is handled by a JDBC adapter service.

*   **Database Operation**: The service performs a `SELECT` operation to retrieve a single record from a database view.

*   **Database Connection**: The database connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. The configuration for this connection reveals the following details:
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port Number**: `1433`
    *   **Database Name**: `Sparx_Support`
    *   **User**: `sparx_dbuser` (password is masked but indicated as configurable)
    *   **Driver**: `com.microsoft.sqlserver.jdbc.SQLServerDataSource` (indicating a Microsoft SQL Server database)
    *   **Transaction Type**: `NO_TRANSACTION`

*   **SQL Queries or Stored Procedures Called**:
    *   **SQL View Used**: `Sparx_EASi_System` (identified by `tableName` in the adapter configuration). This is a `VIEW`.
    *   **SQL Query**: The `getSystemSummaryAllFieldsById` adapter service executes a `SELECT` query as follows:
        ```sql
        SELECT
            t1."System ID",
            t1."Sparx System ID",
            t1."Sparx System GUID",
            t1."System Name",
            t1.Acronym,
            t1."Object State",
            t1."CMS UUID",
            t1.ID,
            t1.nextVersionID,
            t1.previousVersionID,
            t1."ICT Object ID",
            t1.Description,
            t1.Version,
            t1.status,
            t1.belongsTo,
            t1."Is CMS Owned",
            t1."Beneficiary Address",
            t1."Beneficiary Address Source",
            t1."Beneficiary Address Source Other",
            t1."Benefitiary Address Purpose",
            t1."Other Benefitiary Address Purpose",
            t1."Banking Data",
            t1."Cost Per Year",
            t1."Number of Federal FTE",
            t1."Number of Contractor FTE",
            t1."Number of Supported Users Per Month",
            t1."Beneficiary Information",
            t1."Edit Beneficiary Information",
            t1."System has UI",
            t1."Cloud Migration Plan",
            t1."Cloud Migrated Date",
            t1."Hosted on Cloud",
            t1."System Customization",
            t1."Front End Access Type",
            t1."CMS System Access",
            t1."IP Enabled Asset Count",
            t1."Percent IPV6",
            t1."IPV6 Transition Plan",
            t1."Hard Coded IP Address",
            t1."E-Cap Participation",
            t1."Start Date",
            t1."Development Complete Percent",
            t1."Development Work Still Underway",
            t1."Agile Methodology Use",
            t1."Deployment Frequency",
            t1."Next Major Tech Refresh Date",
            t1."Retire or Replace",
            t1."Retire or Replace Date",
            t1."Planned Retirement Quarter",
            t1."Business Artifacts on Demand",
            t1."Test Script on Demand",
            t1."Test Reports on Demand",
            t1."Test Plan on Demand",
            t1."Source Code on Demand",
            t1."Design on Demand",
            t1."Ops and Maintenance Plans on Demand",
            t1."Requirements on Demand",
            t1."Business Artifact Location",
            t1."Test Script Location",
            t1."Test Reports Location",
            t1."Test Plan Location",
            t1."Souce Code Location",
            t1."System Design Location",
            t1."Ops and Maintenance Plan Location",
            t1."Requirements Location",
            t1."Records Management Format",
            t1."Records Management Format Other",
            t1."Records Management Metadata",
            t1."Records Management Approved Schedule",
            t1."Records Management Disposal",
            t1."System Data Authoritative Source",
            t1."System Data Location",
            t1."System Data Location Notes",
            t1."Deployment AdHoc Frequency",
            t1."Locally Stored User Info",
            t1."MFA Method",
            t1."MFA Other",
            t1."Network Traffic Encryption Management",
            t1."Data At Rest Encryption Management",
            t1."No Persistent Records Flag",
            t1."Records Management Disposal Location",
            t1."Records Management Disposal Plan",
            t1."Records Under Legal Hold",
            t1."Legal Hold Case Name",
            t1."No Major Refresh Flag",
            t1."No Planned Major Refresh Flag",
            t1."API Developed",
            t1."API Data Area",
            t1."API Accessibility",
            t1."Does the API use FHIR",
            t1."Does the API use FHIR Other",
            t1."System has API Gateway",
            t1."Artificial Intelligence Plan",
            t1."Artificial Intelligence",
            t1."Artificial Intelligence - OTHER Description",
            t1."AI Project Life Cycle Stage",
            t1."No Contracts Flag",
            t1."No Sub Systems Flag",
            t1."No URLs Flag",
            t1."Next System Survey",
            t1."Current System Survey",
            t1.businessOwnerOrg,
            t1."Business Owner Organization Component",
            t1."System Maintainer Organization",
            t1."System Maintainer Organization Component",
            t1."ATO Effective Date",
            t1."ATO Expiration Date",
            t1."System Ownership"
        FROM
            Sparx_EASi_System t1
        WHERE
            t1."Sparx System GUID" = ?
        ```

*   **Data Mapping between Service Inputs and Database Parameters**:
    *   The `id` input parameter to the `systemDetailFindById` service is mapped directly to the `"Sparx System GUID"` input field of the `getSystemSummaryAllFieldsById` adapter service. This value (`?`) is then used in the `WHERE` clause of the SQL query to filter the results.

## External API Interactions

Based on the provided Webmethods files, this service does not directly interact with external APIs (third-party web services). Its primary data source is the internal SQL Server database accessed via a JDBC adapter. The utility services `pub.json:documentToJSONString` and `pub.xml:documentToXMLString` are used for formatting the final output as JSON or XML, but these are internal platform services, not external API calls.

## Main Service Flow

The `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/systemDetailFindById/flow.xml` defines the main execution logic:

1.  The entire service logic is enclosed within a `SEQUENCE` block configured as a `TRY` block. This ensures that any unexpected errors during execution are caught and handled by the `CATCH` block.

2.  **Invoke Database Adapter (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryAllFieldsById`)**:
    *   The `id` (System GUID) received as input to `systemDetailFindById` is copied to the `getSystemSummaryAllFieldsInput."Sparx System GUID"` field for the invoked adapter service.
    *   This adapter executes the SQL `SELECT` query against the `Sparx_EASi_System` view, fetching system details for the provided GUID.
    *   After the database call returns, the `getSystemSummaryAllFieldsInput` document is explicitly deleted from the pipeline to free up memory and clean intermediate data.

3.  **Check for Record Existence (`BRANCH TIMEOUT="" LABELEXPRESSIONS="true"`)**:
    *   A `BRANCH` statement, acting like an `if-else` structure, evaluates the `Selected` field within the `getSystemSummaryAllFieldsOutput` from the database query.

    *   **Success Path (`SEQUENCE NAME="%getSystemSummaryAllFieldsOutput/Selected% == 1"`)**: This branch executes if `Selected` is `1`, indicating that a single record was found in the database.
        *   **Invoke Data Transformation (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemDetailFindById:mapSystemDetail`)**: This service is called to transform the raw database output into the standardized `SystemDetail` document type. The `getSystemSummaryAllFieldsOutput` (containing the `results` array with one record) is automatically available to this mapping service.
        *   **Output Mapping (`MAP`)**: The `SystemDetail` object generated by `mapSystemDetail` is copied to the `_generatedResponse` field, which represents the final successful output of the `systemDetailFindById` service. The intermediate `SystemDetail` object (produced by `mapSystemDetail`) is then deleted from the pipeline.

    *   **Record Not Found Path (`SEQUENCE NAME="$default"`)**: This branch executes if the `Selected` field is not `1` (implying no record was found or an unexpected number of records were returned for the given ID).
        *   **Configure Error Response (`MAP TIMEOUT="" MODE="STANDALONE"`)**: This `MAP` step explicitly sets values for a `SetResponse` document, preparing a client-side error message:
            *   `responseCode`: "400"
            *   `responsePhrase`: "Bad Request"
            *   `result`: "error"
            *   `message`: ["Record not found"]
            *   `format`: "application/json"
        *   **Exit Service (`EXIT FROM="$parent" SIGNAL="FAILURE"`)**: This statement immediately stops the execution of the main `systemDetailFindById` service. `SIGNAL="FAILURE"` ensures that the service terminates with an error status, and the configured `SetResponse` (which will be processed by `cms.eadg.utils.api:setResponse` implicitly or via error handling) is sent back to the client.

4.  **Cleanup (`MAP TIMEOUT="" MODE="STANDALONE"`)**: Regardless of the branch taken (unless an early `EXIT` occurs as in the "$default" sequence), this `MAP` step performs general pipeline cleanup. It deletes various intermediate documents that are no longer needed: `id`, `ObjectByReportResponse`, `Response`, `SetResponse`, and `getSystemSummaryAllFieldsOutput`.

5.  **CATCH Block (`SEQUENCE TIMEOUT="" EXIT-ON="FAILURE" FORM="CATCH"`)**: This block is executed if any unhandled exception occurs within the `TRY` block.
    *   **Get Last Error (`INVOKE pub.flow:getLastError`)**: This built-in service retrieves details about the exception that caused the flow to enter the `CATCH` block. The error information is stored in the `lastError` document.
    *   **Handle Error (`INVOKE cms.eadg.utils.api:handleError`)**: This utility service is invoked to process the captured error. It takes the `lastError` details (and potentially a pre-configured `SetResponse` if one was created before the error) and prepares a standardized error response for the client.
    *   **Cleanup (`MAP MODE="OUTPUT"`)**: After `handleError` is invoked, the `lastError` document is deleted from the pipeline, as its information has now been processed.

## Dependency Service Flows

The main `systemDetailFindById` service relies on several other services (dependencies) to perform its full functionality:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryAllFieldsById`**:
    *   **Purpose**: This is a database adapter service responsible for querying the `Sparx_EASi_System` view in the `Sparx_Support` database to retrieve all available fields for a specific system identified by its "Sparx System GUID". It acts as the direct interface to the database.
    *   **Integration**: It's the first major step in the `systemDetailFindById` service's `TRY` block, fetching the raw data that needs to be processed.
    *   **Input/Output Contract**:
        *   **Input**: A document named `getSystemSummaryAllFieldsInput` containing a field `"Sparx System GUID"` (string).
        *   **Output**: A document `getSystemSummaryAllFieldsOutput` which includes:
            *   `Selected`: A string indicating if a record was found (e.g., "1" for found).
            *   `results`: An array of records, where each record contains the raw data from the `Sparx_EASi_System` view's columns.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemDetailFindById:mapSystemDetail`**:
    *   **Purpose**: This service's sole purpose is to transform the flat `results` structure obtained from the `getSystemSummaryAllFieldsById` adapter into the more structured `SystemDetail` document type required for the API's output. It performs extensive field-level mapping and data type conversions.
    *   **Integration**: It is invoked within the success branch of the main service flow, acting as a data transformation layer after data retrieval.
    *   **Input/Output Contract**:
        *   **Input**: The `getSystemSummaryAllFieldsOutput` document from the database adapter call (specifically, it expects the `results` array within it).
        *   **Output**: A `SystemDetail` document, which is the final structured output for the API.
    *   **Specialized Processing**: This service utilizes several utility services for data transformation:
        *   `cms.eadg.utils.date:dateTimeStringToObject`: Converts date strings (e.g., "MM/dd/yyyy") into Java `Date` objects for fields like `movingToCloudDate`, `atoEffectiveDate`, `atoExpirationDate`, and `systemProductionDate`.
        *   `cms.eadg.utils.map:convertBoolean`: Converts string representations of boolean values (e.g., "Y"/"N", "True"/"False") into actual Java `Boolean` objects. This is used for many boolean flags like `storesBeneficiaryAddress`, `isCmsOwned`, `agileUsed`, etc.
        *   `cms.eadg.utils.string:tokenize`: Splits delimited strings (often by newline or pipe characters) into arrays of strings. This is used for multi-valued fields like `beneficiaryAddressPurpose`, `beneficiaryInformation`, and `apiDataArea`.
        *   `cms.eadg.utils.math:toNumberIf`: Attempts to convert a string to a number (`BigInteger` in this case) if the string is not null. This is used for `ipEnabledAssetCount`.

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A generic error handling service designed to process exceptions that occur in other services. It standardizes the error response format.
    *   **Integration**: Called within the `CATCH` block of the main `systemDetailFindById` service when an unexpected error occurs.
    *   **Input/Output Contract**:
        *   **Input**: Optionally, a `SetResponse` document (if a specific error message/code needs to be overridden) and a `lastError` document (containing details of the exception from `pub.flow:getLastError`).
        *   **Output**: This service configures the HTTP response codes and the response body directly, rather than returning a document to the calling service's pipeline.
    *   **Logic**: It uses a `BRANCH` on the presence of `SetResponse`. If `SetResponse` is `$null`, it defaults to a `500 Internal Server Error` and populates the `SetResponse` document using the details from `lastError`. If `SetResponse` is provided (e.g., in `systemDetailFindById` for the "Record not found" case), it uses those values. It then always calls `cms.eadg.utils.api:setResponse` to finalize the HTTP response.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This is a crucial utility service responsible for setting the actual HTTP response code, reason phrase, and content type, and then serializing the response body into either JSON or XML format based on the `format` field in the `SetResponse` document.
    *   **Integration**: Invoked by `handleError` (for all error types) and also directly by `systemDetailFindById` (for the "record not found" scenario).
    *   **Input/Output Contract**:
        *   **Input**: A `SetResponse` document containing `responseCode`, `responsePhrase`, `result`, `message` (array), and `format` (e.g., "application/json" or "application/xml").
        *   **Output**: No specific output document, as it directly manipulates the HTTP response.
    *   **Logic**:
        *   It first maps the `result` and `message` from the input `SetResponse` into a standard `Response` document.
        *   It then uses a `BRANCH` on the `SetResponse/format` field to determine the serialization method:
            *   If `application/json`, it calls `pub.json:documentToJSONString` to convert the `Response` document into a JSON string.
            *   If `application/xml`, it first maps the `Response` document into a `ResponseRooted` structure (which simply wraps the `Response` document for XML output) and then calls `pub.xml:documentToXMLString` to convert it to an XML string.
        *   Finally, it calls `pub.flow:setResponseCode` to set the HTTP status code and `pub.flow:setResponse2` to send the serialized response body to the client.

## Data Structures and Types

The service extensively uses Webmethods "Document Types" (which are effectively structured data records or schemas) to define its data models. These are defined in `.ndf` files under `docTypes` directories.

*   `cms.eadg.alfabet.api.v01.docs.reports.cedar.core:SystemDetail`: This is an intermediate document type that directly mirrors the columns returned by the `Sparx_EASi_System` database view. Its fields mostly contain raw `string` data, requiring further transformation.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemDetail`: This is the primary output data model for the API. It is a composite record type, containing fields directly at its root level and also nested references to other detailed document types:
    *   `id`: string
    *   `nextVersionId`: string (Optional)
    *   `previousVersionId`: string (Optional)
    *   `ictObjectId`: string
    *   `uuid`: string (Optional)
    *   `name`: string
    *   `description`: string (Optional)
    *   `version`: string
    *   `acronym`: string (Optional)
    *   `state`: string (Optional)
    *   `status`: string (Optional)
    *   `belongsTo`: string (Optional)
    *   `businessOwnerOrg`: string (Optional)
    *   `businessOwnerOrgComp`: string (Optional)
    *   `systemMaintainerOrg`: string (Optional)
    *   `systemMaintainerOrgComp`: string (Optional)
    *   `atoEffectiveDate`: `java.util.Date` (Optional)
    *   `atoExpirationDate`: `java.util.Date` (Optional)
    *   `BusinessOwnerInformation`: (Reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BusinessOwnerInformation`) (Optional)
    *   `DataCenterHosting`: (Reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DataCenterHosting`) (Optional)
    *   `SystemMaintainerInformation`: (Reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemMaintainerInformation`) (Optional)
    *   `SoftwareProductDetails`: (Reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SoftwareProductDetails`) (Optional)

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BusinessOwnerInformation`: Contains details about system ownership and beneficiary information. Fields include booleans (e.g., `isCmsOwned`), strings, and string arrays (e.g., `beneficiaryAddressPurpose`).

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DataCenterHosting`: Contains information about cloud migration and hosting. Fields include strings and `java.util.Date` objects.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SoftwareProductDetails`: Contains details about APIs and AI usage related to the system. Fields include strings, booleans, and string arrays.

*   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemMaintainerInformation`: Contains extensive details about system maintenance, network access, development practices, and data management. Fields cover various data types including strings, booleans, `java.math.BigInteger`, `java.util.Date`, and string arrays.

*   `cms.eadg.utils.api.docs:Response`: A simple, generic response structure used for API messages, typically within error responses. It has `result` (string) and `message` (string array).

*   `cms.eadg.utils.api.docs:SetResponse`: An internal document type used to configure various aspects of the HTTP response (code, phrase, content type, and the message body) before it's sent to the client.

*   `cms.eadg.utils.api.docs:ResponseRooted`: A wrapper document type (containing a single `Response` field) used specifically when serializing to XML, ensuring a root element for the XML output.

The `mapSystemDetail` service is responsible for the crucial data transformation logic, converting raw database string outputs into the appropriate types (booleans, dates, numbers, arrays) and nesting them into the final hierarchical `SystemDetail` structure. Fields are often marked as "Optional" (`field_opt: true`) in the Webmethods document type definitions, meaning they might not always be present in the output.

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` blocks and utility services to provide consistent error responses.

*   **HTTP 200 OK (Success)**: When a system is successfully found and its details are mapped, the service implicitly returns a 200 OK status code. No explicit `pub.flow:setResponseCode` is called for success, but this is the default behavior if the flow completes without signaling a failure. The response body will be the structured `SystemDetail` JSON (or XML) object.

*   **HTTP 400 Bad Request (Client Error - Record Not Found)**:
    *   **Scenario**: This occurs when a request is syntactically correct but the specified `id` does not correspond to an existing system in the database.
    *   **Implementation**: The `BRANCH` condition `"%getSystemSummaryAllFieldsOutput/Selected% == 1"` explicitly checks if a record was found. If not, the `$default` sequence is executed.
    *   **Response**: The service explicitly sets the HTTP status code to `400` with the reason phrase "Bad Request". The response body is configured as a JSON (or XML) object with:
        *   `result`: "error"
        *   `message`: ["Record not found"]
    *   **Flow Control**: `EXIT FROM="$parent" SIGNAL="FAILURE"` is used to immediately terminate the service execution and send this specific error response.

*   **HTTP 500 Internal Server Error (Server Error - Unhandled Exception)**:
    *   **Scenario**: This covers any unexpected errors that occur during the service's execution within the `TRY` block, such as database connectivity issues, malformed data preventing type conversion, or other unhandled exceptions.
    *   **Implementation**: The `CATCH` block of the main service flow is triggered.
    *   **Response**: The `pub.flow:getLastError` service retrieves the technical details of the exception. The `cms.eadg.utils.api:handleError` utility then processes this error. By default (when no specific `SetResponse` document is provided to `handleError`), it sets the HTTP status code to `500` with the reason phrase "Internal Server Error". The response body will contain:
        *   `result`: "error"
        *   `message`: [the technical error description from `lastError`]
    *   **Fallback Behavior**: The `handleError` service acts as a centralized point for standardizing internal server errors. If for any reason the `SetResponse` document passed to `handleError` is null, it defaults to a 500 error, ensuring a consistent error message even for unexpected issues.

## Detailed Source Database Column to Output Object Property Mapping

This mapping is derived from the `MAPCOPY` operations in `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/operations/systemDetailFindById:mapSystemDetail/flow.xml` and the output structure of `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/adapters/SystemSummary:getSystemSummaryAllFieldsById/node.ndf`.

*   `results[].ID`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."System ID"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Sparx System ID"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Sparx System GUID"`:
    *   `SystemDetail.id`
    *   `SystemDetail.ictObjectId`
*   `results[]."System Name"`: `SystemDetail.name`
*   `results[].Acronym`: `SystemDetail.acronym`
*   `results[]."Object State"`: `SystemDetail.state`
*   `results[]."CMS UUID"`: `SystemDetail.uuid`
*   `results[].nextVersionID`: `SystemDetail.nextVersionId`
*   `results[].previousVersionID`: `SystemDetail.previousVersionId`
*   `results[]."ICT Object ID"`: `SystemDetail.ictObjectId` (already mapped from "Sparx System GUID", this is a redundant mapping in `mapSystemDetail`)
*   `results[].Description`: `SystemDetail.description`
*   `results[].Version`: `SystemDetail.version`
*   `results[].status`: `SystemDetail.status`
*   `results[].belongsTo`: `SystemDetail.belongsTo`
*   `results[]."Is CMS Owned"`: `SystemDetail.BusinessOwnerInformation.isCmsOwned` (String "Y"/"N" to Boolean)
*   `results[]."Beneficiary Address"`: `SystemDetail.BusinessOwnerInformation.storesBeneficiaryAddress` (String to Boolean)
*   `results[]."Beneficiary Address Source"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressSource` (Newline-delimited String to String Array)
*   `results[]."Beneficiary Address Source Other"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressSourceOther`
*   `results[]."Benefitiary Address Purpose"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressPurpose` (Newline-delimited String to String Array)
*   `results[]."Other Benefitiary Address Purpose"`: `SystemDetail.BusinessOwnerInformation.beneficiaryAddressPurposeOther`
*   `results[]."Banking Data"`: `SystemDetail.BusinessOwnerInformation.storesBankingData` (String to Boolean)
*   `results[]."Cost Per Year"`: `SystemDetail.BusinessOwnerInformation.costPerYear`
*   `results[]."Number of Federal FTE"`: `SystemDetail.BusinessOwnerInformation.numberOfFederalFte`
*   `results[]."Number of Contractor FTE"`: `SystemDetail.BusinessOwnerInformation.numberOfContractorFte`
*   `results[]."Number of Supported Users Per Month"`: `SystemDetail.BusinessOwnerInformation.numberOfSupportedUsersPerMonth`
*   `results[]."Beneficiary Information"`: `SystemDetail.BusinessOwnerInformation.beneficiaryInformation` (Pipe-delimited String to String Array)
*   `results[]."Edit Beneficiary Information"`: `SystemDetail.BusinessOwnerInformation.editBeneficiaryInformation` (String to Boolean)
*   `results[]."System has UI"`: `SystemDetail.BusinessOwnerInformation.508UserInterface`
*   `results[]."Cloud Migration Plan"`: `SystemDetail.DataCenterHosting.movingToCloud`
*   `results[]."Cloud Migrated Date"`: `SystemDetail.DataCenterHosting.movingToCloudDate` (String "MM/dd/yyyy" to Date)
*   `results[]."Hosted on Cloud"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."System Customization"`: `SystemDetail.SystemMaintainerInformation.systemCustomization`
*   `results[]."Front End Access Type"`: `SystemDetail.SystemMaintainerInformation.frontendAccessType`
*   `results[]."CMS System Access"`: `SystemDetail.SystemMaintainerInformation.netAccessibility`
*   `results[]."IP Enabled Asset Count"`: `SystemDetail.SystemMaintainerInformation.ipEnabledAssetCount` (String to BigInteger)
*   `results[]."Percent IPV6"`: `SystemDetail.SystemMaintainerInformation.ip6EnabledAssetPercent`
*   `results[]."IPV6 Transition Plan"`: `SystemDetail.SystemMaintainerInformation.ip6TransitionPlan`
*   `results[]."Hard Coded IP Address"`: `SystemDetail.SystemMaintainerInformation.hardCodedIpAddress` (String to Boolean)
*   `results[]."E-Cap Participation"`: `SystemDetail.SystemMaintainerInformation.ecapParticipation` (String to Boolean)
*   `results[]."Start Date"`: `SystemDetail.SystemMaintainerInformation.systemProductionDate` (String "MM/dd/yyyy" to Date)
*   `results[]."Development Complete Percent"`: `SystemDetail.SystemMaintainerInformation.devCompletionPercent`
*   `results[]."Development Work Still Underway"`: `SystemDetail.SystemMaintainerInformation.devWorkDescription`
*   `results[]."Agile Methodology Use"`: `SystemDetail.SystemMaintainerInformation.agileUsed` (String to Boolean)
*   `results[]."Deployment Frequency"`: `SystemDetail.SystemMaintainerInformation.deploymentFrequency`
*   `results[]."Next Major Tech Refresh Date"`: `SystemDetail.SystemMaintainerInformation.majorRefreshDate` (String "MM/dd/yyyy" to Date)
*   `results[]."Retire or Replace"`: `SystemDetail.SystemMaintainerInformation.plansToRetireReplace`
*   `results[]."Retire or Replace Date"`: `SystemDetail.SystemMaintainerInformation.yearToRetireReplace`
*   `results[]."Planned Retirement Quarter"`: `SystemDetail.SystemMaintainerInformation.quarterToRetireReplace`
*   `results[]."Business Artifacts on Demand"`: `SystemDetail.SystemMaintainerInformation.businessArtifactsOnDemand` (String to Boolean)
*   `results[]."Test Script on Demand"`: `SystemDetail.SystemMaintainerInformation.testScriptsOnDemand` (String to Boolean)
*   `results[]."Test Reports on Demand"`: `SystemDetail.SystemMaintainerInformation.testReportsOnDemand` (String to Boolean)
*   `results[]."Test Plan on Demand"`: `SystemDetail.SystemMaintainerInformation.testPlanOnDemand` (String to Boolean)
*   `results[]."Source Code on Demand"`: `SystemDetail.SystemMaintainerInformation.sourceCodeOnDemand` (String to Boolean)
*   `results[]."Design on Demand"`: `SystemDetail.SystemMaintainerInformation.systemDesignOnDemand` (String to Boolean)
*   `results[]."Ops and Maintenance Plans on Demand"`: `SystemDetail.SystemMaintainerInformation.omDocumentationOnDemand` (String to Boolean)
*   `results[]."Requirements on Demand"`: `SystemDetail.SystemMaintainerInformation.systemRequirementsOnDemand` (String to Boolean)
*   `results[]."Business Artifact Location"`: `SystemDetail.SystemMaintainerInformation.recordsManagementBucket` (Newline-delimited String to String Array)
*   `results[]."Test Script Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Test Reports Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Test Plan Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Souce Code Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."System Design Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Ops and Maintenance Plan Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Requirements Location"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Records Management Format"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Records Management Format Other"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Records Management Metadata"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Records Management Approved Schedule"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Records Management Disposal"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."System Data Authoritative Source"`: `SystemDetail.SystemMaintainerInformation.authoritativeDatasource`
*   `results[]."System Data Location"`: `SystemDetail.SystemMaintainerInformation.systemDataLocation` (Pipe-delimited String to String Array)
*   `results[]."System Data Location Notes"`: `SystemDetail.SystemMaintainerInformation.systemDataLocationNotes`
*   `results[]."Deployment AdHoc Frequency"`: `SystemDetail.SystemMaintainerInformation.adHocAgileDeploymentFrequency`
*   `results[]."Locally Stored User Info"`: `SystemDetail.SystemMaintainerInformation.locallyStoredUserInformation` (String to Boolean)
*   `results[]."MFA Method"`: `SystemDetail.SystemMaintainerInformation.multifactorAuthenticationMethod` (Pipe-delimited String to String Array)
*   `results[]."MFA Other"`: `SystemDetail.SystemMaintainerInformation.multifactorAuthenticationMethodOther`
*   `results[]."Network Traffic Encryption Management"`: `SystemDetail.SystemMaintainerInformation.networkTrafficEncryptionKeyManagement`
*   `results[]."Data At Rest Encryption Management"`: `SystemDetail.SystemMaintainerInformation.dataAtRestEncryptionKeyManagement`
*   `results[]."No Persistent Records Flag"`: `SystemDetail.SystemMaintainerInformation.noPersistentRecordsFlag` (String to Boolean)
*   `results[]."Records Management Disposal Location"`: `SystemDetail.SystemMaintainerInformation.recordsManagementDisposalLocation`
*   `results[]."Records Management Disposal Plan"`: `SystemDetail.SystemMaintainerInformation.recordsManagementDisposalPlan`
*   `results[]."Records Under Legal Hold"`: `SystemDetail.SystemMaintainerInformation.recordsUnderLegalHold` (String to Boolean)
*   `results[]."Legal Hold Case Name"`: `SystemDetail.SystemMaintainerInformation.legalHoldCaseName`
*   `results[]."No Major Refresh Flag"`: `SystemDetail.SystemMaintainerInformation.noMajorRefresh` (String to Boolean)
*   `results[]."No Planned Major Refresh Flag"`: `SystemDetail.SystemMaintainerInformation.noPlannedMajorRefresh` (String to Boolean)
*   `results[]."API Developed"`: `SystemDetail.SoftwareProductDetails.apisDeveloped`
*   `results[]."API Data Area"`: `SystemDetail.SoftwareProductDetails.apiDataArea` (Newline-delimited String to String Array)
*   `results[]."API Accessibility"`: `SystemDetail.SoftwareProductDetails.apisAccessibility`
*   `results[]."Does the API use FHIR"`: `SystemDetail.SoftwareProductDetails.apiFHIRUse`
*   `results[]."Does the API use FHIR Other"`: `SystemDetail.SoftwareProductDetails.apiFHIRUseOther`
*   `results[]."System has API Gateway"`: `SystemDetail.SoftwareProductDetails.systemHasApiGateway` (String to Boolean)
*   `results[]."Artificial Intelligence Plan"`: `SystemDetail.SoftwareProductDetails.aiPlan`
*   `results[]."Artificial Intelligence"`: `SystemDetail.SoftwareProductDetails.systemAiType` (Newline-delimited String to String Array)
*   `results[]."Artificial Intelligence - OTHER Description"`: `SystemDetail.SoftwareProductDetails.systemAiTypeOther`
*   `results[]."AI Project Life Cycle Stage"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."No Contracts Flag"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."No Sub Systems Flag"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."No URLs Flag"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Next System Survey"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."Current System Survey"`: No direct mapping visible to the main `SystemDetail` or its sub-documents from the provided files.
*   `results[]."ATO Effective Date"`: `SystemDetail.atoEffectiveDate` (String "MM/dd/yyyy" to Date)
*   `results[]."ATO Expiration Date"`: `SystemDetail.atoExpirationDate` (String "MM/dd/yyyy" to Date)
*   `results[]."System Ownership"`: `SystemDetail.BusinessOwnerInformation.systemOwnership`

```