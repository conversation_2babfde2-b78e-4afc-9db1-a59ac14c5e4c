# Webmethods Service Explanation: CmsEadgCedarCoreApi domainModelLevelFindList

This document provides a detailed explanation of the Webmethods service `domainModelLevelFindList`, focusing on its business purpose, technical implementation, data structures, and error handling. This service is designed to retrieve hierarchical level information for a specified domain model, with its core data originating from a global Webmethods configuration variable rather than a direct database query. The primary challenge for your TypeScript porting project will be to accurately map the data transformation logic and replicate the error handling mechanisms.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `domainModelLevelFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `domainModelLevelFindList` service's business purpose is to provide the defined hierarchy levels for a given "domain model". Imagine a system where different "models" (e.g., "Organization", "Project", "System") have predefined sub-levels (e.g., Organization has "Division", "Department"; Project has "Phase", "Task"). This service, given a model name, returns those associated levels.

The service expects one input parameter:

*   `model` (string): The name of the specific reference model for which to return hierarchy levels. This value is expected to be one of the model names defined in the internal configuration.

The service produces the following output:

*   `_generatedResponse` (document type: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DomainModelLevelFindResponse`): This contains the list of domain model levels and their count.

Key validation rules for this service include:

*   The `model` input parameter is mandatory. If it is missing or null, the service returns a "Bad Request" error.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods services are constructed using a visual programming paradigm known as "Flow" services. Here's how the key elements translate to familiar programming concepts:

*   **SEQUENCE**: Analogous to a block of code or a function body in procedural programming. Steps within a sequence are executed in order. A `SEQUENCE` with `FORM="TRY"` acts as a `try` block, catching any errors that occur within it.
*   **BRANCH**: Similar to a `switch` statement or a series of `if-else if-else` conditions. When `LABELEXPRESSIONS="true"`, the `NAME` attribute of the child `SEQUENCE` elements contains boolean expressions that are evaluated. The first `SEQUENCE` whose expression evaluates to `true` is executed. The `$default` branch (if present) is executed if no other condition is met.
*   **MAP**: This element is used for data transformation and manipulation. It allows you to define how data flows from source variables to target variables within the service's "pipeline" (the in-memory data context).
    *   **MAPSET**: Assigns a literal value to a variable.
    *   **MAPCOPY**: Copies the value of one variable to another.
    *   **MAPDELETE**: Removes a variable from the pipeline. This is crucial for memory management and preventing sensitive data from persisting longer than needed.
*   **INVOKE**: Represents a call to another service (function). Webmethods has many built-in "public" services (`pub.string`, `pub.flow`, etc.) and custom services developed within packages (`cms.eadg.utils.api`). `VALIDATE-IN` and `VALIDATE-OUT` refer to input/output validation rules for the invoked service.
*   **TRY/CATCH**: Implements error handling. The `TRY` block encapsulates the main business logic. If any step within the `TRY` block fails, execution immediately transfers to the corresponding `CATCH` block. This is comparable to `try { ... } catch (e) { ... }` in other languages.
*   **LOOP**: Iterates over elements in an array. The `IN-ARRAY` attribute specifies the array to iterate.
*   **EXIT**: Used to control flow. `EXIT FROM="$parent"` means exit the enclosing block (e.g., a `SEQUENCE` or `LOOP`). `SIGNAL="FAILURE"` or `SIGNAL="SUCCESS"` indicate whether the exit is due to an error or successful completion. `SIGNAL="SUCCESS"` when used within a `LOOP` essentially acts as a `break` statement, stopping the iteration.
*   **Webmethods Pipeline**: This is the context (an `IData` object in Java terms) that holds all variables (inputs, outputs, and intermediate data) that are available to the current service flow. Data is passed between steps by adding or removing fields from this pipeline.
*   **Global Variables**: Configuration values stored at the Integration Server level. They are accessed using `%variable.name%`. In this service, `%cedar.core.domain.model.levels%` is a global variable.

## Database Interactions

Based on the provided XML files for `domainModelLevelFindList` and its immediate dependencies, **this service does not perform any direct database interactions**.

The data representing the "domain model levels" is retrieved from a Webmethods **global variable** named `%cedar.core.domain.model.levels%`. This means the data is pre-configured and stored within the Webmethods Integration Server's environment, rather than being queried from a relational database at runtime by this specific service. Therefore, there are no SQL tables, views, or stored procedures used by this service.

## External API Interactions

The `domainModelLevelFindList` service does not interact with any external APIs (e.g., REST or SOAP services hosted outside of this Webmethods Integration Server instance).

It primarily invokes other internal Webmethods services for common utility functions:

*   `pub.string:tokenize`: A built-in service to split a string into an array of substrings based on a delimiter.
*   `pub.list:sizeOfList`: A built-in service to get the size of a list (array).
*   `pub.math:toNumber`: A built-in service to convert a string to a number.
*   `pub.flow:getLastError`: A built-in service to retrieve information about the last error that occurred in the flow.
*   `pub.flow:setResponse2`: A built-in service to set the HTTP response body and content type.
*   `pub.flow:setResponseCode`: A built-in service to set the HTTP response status code and reason phrase.
*   `pub.json:documentToJSONString`: A built-in service to convert a Webmethods document (structured data) into a JSON string.
*   `pub.xml:documentToXMLString`: A built-in service to convert a Webmethods document into an XML string.
*   `cms.eadg.utils.api:handleError`: A custom utility service within the same Webmethods environment for standardizing error responses.
*   `cms.eadg.utils.api:setResponse`: A custom utility service within the same Webmethods environment for formatting and setting general API responses (both success and error).
*   `cms.eadg.utils.math:toNumberIf`: A custom utility service within the same Webmethods environment to safely convert a string to a number only if it's not null or empty.

## Main Service Flow

The `domainModelLevelFindList` service executes the following steps:

1.  **Error Handling (TRY Block)**: The entire main logic is enclosed in a `SEQUENCE` with `FORM="TRY"`, allowing for centralized error handling in the `CATCH` block.

2.  **Input Validation**:
    *   A `BRANCH` statement is used to check if the mandatory `model` input parameter is null.
    *   `SEQUENCE NAME="%model% == $null"`: If `model` is null, this branch executes.
        *   A `MAP` step sets details for a `SetResponse` document type:
            *   `responseCode`: "400"
            *   `responsePhrase`: "Bad Request"
            *   `result`: "error"
            *   `message`: "Please provide required parameter 'model'"
            *   `format`: "application/json"
        *   An `EXIT` step (`FROM="$parent" SIGNAL="FAILURE"`) immediately stops the execution of the main service flow, preventing further processing and returning the error.

3.  **Retrieve Domain Levels Configuration**:
    *   A `MAP` step copies the value from the global variable `%cedar.core.domain.model.levels%` into a local pipeline variable named `domainLevelsString`.
    *   The `SetResponse` variable (used for the validation error above) is deleted to clean up the pipeline if validation passed.

4.  **Parse Master List of Domain Models**:
    *   `INVOKE pub.string:tokenize`: The `domainLevelsString` is split using `|` as a delimiter. This likely separates entries for different domain models (e.g., "modelA;level1;level2|modelB;levelX;levelY" would become ["modelA;level1;level2", "modelB;levelX;levelY"]). The resulting array is initially `valueList`.
    *   An output `MAP` step copies `valueList` to `domainLevels` and cleans up `valueList` and the temporary input variables from `tokenize`. So `domainLevels` now holds an array of strings, each representing a full definition for a domain model.

5.  **Find and Process the Requested Domain Model**:
    *   `LOOP IN-ARRAY="/domainLevels"`: The service iterates through each entry in the `domainLevels` array.
    *   Inside the loop:
        *   `INVOKE pub.string:tokenize`: Each `domainLevels` string (representing a single model's definition, e.g., "modelName;level1;level2") is tokenized again, this time using `;` as the delimiter. The result is stored in `valueList` (e.g., ["modelName", "level1", "level2"]).
        *   `BRANCH LABELEXPRESSIONS="true"`: This `BRANCH` checks if the current model definition matches the `model` parameter provided in the input.
            *   `SEQUENCE NAME="%model% == %valueList[0]%"`: This condition checks if the input `model` string is equal to the first element (`valueList[0]`) of the newly tokenized `valueList` (which is expected to be the model name itself).
            *   If the condition is true (a match is found):
                *   `MAP`: The entire `valueList` (containing the model name and its levels) is copied to `_generatedResponse/DomainModelLevels`.
                *   `INVOKE pub.list:sizeOfList`: Calculates the number of elements in the `valueList` and stores it in `size`. This represents the number of levels for the matched model (including the model name itself).
                *   `INVOKE cms.eadg.utils.math:toNumberIf`: Converts the `size` (which is a string) to a `java.lang.Long` and maps it to `_generatedResponse/count`. This utility ensures a safe conversion, handling cases where `size` might be empty or null (though not expected here).
                *   `EXIT FROM="$parent" SIGNAL="SUCCESS"`: This terminates the `LOOP` (the `$parent` here refers to the `LOOP`), as the correct domain model has been found and processed.

6.  **Cleanup**:
    *   A final `MAP` step at the end of the `TRY` block cleans up intermediate variables (`model`, `domainLevels`, `valueList`).

7.  **Error Handling (CATCH Block)**:
    *   If any error occurs within the `TRY` block:
        *   `INVOKE pub.flow:getLastError`: Retrieves the error details, including the error message.
        *   `MAP`: Deletes any partially built `_generatedResponse` to ensure a clean error response is sent.
        *   `INVOKE cms.eadg.utils.api:handleError`: This utility service is called to standardize the error response format and set the appropriate HTTP status code (typically 500 Internal Server Error for unhandled exceptions).

## Dependency Service Flows

The main service `domainModelLevelFindList` relies on several utility services to perform common tasks and standardize responses.

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This service is designed to centralize and standardize the handling of errors within the API. It ensures that all error responses follow a consistent format and correctly set HTTP status codes.
    *   **Integration with Main Flow**: It is invoked within the `CATCH` block of `domainModelLevelFindList`. This means `handleError` is called whenever an unexpected exception occurs during the execution of `domainModelLevelFindList`.
    *   **Input/Output Contract**:
        *   **Input**: Takes `SetResponse` (an optional document reference that might contain pre-defined error details from the caller, like in the initial validation for `model`) and `lastError` (a document reference `pub.event:exceptionInfo` populated by `pub.flow:getLastError` with details of the exception).
        *   **Output**: Internally calls `cms.eadg.utils.api:setResponse` to format the final HTTP response.
    *   **Specialized Processing**: It checks if a `SetResponse` object is already present in the pipeline (meaning the calling service might have initiated an error response, like a 400 Bad Request).
        *   If `SetResponse` is `$null` (no custom error response initiated), it populates a default `SetResponse` for a `500 Internal Server Error`, using the error message from `lastError`.
        *   If `SetResponse` is already present, it passes it directly to `setResponse` for final formatting.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is responsible for constructing the final HTTP response, including mapping the response body to either JSON or XML format and setting the HTTP status code and `Content-Type` header.
    *   **Integration with Main Flow**: It is called directly by `domainModelLevelFindList` for successful responses and validation errors, and it is also called by `cms.eadg.utils.api:handleError` for general exceptions.
    *   **Input/Output Contract**:
        *   **Input**: Takes a `SetResponse` document type, which contains `responseCode`, `responsePhrase`, `result`, `message`, and `format` (e.g., "application/json", "application/xml").
        *   **Output**: Sets the HTTP response code and body.
    *   **Specialized Processing**:
        *   Maps the `result` and `message` from `SetResponse` to a generic `Response` document type (`cms.eadg.utils.api.docs:Response`), which forms the core of the response body.
        *   Uses a `BRANCH` to decide the serialization format based on `SetResponse/format`:
            *   **`application/json`**: Invokes `pub.json:documentToJSONString` to convert the `Response` document into a JSON string.
            *   **`application/xml`**: Wraps the `Response` document within a `ResponseRooted` document (`cms.eadg.utils.api.docs:ResponseRooted`) to provide a single root element for the XML, then invokes `pub.xml:documentToXMLString` to convert it to an XML string.
        *   Finally, it invokes `pub.flow:setResponseCode` to set the HTTP status code and `pub.flow:setResponse2` to set the actual response body string and the `Content-Type` header (based on `SetResponse/format`).

*   **`cms.eadg.utils.math:toNumberIf`**:
    *   **Purpose**: This is a utility service to safely convert a string representation of a number to a numeric object (e.g., `java.lang.Long`), but only if the input string is not null or empty. This prevents conversion errors on optional empty fields.
    *   **Integration with Main Flow**: It is invoked by `domainModelLevelFindList` to convert the count of domain levels (which `pub.list:sizeOfList` returns as a string) into a numeric type (`java.lang.Long`) for the output `_generatedResponse/count` field.
    *   **Input/Output Contract**:
        *   **Input**: `num` (string to be converted), `convertAs` (string, specifying the target Java numeric type, e.g., "java.lang.Long").
        *   **Output**: `num` (object, the converted numeric value).
    *   **Specialized Processing**: It uses a `BRANCH` with a `LABELEXPRESSION` (`%num% != $null && %num% != ''`) to ensure `pub.math:toNumber` is only called if `num` actually contains a value. If `num` is null or empty, no conversion happens, and `num` remains null in the output, which is then mapped further up the chain.

## Data Structures and Types

The service processes and returns data using specific Webmethods document types, which are analogous to structured data objects or interfaces in other programming languages.

*   **Input Data Model**:
    *   `model` (string): This is the sole input field, representing the domain model name. It is mandatory.

*   **Output Data Model**:
    *   `_generatedResponse` (document reference to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:DomainModelLevelFindResponse`):
        *   `count` (object, specifically `java.math.BigInteger` according to its `node.ndf`, but dynamically converted to `java.lang.Long` by the `toNumberIf` service): Represents the total number of hierarchy levels associated with the requested domain model.
        *   `DomainModelLevels` (string array): An array containing the names of the hierarchy levels for the requested domain model.

*   **Utility/Error Response Data Models**:
    *   `cms.eadg.utils.api.docs:SetResponse`: A flexible document type used internally to configure the HTTP response properties before sending. It includes:
        *   `responseCode` (string): HTTP status code (e.g., "200", "400", "500").
        *   `responsePhrase` (string): HTTP reason phrase (e.g., "OK", "Bad Request", "Internal Server Error").
        *   `result` (string): A custom API status, usually "success" or "error".
        *   `message` (string array): An array of messages providing more details about the response (e.g., error messages).
        *   `format` (string): The desired content type for the response body (e.g., "application/json", "application/xml").
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` (and `cms.eadg.utils.api.docs:Response`): A basic document type for the API response body, typically containing:
        *   `result` (string): "success" or "error".
        *   `message` (string array): Detailed messages.
    *   `cms.eadg.utils.api.docs:ResponseRooted`: A wrapper document type specifically used when generating XML responses to ensure there's a single root element. It contains `Response`.
    *   `pub.event:exceptionInfo`: A built-in document type used by `pub.flow:getLastError` to provide details about a caught exception, including the error message.

*   **Data Transformation Logic**: The service retrieves a single global string variable. This string is structured with `|` delimiters separating different model definitions, and each model definition uses `;` delimiters to separate the model name from its associated levels. For example, a global variable value like "Org;Division;Department|Project;Phase;Task" would yield "Org" with levels ["Division", "Department"] or "Project" with levels ["Phase", "Task"] depending on the `model` input.

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` blocks and custom utility services to ensure consistent error responses.

*   **Missing Input Parameter (`model`)**:
    *   **Scenario**: The `model` input parameter is null or empty.
    *   **Handling**: This is a specific validation check at the beginning of the service.
    *   **Response Code**: `400 Bad Request`
    *   **Error Message Format**:
        ```json
        {
          "result": "error",
          "message": ["Please provide required parameter 'model'"]
        }
        ```
    *   **HTTP Response Headers**: `Content-Type: application/json`

*   **Unhandled Exceptions (Internal Server Error)**:
    *   **Scenario**: Any other unexpected error occurs during the service execution (e.g., issues with string tokenization, unexpected data format in the global variable).
    *   **Handling**: The `CATCH` block is triggered, which invokes `pub.flow:getLastError` to retrieve exception details and then calls `cms.eadg.utils.api:handleError` to process the error.
    *   **Response Code**: `500 Internal Server Error`
    *   **Error Message Format**:
        ```json
        {
          "result": "error",
          "message": ["<Technical error message from Webmethods>"]
        }
        ```
    *   **HTTP Response Headers**: `Content-Type: application/json` (default, but can be configured to XML)

The `cms.eadg.utils.api:setResponse` service is the central point for formatting both success and error responses. It dynamically sets the `Content-Type` header (either `application/json` or `application/xml`) based on the `format` field within the `SetResponse` document, and it uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to apply the determined HTTP status and body to the outgoing response.