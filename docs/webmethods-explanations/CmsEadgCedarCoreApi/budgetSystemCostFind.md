# Webmethods Service Explanation: CmsEadgCedarCoreApi budgetSystemCostFind

The `budgetSystemCostFind` service is designed to retrieve annual budget and actual cost data for systems. It provides the flexibility to fetch data for a specific system, identified by its `systemId`, or to retrieve cost data for all available systems if no specific ID is provided. The primary business purpose of this service is to offer an API endpoint for applications to query system-related budget information, likely for reporting, analysis, or integration with other financial or asset management systems.

The service accepts a single optional input parameter:
*   `systemId` (string, optional): This parameter, if provided, specifies the unique identifier of a system. When present, the service will return budget and actual cost data only for that particular system. If this parameter is omitted, the service retrieves data for all systems.

The expected output is a structured response containing a count of records and an array of `BudgetActualCost` objects. Each object in the array provides details about a system's budget and actual cost for a specific fiscal year.

Key validation rules involve checking the presence of the `systemId` parameter to determine which underlying database query to execute. Error handling is generalized, converting any unhandled exceptions into a standardized API error response.

* Package Name: `CmsEadgCedarCoreApi`
* Service Name: `budgetSystemCostFind`
* Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm, where services are constructed by arranging and configuring predefined building blocks called "flow steps." These flow steps control the execution flow and manipulate data within the service pipeline (a runtime memory area where data is passed between steps).

*   **SEQUENCE**: A `SEQUENCE` block is similar to a standard sequential block of code in other programming languages. Steps within a sequence execute in the order they appear. A `SEQUENCE` can be configured to `EXIT-ON="FAILURE"`, meaning if any step within it fails, the entire sequence will terminate, and control will pass to the next available error handling block (like a `CATCH` block). A `FORM="TRY"` attribute indicates the start of a try block for error handling.
*   **BRANCH**: A `BRANCH` step is a conditional control flow element, akin to an `if/else if/else` or `switch` statement. It evaluates an expression (`LABELEXPRESSIONS="true"`) or a specific variable (`SWITCH="/variableName"`) and directs execution to the first `SEQUENCE` (or other flow step) whose `NAME` matches the evaluated value. A `SEQUENCE` with `NAME="$default"` acts as the `else` or default case if no other conditions are met.
*   **MAP**: A `MAP` step is used for data transformation and manipulation within the service pipeline. It allows copying data from one variable to another, deleting variables, or setting static values.
    *   `MAP MODE="INPUT"`: Maps data *into* a service that is about to be invoked.
    *   `MAP MODE="OUTPUT"`: Maps data *from* a service that just finished executing, typically to restructure or rename variables for the subsequent steps in the flow.
    *   `MAP MODE="STANDALONE"`: Maps data within the current pipeline without being tied to an `INVOKE` step.
*   **INVOKE**: An `INVOKE` step is used to call another Webmethods service (either a built-in one, a custom flow service, or an adapter service). It's equivalent to calling a function or method in other programming languages.
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flow services support structured error handling using `TRY` and `CATCH` sequences. A `SEQUENCE` with `FORM="TRY"` encloses the main business logic. If an error occurs within this `TRY` block, execution immediately jumps to a `SEQUENCE` with `FORM="CATCH"`. This `CATCH` block typically contains logic to handle the error, such as logging, setting an appropriate error response, and cleaning up resources.

## Database Interactions

This service interacts with a single SQL database through JDBC adapter services.

The database connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`. The connection details, decoded from the XML, indicate a SQL Server database:
*   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
*   **Port Number**: `1433`
*   **Database Name**: `Sparx_Support`
*   **User**: `sparx_dbuser`
*   **Transaction Type**: `NO_TRANSACTION` (meaning each query runs in its own transaction)

The SQL operations are performed against a database **view** named `dbo.Sparx_System_Annual_Cost`.

Two main database operations are performed:
1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCost`**: This adapter service executes a `SELECT` query on the `Sparx_System_Annual_Cost` view, filtering the results by a specific system GUID.
    *   **Database View**: `dbo.Sparx_System_Annual_Cost`
    *   **SQL Query**: Equivalent to `SELECT "System Name", "Sparx System GUID", "Sparx System ID", Acronym, Cost, Year FROM dbo.Sparx_System_Annual_Cost t1 WHERE t1."Sparx System GUID" = ?`
    *   **Input Parameter Mapping**: The `systemId` input to the main service is mapped to the `"Sparx System GUID"` parameter of this query.
2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCostAll`**: This adapter service also executes a `SELECT` query on the `Sparx_System_Annual_Cost` view, but without any filters, retrieving all records.
    *   **Database View**: `dbo.Sparx_System_Annual_Cost`
    *   **SQL Query**: Equivalent to `SELECT "System Name", "Sparx System GUID", "Sparx System ID", Acronym, Cost, Year FROM dbo.Sparx_System_Annual_Cost t1`
    *   **Input Parameters**: This query takes no input parameters.

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external third-party business APIs. It leverages internal Webmethods utility services (`pub.flow`, `pub.json`, `pub.xml`, `cms.eadg.utils.api`) for flow control, response formatting, and error handling, but these are not considered external business API calls in this context.

## Main Service Flow

The `budgetSystemCostFind` service flow is structured as follows:

1.  **Main Execution Block (TRY)**: The entire core logic of the service is enclosed within a `TRY` block. This ensures that any unhandled error during execution will be caught by the subsequent `CATCH` block.

2.  **Conditional Data Retrieval (BRANCH)**:
    *   The first `BRANCH` step evaluates the `systemId` input parameter.
    *   **If `systemId` is not null (`%systemId% != $null`)**:
        *   The service invokes the adapter service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCost`.
        *   A `MAP` step preceding this invocation copies the `systemId` from the main service's input pipeline to the `"Sparx System GUID"` input field required by `getAnnualCost`.
        *   After `getAnnualCost` executes, an output `MAP` step cleans up the `getAnnualCostInput` and `systemId` variables from the pipeline, as they are no longer needed.
    *   **If `systemId` is null (`$default`)**:
        *   The service invokes the adapter service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCostAll`. This service retrieves all system annual cost records without any filtering.

3.  **Response Aggregation and Transformation (BRANCH)**:
    *   The second `BRANCH` step evaluates the `results` field from the output of the database query (`/getAnnualCostOutput/results`). This is implicitly checking if any records were returned.
    *   **If `results` is 0 (`0`)**: This indicates that no records were found.
        *   A `MAP` step (`NAME="0"`) sets the `count` field of the `_generatedResponse` (of type `BudgetActualSystemCostResponse`) to `0`. This creates an empty response object with a zero count.
    *   **If `results` is not 0 (`$default`)**: This means one or more records were returned.
        *   A `LOOP` step iterates over each record in the `getAnnualCostOutput/results` array. For each record, it maps the database columns to the fields of a `BudgetActualSystemCost` object, which is then added to the `BudgetActualCost` array within `_generatedResponse`.
        *   The `count` field of `_generatedResponse` is populated from the `Selected` field of `getAnnualCostOutput`, which represents the total number of records returned by the SQL query.

4.  **Cleanup (MAP)**: After the data retrieval and mapping, a final `MAP` step removes the `getAnnualCostOutput` variable from the pipeline, tidying up the service's context.

5.  **Error Handling (CATCH)**:
    *   If any error occurs within the initial `TRY` block, execution transfers to this `CATCH` block.
    *   The service first invokes `pub.flow:getLastError` to retrieve details about the error that occurred.
    *   Then, it invokes `cms.eadg.utils.api:handleError`. This utility service is responsible for converting the raw error information into a standardized API error response. It sets the HTTP status code (typically 500 for internal server errors) and a corresponding message, then formats the error as a JSON or XML string for the client.

## Dependency Service Flows

The main service `budgetSystemCostFind` depends on several other services, primarily for database interaction and common API utilities.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCost`**:
    *   **Purpose**: This is a JDBC adapter service specifically configured to query the `dbo.Sparx_System_Annual_Cost` database view. Its purpose is to retrieve the annual cost data for a *single* system based on its unique identifier (GUID).
    *   **Integration**: It's invoked by the main service when a `systemId` is provided. The `systemId` input is directly passed as a filter to the underlying SQL query.
    *   **Input/Output Contract**: It takes `"Sparx System GUID"` as input and returns a record containing query `results` (an array of records for each row) and `Selected` (the count of results).
    *   **Specialized Processing**: It executes a parameterized `SELECT` SQL query against the `dbo.Sparx_System_Annual_Cost` view with a `WHERE` clause: `t1."Sparx System GUID" = ?`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budgetSystemCost:getAnnualCostAll`**:
    *   **Purpose**: Similar to `getAnnualCost`, this is also a JDBC adapter service querying the `dbo.Sparx_System_Annual_Cost` database view. Its purpose is to retrieve the annual cost data for *all* systems.
    *   **Integration**: It's invoked by the main service when no `systemId` is provided, serving as the default behavior to fetch all records.
    *   **Input/Output Contract**: It takes no specific inputs (apart from potential override credentials) and returns the same output structure (`results` array and `Selected` count) as `getAnnualCost`.
    *   **Specialized Processing**: It executes a simple `SELECT` SQL query against the `dbo.Sparx_System_Annual_Cost` view without any `WHERE` clause.

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This is a centralized utility service for standardizing error responses across APIs. It takes raw error information (from `pub.flow:getLastError`) and formats it into a consistent structure for clients.
    *   **Integration**: It is invoked within the `CATCH` block of the main service whenever an unexpected error occurs during the service's execution.
    *   **Input/Output Contract**: It takes a `lastError` (from `pub.event:exceptionInfo`) and an optional `SetResponse` document (to customize the error response). It primarily populates internal pipeline variables used by `setResponse` for the final output.
    *   **Specialized Processing**: It defaults to setting the HTTP response code to `500` and the message to "Internal Server Error" if no specific `SetResponse` details are provided. It then invokes `cms.eadg.utils.api:setResponse` to construct and send the actual HTTP response.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This utility service is responsible for generating the final HTTP response to the client. It formats the response payload (either JSON or XML) and sets the appropriate HTTP status code and content type.
    *   **Integration**: It is invoked by `handleError` (for error responses) and would typically be called by other main services for successful responses.
    *   **Input/Output Contract**: It takes a `SetResponse` document (containing response code, phrase, result status, messages, and format) and constructs the final output.
    *   **Specialized Processing**: It uses `pub.json:documentToJSONString` or `pub.xml:documentToXMLString` to serialize the response document based on the desired format (`application/json` or `application/xml`) specified in `SetResponse/format`. It then uses `pub.flow:setResponseCode` to set the HTTP status and `pub.flow:setResponse2` to send the content type and response body.

## Data Structures and Types

The service primarily uses and produces the following key data structures:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BudgetActualSystemCost`**:
    *   This document type defines the structure for a single item of system cost data in the output array.
    *   **Fields**:
        *   `FiscalYear` (string, optional): Represents the fiscal year associated with the cost.
        *   `ActualSystemCost` (string, optional): Represents the actual cost of the system for that year.
        *   `systemId` (string, optional): The GUID of the system.
    *   **Field Validation Rules**: All fields are optional and are of type string. No explicit length or format validations are defined in the `node.ndf`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BudgetActualSystemCostResponse`**:
    *   This document type serves as the root container for the service's successful output. It holds a count of the items returned and an array of `BudgetActualCost` objects.
    *   **Fields**:
        *   `count` (object - BigInteger, nillable): The total number of `BudgetActualCost` records returned.
        *   `BudgetActualCost` (record reference to `BudgetActualSystemCost`, array `[1]`, optional): An array containing the individual system cost records.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`**:
    *   A generic response document type used for error messages.
    *   **Fields**:
        *   `result` (string, optional): Typically "success" or "error".
        *   `message` (string array `[1]`, optional): An array of human-readable messages, often used to convey error details.

*   **`cms.eadg.utils.api.docs:SetResponse`**:
    *   An internal utility document used by `cms.eadg.utils.api:handleError` and `cms.eadg.utils.api:setResponse` to pass standardized response details (HTTP code, phrase, content type, and messages) throughout the utility flow.

The following data mapping occurs from the `dbo.Sparx_System_Annual_Cost` view to the `BudgetActualSystemCost` output object:

*   `"Sparx System GUID"` (Database Column): `systemId` (Output Object Property)
*   `Cost` (Database Column): `ActualSystemCost` (Output Object Property)
*   `Year` (Database Column): `FiscalYear` (Output Object Property)

The `count` property in the `BudgetActualSystemCostResponse` is directly mapped from the `Selected` field returned by the database adapter services, which indicates the number of rows selected. Other database columns like `"System Name"`, `"Sparx System ID"`, and `Acronym` are retrieved by the database adapters but are not mapped to the final `BudgetActualSystemCost` output object; they are effectively discarded from the pipeline before the final response is constructed.

The other referenced document types (`ObjectByReportResponse`, `mission_essential_function`, `software_product`, `SystemDetail`, `ResponseRooted`, `exceptionInfo`) are not directly used as primary input/output types for this specific `budgetSystemCostFind` service but might be part of other services within the broader Webmethods project or internal Webmethods system documents (`exceptionInfo`).

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` block structure, delegating the actual error response formatting to a centralized utility.

*   **Error Scenarios Covered**: Any unhandled exception that occurs during the execution of the main `TRY` block (e.g., database connection issues, SQL execution errors, data mapping errors) will trigger the `CATCH` block.
*   **Error Handling Flow**:
    1.  Upon an error, `pub.flow:getLastError` is invoked to retrieve detailed information about the exception (e.g., error message, stack trace).
    2.  This error information is then passed to `cms.eadg.utils.api:handleError`.
    3.  The `handleError` service, by default, prepares an error response with an HTTP status code of `500 Internal Server Error`. It populates the `message` field of the generic `Response` document with the actual error message from the exception.
*   **HTTP Response Codes Used**:
    *   `500 Internal Server Error`: This is the default response code set by `cms.eadg.utils.api:handleError` for any uncaught exceptions within the service.
    *   The service's `node.ndf` also defines potential output fields for `400` (Bad Request), `401` (Unauthorized), and `500` (Internal Server Error) response types. While only `500` is explicitly set by `handleError` in the provided `flow.xml`, this indicates the API is designed to potentially return these other error codes under different circumstances (e.g., if input validation failures or authentication issues were handled explicitly before the main `TRY` block).
*   **Error Message Formats**: Error messages are typically returned in a standardized `Response` document type (which can be serialized as JSON or XML depending on the client's request or a default setting in `setResponse`). This `Response` document contains a `result` field (e.g., "error") and a `message` array detailing the error.
*   **Fallback Behaviors**: The `handleError` service acts as a centralized fallback. Instead of each service handling its errors uniquely, all errors are funneled through this utility, ensuring consistency in error responses across the API suite. This pattern promotes maintainability and easier client-side error parsing.