# Webmethods Service Explanation: CmsEadgCedarCoreApi systemSummaryFindList

This document provides a comprehensive explanation of the Webmethods service `systemSummaryFindList`, located within the `CmsEadgCedarCoreApi` package. It is designed to assist an experienced software developer unfamiliar with Webmethods in understanding its functionality, data flow, and underlying logic, with a focus on database column to JSON output property mapping for a TypeScript porting project.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `systemSummaryFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `systemSummaryFindList` service is designed to retrieve a list of system summaries from a backend database. Its primary business purpose is to provide an overview of various systems, allowing filtering based on their state, status, version, and relationships (e.g., belonging to a parent system). It also supports filtering by user roles and specific "include in survey" criteria. A key feature is the ability to request only system IDs and names for lightweight responses.

Input parameters for this service are:

*   `state` (string, optional): Filters systems by their current state (e.g., "Active", "Retired").
*   `status` (string, optional): Filters systems by their operational status.
*   `version` (string, optional): Filters systems by a specific version string.
*   `includeInSurvey` (boolean/object, optional): A flag indicating whether to include only systems eligible for a survey. This is converted to a string before database interaction.
*   `idsOnly` (boolean/object, optional): A flag which, if true, restricts the output to only the system ID and name, providing a minimal response.
*   `belongsTo` (string, optional): Filters systems that are sub-systems of the provided system ID.
*   `limit` (object - BigInteger, optional): Specifies the maximum number of systems to return. Defaults to 1000 if not provided.
*   `offset` (object - BigInteger, optional): Defines the starting position of the first record returned. Defaults to 0 if not provided.
*   `userName` (string, optional): Filters systems based on a specific user's EUA (Enterprise User Account) having an associated role.
*   `roleType` (string, optional): Used in conjunction with `userName` to filter systems by a specific role type (e.g., "Owner", "Maintainer").

The expected output is a structured JSON (or XML) response containing a `SystemSummaryResponse` object. This object includes a `count` of the systems found and an array of `SystemSummary` objects. Each `SystemSummary` object contains detailed information about a system, or just its ID and name if `idsOnly` was specified.

Key validation rules implemented in the service include:

*   Conversion of `includeInSurvey` from a boolean to a string ("true" or "false") before passing to the database.
*   Conditional execution of database queries based on the presence of `userName`, which determines if a role-based query is needed.
*   Mapping of raw database results (received as a JSON string) into a structured Webmethods document type, and then transforming it into the final `SystemSummary` objects.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow" services. These services are essentially executable workflows composed of various steps. Here's what some key elements represent in this service's context:

*   **SEQUENCE**: Analogous to a sequential block of code in traditional programming. Steps within a sequence execute one after another in the order they appear. If any step within a sequence fails, the entire sequence (and often the parent flow) fails unless explicitly handled. A `SEQUENCE` can also act as a `TRY` or `CATCH` block for error handling. In this service, the outermost `SEQUENCE` is a `TRY` block.
*   **BRANCH**: Similar to a `switch` statement or `if-else if-else` structure. A `BRANCH` evaluates a specified variable (the "switch variable") and executes a specific path (another `SEQUENCE` or `INVOKE`) based on its value. The `NAME` attribute of the child `SEQUENCE` or `INVOKE` node defines the value it matches. The `$null` name matches a null value, and `$default` acts as an `else` block, executing if no other branch matches.
*   **MAP**: A fundamental data transformation step. It's used to move, rename, delete, or transform data within the "pipeline" (Webmethods' term for its in-memory data context, similar to a shared data structure between steps).
    *   `MAPTARGET` defines the structure of the data after the mapping.
    *   `MAPSOURCE` defines the current data available for mapping.
    *   **MAPCOPY**: Copies data from a source field to a target field. This is like `target_field = source_field;`.
    *   **MAPSET**: Sets a literal value to a target field. This is like `target_field = "literal_value";`.
    *   **MAPDELETE**: Removes a field from the pipeline. This is crucial for pipeline management to avoid passing unnecessary data between steps and to clean up temporary variables.
*   **INVOKE**: Used to call another Webmethods service (either a built-in one or a custom one). It's like calling a function or method in traditional programming.
    *   `VALIDATE-IN` and `VALIDATE-OUT` specify whether input/output validation should occur. `$none` means no validation is performed.
    *   The `MAP MODE="INVOKEINPUT"` and `MAP MODE="INVOKEOUTPUT"` blocks within an `INVOKE` define how data is mapped to the invoked service's inputs and from its outputs, respectively.
*   **TRY/CATCH blocks**: Standard error handling mechanism. A `SEQUENCE` can be marked as `FORM="TRY"` or `FORM="CATCH"`. If an error occurs within a `TRY` block, execution immediately jumps to its associated `CATCH` block. This is analogous to `try { ... } catch (e) { ... }` in Java or TypeScript.
*   **LOOP**: Iterates over an array (list) in the pipeline. `IN-ARRAY` specifies the input array, and `OUT-ARRAY` specifies where the transformed elements will be accumulated. Inside the loop, `MAP` operations process each element. This is similar to a `for...of` loop or `array.forEach()` in TypeScript.

Input validation and branching logic in Webmethods flows are primarily handled by `MAP` steps (e.g., setting default values, converting types) and `BRANCH` steps (e.g., checking for the presence of `userName` to determine which database query to execute). The flow visually depicts these conditional paths.

## Database Interactions

The `systemSummaryFindList` service interacts with a SQL database using Webmethods JDBC Adapter services. These adapters simplify database operations by abstracting away the direct SQL command execution, allowing configuration through XML.

*   **Database Operations**: The service performs read operations by invoking stored procedures.
*   **Database Connection**: Both database adapter services (`getSystemSummaryListSP` and `getSystemSummaryListByRole`) use the `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans` JDBC connection. The connection details, although not explicitly shown in plain text, are configured within the `node.ndf` file of the connection and point to a SQL Server instance named `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` on port `1433`, connecting to the `Sparx_Support` database with username `sparx_dbuser`. It explicitly states "NO_TRANSACTION" implying no distributed transactions are used.

*   **SQL Queries / Stored Procedures Called**:
    1.  **`dbo.SP_Get_SystemList;1`**: This stored procedure is invoked when the `userName` input parameter is not provided (or is null). It likely retrieves a list of systems based on filters like `objectState`, `status`, `version`, `belongsTo`, and `includeInSurvey`.
    2.  **`dbo.SP_Get_SystemListRole;1`**: This stored procedure is invoked when the `userName` input parameter *is* provided. It adds filtering capabilities based on `personEUA` (mapped from `userName`) and `personRole` (mapped from `roleType`), in addition to the other system filters.

*   **Data Mapping between Service Inputs and Database Parameters**:

    *   **For `dbo.SP_Get_SystemList;1` (via `getSystemSummaryListSP`):**
        *   Service Input `state` maps to Stored Procedure parameter `@objectState` (NVARCHAR)
        *   Service Input `status` maps to Stored Procedure parameter `@status` (NVARCHAR)
        *   Service Input `version` maps to Stored Procedure parameter `@version` (NVARCHAR)
        *   Service Input `belongsTo` maps to Stored Procedure parameter `@belongsTo` (NVARCHAR)
        *   Service Input `strIncludeInSurvey` (derived from `includeInSurvey` boolean) maps to Stored Procedure parameter `@includeInSurvey` (NVARCHAR)
        *   Stored Procedure Output `@Outputjson` (LONGVARCHAR) is retrieved and mapped to `jsonString` in the pipeline.
        *   Stored Procedure Return Value `@RETURN_VALUE` (INTEGER) is retrieved and mapped to `@RETURN_VALUE` in the pipeline.

    *   **For `dbo.SP_Get_SystemListRole;1` (via `getSystemSummaryListByRole`):**
        *   Service Input `userName` maps to Stored Procedure parameter `@personEUA` (NVARCHAR)
        *   Service Input `roleType` maps to Stored Procedure parameter `@personRole` (NVARCHAR)
        *   Service Input `state` maps to Stored Procedure parameter `@objectState` (NVARCHAR)
        *   Service Input `status` maps to Stored Procedure parameter `@status` (NVARCHAR)
        *   Service Input `version` maps to Stored Procedure parameter `@version` (NVARCHAR)
        *   Service Input `belongsTo` maps to Stored Procedure parameter `@belongsTo` (NVARCHAR)
        *   Service Input `strIncludeInSurvey` (derived from `includeInSurvey` boolean) maps to Stored Procedure parameter `@includeInSurvey` (NVARCHAR)
        *   Stored Procedure Output `@Outputjson` (LONGVARCHAR) is retrieved and mapped to `jsonString` in the pipeline.
        *   Stored Procedure Return Value `@RETURN_VALUE` (INTEGER) is retrieved and mapped to `@RETURN_VALUE` in the pipeline.

The `limit` and `offset` parameters are passed as inputs to the main service but are not directly mapped to the stored procedures in the provided XML for the adapters. This implies that the stored procedures `SP_Get_SystemList` and `SP_Get_SystemListRole` might handle paging internally, or these parameters are implicitly handled by a wrapper around the adapter calls that is not detailed in the provided `flow.xml`. For accurate TypeScript porting, it's critical to confirm how these parameters are used in the stored procedure definitions.

## External API Interactions

Based on the provided Webmethods files, this service does not appear to make direct calls to external third-party APIs. Its primary interactions are with the internal database via JDBC adapters and other internal Webmethods services.

## Main Service Flow

The `systemSummaryFindList` service's flow executes as follows:

1.  **Initial Mapping and Input Preparation**:
    *   A `MAP` step is used to prepare query parameters.
    *   The `includeInSurvey` boolean input is deleted from the pipeline.
    *   An empty array of `SystemSummary` objects is initialized in the pipeline as `SystemSummary`. This serves as a placeholder or default.
    *   The service `cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString` is invoked to convert the `includeInSurvey` boolean value (if present) into a string (`"true"` or `"false"`) and store it as `strIncludeInSurvey`. This string is then used for the database calls.

2.  **Conditional Database Invocation**:
    *   A `BRANCH` step is used, switching on the `userName` input parameter.
    *   **If `userName` is null (`$null` branch)**:
        *   The service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryListSP` is invoked. This adapter calls the `dbo.SP_Get_SystemList;1` stored procedure.
        *   Inputs `state`, `status`, `version`, `belongsTo`, and `strIncludeInSurvey` are mapped to the corresponding `@objectState`, `@status`, `@version`, `@belongsTo`, and `@includeInSurvey` parameters of the stored procedure.
        *   After the stored procedure returns, the input parameters (`getSystemSummaryListSPInput`, `state`, `status`, `version`, `belongsTo`, `limit`, `offset`, `strIncludeInSurvey`) are deleted from the pipeline.
        *   The `@RETURN_VALUE` and `@Outputjson` from the stored procedure are copied to the main pipeline.
        *   The stored procedure output object (`getSystemSummaryListSPOutput`) is then deleted.
    *   **If `userName` is not null (`$default` branch)**:
        *   The service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryListByRole` is invoked. This adapter calls the `dbo.SP_Get_SystemListRole;1` stored procedure.
        *   Inputs `userName`, `roleType`, `state`, `status`, `version`, `belongsTo`, and `strIncludeInSurvey` are mapped to the corresponding `@personEUA`, `@personRole`, `@objectState`, `@status`, `@version`, `@belongsTo`, and `@includeInSurvey` parameters of the stored procedure.
        *   Similar cleanup and copying of `@RETURN_VALUE` and `@Outputjson` occur after the invocation.

3.  **Result Processing and Transformation**:
    *   Another `BRANCH` step evaluates the `@RETURN_VALUE` from the database call. The `flow.xml` only shows a branch for `0` (indicating success) and a `$default` branch.
    *   **If `@RETURN_VALUE` is `0` (Success)**:
        *   A nested `BRANCH` evaluates the `@Outputjson` value.
        *   **If `@Outputjson` is null (`$null` branch)**:
            *   This path implies no data was returned by the stored procedure (e.g., an empty list).
            *   A `MAP` step initializes `_generatedResponse/SystemSummary` (the output list) to an empty array.
            *   `_generatedResponse/count` is explicitly set to `0`.
        *   **If `@Outputjson` is not null (`$default` branch)**:
            *   The `pub.json:jsonStringToDocument` service is called to convert the JSON string received from the database (`@Outputjson`) into a Webmethods document (record) structure, accessible as `document`.
            *   The `pub.list:sizeOfList` service is called to determine the number of records in the `document/ResultSet` array, and this `size` is used for the output `count`.
            *   A `BRANCH` on `idsOnly` determines the detailed mapping logic:
                *   **If `idsOnly` is `true`**:
                    *   A `LOOP` iterates over `document/ResultSet` and maps each item to a `SystemSummary` output object. Inside the loop, only `id` and `name` from the database result set are copied to `SystemSummary.id` and `SystemSummary.name` respectively.
                *   **If `idsOnly` is `false` (`$default` branch)**:
                    *   A `LOOP` iterates over `document/ResultSet` and maps each item to a `SystemSummary` output object. This is the detailed mapping section where most database columns are mapped to corresponding `SystemSummary` fields. Date conversion services (`cms.eadg.utils.date:dateTimeStringToObject`) are invoked within the loop to transform date strings like "ATO Effective Date" and "ATO Expiration Date" into date objects.
            *   Finally, the collected `SystemSummary` list is mapped to `_generatedResponse/SystemSummary`, and the `size` is mapped to `_generatedResponse/count`.

4.  **Pipeline Cleanup**:
    *   The `pub.flow:clearPipeline` service is invoked to remove all temporary variables from the pipeline, except for `_generatedResponse`, which is explicitly preserved. This ensures a clean output and avoids exposing internal processing variables.

5.  **Error Handling (CATCH block)**:
    *   If any step within the `TRY` block fails, execution transfers to this `CATCH` block.
    *   `pub.flow:getLastError` is called to retrieve detailed information about the error that occurred.
    *   The `cms.eadg.utils.api:handleError` service is then invoked, passing the `lastError` information. This service is responsible for formatting the error response appropriately (e.g., setting HTTP status codes, error messages).

## Dependency Service Flows

The main service `systemSummaryFindList` relies on several other Webmethods services:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryListSP` (TYPE=DEPENDENCY_FILE)**:
    *   **Purpose**: This is a JDBC Adapter service designed to execute the `SP_Get_SystemList;1` stored procedure in the `dbo` schema of the `Sparx_Support` database. It retrieves a list of system summaries based on state, status, version, belongsTo, and includeInSurvey criteria, without considering user roles.
    *   **Integration**: It's called directly by the main service when `userName` is null. Its output (`@RETURN_VALUE` and `@Outputjson`) directly informs the subsequent processing in the main flow.
    *   **Input/Output Contract**: Takes system filter criteria as input and returns an integer return value and a JSON string (`@Outputjson`) containing the query results.
    *   **Specialized Processing**: Handles the direct invocation of the stored procedure and returns its output.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SystemSummary:getSystemSummaryListByRole` (TYPE=DEPENDENCY_FILE)**:
    *   **Purpose**: Similar to `getSystemSummaryListSP`, this JDBC Adapter service executes the `SP_Get_SystemListRole;1` stored procedure, also in the `dbo` schema of the `Sparx_Support` database. This version includes filtering by a person's EUA and their associated role.
    *   **Integration**: It's called when `userName` is provided, indicating a role-based system list is required. Its output also directly feeds into the main service's result processing.
    *   **Input/Output Contract**: Takes system filter criteria, plus `personEUA` and `personRole`, and returns an integer return value and a JSON string (`@Outputjson`) with results.
    *   **Specialized Processing**: Handles the direct invocation of the role-specific stored procedure.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.systemSummaryFindList:convertBooleanToString` (Implicit)**:
    *   **Purpose**: A utility service to convert a boolean value (from `includeInSurvey` input) into its string representation ("true" or "false").
    *   **Integration**: Called early in the main service flow to prepare the `includeInSurvey` parameter for the database adapters, as database stored procedures often expect string inputs for such flags.

*   **`pub.json:jsonStringToDocument` (Built-in Webmethods service)**:
    *   **Purpose**: Converts a JSON formatted string into a Webmethods document (IData object). This is essential for parsing the JSON output from the database stored procedures into a manipulable data structure within the Webmethods pipeline.
    *   **Integration**: Called immediately after receiving the `@Outputjson` from the database adapters, allowing the service to extract individual fields from the JSON data.

*   **`pub.list:sizeOfList` (Built-in Webmethods service)**:
    *   **Purpose**: Determines the number of elements in a given list (array) within the Webmethods pipeline.
    *   **Integration**: Used to count the number of `ResultSet` entries returned from the database, which is then mapped to the `count` field of the `SystemSummaryResponse`.

*   **`cms.eadg.utils.date:dateTimeStringToObject` (Implicit)**:
    *   **Purpose**: A utility service to parse a date string (specifically "MM/dd/yyyy" format as configured in the mapping) into a Java `java.util.Date` object.
    *   **Integration**: Called within the `LOOP` that processes the database `ResultSet` for each `SystemSummary` record. This transforms the `ATO Effective Date` and `ATO Expiration Date` strings from the database into proper date objects for the output `SystemSummary` structure.

*   **`pub.flow:clearPipeline` (Built-in Webmethods service)**:
    *   **Purpose**: Resets the entire pipeline, removing all variables. It can be configured to preserve specific variables.
    *   **Integration**: Used at the end of the main service's successful execution path to clean up temporary variables, ensuring only the intended output (`_generatedResponse`) remains.

*   **`pub.flow:getLastError` (Built-in Webmethods service)**:
    *   **Purpose**: Retrieves information about the last error that occurred in the current flow execution.
    *   **Integration**: Called at the beginning of the `CATCH` block to get details of the error, which are then passed to the custom error handling service.

*   **`cms.eadg.utils.api:handleError` (TYPE=DEPENDENCY_FILE)**:
    *   **Purpose**: A custom error handling service responsible for taking the `lastError` information, setting appropriate HTTP response codes and messages, and preparing the error response body (JSON or XML).
    *   **Integration**: Invoked in the `CATCH` block of the main service. It ensures consistent error reporting across the API. Internally, it relies on `cms.eadg.utils.api:setResponse`.

*   **`cms.eadg.utils.api:setResponse` (TYPE=DEPENDENCY_FILE)**:
    *   **Purpose**: A custom service that sets the HTTP response code and body based on the provided `SetResponse` document type. It converts the response data into either JSON (`pub.json:documentToJSONString`) or XML (`pub.xml:documentToXMLString`).
    *   **Integration**: Called by `handleError` to finalize the HTTP response, including setting the `Content-Type` header based on the requested `format`. It then uses `pub.flow:setResponseCode` and `pub.flow:setResponse2`.

## Data Structures and Types

The service heavily relies on predefined document types (Webmethods' equivalent of data models or schemas).

*   **Input Data Model (`sig_in` of `systemSummaryFindList/node.ndf`)**:
    *   `state`: `string` (optional, System state)
    *   `status`: `string` (optional, System status)
    *   `version`: `string` (optional, System versions)
    *   `includeInSurvey`: `object` (optional, `java.lang.Boolean` wrapper, Include only system census eligible systems)
    *   `idsOnly`: `object` (optional, `java.lang.Boolean` wrapper, Return only system ids and names)
    *   `belongsTo`: `string` (optional, Return only sub-systems of the system ID provided)
    *   `limit`: `object` (optional, `java.math.BigInteger` wrapper, Limits the number of systems returned. Default 1000)
    *   `offset`: `object` (optional, `java.math.BigInteger` wrapper, Defines the starting position of the first record returned. Default 0)
    *   `userName`: `string` (optional, EUA of Person that has a Role associated with any System)
    *   `roleType`: `string` (optional, Role Type of Person that is associated with any System)

*   **Output Data Model (`sig_out` of `systemSummaryFindList/node.ndf`)**:
    *   `_generatedResponse`: `recref` to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemSummaryResponse` (optional)
        *   `count`: `object` (`java.math.BigInteger`), Total count of systems.
        *   `SystemSummary`: `recref` to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemSummary` (array, optional)

    *   `400`, `401`, `404`, `500`: `record` containing a `Response` `recref` to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` (optional). These represent potential error responses.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:SystemSummary` (Detail Object)**:
    *   `id`: `string`
    *   `nextVersionId`: `string` (optional)
    *   `previousVersionId`: `string` (optional)
    *   `ictObjectId`: `string`
    *   `uuid`: `string` (optional)
    *   `name`: `string`
    *   `description`: `string` (optional)
    *   `version`: `string`
    *   `acronym`: `string` (optional)
    *   `state`: `string` (optional)
    *   `status`: `string` (optional)
    *   `belongsTo`: `string` (optional)
    *   `businessOwnerOrg`: `string` (optional)
    *   `businessOwnerOrgComp`: `string` (optional)
    *   `systemMaintainerOrg`: `string` (optional)
    *   `systemMaintainerOrgComp`: `string` (optional)
    *   `atoEffectiveDate`: `object` (`java.util.Date`, optional)
    *   `atoExpirationDate`: `object` (`java.util.Date`, optional)

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` (General API Response)**:
    *   `result`: `string` (e.g., "success", "error")
    *   `message`: `string` (array, details about the response)

*   **`cms.eadg.utils.api.docs:SetResponse` (Internal Utility Document Type)**:
    *   `responseCode`: `string` (HTTP status code, e.g., "200", "500")
    *   `responsePhrase`: `string` (HTTP reason phrase, e.g., "OK", "Internal Server Error")
    *   `result`: `string` (e.g., "success", "error")
    *   `message`: `string` (array, message details)
    *   `format`: `string` (Content type, e.g., "application/json", "application/xml")

*   **Data Transformation Logic**:
    *   **Boolean to String**: `includeInSurvey` (`boolean`) is converted to `strIncludeInSurvey` (`string`) for database calls.
    *   **JSON String to Document**: The `@Outputjson` string from the stored procedures is parsed into a structured Webmethods document (`document/ResultSet`) using `pub.json:jsonStringToDocument`.
    *   **Looping and Field Mapping**: The service iterates over the `document/ResultSet` array. Each element in this array corresponds to a row from the database result. Fields from these rows are mapped to the properties of the `SystemSummary` document type.
    *   **Date String to Date Object**: `ATO Effective Date` and `ATO Expiration Date` (strings from DB) are converted to `atoEffectiveDate` and `atoExpirationDate` (Java Date objects) using `cms.eadg.utils.date:dateTimeStringToObject` with the "MM/dd/yyyy" pattern.

### Source Database Column to Output Object Property Mapping:

This mapping details how the database columns (as they appear in the `document/ResultSet` after `jsonStringToDocument` converts the SQL procedure's JSON output) are transformed into the `SystemSummary` output object properties.

*   When `idsOnly` is `true`:
    *   `id`: `SystemSummary.id`
    *   `name`: `SystemSummary.name`

*   When `idsOnly` is `false` (default path):
    *   `id`: `SystemSummary.id`
    *   `name`: `SystemSummary.name`
    *   `nextVersionId`: `SystemSummary.nextVersionId`
    *   `previousVersionId`: `SystemSummary.previousVersionId`
    *   `description`: `SystemSummary.description`
    *   `version`: `SystemSummary.version`
    *   `acronym`: `SystemSummary.acronym`
    *   `objectState`: `SystemSummary.state`
    *   `status`: `SystemSummary.status`
    *   `belongsTo`: `SystemSummary.belongsTo`
    *   `businessOwnerOrg`: `SystemSummary.businessOwnerOrg`
    *   `businessOwnerComp`: `SystemSummary.businessOwnerOrgComp`
    *   `systemMaintainerOrg`: `SystemSummary.systemMaintainerOrg`
    *   `systemMaintainerComp`: `SystemSummary.systemMaintainerOrgComp`
    *   `ictObjectId`: `SystemSummary.ictObjectId`
    *   `uuid`: `SystemSummary.uuid`
    *   `ATO Effective Date`: `SystemSummary.atoEffectiveDate` (converted to `java.util.Date` object from "MM/dd/yyyy" string)
    *   `ATO Expiration Date`: `SystemSummary.atoExpirationDate` (converted to `java.util.Date` object from "MM/dd/yyyy" string)

## Error Handling and Response Codes

The service employs a structured error handling mechanism using a Webmethods `TRY-CATCH` block pattern.

*   **Error Scenarios**: Any unhandled exception during the execution of the main `SEQUENCE` (the `TRY` block) will trigger the error handling. This could include database connection issues, stored procedure errors, data conversion failures, or unexpected null values.
*   **Error Capture**: Upon an error, `pub.flow:getLastError` is invoked in the `CATCH` block to retrieve detailed information about the exception, including the error message.
*   **Centralized Error Handling**: The `cms.eadg.utils.api:handleError` service is called. This service is a reusable component for standardizing API error responses.
    *   It checks if a specific `SetResponse` structure was already populated (e.g., by a prior step aiming for a specific HTTP error). If not, it defaults to a **500 Internal Server Error**.
    *   It sets the `responseCode` to "500" and `responsePhrase` to "Internal Server Error" by default.
    *   The `result` field of the response body is set to "error".
    *   The `message` field of the response body is populated with the error message from `lastError`.
    *   The `format` is set to "application/json" by default, preparing the response body as JSON. It also has a branch for "application/xml" which wraps the response in a "ResponseRooted" object and uses `pub.xml:documentToXMLString`.
*   **HTTP Response Codes**:
    *   **Success**: A successful execution typically implies an HTTP status code of **200 OK**, which is the default for successful API responses if no explicit status is set otherwise.
    *   **Error**: The `handleError` service explicitly sets the HTTP response code to **500 Internal Server Error** if no specific error `SetResponse` document is provided. The `node.ndf` for `systemSummaryFindList` also defines potential output `record` types for `400`, `401`, `404`, and `500` indicating these are expected error codes, although the flow logic in `flow.xml` only explicitly routes to a general 500 error for unhandled exceptions. If specific business logic were to identify a bad request (400), unauthorized (401), or not found (404) scenario, the `handleError` service (or a service preceding it) would need to populate the `SetResponse` document with the appropriate code.
*   **Error Message Formats**: The error response body is formatted as a JSON (or XML) object with `result` and `message` fields, providing a clear indication of failure and a human-readable explanation.
*   **Fallback Behaviors**: The `clearPipeline` step ensures that even if an error occurs, the pipeline is cleaned to avoid leaking sensitive data, while still preserving the prepared error response.