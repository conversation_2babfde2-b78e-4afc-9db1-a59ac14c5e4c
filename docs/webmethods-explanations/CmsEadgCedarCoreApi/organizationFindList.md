# Webmethods Service Explanation: CmsEadgCedarCoreApi organizationFindList

This document provides a comprehensive explanation of the Webmethods service `organizationFindList`, part of the `CmsEadgCedarCoreApi` package. As an experienced software developer new to Webmethods, this explanation aims to clarify the service's functionality, its interactions with databases and other APIs, and the internal Webmethods flow logic, with a particular focus on data mapping.

The primary business purpose of the `organizationFindList` service is to retrieve information about the official CMS organizational hierarchy. It acts as an API endpoint that can provide a complete list of organizations or filter them based on specific criteria like ID, name, or acronym. The service is designed to return a structured response containing organizational details, potentially in a hierarchical (tree-like) format.

The service accepts three optional input parameters:

*   `id`: The unique identifier of a specific organization. If provided, the service should return the specified organization and its children.
*   `name`: The exact name of an organization. Similar to `id`, it's used for filtering to return the specified organization and its children.
*   `acronym`: The acronym of an organization, also used for filtering.

The expected output is a structured JSON or XML object containing a list of `Organization` records, along with a `count` of the results. In error scenarios, it returns a standard error response with an HTTP status code and a descriptive message. The key validation rule is that at least one of the filtering parameters (`id`, `name`, `acronym`) must be present for filtering to occur; otherwise, a full list of "Active" organizations is retrieved.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `organizationFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm called "Flow" to define service logic. These "Flow" services are essentially sequences of steps that manipulate data within a central "pipeline" (similar to a context or shared memory space).

*   **SEQUENCE**: This element defines a block of steps that are executed sequentially. It's akin to a standard block of code in other programming languages. A `SEQUENCE` can have an `EXIT-ON` attribute (e.g., `FAILURE`) which dictates whether the sequence should stop executing if a step fails. `FORM="TRY"` indicates the start of a try-catch block for error handling.
*   **BRANCH**: Similar to a `switch` statement or an `if-else if-else` structure. It executes one of its child steps based on a condition (often an expression or a variable value). `LABELEXPRESSIONS="true"` means the labels on the child steps are evaluated as boolean expressions.
*   **MAP**: This is a powerful data transformation step. It allows you to define mappings between input and output variables in the pipeline. It can copy, move, set, and delete data fields.
*   **INVOKE**: This element calls another Webmethods service. It takes input from the current pipeline, passes it to the invoked service, and then receives output from the invoked service back into the pipeline. This is how modularity and reusability are achieved.

Within `MAP` steps, specific operations are common:

*   **MAPSET**: Assigns a static value or a value derived from an expression to a pipeline variable.
*   **MAPCOPY**: Copies data from one pipeline variable to another. This is crucial for transforming data shapes and renaming fields.
*   **MAPDELETE**: Removes a variable from the pipeline, helping to keep the pipeline clean and manage memory.

**Error Handling (TRY/CATCH blocks)**: Webmethods Flow services support structured error handling using `TRY` and `CATCH` sequences. If an error occurs within the `TRY` block, execution immediately jumps to the `CATCH` block. The `pub.flow:getLastError` service is typically invoked within the `CATCH` block to retrieve details about the error (like error message, stack trace).

**Input Validation and Branching Logic**: In Webmethods, input validation and conditional logic are often implemented using `BRANCH` steps. By checking if specific input parameters (`$null` checks) are present or meet certain criteria, the service can branch to different processing paths. For instance, if an optional input parameter is provided, a specific sub-flow might be executed; otherwise, a default path is taken.

## Database Interactions

The `organizationFindList` service interacts with a SQL database through an adapter service.

The primary database interaction occurs via the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Organization:getOrganization` service. This is a JDBC adapter service configured to query the database.

*   **Database Connection**: The `getOrganization` adapter utilizes the `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans` connection. This connection is configured to connect to a SQL Server database.
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port**: `1433`
    *   **Database Name**: `Sparx_Support`
    *   **User**: `sparx_dbuser` (password is stored securely and not exposed).
    *   **Transaction Type**: `NO_TRANSACTION` (meaning each database operation commits immediately and is not part of a larger, coordinated transaction).

*   **SQL Query**: The `getOrganization` adapter executes a SQL `SELECT` statement against a specific database object.
    *   **Database Object (View)**: `Sparx_Organization`
    *   **SQL Query Details**: The query selects all relevant columns from the `Sparx_Organization` view.
        ```sql
        SELECT T1."Organization ID", T1."Sparx Organization ID", T1."Sparx Organization GUID", T1."Organization Name", T1.Acronym, T1.Comments, T1."End Date", T1."Full Path", T1."Full Path with Org Name", T1."Org State", T1."Org Type", T1."Organization Component", T1."Organization Name Level", T1."Start Date", T1.Description, T1."Parent Organization GUID"
        FROM Sparx_Organization T1
        WHERE T1."Org State" = ?
        ```
    *   **Input Parameter Mapping**: The `WHERE` clause `T1."Org State" = ?` indicates a parameterized query. In the `organizationFindList` service, this parameter is hardcoded to "Active" before the `getOrganization` adapter is invoked. This means the initial database call always retrieves only organizations with an "Active" state.

*   **Data Mapping from Database to Service Output**: The results from the SQL query are mapped from the `getOrganizationOutput.results` (an array of records representing database rows) to the service's internal `OrganizationList` (an array of `cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:Organization` document type). This mapping is crucial for transforming raw database column names into more user-friendly API output property names.

    The following is a detailed list of source database columns (from the `Sparx_Organization` view) to their corresponding output object properties:

    *   `Sparx_Organization."Sparx Organization GUID"`: `id`
    *   `Sparx_Organization."Organization Name"`: `name`
    *   `Sparx_Organization.Acronym`: `acronym`
    *   `Sparx_Organization.Comments`: `description`
    *   `Sparx_Organization."Organization Component"`: `component`
    *   `Sparx_Organization."Full Path with Org Name"`: `fullPath`
    *   `Sparx_Organization."Organization Name Level"`: `level`
    *   `Sparx_Organization."Parent Organization GUID"`: `parentId`

    Note that several columns from the `Sparx_Organization` view (e.g., `"Organization ID"`, `"Sparx Organization ID"`, `"End Date"`, `"Full Path"`, `"Org State"`, `"Org Type"`, `"Start Date"`, and `Description` from the database's `Description` column, as opposed to `Comments`) are retrieved by the adapter but are not directly mapped to fields in the final output `Organization` object. `Org State` is only used for filtering the initial query.

## External API Interactions

The provided Webmethods files indicate the presence of an external API interaction in the `cms.eadg.alfabet.api.v01.resources.objects.byReport.services:postObjectByReport` service, which is identified as a dependency. While the main `organizationFindList` service *could* theoretically invoke this, the provided `flow.xml` for `organizationFindList` does *not* directly call `postObjectByReport`. Instead, `organizationFindList` relies on a direct JDBC adapter call (`getOrganization`) for its data source.

However, it's worth noting the `postObjectByReport`'s functionality, as it showcases how external REST API calls are made in Webmethods:

*   **Service Called**: `pub.client:http`
*   **Request Format**: JSON payload, transformed from an internal document structure (`ObjectByReportRequest`) using `pub.json:documentToJSONString`.
*   **Authentication**: Uses a `Bearer` token (`token` input, populated from global variables `%alfabet.api.token.1%`, etc.). The `pub.client:http` service is configured to use "Bearer" authentication.
*   **Request URL Construction**: The full URL is dynamically constructed by concatenating a global base URL (`%alfabet.api.url%`) and a specific path (`/v2/objects`).
*   **Response Handling**: The HTTP response (bytes) is converted to a string (`pub.string:bytesToString`), then parsed from a JSON string back into a document (`pub.json:jsonStringToDocument`).
*   **Error Handling**: The `postObjectByReport` service includes a `BRANCH` on the HTTP status code. A `200` status indicates success. Other status codes trigger error mapping, including a special case to remap a `403` (Forbidden) error from the external API to a `500` (Internal Server Error) before exiting with a failure signal. General errors are caught and handled by `cms.eadg.utils.api:setResponse`.

Although `organizationFindList` does not directly use this external API, its presence in the dependency files shows that the Webmethods environment supports and frequently uses external REST API integrations, similar to how one might use an HTTP client library in TypeScript.

## Main Service Flow

The `organizationFindList` service orchestrates a series of steps within a `TRY-CATCH` block to retrieve and process organizational data.

1.  **Initial Data Retrieval (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Organization:getOrganization`)**:
    *   The first step is to call a JDBC adapter service that queries the `Sparx_Organization` view in the database.
    *   An input mapping explicitly sets the `"Org State"` parameter to "Active", meaning this service will always initially fetch only active organizations from the database.
    *   The output from the database query (a flat list of organization records) is then mapped to a document list named `OrganizationList` (of type `cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:Organization`). Simultaneously, the total `count` of records is retrieved.
    *   Intermediate database input parameters (`getOrganizationInput`) and raw database output (`getOrganizationOutput`) are deleted from the pipeline to clean up.

2.  **Building the Organizational Tree (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:mapOrganizationTree`)**:
    *   The `OrganizationList` (a flat list of organizations with parent IDs) is passed to a Java service (`mapOrganizationTree`).
    *   This Java service is responsible for transforming the flat list into a hierarchical tree structure, where each organization can contain nested child organizations. This is crucial for representing the organizational hierarchy.
    *   The output of this step is `OrganizationTree`, a structured representation of the organizations.
    *   The original `OrganizationList` is deleted from the pipeline after this step.

3.  **Initial Response Generation and Preparation for Filtering**:
    *   A `MAP` step takes the `OrganizationTree` and the `count` from the previous steps.
    *   These are mapped into the `_generatedResponse` document, which conforms to the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:OrganizationFindResponse` structure (containing `Organizations` as a list of the tree nodes and `count`).
    *   The intermediate `OrganizationTree` is then deleted.

4.  **Conditional Filtering Logic (`BRANCH %id% != $null || %name% != $nulll || %acronym% != $null`)**:
    *   This `BRANCH` statement acts as a conditional filter. It checks if any of the service's input parameters (`id`, `name`, or `acronym`) were provided by the caller.
    *   **If a filter parameter is present (condition is true)**:
        *   The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:filterOrganizationTree`.
        *   The `_generatedResponse` (containing the full tree of active organizations) and the input filter parameters (`id`, `name`, `acronym`) are passed to `filterOrganizationTree`. This sub-service will then apply the specified filter to the organizational tree.
        *   The output of `filterOrganizationTree` (the filtered `OrganizationFindResponse`) is copied back to `_generatedResponse`, effectively replacing the full tree with the filtered one.
        *   The original `OrganizationFindResponse` is deleted.
    *   **If no filter parameters are present (condition is false)**:
        *   The `filterOrganizationTree` service is skipped.
        *   The `_generatedResponse` (which still contains the full tree of "Active" organizations) passes through untouched, effectively returning all active organizations.

5.  **Final Pipeline Cleanup**:
    *   A final `MAP` step is executed to clean up the initial input parameters (`id`, `name`, `acronym`) and the `count` from the pipeline, ensuring only the `_generatedResponse` (and any error-related information in the `CATCH` block) remains as output.

**Error Scenarios and Handling**:
*   The entire main flow is wrapped in a `SEQUENCE` with `FORM="TRY"`.
*   If any step within this `TRY` block fails, control immediately transfers to the `SEQUENCE` with `FORM="CATCH"`.
*   Inside the `CATCH` block:
    *   `pub.flow:getLastError` is called to retrieve detailed information about the error that occurred (e.g., error message, exception type).
    *   `cms.eadg.utils.api:handleError` is then invoked. This is a generic utility service designed to standardize error responses. It processes the error information and prepares a formatted error message, setting appropriate HTTP status codes (e.g., 500 for internal server errors) and messages.

## Dependency Service Flows

The `organizationFindList` service relies on several other services to perform its tasks, encapsulating complex logic into reusable components.

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Organization:getOrganization`**:
    *   **Purpose**: This is an auto-generated JDBC Adapter Service designed to retrieve raw organization data directly from the `Sparx_Organization` view in the SQL Server database.
    *   **Integration**: It's the first major step in the `organizationFindList` flow, providing the foundational dataset.
    *   **Input/Output Contract**: It takes a single input parameter, `"Org State"`, which is hardcoded to "Active" by its caller. Its output is `getOrganizationOutput`, which contains a `results` array (a flat list of database records) and a `count` of these results.
    *   **Specialized Processing**: This service abstracts the direct SQL query, providing a structured output that can be easily mapped in subsequent flow steps. It handles the database connection and query execution.

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:mapOrganizationTree`**:
    *   **Purpose**: This is a Java service responsible for transforming a flat list of organizations (each with an `id`, `parentId`, etc.) into a hierarchical tree structure. This is a common pattern when dealing with recursive data relationships.
    *   **Integration**: It processes the `OrganizationList` received from `getOrganization` immediately, creating the tree structure before any filtering logic is applied.
    *   **Input/Output Contract**:
        *   Input: `OrganizationList` (an array of `Organization` documents, representing the flat data).
        *   Output: `OrganizationTree` (an array of `Organization` documents, where each document can contain a nested `Organization` array for its children).
    *   **Specialized Processing**: The `node.idf` file associated with this service confirms its Java implementation. It contains a `convertOrg` function which recursively processes the flat list, identifying parent-child relationships and embedding children within their respective parent `Organization` objects. This allows the API to return a deeply nested organizational structure.

3.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.organizationFindList:filterOrganizationTree`**:
    *   **Purpose**: This Flow service applies filtering criteria (ID, name, or acronym) to the pre-built `OrganizationTree`.
    *   **Integration**: It's conditionally invoked in the main service flow only if filter parameters (`id`, `name`, `acronym`) are provided by the client.
    *   **Input/Output Contract**:
        *   Input: The full `OrganizationFindResponse` (containing the `OrganizationTree`) and the filter parameters (`id`, `name`, `acronym`).
        *   Output: A filtered `OrganizationFindResponse`, containing only the matching organizations and their descendants.
    *   **Specialized Processing**: This service demonstrates a common Webmethods pattern:
        *   It converts the `OrganizationFindResponse` document to an XML string (`pub.xml:documentToXMLString`) and then to an XML Node (`pub.xml:xmlStringToXMLNode`). This is likely done to enable filtering using XQL (XML Query Language), which is powerful for navigating and querying XML structures.
        *   It uses a `BRANCH` to apply different XQL queries based on which input filter (`id`, `name`, or `acronym`) is present.
            *   For `id`: `//Organizations[id='%id%']`
            *   For `name`: `//Organizations[name='%name%']`
            *   For `acronym`: `//Organizations[acronym='%acronym%']`
        *   Finally, it converts the filtered XML Node back into an IData document (`pub.xml:xmlNodeToDocument`), which is the `OrganizationFindResponse`. This process of converting to/from XML for querying is a notable Webmethods specific technique.

4.  **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A generic utility service designed to standardize the structure and content of error responses across the API.
    *   **Integration**: Invoked in the `CATCH` block of the main service flow (`organizationFindList`) whenever an unhandled error occurs. It's also used in other dependency services (e.g., `postObjectByReport`) for consistent error reporting.
    *   **Input/Output Contract**: Takes error details (typically from `pub.flow:getLastError`) and an optional `SetResponse` document (pre-configured error details). Its primary side effect is setting HTTP response headers (status code, phrase) and populating the `Response` document with error information.

5.  **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: A generic utility service to set the final HTTP response code and format the output body.
    *   **Integration**: Used by `handleError` and other services to prepare the final response to the client.
    *   **Input/Output Contract**: Takes a `SetResponse` document (containing desired response code, phrase, result status, message, and format like `application/json` or `application/xml`).
    *   **Specialized Processing**: It dynamically converts the response data to either JSON (`pub.json:documentToJSONString`) or XML (`pub.xml:documentToXMLString`) based on the requested `format`, and then sets the HTTP response code and content type using `pub.flow:setResponseCode` and `pub.flow:setResponse2`. This abstraction means individual services don't need to worry about the serialization or HTTP header manipulation.

## Data Structures and Types

Webmethods uses "Document Types" (often represented as `.ndf` files) to define data structures, similar to DTOs or interfaces in other languages.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Organization`**: This document type defines the structure of a single organization object in the API's output.
    *   `id` (string): Unique identifier for the organization (mapped from `Sparx Organization GUID`).
    *   `name` (string): Organization's full name.
    *   `acronym` (string, optional): Organization's acronym.
    *   `description` (string, optional): Detailed description (mapped from `Comments`).
    *   `isComponent` (boolean, optional): Indicates if it's a component.
    *   `component` (string, optional): Component name.
    *   `fullPath` (string, optional): Full path of the organization in the hierarchy.
    *   `level` (BigInteger, optional): Level in the hierarchy (derived from `Organization Name Level`).
    *   `parentId` (string, optional): ID of the parent organization.
    *   `Organization` (recref, dim=1, optional): Recursive reference, allowing nested `Organization` objects to represent the tree structure.
    *   **TypeScript Porting Consideration**: This recursive structure would translate directly to an interface with an optional array of itself, e.g., `children?: Organization[]`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:OrganizationFindResponse`**: The top-level output structure for the `organizationFindList` service.
    *   `count` (BigInteger): Total number of organizations in the response.
    *   `Organizations` (recref, dim=1, optional): An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Organization` documents, representing the list/tree of organizations.

*   **`cms.eadg.utils.api.docs:Response`**: A generic response structure for API operations.
    *   `result` (string, optional): Typically "success" or "error".
    *   `message` (string array, optional): A list of messages (e.g., error descriptions).

*   **`cms.eadg.utils.api.docs:SetResponse`**: An internal document type used by utility services (`handleError`, `setResponse`) to configure the outgoing HTTP response.
    *   `responseCode` (string): HTTP status code (e.g., "200", "500").
    *   `responsePhrase` (string): HTTP reason phrase (e.g., "OK", "Internal Server Error").
    *   `result` (string): Overall result status ("success" or "error").
    *   `message` (string array): Messages to be included in the response body.
    *   `format` (string): Content type for the response body (e.g., "application/json", "application/xml").

Input parameters (`id`, `name`, `acronym`) are defined as simple strings in the service's `node.ndf`. They are all optional. There are no explicit field validation rules (like min/max length, regex patterns) defined within the `flow.xml` for these inputs; validation is implicitly handled by the XQL queries in `filterOrganizationTree` (which will simply return no results if the input doesn't match).

Data transformation logic primarily occurs in the `MAP` steps, where fields are copied and renamed, and in the `mapOrganizationTree` Java service, which performs the complex structural transformation from a flat list to a hierarchy.

## Error Handling and Response Codes

The `organizationFindList` service implements a robust error handling strategy using Webmethods' built-in `TRY-CATCH` blocks and reusable utility services.

*   **Overall Strategy**: The core logic of the `organizationFindList` service is enclosed within a `TRY` block. Any unhandled exception or explicit failure signal (`EXIT FROM "$parent" SIGNAL="FAILURE"`) within this `TRY` block will immediately transfer control to the corresponding `CATCH` block.

*   **Error Scenarios Covered**:
    *   **Database Errors**: Issues with the `getOrganization` JDBC adapter (e.g., connection problems, invalid SQL, database down) will trigger the `CATCH` block.
    *   **Internal Processing Errors**: Failures within subsequent mapping steps or Java services (`mapOrganizationTree`, `filterOrganizationTree`) will also be caught.
    *   **External API Errors (indirect)**: While `organizationFindList` doesn't directly call external APIs, the `postObjectByReport` dependency service *does* handle its own external API errors. If `postObjectByReport` were called and failed, it would propagate an error up to its caller, which would then be caught by `organizationFindList` if it were in the execution path.

*   **Error Handling Flow in `CATCH` block**:
    1.  **Retrieve Last Error**: `pub.flow:getLastError` is invoked to get a detailed `exceptionInfo` document, containing the error message, exception type, and stack trace. This is crucial for debugging.
    2.  **Standardize Error Response**: The `cms.eadg.utils.api:handleError` service is called. This utility service is central to consistent error reporting:
        *   It inspects the `lastError` information.
        *   It sets the `SetResponse` document (an internal structure) with an HTTP `responseCode` of `500` ("Internal Server Error") and a `responsePhrase` of "Internal Server Error".
        *   The `result` field in `SetResponse` is set to "error".
        *   The actual error message from `lastError` is copied into the `message` field of `SetResponse`.
        *   The `format` is specified as "application/json" (or "application/xml" if the client requested XML).
        *   A specific scenario within `postObjectByReport` (a sub-dependency not directly called by `organizationFindList` but showing a pattern) demonstrates how `403` (Forbidden) from an external API is remapped to a `500` internal error to avoid exposing sensitive external API status codes.
    3.  **Set HTTP Response**: Finally, the `cms.eadg.utils.api:setResponse` service is invoked. This service reads the `SetResponse` document and:
        *   Calls `pub.flow:setResponseCode` to set the HTTP status code (e.g., 500) and reason phrase in the HTTP response headers.
        *   Converts the `Response` body content (containing the "error" status and message) into the specified `format` (JSON or XML) using `pub.json:documentToJSONString` or `pub.xml:documentToXMLString`.
        *   Sets the HTTP response body and `Content-Type` header using `pub.flow:setResponse2`.
    4.  **Signal Failure**: The `EXIT FROM "$parent" SIGNAL="FAILURE"` statement ensures that the execution of `organizationFindList` stops immediately after handling the error and propagates the failure status to any calling service or the HTTP listener, signaling an unsuccessful operation.

*   **HTTP Response Codes Used**:
    *   `200 OK`: For successful responses (handled in the main `TRY` block flow). The `organizationFindList` service implicitly returns 200 if no error occurs and the `_generatedResponse` is returned.
    *   `400 Bad Request`: Signaled in `node.ndf` as a possible output, typically used for client-side input validation errors, though not explicitly set in the provided `flow.xml` for `organizationFindList` or its direct dependencies. The generic `handleError` service could be configured for this by its callers.
    *   `401 Unauthorized`: Signaled in `node.ndf` as a possible output, for authentication failures.
    *   `500 Internal Server Error`: The primary error code used by `handleError` for unhandled exceptions or internal service failures.

*   **Error Message Formats**: Error messages are typically returned in a standardized JSON or XML format, containing a `result` field (e.g., "error") and a `message` field (an array of strings providing details). This consistent format aids client applications in parsing and displaying error information.

*   **Fallback Behaviors**: In case of filter parameters (`id`, `name`, `acronym`) being absent, the service falls back to retrieving and returning all "Active" organizations. This ensures a default behavior rather than returning an empty or error response unnecessarily.