# Webmethods Service Explanation: CmsEadgCedarCoreApi stakeholderFindList

This document explains the Webmethods Integration Server flow service `stakeholderFindList`, which is part of the `CmsEadgCedarCoreApi` package. The service is designed to retrieve a list of stakeholders from a backend database, allowing for filtering based on various attributes and supporting an option to return only essential identification details. The output is structured as a JSON or XML response containing the retrieved stakeholder information.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `stakeholderFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `stakeholderFindList` service's primary business purpose is to provide an API endpoint for querying and retrieving stakeholder records. It acts as an interface between client applications and the underlying data source (a SQL Server database, in this case).

The service accepts the following optional input parameters, which can be used to filter the list of stakeholders returned:

*   `id`: Stakeholder ID.
*   `name`: Stakeholder name.
*   `version`: Stakeholder version.
*   `state`: Stakeholder state.
*   `status`: Stakeholder status.
*   `idsOnly`: A boolean flag. If `true` (or "Yes" after conversion), the service returns only the stakeholder `id` and `name`. If `false` (or "No"), it returns full stakeholder details including `description`, `version`, `state`, and `status`.

The expected output of the service is a `StakeholderFindResponse` object, which includes a `count` of the returned stakeholders and a list of `Stakeholders` objects. Each `Stakeholder` object contains fields like `id`, `name`, `description`, `version`, `state`, and `status`, though the `idsOnly` input parameter will limit which of these fields are populated. In case of an error, a standardized error response is returned with appropriate HTTP status codes.

Key validation rules implemented by the service primarily involve type conversion and data mapping. The service assumes that the input parameters, if provided, are strings and handles the conversion of the `idsOnly` boolean input to a string "Yes" or "No" for its internal logic. Errors during database interaction or data processing are caught and handled generically.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods flow services use a visual programming model composed of "nodes" that represent specific actions. Here's how the key elements in this service translate to familiar programming constructs:

*   **SEQUENCE**: Analogous to a block of code in most programming languages (e.g., `{ ... }` in JavaScript/TypeScript). Nodes within a sequence execute in order. A `SEQUENCE` can be configured as a `TRY` or `CATCH` block for error handling.
*   **BRANCH**: Similar to a `switch` statement or an `if-else if-else` ladder. It evaluates a given variable (the "switch" variable) and executes a specific `SEQUENCE` based on its value. The `$null` label handles cases where the switch variable is null, and `$default` handles all other cases not explicitly matched.
*   **MAP**: This is a powerful data transformation tool, comparable to assigning values between variables or objects, often involving property mapping.
    *   **MAPSET**: Assigns a literal value or an expression result to a target field. This is like `variable = "value";`.
    *   **MAPCOPY**: Copies the value from a source field to a target field. This is like `targetVariable = sourceVariable;`.
    *   **MAPDELETE**: Removes a field from the pipeline (the in-memory data structure holding all variables). This is used for pipeline cleanup, similar to garbage collection hints or explicitly nulling out variables in other languages.
*   **INVOKE**: Calls another Webmethods service (either a built-in one or a custom one). This is equivalent to calling a function or method in a standard programming language.
*   **LOOP**: Iterates over an array (list) of documents. For each element in the array, the nested sequence of steps is executed. Within a loop, the `$iteration` variable holds the current loop index, and `%index%` can be used to access elements in target arrays (e.g., `Stakeholders[%stakeHolderIndex%]`).
*   **TRY/CATCH blocks**: These define error handling. Code within the `TRY` block is executed. If an error occurs, control immediately transfers to the `CATCH` block, allowing for graceful error handling. This mirrors `try { ... } catch (error) { ... }` in many languages.

## Database Interactions

The service primarily interacts with a SQL Server database through a Webmethods JDBC Adapter service.

*   **Database Connection**: The service uses the connection named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.
    *   This connection is configured to connect to a Microsoft SQL Server database.
    *   Server Name: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   Port: `1433`
    *   Database Name: `Sparx_Support`
    *   User: `sparx_dbuser` (password is stored securely and not visible here)
    *   Transaction Type: `NO_TRANSACTION` (meaning this connection does not participate in global transactions).
*   **Database Operation**: The service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.StakeHolder:getStakeHolderSPNoSignature` is an Adapter Service that invokes a stored procedure.
    *   **Stored Procedure Called**: `SP_Get_ExchangeRoleList;1`
    *   **Input Parameters to Stored Procedure**:
        *   `@objectState`: Mapped from the service's `state` input. (NVARCHAR type)
        *   `@status`: Mapped from the service's `status` input. (NVARCHAR type)
        *   `@version`: Mapped from the service's `version` input. (NVARCHAR type)
        *   `@id`: Mapped from the service's `id` input. (NVARCHAR type)
        *   `@name`: Mapped from the service's `name` input. (NVARCHAR type)
    *   **Output Parameters from Stored Procedure**:
        *   `@Outputjson`: This parameter receives a JSON string from the stored procedure. (LONGNVARCHAR type)

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external APIs (e.g., HTTP REST calls to third-party services). Its primary data source is the internal SQL Server database, which is accessed via a JDBC adapter. From the perspective of Webmethods, the database adapter is an internal component rather than an external API.

## Main Service Flow

The `stakeholderFindList` service's flow executes as follows:

1.  **Initialization (MAP)**: A variable `stakeHolderIndex` is initialized to "0". This variable acts as a counter for populating the output `Stakeholders` array during iteration.
2.  **Database Call (INVOKE `getStakeHolderSPNoSignature`)**:
    *   Input parameters received by `stakeholderFindList` (`id`, `name`, `version`, `state`, `status`) are mapped directly to the corresponding input parameters for the `SP_Get_ExchangeRoleList;1` stored procedure.
    *   The stored procedure executes and returns its result as a JSON string in the `@Outputjson` field.
3.  **JSON to Document Conversion (INVOKE `pub.json:jsonStringToDocument`)**:
    *   The JSON string received from the database (`@Outputjson`) is converted into a Webmethods document (an in-memory hierarchical data structure). This document typically contains a `ResultSet` array, where each element is a record representing a row returned by the stored procedure.
    *   Intermediate variables related to the database call (`jsonString`, `getStakeHolderSPNoSignatureInput`, `getStakeHolderSPNoSignatureOutput`) are then deleted to free up memory.
4.  **Count Results (INVOKE `pub.list:sizeOfList`)**:
    *   The service calculates the number of records returned by the database by getting the `size` of the `document/ResultSet` array.
    *   The temporary `fromList` variable used for this calculation is deleted.
5.  **`idsOnly` Flag Conversion (INVOKE `cms.eadg.utils.map:convertBooleanToString`)**:
    *   The `idsOnly` boolean input parameter is converted into a string "Yes" or "No". This string is stored in a pipeline variable named `ids`.
    *   The original `idsOnly` boolean, `boolean` (temporary from conversion service), and `value` (output of conversion service) are then deleted.
6.  **Conditional Data Mapping (BRANCH on `/ids`)**: This is where the service's behavior changes based on the `idsOnly` flag.
    *   **If `ids` is "Yes" (meaning `idsOnly` was true)**:
        *   **Loop (`LOOP IN-ARRAY="/document/ResultSet"`)**: The service iterates through each record in the `ResultSet` obtained from the database.
            *   **Map for `idsOnly` Output**: For each record, only the `refstr` (mapped to `id`) and `name` (mapped to `name`) fields are extracted and populated into the `Stakeholders` array of the final response (`_generatedResponse`).
            *   **Increment Index**: `stakeHolderIndex` is incremented using `pub.math:addInts` to correctly populate the next element in the `Stakeholders` array.
        *   **Final Response Population**: After the loop, the accumulated `Stakeholders` array is moved to the `_generatedResponse` document. The temporary `Stakeholders` array is then deleted.
        *   **Count Conversion**: The `size` (total record count) is converted to a `Long` using `cms.eadg.utils.math:toNumberIf` and mapped to `_generatedResponse/count`.
    *   **If `ids` is anything else (e.g., "No", null, or not set)**:
        *   **Loop (`LOOP IN-ARRAY="/document/ResultSet"`)**: The service iterates through each record in the `ResultSet`.
            *   **Map for Full Output**: All relevant fields from the database record (`refstr`, `name`, `description`, `version`, `state`, `status`) are extracted and populated into the `Stakeholders` array of the final response (`_generatedResponse`).
            *   **Increment Index**: `stakeHolderIndex` is incremented using `pub.math:addInts`.
        *   **Final Response Population**: After the loop, the accumulated `Stakeholders` array is moved to the `_generatedResponse` document.
        *   **Count Conversion**: The `size` (total record count) is converted to a `Long` using `cms.eadg.utils.math:toNumberIf` and mapped to `_generatedResponse/count`.
7.  **Pipeline Cleanup (MAP)**: A final `MAP` step is executed to delete all intermediate variables from the pipeline, ensuring only the `_generatedResponse` (or error structures in a CATCH scenario) remains for the service's output.

## Dependency Service Flows

The main `stakeholderFindList` service relies on several other services to perform its operations:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.StakeHolder:getStakeHolderSPNoSignature`**:
    *   **Purpose**: This is the core data retrieval service. It abstracts the direct interaction with the database, allowing the main service to simply provide parameters and receive results without needing to know the specifics of JDBC or SQL.
    *   **Integration**: It's invoked early in the `TRY` block to fetch the raw stakeholder data (as a JSON string) from the `SP_Get_ExchangeRoleList;1` stored procedure.
    *   **Input/Output**: It takes filtered stakeholder attributes as input and returns a JSON string containing the query results.
*   **`pub.json:jsonStringToDocument`**:
    *   **Purpose**: A built-in Webmethods service used to parse a JSON string into a structured Webmethods document (an IData object). This makes the JSON data accessible for mapping within the flow service.
    *   **Integration**: Used immediately after the database call to convert the JSON output from the stored procedure into a usable document format.
*   **`pub.list:sizeOfList`**:
    *   **Purpose**: A built-in Webmethods service to determine the number of elements in a list or array.
    *   **Integration**: Used to get the total count of stakeholders returned by the database, which is then mapped to the `count` field in the final `StakeholderFindResponse`.
*   **`cms.eadg.utils.map:convertBooleanToString`**:
    *   **Purpose**: A utility service to convert a Java `Boolean` object (such as the `idsOnly` input) into a string "Yes" or "No".
    *   **Integration**: It's used to prepare the `idsOnly` input for the `BRANCH` statement, which relies on string matching ("Yes" vs. "$default").
*   **`pub.math:addInts`**:
    *   **Purpose**: A built-in Webmethods service to add two integer strings.
    *   **Integration**: Used repeatedly within the loops to increment the `stakeHolderIndex` for dynamically populating array elements in the output.
*   **`cms.eadg.utils.math:toNumberIf`**:
    *   **Purpose**: A utility service likely designed to safely convert a string to a number (specifically a `Long` in this case) only if it's a valid numeric string, otherwise it might handle nulls or non-numeric values gracefully.
    *   **Integration**: Used to convert the `size` (from `sizeOfList`) to a numeric `count` field for the `_generatedResponse`.
*   **`pub.flow:getLastError`**:
    *   **Purpose**: A built-in Webmethods service to retrieve details about the last error that occurred in the current pipeline.
    *   **Integration**: Called at the beginning of the `CATCH` block to get the exception information.
*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A common utility service for standardized error handling and response generation. It populates an error structure and sets HTTP response codes.
    *   **Integration**: Invoked in the `CATCH` block. It receives the `lastError` and formats a 500 Internal Server Error response. It then delegates to `cms.eadg.utils.api:setResponse` for actual response serialization and HTTP header setting.
*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: A utility service responsible for taking a structured response document, serializing it to JSON or XML, setting appropriate HTTP headers (`Content-Type`), and sending the response with the correct HTTP status code.
    *   **Integration**: Called by `handleError` (or potentially directly by the main service if a success response needs explicit formatting). It uses `pub.json:documentToJSONString` or `pub.xml:documentToXMLString` internally.
*   **`pub.json:documentToJSONString`**:
    *   **Purpose**: A built-in Webmethods service to convert a structured Webmethods document into a JSON string.
    *   **Integration**: Used by `cms.eadg.utils.api:setResponse` when the response format is JSON.
*   **`pub.xml:documentToXMLString`**:
    *   **Purpose**: A built-in Webmethods service to convert a structured Webmethods document into an XML string.
    *   **Integration**: Used by `cms.eadg.utils.api:setResponse` when the response format is XML, often wrapping the primary response in a "rooted" element.
*   **`pub.string:objectToString`**:
    *   **Purpose**: A built-in Webmethods service to convert any object to its string representation.
    *   **Integration**: Used by `cms.eadg.utils.map:convertBooleanToString` to get the string representation of the boolean value.
*   **`pub.flow:setResponseCode`**:
    *   **Purpose**: A built-in Webmethods service to set the HTTP response status code (e.g., 200 OK, 500 Internal Server Error).
    *   **Integration**: Used by `cms.eadg.utils.api:setResponse` to control the HTTP response status.
*   **`pub.flow:setResponse2`**:
    *   **Purpose**: A built-in Webmethods service to set the HTTP response body content and content type.
    *   **Integration**: Used by `cms.eadg.utils.api:setResponse` to deliver the final serialized (JSON or XML) response to the client.

## Data Structures and Types

The service defines and uses several document types (record structures in Webmethods) for its inputs and outputs:

*   **Service Input (implicit)**:
    *   `id`: `string` (optional, Stakeholder ID)
    *   `name`: `string` (optional, Stakeholder name)
    *   `version`: `string` (optional, Stakeholder version)
    *   `state`: `string` (optional, Stakeholder state)
    *   `status`: `string` (optional, Stakeholder status)
    *   `idsOnly`: `object` (specifically, `java.lang.Boolean`, optional, indicates if only IDs and names should be returned)

*   **Output Data Model (recref: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:StakeholderFindResponse`)**:
    *   `count`: `object` (specifically, `java.math.BigInteger` type after conversion, represents the total number of stakeholders found).
    *   `Stakeholders`: An array (`field_dim=1`) of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Stakeholder` objects.

*   **Stakeholder Data Model (recref: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Stakeholder`)**:
    *   `id`: `string` (required)
    *   `name`: `string` (optional)
    *   `description`: `string` (optional)
    *   `version`: `string` (optional)
    *   `state`: `string` (optional)
    *   `status`: `string` (optional)
    (Note: `description`, `version`, `state`, `status` are only populated if `idsOnly` is `false` or not provided.)

*   **Error Response Data Model (recref: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`)**:
    *   `result`: `string` (e.g., "error", "success")
    *   `message`: `string[]` (an array of messages, typically error details)

*   **Intermediate Data Structures**:
    *   `document`: The parsed JSON output from the database, containing a `ResultSet` array. The structure of `ResultSet` depends on the stored procedure's JSON output. Based on mapping, it includes:
        *   `refstr`: The stakeholder ID (used for `id` field).
        *   `name`: The stakeholder name (used for `name` field).
        *   `description`: The stakeholder description (used for `description` field in full mode).
        *   `version`: The stakeholder version (used for `version` field in full mode).
        *   `state`: The stakeholder state (used for `state` field in full mode).
        *   `status`: The stakeholder status (used for `status` field in full mode).
    *   `stakeHolderIndex`: `string` (internal counter for array mapping).
    *   `size`: `string` (result from `pub.list:sizeOfList`, represents the total count).
    *   `ids`: `string` (result of `idsOnly` boolean conversion, either "Yes" or "No").

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' built-in `TRY-CATCH` mechanism.

*   **Catching Errors**: The entire main service flow is wrapped in a `TRY` block. If any error or exception occurs during the execution of any step within this `TRY` block (e.g., database connectivity issues, data parsing errors, unhandled null pointers), the execution immediately shifts to the `CATCH` block.
*   **Retrieving Error Details**: Inside the `CATCH` block, `pub.flow:getLastError` is invoked. This service retrieves a detailed `exceptionInfo` document, which includes the error message, stack trace, and other relevant exception properties.
*   **Standardized Error Response**: The `cms.eadg.utils.api:handleError` service is then called.
    *   By default, `handleError` constructs an error response indicating an "Internal Server Error" (HTTP 500). It sets the `result` field to "error" and populates the `message` field(s) with the error details obtained from `pub.flow:getLastError`.
    *   The `handleError` service then calls `cms.eadg.utils.api:setResponse` to finalize the response.
*   **Response Serialization and HTTP Status**: The `cms.eadg.utils.api:setResponse` service:
    *   Takes the constructed error details.
    *   Serializes them into either a JSON or XML string based on the `format` specified (defaults to `application/json` in `handleError`).
    *   Calls `pub.flow:setResponseCode` to set the appropriate HTTP status code (e.g., 500).
    *   Calls `pub.flow:setResponse2` to write the serialized error message to the HTTP response body.

The `node.ndf` for `stakeholderFindList` explicitly lists the following potential HTTP response codes in its `sig_out` (signature output) section, indicating that the service is designed to return these under different error conditions:

*   `400`: Bad Request (likely for input validation failures, though not explicitly shown in this flow).
*   `401`: Unauthorized.
*   `404`: Not Found.
*   `500`: Internal Server Error (the default for uncaught exceptions handled by `cms.eadg.utils.api:handleError`).

## Detailed Data Mapping (Source to Output)

The service fetches data from the `SP_Get_ExchangeRoleList;1` stored procedure. This stored procedure returns a JSON string, which `pub.json:jsonStringToDocument` converts into a document structure. The fields within this intermediate `document/ResultSet` are then mapped to the `Stakeholder` output object.

SQL Stored Procedure: `SP_Get_ExchangeRoleList;1`

Database Column (from `document/ResultSet`) to Output Object Property (in `Stakeholders` array):

*   When `idsOnly` is `true` (or "Yes"):
    *   `refstr`: `id`
    *   `name`: `name`

*   When `idsOnly` is `false` (or "No" or unspecified/default):
    *   `refstr`: `id`
    *   `name`: `name`
    *   `description`: `description`
    *   `version`: `version`
    *   `state`: `state`
    *   `status`: `status`

*   Additionally, the total count of returned records is mapped:
    *   `size` (from `pub.list:sizeOfList` applied to `document/ResultSet`): `_generatedResponse/count`

It's important to note that the `SP_Get_ExchangeRoleList;1` stored procedure determines the exact column names (like `refstr`, `name`, `description`, etc.) and their data types as part of its JSON output. The Webmethods flow service then consumes this JSON and maps these specific names to the `Stakeholder` document type fields.