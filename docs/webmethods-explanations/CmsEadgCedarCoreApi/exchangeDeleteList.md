# Webmethods Service Explanation: CmsEadgCedarCoreApi exchangeDeleteList

This document provides a comprehensive explanation of the Webmethods service `exchangeDeleteList` within the `CmsEadgCedarCoreApi` package. It details the service's purpose, internal mechanics, data transformations, and interactions with external systems, focusing on aspects relevant for porting to TypeScript.

*   Package Name: `CmsEadgCedar<PERSON>oreApi`
*   Service Name: `exchangeDeleteList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `exchangeDeleteList` service is designed to perform a "logical delete" or status update on a list of data exchange records within a backend system. Despite its name suggesting a hard delete, the underlying implementation uses an update operation. This service is typically exposed as an API endpoint to allow external systems to mark data exchanges as inactive or deleted without physically removing them from the database.

The primary input to the service is an array of data exchange identifiers. However, a deeper analysis of the flow indicates that the service expects a more comprehensive input structure (`ExchangeAddRequest`) containing the full `Exchange` objects that represent the data exchanges to be updated. The `id` array, explicitly defined as an input parameter, seems to primarily serve for initial validation of the list size and may be a remnant or alternative input not fully utilized in this specific flow's core logic for the actual update.

The expected output of the service is a standard response indicating the success or failure of the update operation, including messages detailing any partial failures or errors encountered during processing. There are no direct outputs of transformed database columns as JSON objects; the service primarily facilitates a data modification operation and reports its outcome.

Key validation rules include checking the count of requested objects against the count of successfully updated objects to determine if the operation was a full success, a partial success, or a total failure.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods uses a flow-based programming model where services are constructed by arranging pre-defined steps. These steps manipulate data within a shared memory space called the "pipeline" (similar to a context object or data bag in other frameworks).

*   **SEQUENCE**: Analogous to a sequential block of code in traditional programming. Steps within a SEQUENCE execute in order. If a step within a SEQUENCE fails (throws an exception), the entire SEQUENCE can be configured to exit on failure, similar to an early return or throwing an exception in a code block.
*   **BRANCH**: Similar to a `switch` statement or `if-else if-else` ladder. It evaluates a single expression (`SWITCH` attribute) and executes the first child SEQUENCE whose `NAME` attribute matches the evaluated expression. A `$default` branch acts like a `default` case.
*   **MAP**: This is the core data transformation step, similar to an object mapper or data transfer object (DTO) transformation. It allows copying, setting, and deleting data fields within the pipeline.
    *   **MAPSET**: Assigns a static value to a field.
    *   **MAPCOPY**: Copies data from one field to another.
    *   **MAPDELETE**: Removes a field from the pipeline.
*   **INVOKE**: Calls another Webmethods service or a built-in function (like `pub.list:sizeOfList`). This is similar to calling a function or method in traditional programming.
*   **Error Handling (TRY/CATCH blocks)**: Represented by `SEQUENCE` elements with `FORM="TRY"` and `FORM="CATCH"`. A `TRY` block encapsulates operations that might fail. If an error occurs within the `TRY` block, execution immediately jumps to the corresponding `CATCH` block. This is comparable to `try...catch` blocks in JavaScript or Java.
*   **Pipeline Data Paths**: Fields in Webmethods are referenced using XPath-like paths, e.g., `/UpdateRequest/Objects`. Array elements are accessed using `[index]`, e.g., `Relations[%indexRelations%]`.

## Database Interactions

The service primarily interacts with the database through a single JDBC adapter service: `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP`. This adapter service is configured to invoke a stored procedure.

*   **Database Operations Performed**: The `exchangeDeleteList` service performs an **update** operation on data exchanges, which functions as a "soft delete" or "logical delete" by modifying a status field rather than physically removing the records.
*   **Database Connection Configuration**: The adapter uses the `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans` JDBC connection.
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Database Name**: `Sparx_Support`
    *   **Port Number**: `1433`
    *   **User**: `sparx_dbuser` (password is externalized)
    *   **Transaction Type**: `NO_TRANSACTION` (meaning each operation is committed individually, no atomic transaction across multiple operations).
*   **SQL Queries or Stored Procedures Called**:
    *   **Stored Procedure**: `SP_Update_SystemDataExchange_json`
    *   This stored procedure takes two `NVARCHAR` parameters: `@jsonInput` and `@jsonOutput` (which is also an output parameter). It also returns an `INTEGER` value as `@RETURN_VALUE`. This indicates that the stored procedure processes and returns data in JSON format, handling the internal mapping to/from database tables. The specific tables or views used by this stored procedure are encapsulated within its definition on the database side and are not exposed in the Webmethods configuration.
*   **Data Mapping between Service Inputs and Database Parameters**:
    *   **Input Data to Database**: The `UpdateRequest` document (specifically its `Objects` array, where each object's `Values` field contains an `InformationFlow` document) is converted into a JSON string (`@jsonInput`) and passed to `SP_Update_SystemDataExchange_json`. This `InformationFlow` document contains all the fields necessary to identify and update an `Exchange` record, including setting its "deleted" status.
    *   **Output Data from Database**: The `SP_Update_SystemDataExchange_json` returns a JSON string (`@jsonOutput`) and an integer return value (`@RETURN_VALUE`). The `@jsonOutput` is then mapped to the `UpdateResponse` document type. The `@RETURN_VALUE` (presumably indicating the count of affected records) is mapped to the `UpdateResponse.Count` field.

## External API Interactions

Based on the provided files, this service does not directly call any external REST or SOAP APIs. Its primary interaction is with a backend database via a JDBC adapter. The document types with `alfabet.api.v01` in their names suggest that the Webmethods environment might integrate with an "Alfabet" system, and these document types are likely used to structure data for that integration. However, in this specific `exchangeDeleteList` service, the interaction is abstracted through the `updateExchangeSP` which handles the underlying communication, possibly with Alfabet via the stored procedure.

## Main Service Flow

The main service, `exchangeDeleteList`, follows a structured flow within a `TRY-CATCH` block for robust error handling.

1.  **Initialize Variables**:
    *   A `MAP` step initializes internal variables.
    *   It calls `pub.list:sizeOfList` to determine the `listSize` of the input `id` array. This is likely for validation or reporting purposes.
2.  **Prepare Update Request**:
    *   It invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapExchange` service. This is a critical step where the incoming `ExchangeAddRequest` (which contains an array of `Exchange` objects) is transformed into the `UpdateRequest` document type required by the underlying database stored procedure. This includes mapping various fields and creating associated `Relations` objects.
    *   After `mapExchange`, the `UpdateRequest` document is converted into a `jsonString` using `pub.json:documentToJSONString`.
3.  **Execute Database Update**:
    *   The service then invokes the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP` adapter service. This service executes the `SP_Update_SystemDataExchange_json` stored procedure in the backend database, passing the prepared `jsonString` as input.
4.  **Handle Database Response**:
    *   A `BRANCH` step is used to evaluate the `@RETURN_VALUE` received from the `updateExchangeSP`. This value likely indicates the success or failure count of the database operation.
    *   **Successful Update (Return Value "0")**: If the return value is "0" (as seen in the `SEQUENCE NAME="0"` branch), it means the update operation succeeded for the requested items. The service then (conditionally, if the `mapResponse` service is enabled) calls `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapResponse` to generate a success `Response` object. A `MAP` step explicitly sets the `result` to "success".
    *   **Default/Unhandled Cases**: The `$default` branch indicates that any non-zero return value from `updateExchangeSP` (or any other unhandled condition in the branch) will lead to an `EXIT` with a `FAILURE` signal, indicating an unspecific failure.
5.  **Cleanup**: A final `MAP` step performs cleanup by deleting intermediate variables from the pipeline, ensuring a clean output.
6.  **Error Handling (CATCH block)**:
    *   If any error occurs during the main `TRY` sequence, the `CATCH` block is executed.
    *   It first retrieves the last error information using `pub.flow:getLastError`.
    *   Then, it invokes `cms.eadg.utils.api:handleError` to process the error. This utility service sets appropriate HTTP response codes, phrases, and error messages based on the nature of the error, ultimately returning an error response to the client.

## Dependency Service Flows

The `exchangeDeleteList` service relies on several key dependency services to perform its operations:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapExchange`**:
    *   **Purpose**: This is the core data transformation service. Its role is to convert the client-provided `Exchange` objects (nested within an `ExchangeAddRequest`) into the `UpdateRequest` structure expected by the backend `SP_Update_SystemDataExchange_json` stored procedure.
    *   **Integration**: It's invoked early in the main service flow to prepare the data payload for the database call.
    *   **Input/Output Contract**:
        *   Input: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` objects.
        *   Output: A single `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest` object.
    *   **Specialized Processing**: This service iterates through each `Exchange` object, mapping its fields to corresponding fields within a generic `InformationFlow` document. It also constructs `Relations` objects to represent the relationships between the `Exchange` (which maps to an "InformationFlow" in the backend system) and its associated `fromOwnerId`, `toOwnerId`, and `typeOfData` elements. It leverages utility services like `pub.string:makeString` (to join array elements with `|` separator), `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:convertBooleanToString` (to convert boolean values to string "true" or "false"), `cms.eadg.utils.date:formatDateIf` (to format dates), and `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:createExchangeName` (to construct a name).

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP`**:
    *   **Purpose**: This is the database adapter service. It's responsible for executing the `SP_Update_SystemDataExchange_json` stored procedure in the `Sparx_Support` database.
    *   **Integration**: It's invoked after `mapExchange` has prepared the JSON input string.
    *   **Input/Output Contract**:
        *   Input: A JSON string (`@jsonInput`) derived from the `UpdateRequest` document.
        *   Output: An integer return value (`@RETURN_VALUE`) and a JSON string (`@jsonOutput`), which is then mapped to an `UpdateResponse` object.
    *   **Specialized Processing**: This service abstracts the direct SQL call, enabling Webmethods to interact with a stored procedure that handles the actual data manipulation logic and potentially complex business rules within the database.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapResponse`**:
    *   **Purpose**: This service maps the detailed `UpdateResponse` received from the database (via `updateExchangeSP`) to a simpler, generic `Response` object (`result`, `message`) for the client. It also determines the overall success status (full, partial, or total failure).
    *   **Integration**: It's invoked after `updateExchangeSP` returns, typically within a `BRANCH` structure to handle different outcomes.
    *   **Input/Output Contract**:
        *   Input: The original `UpdateRequest` (for comparison) and the `UpdateResponse` from the database.
        *   Output: A `cms.eadg.utils.api.docs:Response` object.
    *   **Specialized Processing**: It calculates the total count of objects and relations in the original request and compares it to the `Count` returned in the `UpdateResponse`. It uses a `BRANCH` with label expressions (`%requestTotalCount% == %responseCount%` and `%responseCount% == 0`) to determine the success state. In case of partial failure, it calls `cms.eadg.alfabet.api.v01.resources.update.utils:extractRejectedObjectsMessages` to extract specific error messages from rejected objects, and `pub.list:appendToStringList` to consolidate these messages.

*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: A generic error handling utility service. It normalizes error messages and sets appropriate HTTP response details.
    *   **Integration**: Invoked in the `CATCH` block of the main service and within `mapResponse` for specific error scenarios.
    *   **Specialized Processing**: It uses `pub.flow:getLastError` to retrieve details of the last thrown exception and then sets standard `responseCode`, `responsePhrase`, `result`, `message`, and `format` fields in a `SetResponse` document.

*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service takes the prepared `SetResponse` document and converts the response body to the specified format (JSON or XML) and sets the HTTP response code.
    *   **Integration**: Called by `handleError` and typically at the end of successful paths to prepare the final HTTP response.
    *   **Specialized Processing**: It uses `pub.json:documentToJSONString` or `pub.xml:documentToXMLString` based on the `SetResponse.format` field to serialize the response object into a string. It then uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to send the HTTP response.

## Data Structures and Types

The service heavily relies on several Webmethods Document Types, which define the structure of data processed in the pipeline. These are analogous to DTOs or data models in TypeScript.

*   **Input Data Model (`id` array, `ExchangeAddRequest` which contains `Exchange` objects)**:
    *   `id`: `string[]` (array of string identifiers for exchanges). This is the explicit input but its direct usage in the core logic is limited to `listSize` calculation.
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeAddRequest`: The actual data payload for the operation.
        *   `Exchanges`: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange[]` (an array of Exchange objects).
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`: Represents a single data exchange. Many fields are optional (`field_opt`).
        *   `exchangeId`: `string` (unique identifier for the exchange)
        *   `exchangeName`: `string` (name of the exchange)
        *   `exchangeDescription`: `string` (description of the exchange)
        *   `exchangeVersion`: `string` (version of the exchange)
        *   `exchangeState`: `string` (current state/status, e.g., 'Active', 'Deleted')
        *   `exchangeStartDate`: `Date` (start date of the exchange)
        *   `exchangeEndDate`: `Date` (end date of the exchange)
        *   `exchangeRetiredDate`: `Date` (retirement date, likely set during "delete")
        *   `fromOwnerId`: `string` (ID of the source owner)
        *   `fromOwnerName`: `string` (Name of the source owner)
        *   `fromOwnerType`: `string` (Type of the source owner)
        *   `toOwnerId`: `string` (ID of the target owner)
        *   `toOwnerName`: `string` (Name of the target owner)
        *   `toOwnerType`: `string` (Type of the target owner)
        *   `connectionFrequency`: `string[]` (array of frequencies, e.g., 'Daily', 'Weekly')
        *   `dataExchangeAgreement`: `string` (agreement details)
        *   `containsBeneficiaryAddress`: `boolean` (flag if PII address exists)
        *   `businessPurposeOfAddress`: `string[]` (array of business purposes for address)
        *   `isAddressEditable`: `boolean` (flag if address is editable)
        *   `containsPii`: `boolean` (flag if contains Personally Identifiable Information)
        *   `containsPhi`: `boolean` (flag if contains Protected Health Information)
        *   `containsHealthDisparityData`: `boolean` (flag if contains health disparity data)
        *   `containsBankingData`: `boolean` (flag if contains banking data)
        *   `isBeneficiaryMailingFile`: `boolean` (flag if is a beneficiary mailing file)
        *   `sharedViaApi`: `boolean` (flag if shared via API)
        *   `apiOwnership`: `string` (API ownership details)
        *   `typeOfData`: Array of `{id: string, name: string}` (types of data exchanged)
        *   `numOfRecords`: `string` (number of records)
        *   `dataFormat`: `string` (format of data, e.g., 'JSON', 'XML')
        *   `dataFormatOther`: `string` (other data format details)
        *   `exchangeContainsCUI`: `boolean` (flag if contains Controlled Unclassified Information)
        *   `exchangeCUIDescription`: `string` (CUI description)
        *   `exchangeCUIType`: `string[]` (array of CUI types)
        *   `exchangeConnectionAuthenticated`: `boolean` (flag if connection is authenticated)
        *   `exchangeNetworkProtocol`: `string[]` (array of network protocols)
        *   `exchangeNetworkProtocolOther`: `string` (other network protocol details)

*   **Intermediate Data Model (for database interaction)**:
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`: The payload sent to the stored procedure as JSON.
        *   `CurrentProfile`: `string` (set to "API User")
        *   `APICulture`: `string` (optional)
        *   `Objects`: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object[]` (array of generic objects). Each `Object` contains:
            *   `ClassName`: `string` (set to "InformationFlow")
            *   `RefStr`: `string` (mapped from `Exchange.exchangeId`)
            *   `Values`: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:InformationFlow` (a flat structure containing many fields, derived from `Exchange` fields). This acts as the direct data record for the database update.
        *   `Relations`: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations[]` (array of relations). These define relationships between objects (e.g., Exchange to FromOwner, Exchange to ToOwner, Exchange to TypeOfData). Each `Relations` object has:
            *   `FromRef`: `string` (source object ID)
            *   `ToRef`: `string` (target object ID)
            *   `Property`: `string` (type of relationship, e.g., "From", "To", "FromOwner", "ToOwner", "cms_data_exch_type_of_data")

*   **Output Data Models**:
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateResponse`: The structured response received from the `SP_Update_SystemDataExchange_json` procedure.
        *   `NewObjects`: `any` (placeholder for newly created objects, not expected for delete)
        *   `RejectedObjects`: `Array<{RefStr: string, Message: string}>` (list of objects that failed to update with error messages)
        *   `NotFound`: `any` (list of objects not found)
        *   `count`: `number` (total count of successful operations)
        *   `Count`: `number` (duplicate of `count`, usually for different API versions)
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` (or `cms.eadg.utils.api.docs:Response`): The final output returned to the client.
        *   `result`: `string` (e.g., "success", "error")
        *   `message`: `string[]` (array of messages, including success or error details).

*   **Data Transformation Logic**:
    *   **Boolean to String**: Boolean fields from `Exchange` (e.g., `isAddressEditable`, `containsPii`) are converted to string representations (likely "true" or "false") before being mapped to `InformationFlow` fields.
    *   **Date Formatting**: `Date` objects from `Exchange` (e.g., `exchangeRetiredDate`) are formatted into `MM/dd/yyyy` strings for `InformationFlow`.
    *   **Array Joining**: Array fields from `Exchange` (e.g., `connectionFrequency`, `businessPurposeOfAddress`) are joined into a single string using a `|` delimiter for `InformationFlow`.
    *   **Relations Creation**: For array fields like `typeOfData`, instead of directly mapping to a single field in `InformationFlow`, separate `Relations` objects are created to represent the many-to-many relationship with related lookup entities.

## Error Handling and Response Codes

The service employs a comprehensive error handling strategy using Webmethods' `TRY-CATCH` blocks and utility services.

*   **Different Error Scenarios Covered**:
    *   **Internal Service Errors (500)**: Any unhandled exception during the main flow (caught by the main `CATCH` block) will result in a 500 Internal Server Error. The message will include the details of the last error captured.
    *   **Total Failure (400 Bad Request)**: If the `responseCount` from the database (number of successfully updated records) is `0`, but the `requestTotalCount` (total items sent for update) is greater than zero, it signifies a total failure. The service returns a 400 Bad Request with a message like "No updates made. Invalid request."
    *   **Partial Failure (400 Bad Request)**: If `responseCount` is greater than `0` but less than `requestTotalCount`, it indicates a partial failure. The service returns a 400 Bad Request with a message like "Partial failure. Please re-pull exchanges." Additionally, it extracts detailed messages from the `RejectedObjects` list returned by the database and appends them to the response messages.
    *   **Success (200 OK)**: If `responseCount` equals `requestTotalCount`, it's a full success, returning a 200 OK with a "All records successfully updated" message.

*   **HTTP Response Codes Used**:
    *   `200 OK`: For successful operations.
    *   `400 Bad Request`: For total or partial failures related to the input data or database operation outcomes.
    *   `500 Internal Server Error`: For unexpected system errors.

*   **Error Message Formats**:
    *   Error messages are consolidated into a `message` array within the `Response` object.
    *   For internal errors, the `error` message from `pub.flow:getLastError` is used.
    *   For partial failures, specific messages from `RejectedObjects` (from the database response) are appended.

*   **Fallback Behaviors**: The `cms.eadg.utils.api:handleError` service acts as a centralized error dispatcher, ensuring that even if specific error branches are not explicitly defined, a generic 500 Internal Server Error is returned, providing some level of detail from the caught exception.