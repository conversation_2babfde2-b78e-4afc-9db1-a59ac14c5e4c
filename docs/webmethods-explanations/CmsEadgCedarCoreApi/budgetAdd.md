# Webmethods Service Explanation: CmsEadgCedarCoreApi budgetAdd

The `budgetAdd` service, found within the `CmsEadgCedarCoreApi` package, is designed to facilitate the addition of new budget entries into the Alfabet system, a software product often used for IT planning and portfolio management. This service acts as an intermediary, taking a list of budget documents as input, transforming them into a format suitable for Alfabet, and then persisting this data through a database stored procedure.

The service's primary business purpose is to enable the bulk creation of budget records associated with projects and systems. It expects a list of `Budget` documents, each containing details such as `projectId`, `systemId`, `fundingId`, and `funding`. The successful execution of this service results in a generic success `Response` object. Key validation includes ensuring that a list of budgets is provided; otherwise, the service will fail immediately.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `budgetAdd`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm, where services are constructed by connecting a series of "nodes" in a "flow". These nodes represent common programming constructs:

*   **SEQUENCE**: Analogous to a block of code, statements within a sequence execute in order. If any step within a sequence fails, the entire sequence (and potentially the calling block) fails.
*   **BRANCH**: Similar to a `switch` or `if/else if/else` statement in traditional programming. It evaluates a specified variable or expression and directs the flow to a specific sub-sequence (or "path") based on the value. A `$null` case often handles missing or null values, and a `$default` case acts as a catch-all.
*   **MAP**: This is a powerful data transformation step. It's where data is moved, manipulated, or transformed between different variables or document types within the service's "pipeline" (its in-memory data context).
    *   **MAPSET**: Assigns a literal value to a variable or field.
    *   **MAPCOPY**: Copies the value from one variable or field to another.
    *   **MAPDELETE**: Removes a variable or field from the pipeline.
*   **INVOKE**: Represents a call to another service, function, or adapter. This is how modularity is achieved, allowing services to reuse logic defined elsewhere.
*   **TRY/CATCH Blocks (FORM="TRY" and FORM="CATCH" on SEQUENCE nodes)**: Webmethods flow services support structured error handling akin to `try-catch` blocks. A `SEQUENCE` with `FORM="TRY"` will attempt to execute its steps. If an error occurs, control immediately transfers to a corresponding `SEQUENCE` with `FORM="CATCH"`. This allows for graceful error recovery or standardized error reporting.
*   **Input Validation and Branching Logic**: Input validation often happens early in a service using `BRANCH` statements to check for mandatory inputs. If a required input is missing, an `EXIT` step can be used to terminate the service flow with a specified failure message, preventing further processing with invalid data.

## Database Interactions

The `budgetAdd` service interacts with a SQL database to persist the budget information. This interaction occurs via the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:addBudgetInput` adapter service.

The `addBudgetInput` adapter is a JDBC Adapter Service, specifically configured to call a stored procedure.

*   **Database Connection**:
    *   The connection used is `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.
    *   This connection points to a SQL Server database.
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port Number**: `1433`
    *   **Database Name**: `Sparx_Support`
    *   **User**: `sparx_dbuser` (password is stored separately)
    *   **Transaction Type**: `NO_TRANSACTION` (meaning this operation is not part of a larger, coordinated database transaction managed by the Integration Server).

*   **SQL Stored Procedure Called**:
    *   **Stored Procedure Name**: `SP_Insert_SystemBudget_json`
    *   **Catalog Name**: `<current catalog>` (likely `Sparx_Support`)
    *   **Schema Name**: `<current schema>` (implies the default schema for `sparx_dbuser`, typically `dbo`)

*   **Data Mapping to Stored Procedure Parameters**:
    *   The adapter service `addBudgetInput` takes one primary input, `@jsonInput`, which is a string (`NVARCHAR`) and is mapped from the `jsonString` field in the pipeline. This `jsonString` is generated by serializing the `UpdateRequest` document (which contains the transformed budget data) into a JSON string.
    *   The stored procedure is expected to return an integer value, which is captured by `@RETURN_VALUE` (type `INTEGER`). This return value is used by the main service flow to determine success or failure.

*   **Data Mapping for Database Insert**:
    The `mapBudget` service prepares the data for the `SP_Insert_SystemBudget_json` stored procedure. It transforms the incoming `Budget` documents into an `UpdateRequest` structure, which comprises `Objects` and `Relations`. The `Objects` contain the actual budget data mapped into a `ProjectArch` format, and `Relations` define the linkages.

    The mapping from the input `Budget` document to the `ProjectArch` object (which is then embedded in `UpdateRequest/Objects/Values`) and `Relations` is as follows:

    *   `Budgets.funding`: `ProjectArch.cms_funding`
    *   `Budgets.systemId`: `ApplicationRelation.ToRef`
    *   `Budgets.id`: `ProjectRelation.ToRef`
    *   `$iteration` (internal loop counter for each Budget in the input list):
        *   `UpdateRequest.Objects.Id`
        *   `ApplicationRelation.FromId`
        *   `ProjectRelation.FromId`

    Hardcoded values for mapping:
    *   `ProjectArch.ClassName`: "ProjectArch"
    *   `ProjectRelation.Property`: "project"
    *   `ApplicationRelation.Property`: "object"

    This indicates that for each input `Budget`, a `ProjectArch` object is created with its `cms_funding` attribute populated. Additionally, two `Relations` entries are created: one linking the newly created `ProjectArch` (via `$iteration` as `FromId`) to the `Budget.id` (as `ToRef`) with a "project" property, and another linking the `ProjectArch` to `Budget.systemId` with an "object" property. This suggests a complex data model in Alfabet involving generic "Objects" and "Relations" between them, where "ProjectArch" is a specific type of "Object".

## External API Interactions

Based on the provided XML files, this service does not directly invoke external REST or SOAP APIs outside of the Webmethods environment. All `INVOKE` statements point to internal Webmethods services (`pub.json`, `pub.flow`, `cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetAdd:mapBudget`, `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:addBudgetInput`, `cms.eadg.utils.api:handleError`, `cms.eadg.utils.api:setResponse`) or database adapters. The `addBudgetInput` service, while interacting with a database, is considered an internal integration component rather than an external API call in the typical sense (e.g., calling a third-party REST service over HTTP).

## Main Service Flow

The `budgetAdd` service executes in a structured flow, encapsulated within a `TRY` block to ensure robust error handling:

1.  **Input Validation (Branch on `/_generatedInput/Budgets`)**:
    *   The service first checks if the `Budgets` list within the `_generatedInput` document (which represents the API request payload) is present.
    *   If `Budgets` is `$null` (meaning no budgets were provided), the service immediately exits with a `FAILURE` signal and a message: "Please provide a list of budgets." This is a crucial early exit validation step.

2.  **Map Budget Data (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetAdd:mapBudget`)**:
    *   If the input validation passes, the service invokes the `mapBudget` dependency service.
    *   **Input Mapping**: The entire `Budgets` array from `_generatedInput` is copied to the `Budgets` input of `mapBudget`. The original `_generatedInput` is then deleted from the pipeline.
    *   **Output Mapping**: Upon successful execution of `mapBudget`, the `Budgets` input to `mapBudget` (which is now the input to this `budgetAdd` service) is deleted. The `UpdateRequest` document generated by `mapBudget` remains in the pipeline.

3.  **Convert UpdateRequest to JSON String (`INVOKE pub.json:documentToJSONString`)**:
    *   The `UpdateRequest` document, which now holds the transformed budget data in a format ready for the database, is converted into a JSON string. This JSON string will be passed as an input parameter to the stored procedure.
    *   **Input Mapping**: The `UpdateRequest` is mapped to the `document` input of `pub.json:documentToJSONString`.
    *   **Output Mapping**: The resulting `jsonString` is retained in the pipeline. Both the original `document` and `UpdateRequest` are deleted to clean up the pipeline.

4.  **Add Budget Input to Database (`INVOKE cms.eadg.cedar.core.api.v2.cedarCore_.adapters.budget:addBudgetInput`)**:
    *   The service then calls the database adapter `addBudgetInput`.
    *   **Input Mapping**: The `jsonString` generated in the previous step is mapped to the `@jsonInput` parameter of the `addBudgetInput` adapter.
    *   **Output Mapping**: The `addBudgetInputOutput` from the adapter (which contains the `@RETURN_VALUE` from the stored procedure) is retained, and the `addBudgetInputInput` is deleted.

5.  **Check Database Operation Result (Branch on `/addBudgetInputOutput/@RETURN_VALUE`)**:
    *   The flow branches based on the `@RETURN_VALUE` received from the `addBudgetInput` adapter.
    *   **Success (Case "0")**: If `@RETURN_VALUE` is "0", it signifies a successful database operation.
        *   A `MAP` step is executed to construct the success response. The `jsonString` and `addBudgetInputOutput` are deleted.
        *   The `_generatedResponse/result` field is explicitly set to the literal string "success".
    *   **Failure (Case "$default")**: If `@RETURN_VALUE` is anything other than "0" (or null, implicitly), the `$default` branch is taken, signaling a `FAILURE` to the parent (which is the `TRY` block). This will cause the execution to jump to the `CATCH` block.

6.  **Error Handling (`CATCH` Block)**:
    *   If any error occurs within the `TRY` block (including the `EXIT` on null budgets or the `$default` branch after the database call), control transfers here.
    *   `INVOKE pub.flow:getLastError`: Retrieves details of the last error that occurred, populating the `lastError` document in the pipeline.
    *   `INVOKE cms.eadg.utils.api:handleError`: This utility service is invoked to standardize error responses. It takes the `lastError` and an optional `SetResponse` document as input. It primarily sets generic error messages and response codes (e.g., 500 Internal Server Error by default if no specific `SetResponse` is provided) and cleans up the error-related fields from the pipeline before returning control.

## Dependency Service Flows

The main `budgetAdd` service relies on several other services to perform its tasks:

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.budgetAdd:mapBudget`**:
    *   **Purpose**: This is a crucial transformation service. Its role is to convert the incoming `Budget` document type into the `UpdateRequest` structure, which is the expected payload for the Alfabet system's update API (presumably via the `SP_Insert_SystemBudget_json` stored procedure).
    *   **Flow**:
        *   **Initialization**: Sets `UpdateRequest/CurrentProfile` to "API User".
        *   **Looping**: It iterates (`LOOP`) over each `Budget` document in the input `Budgets` array. For each `Budget`:
            *   **Initial Mapping**: It maps `Budget.funding` to `ProjectArch.cms_funding`. It also prepares parts of two `Relations` objects (`ProjectRelation` and `ApplicationRelation`) by copying `Budget.systemId` to `ApplicationRelation.ToRef` and `Budget.id` to `ProjectRelation.ToRef`. It hardcodes the `Property` for these relations ("project" and "object" respectively).
            *   **ID Generation**: It uses the internal loop counter `$iteration` as a unique identifier for the `Object` being created within the `UpdateRequest`. This `$iteration` is copied to `UpdateRequest/Objects/Id`, `ApplicationRelation/FromId`, and `ProjectRelation/FromId`. This indicates that the stored procedure expects a temporary, unique identifier for each object within the batch to correlate it with its relations.
            *   **Completing Object Mapping**: It copies the `ProjectArch` document (which was partially populated) into `UpdateRequest/Objects/Values`. It then hardcodes `UpdateRequest/Objects/ClassName` to "ProjectArch".
            *   **Appending Relations**: It uses `pub.list:appendToDocumentList` to append both the `ProjectRelation` and `ApplicationRelation` documents to the `UpdateRequest/Relations` array. This builds up the list of relations for the entire batch update.
            *   **Cleanup**: `ProjectRelation` and `ApplicationRelation` are deleted after being appended.
        *   **Final Cleanup**: After the loop finishes, the input `Budgets` array and the `$iteration` variable are deleted from the pipeline.
    *   **Integration with Main Flow**: The main service `budgetAdd` invokes `mapBudget` early in its flow to prepare the data for the database call.

2.  **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This service provides a centralized mechanism for handling errors in Webmethods APIs. It takes the detailed error information and formats it into a standardized API response.
    *   **Flow**:
        *   It receives `lastError` (from `pub.flow:getLastError`) which contains the exception details.
        *   It can optionally receive a `SetResponse` document if a specific error code/message needs to be returned.
        *   If no `SetResponse` is provided (or the `$null` branch is taken), it defaults to setting a 500 Internal Server Error:
            *   `responseCode` is set to "500".
            *   `responsePhrase` is set to "Internal Server Error".
            *   `result` is set to "error".
            *   `message` is copied from `lastError/error`.
            *   `format` is set to "application/json".
        *   It then invokes `cms.eadg.utils.api:setResponse` to finalize the HTTP response.
        *   **Cleanup**: Deletes `lastError` and `SetResponse` from the pipeline.
    *   **Integration with Main Flow**: The `budgetAdd` service calls `handleError` within its `CATCH` block when an exception occurs.

3.  **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is responsible for setting the HTTP response code and formatting the response body (either JSON or XML) based on the content type requested or defaulted.
    *   **Flow**:
        *   It maps the `result` and `message` from the input `SetResponse` to a `Response` document.
        *   It then branches based on `SetResponse/format` (e.g., "application/json" or "application/xml").
            *   **JSON Branch**: If "application/json", it invokes `pub.json:documentToJSONString` to convert the `Response` document into a `jsonString`. This `jsonString` is then mapped to `responseString`.
            *   **XML Branch**: If "application/xml", it first "roots" the `Response` document into a `ResponseRooted` document (a common practice for XML structures to have a single root element). Then, it invokes `pub.xml:documentToXMLString` to convert this rooted document into an `xmldata` string, which is mapped to `responseString`.
        *   Finally, it invokes `pub.flow:setResponseCode` to set the actual HTTP status code and reason phrase based on `SetResponse/responseCode` and `SetResponse/responsePhrase`.
        *   It implicitly calls `pub.flow:setResponse2` to write the `responseString` to the HTTP response body with the appropriate `contentType`.
        *   **Cleanup**: Deletes various temporary documents and strings (`Response`, `ResponseRooted`, `jsonString`, `xmldata`, `SetResponse`, `responseCode`, `reasonPhrase`, `contentType`).
    *   **Integration with Main Flow**: `handleError` (and implicitly `budgetAdd` on success) uses `setResponse` to generate the final HTTP response payload and status.

## Data Structures and Types

The service heavily relies on several predefined document types:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Budget`**:
    *   Represents a single budget entry.
    *   Fields:
        *   `FiscalYear` (string, optional)
        *   `FundingSource` (string, optional)
        *   `id` (string, optional, OFM budget internal ID)
        *   `Name` (string, optional)
        *   `projectId` (string, **required**, OFM budget project ID)
        *   `systemId` (string, optional, System which this budget funds)
        *   `projectTitle` (string, optional, Title of this project)
        *   `fundingId` (string, optional, Cross-reference ID for relationship between budget project and application)
        *   `funding` (string, optional, Description of the allocation)

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:BudgetAddRequest`**:
    *   The primary input wrapper for the `budgetAdd` service.
    *   Fields:
        *   `Budgets` (list of `Budget` documents, **required**): This is an array (`field_dim="1"`) of `Budget` documents.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`**:
    *   A generic API response structure used for success and error messages.
    *   Fields:
        *   `result` (string, optional, e.g., "success", "error")
        *   `message` (list of strings, optional): Used to convey details about the operation or errors.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`**:
    *   A complex document type used to structure data for batch updates to the Alfabet system.
    *   Fields:
        *   `CurrentProfile` (string, defaults to "API User")
        *   `APICulture` (string, optional)
        *   `Objects` (list of `Object` documents): This array (`field_dim="1"`) holds the core data objects to be updated/created in Alfabet.
        *   `Relations` (list of `Relations` documents, optional): This array (`field_dim="1"`) describes relationships between objects within Alfabet.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object`**:
    *   A generic object structure used within the `UpdateRequest` to represent an entity in Alfabet.
    *   Fields:
        *   `RefStr` (string, optional)
        *   `ClassName` (string): Specifies the type of object (e.g., "ProjectArch").
        *   `Id` (string): Unique identifier for this object within the update request.
        *   `Values` (record): A sub-document that holds the actual data fields for the specific `ClassName`. In this service, it will hold `ProjectArch` data.
        *   `GenericAttributes` (list of records): For flexible attribute handling.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ProjectArch`**:
    *   A specific "class" of object data, used within the `Object/Values` field to define a project archive/budget.
    *   Fields:
        *   `changecategory` (string, optional)
        *   `changecomments` (string, optional)
        *   `cms_funding` (string, optional)
        *   `cms_rest_last_updated_date` (string, optional)
        *   `cms_rest_updated_user` (string, optional)
        *   `comments` (string, optional)
        *   `object` (string, optional)
        *   `project` (string, optional)
        *   `sag_imp_id` (string, optional)
        *   `samplerecordforusecases` (string, optional)
        *   `type` (string, optional)

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations`**:
    *   Defines a relationship between two objects in Alfabet.
    *   Fields:
        *   `FromRef` (string, optional)
        *   `FromId` (string, optional)
        *   `Property` (string): Describes the nature of the relationship (e.g., "project", "object").
        *   `ToRef` (string, optional)
        *   `ToId` (string, optional)

*   Other referenced types (not directly processed or returned by `budgetAdd` but part of the ecosystem): `cms.eadg.alfabet.api.v01.resources.objects.byReport.docs:ObjectByReportResponse`, `cms.eadg.easi.api.v01.resources.system.docs.references:mission_essential_function`, `cms.eadg.easi.api.v01.resources.system.docs.references:software_product`, `cms.eadg.easi.api.v01.resources.system.docs.types:SystemDetail`, `cms.eadg.utils.api.docs:ResponseRooted`, `cms.eadg.utils.api.docs:SetResponse`, `pub.event:exceptionInfo`. These documents define schemas for various data entities or utility service inputs/outputs within the broader Webmethods system.

## Error Handling and Response Codes

The `budgetAdd` service implements a standard error handling strategy:

*   **Initial Input Validation**: The service uses a `BRANCH` and `EXIT` mechanism at the very beginning to validate if the `_generatedInput/Budgets` list is provided. If it's `null`, the service immediately exits with a `SIGNAL="FAILURE"` and a custom message "Please provide a list of budgets.". This will propagate as an error, likely resulting in a 400 Bad Request if exposed as a REST API.

*   **`TRY-CATCH` Block**: The main logic of the service is enclosed within a `SEQUENCE` node with `FORM="TRY"`. This is equivalent to a `try` block in traditional programming. If any error occurs during the execution of steps within this `TRY` block, control is transferred to the associated `SEQUENCE` with `FORM="CATCH"`.

*   **Generic Error Handling (`cms.eadg.utils.api:handleError`)**:
    *   Inside the `CATCH` block, `pub.flow:getLastError` is invoked to retrieve the details of the exception that triggered the catch. This populates the `lastError` document, which typically contains fields like `error`, `message`, `service`, etc.
    *   Then, `cms.eadg.utils.api:handleError` is called. This utility service is designed to take the `lastError` information and format a generic error response. By default, it sets the HTTP response code to 500 (Internal Server Error) and the `result` to "error", with the `message` being the error detail from `lastError`.
    *   The `handleError` service, in turn, calls `cms.eadg.utils.api:setResponse` which ultimately uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to set the HTTP status code (e.g., 500) and the response body content (e.g., JSON error message).

*   **Database Operation Result Handling**: The `BRANCH` statement on `addBudgetInputOutput/@RETURN_VALUE` after the database call acts as an explicit success/failure check for the stored procedure. A `@RETURN_VALUE` of `0` is considered success. Any other value (or the `$default` case) triggers a `SIGNAL="FAILURE"`, directing flow to the `CATCH` block for generalized error reporting.

For TypeScript porting, this error handling pattern can be directly translated using `try...catch` blocks. The initial input validation would be `if (!input.budgets) throw new Error(...)`. The `_generatedResponse` can be modeled as a discriminated union or a standard API response interface. The utility services like `handleError` and `setResponse` would become separate functions that construct and return standardized error objects or throw specific exceptions that the main API handler can catch and translate into HTTP responses.