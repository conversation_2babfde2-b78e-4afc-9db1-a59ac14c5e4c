# Webmethods Service Explanation: CmsEadgCedarCoreApi roleFindById

## Service Overview

The `roleFindById` service within the `CmsEadgCedarCoreApi` package serves the business purpose of retrieving specific role assignments from a backend data source, likely a database containing enterprise architecture management data. It acts as an API endpoint to query role information based on various identifiers.

The service accepts the following input parameters:

*   `application` (string): This is a mandatory parameter indicating the specific application context for the role lookup. For this service, it is expected to be "alfabet".
*   `roleId` (string, optional): A unique identifier for a specific role assignment. If provided, it signifies a direct lookup for a single role.
*   `objectId` (string, optional): The identifier of an object (e.g., a system, application, or process) to which roles are assigned. This is used when searching for roles associated with a particular object.
*   `roleTypeId` (string, optional): An identifier for a specific type of role (e.g., "Owner", "Maintainer"). This parameter is intended to be used in conjunction with `objectId` to narrow down the search for roles assigned to an object by a specific role type.

The expected output of a successful execution is a `RoleFindResponse` object. This object contains a `count` field indicating the number of role assignments found and a `Roles` array, where each element is a `Role` object representing a specific role assignment with its details. In case of validation failures or internal errors, the service returns a standardized error response, including an HTTP status code and a descriptive message.

Key validation rules enforced by the service include:
*   The `application` parameter must be provided and must be "alfabet".
*   At least one of `roleId` or `objectId` must be provided as a search criterion.
*   If `roleId` is provided, then `objectId` and `roleTypeId` must not be provided, as `roleId` implies a direct, singular lookup.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `roleFindById`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow services" to define business logic. Here's how the key elements in this service translate to familiar programming concepts:

*   **FLOW**: This is the top-level container for a service's logic, analogous to a program's `main` function or an overall class definition in languages like TypeScript. It orchestrates the sequence of operations.

*   **SEQUENCE**: A `SEQUENCE` defines a linear block of execution. Steps within a sequence are performed in the order they appear. If a step within a `SEQUENCE` encounters an unhandled error (fails), the `SEQUENCE` itself can be configured to exit, often causing a parent `SEQUENCE` or the entire `FLOW` to fail.
    *   `FORM="TRY"`: This marks the beginning of a "try" block, similar to `try { ... }` in Java or TypeScript. Any errors occurring within this block will be caught by a corresponding `CATCH` block.
    *   `FORM="CATCH"`: This marks a "catch" block, analogous to `catch (error) { ... }`. It is executed when an error occurs in the preceding `TRY` block.

*   **BRANCH**: A `BRANCH` element provides conditional execution, functioning much like a `switch` statement in many programming languages or a series of `if-else if-else` statements. The `SWITCH` attribute specifies the variable or expression whose value determines which sub-sequence (or "case") to execute.
    *   `LABELEXPRESSIONS="true"`: When set to true, the `NAME` attribute of the child `SEQUENCE` elements within the `BRANCH` is interpreted as a boolean expression. The first `SEQUENCE` whose expression evaluates to `true` is executed.
    *   `$default`: A special label within a `BRANCH` that acts as the "else" or "default" case. If none of the other conditions (labeled sequences) are met, the `$default` sequence is executed.

*   **MAP**: `MAP` steps are crucial for data transformation and manipulation within the service's "pipeline" (the in-memory data context). The pipeline holds all input, output, and intermediate variables during service execution.
    *   `MODE="INPUT"`: Used before an `INVOKE` step to transform data from the current pipeline into the expected input format of the service being invoked.
    *   `MODE="OUTPUT"`: Used after an `INVOKE` step to transform data from the invoked service's output back into the main service's pipeline.
    *   `MODE="STANDALONE"`: Performs data transformations directly within the current pipeline, without involving an external service call. This is used for cleanup, initialization, or internal data adjustments.
    *   **MAPSET**: Sets a fixed, literal value to a variable in the pipeline.
    *   **MAPCOPY**: Copies the value of an existing variable in the pipeline to another variable. This is often used for renaming fields or moving data between structures.
    *   **MAPDELETE**: Removes a variable from the pipeline. This is important for cleaning up intermediate data and ensuring only relevant outputs are passed along.

*   **INVOKE**: This element is used to call another Webmethods service, a Java service, or an adapter service (like a database connector). It is analogous to calling a function or method in a conventional programming language.
    *   `SERVICE`: Specifies the fully qualified name of the service to be executed.
    *   `VALIDATE-IN` and `VALIDATE-OUT`: Control whether Webmethods should validate the input and output documents against their defined schemas. `$none` means no validation is performed.

*   **EXIT**: The `EXIT` step explicitly stops the execution of the current `FLOW` or `SEQUENCE`.
    *   `SIGNAL="FAILURE"`: When `FAILURE` is specified, it signals an error condition, causing the service to terminate with a failure state, which can be caught by an upstream `CATCH` block.

*   **LOOP**: The `LOOP` element allows iteration over arrays within the pipeline, similar to a `for...of` loop in TypeScript or a `forEach` loop.
    *   `IN-ARRAY`: Specifies the input array to be processed.
    *   `OUT-ARRAY`: Specifies an array where the results of each iteration's transformations are collected.

## Database Interactions

The `roleFindById` service relies on three distinct JDBC Adapter services for its database interactions. All these adapters connect to the same database, specifically the `Sparx_Support` database, using the `sparxSupportJdbcConnectionNoTrans` connection. This connection is configured to use `com.microsoft.sqlserver.jdbc.SQLServerDataSource`, indicating a Microsoft SQL Server database, and operates without explicit transactions, suggesting that each adapter call is an independent operation or that transaction management is handled at a higher level (which is not observed here).

The common database **VIEW** used by all three adapters is `dbo.Sparx_API_Get_Role`.

Here's a breakdown of each adapter and its interaction:

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByObjectId`**:
    *   **Purpose**: Retrieves role assignments based solely on an object's GUID.
    *   **SQL Query (implied from `IRTNODE_PROPERTY` select/where clauses)**:
        ```sql
        SELECT
            "System ID",
            "Sparx System ID",
            "Sparx System GUID",
            "Role ID",
            "Role Type ID",
            "Sparx Role Type ID",
            "Sparx Role Type GUID",
            "Role Name",
            "Role Description",
            "Asignee ID",
            "Sparx Asignee ID",
            "Sparx Asignee GUID",
            "Asignee User Name",
            "Assignee Is Deleted",
            "Asignee First Name",
            "Asignee Last Name",
            "Asignee Email",
            "Assignee Phone",
            "Assignee Org ID",
            "Sparx Assignee Org ID",
            "Sparx Assignee Org GUID",
            "Assignee Organization Name",
            "Assignee Description",
            "Assignee Type"
        FROM
            dbo.Sparx_API_Get_Role t1
        WHERE
            t1."Sparx System GUID" = ?
        ```
    *   **Data Mapping (Service Input to Database Parameter)**:
        *   `objectId` (from service input) maps to `t1."Sparx System GUID"` in the WHERE clause.

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByObjectIdandRoleTypeId`**:
    *   **Purpose**: Retrieves role assignments based on an object's GUID and a specific role type GUID.
    *   **SQL Query (implied from `IRTNODE_PROPERTY` select/where clauses)**:
        ```sql
        SELECT
            "System ID",
            "Sparx System ID",
            "Sparx System GUID",
            "Role ID",
            "Role Type ID",
            "Sparx Role Type ID",
            "Sparx Role Type GUID",
            "Role Name",
            "Role Description",
            "Asignee ID",
            "Sparx Asignee ID",
            "Sparx Asignee GUID",
            "Asignee User Name",
            "Assignee Is Deleted",
            "Asignee First Name",
            "Asignee Last Name",
            "Asignee Email",
            "Assignee Phone",
            "Assignee Org ID",
            "Sparx Assignee Org ID",
            "Sparx Assignee Org GUID",
            "Assignee Organization Name",
            "Assignee Description",
            "Assignee Type"
        FROM
            dbo.Sparx_API_Get_Role t1
        WHERE
            t1."Sparx System GUID" = ? AND t1."Sparx Role Type GUID" = ?
        ```
    *   **Data Mapping (Service Input to Database Parameters)**:
        *   `objectId` (from service input) maps to `t1."Sparx System GUID"`.
        *   `roleTypeId` (from service input) maps to `t1."Sparx Role Type GUID"`.

3.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByRoleId`**:
    *   **Purpose**: Retrieves a specific role assignment by its role ID.
    *   **SQL Query (implied from `IRTNODE_PROPERTY` select/where clauses)**:
        ```sql
        SELECT
            "System ID",
            "Sparx System ID",
            "Sparx System GUID",
            "Role ID",
            "Role Type ID",
            "Sparx Role Type ID",
            "Sparx Role Type GUID",
            "Role Name",
            "Role Description",
            "Asignee ID",
            "Sparx Asignee ID",
            "Sparx Asignee GUID",
            "Asignee User Name",
            "Assignee Is Deleted",
            "Asignee First Name",
            "Asignee Last Name",
            "Asignee Email",
            "Assignee Phone",
            "Assignee Org ID",
            "Sparx Assignee Org ID",
            "Sparx Assignee Org GUID",
            "Assignee Organization Name",
            "Assignee Description",
            "Assignee Type"
        FROM
            dbo.Sparx_API_Get_Role t1
        WHERE
            t1."Role ID" = ?
        ```
    *   **Data Mapping (Service Input to Database Parameter)**:
        *   `roleId` (from service input) maps to `t1."Role ID"`.

**Detailed Data Mapping (Source Database Columns to Output Object Properties)**:

All three adapter services query the same columns from the `dbo.Sparx_API_Get_Role` view and map them to fields in the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Role` document type within the calling `findSparxRole` service:

*   `Sparx_API_Get_Role` View Columns: Output `Role` Object Property:
    *   `"Sparx System GUID"`: `objectId`
    *   `"Role ID"`: `roleId`
    *   `"Role Name"`: `roleTypeName`
    *   `"Role Description"`: `roleTypeDesc`
    *   `"Asignee User Name"`: `assigneeUserName`
    *   `"Assignee Is Deleted"`: `assigneeIsDeleted`
    *   `"Asignee First Name"`: `assigneeFirstName`
    *   `"Asignee Last Name"`: `assigneeLastName`
    *   `"Asignee Email"`: `assigneeEmail`
    *   `"Assignee Phone"`: `assigneePhone`
    *   `"Assignee Org ID"`: `assigneeOrgId`
    *   `"Assignee Organization Name"`: `assigneeOrgName`
    *   `"Assignee Description"`: `assigneeDesc`
    *   `"Assignee Type"`: `assigneeType`
    *   `"Sparx Role Type GUID"`: `roleTypeId`
    *   `"Sparx Asignee GUID"`: `assigneeId`
    *   `[Hardcoded Value "alfabet"]`: `application`

## External API Interactions

Based on the provided XML files, this Webmethods service does not appear to interact with any external APIs. All data retrieval operations are performed against an internal SQL Server database using JDBC adapters. There are no `INVOKE` statements targeting external web services, REST APIs, or other non-database external systems.

## Main Service Flow

The `roleFindById` service (`CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/roleFindById/flow.xml`) defines the top-level orchestration. It starts with a `TRY` block to manage exceptions and ensures proper error handling.

1.  **Initial Application Validation**:
    *   A `BRANCH` statement immediately validates the `application` input parameter.
    *   **Condition `application` == "alfabet"**: If the `application` input exactly matches "alfabet", the flow proceeds to `INVOKE` the dependency service `cms.eadg.cedar.core.api.v2.cedarCore_.operations.roleFindById:findSparxRole`. This indicates that this API handler is specifically designed for the "alfabet" application context.
    *   **`$default` (Other `application` values)**: If `application` is not "alfabet" (or is missing/null), this `SEQUENCE` is executed.
        *   A `MAP STANDALONE` step is used to construct a "Bad Request" error response. It sets HTTP status code 400 (`responseCode`), phrase "Bad Request" (`responsePhrase`), a general `result` of "error", `format` to "application/json", and a specific `message` array containing "Please specify a valid application".
        *   An `EXIT` step with `SIGNAL="FAILURE"` is then executed. This immediately terminates the current service execution and signals an error, causing the `CATCH` block of the main service to activate.

2.  **Post-Processing/Cleanup (after `findSparxRole` invocation or Bad Request)**:
    *   Following the `BRANCH` (regardless of which path was taken, provided it didn't exit the entire `FLOW` directly), a `MAP STANDALONE` step labeled "cleanup" is executed.
    *   This step uses `MAPDELETE` operations to remove the original input parameters (`application`, `roleId`, `objectId`, `roleTypeId`) and any `SetResponse` object that might have been created in the bad request branch from the pipeline. This ensures that the pipeline only contains the final `_generatedResponse` object for the service's output, maintaining a clean data contract.

3.  **Error Handling (Catch Block)**:
    *   If any error occurs within the main `TRY` block (either due to the explicit `EXIT FAILURE` from the bad request branch or an unhandled exception during the invocation of `findSparxRole`), the `CATCH` block is activated.
    *   It first `INVOKE`s `pub.flow:getLastError` to retrieve detailed information about the exception that occurred. This information includes the error message, stack trace, and other relevant context.
    *   Then, it `INVOKE`s `cms.eadg.utils.api:handleError`, passing it the `lastError` information. This utility service is responsible for standardizing the error response format.
    *   Finally, a `MAP` step is used to delete the `lastError` document from the pipeline, as it has already been processed by `handleError`.

## Dependency Service Flows

The main `roleFindById` service largely depends on the `cms.eadg.cedar.core.api.v2.cedarCore_.operations.roleFindById:findSparxRole` service for its core business logic and data retrieval. This dependency service itself utilizes other adapter services.

### `cms.eadg.cedar.core.api.v2.cedarCore_.operations.roleFindById:findSparxRole`

This service is the primary workhorse, responsible for executing the actual database queries and mapping the raw database results into the structured `Role` document type.

1.  **Input Parameter Validation**:
    *   The service starts with a `BRANCH` using `LABELEXPRESSIONS="true"` for complex conditional logic, ensuring that the input parameters adhere to the API's rules for `roleId`, `objectId`, and `roleTypeId`.
    *   **Scenario 1: No `roleId` AND no `objectId`**:
        *   Condition: `%roleId% == $null && %objectId% == $null`
        *   This `SEQUENCE` triggers if insufficient search criteria are provided.
        *   A `MAP STANDALONE` sets a `SetResponse` document with HTTP 400 Bad Request, message "Either role ID or object ID must be provided".
        *   An `EXIT` with `SIGNAL="FAILURE"` halts execution and signals an error to the calling service (`roleFindById`).
    *   **Scenario 2: Invalid Parameter Combination (`roleId` with `objectId` or `roleTypeId`)**:
        *   Condition: `(%roleId% != $null && %objectId% != $null) || (%roleId% != $null && %roleTypeId% != $null)`
        *   This `SEQUENCE` handles mutually exclusive parameter usage. If `roleId` is present, `objectId` and `roleTypeId` should be absent.
        *   A `MAP STANDALONE` sets a `SetResponse` document with HTTP 400 Bad Request, message "If role ID is provided then object ID and role type ID should not be provided".
        *   An `EXIT` with `SIGNAL="FAILURE"` halts execution and signals an error.
    *   **Scenario 3: Valid Parameter Combinations (`$default` branch)**:
        *   If the input passes the initial validation, the `$default` `SEQUENCE` is executed, which contains another `BRANCH` to handle the different valid query types:
            *   **Sub-Scenario 3.1: `objectId` AND `roleTypeId` are both provided**:
                *   Condition: `%objectId% != $null && %roleTypeId% != $null`
                *   **Invoke `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByObjectIdandRoleTypeId`**: This JDBC adapter is called to query the `dbo.Sparx_API_Get_Role` view using both the `objectId` and `roleTypeId`. Input parameters are mapped from `objectId` to `"Sparx System GUID"` and `roleTypeId` to `"Sparx Role Type GUID"`.
                *   **Data Transformation Loop**: A `LOOP` iterates over the `results` array returned by the adapter. For each database record:
                    *   A `MAP STANDALONE` transforms database column names (e.g., `"Sparx System GUID"`) into the desired `Role` document field names (e.g., `objectId`).
                    *   The `application` field in the `Role` document is explicitly set to the literal "alfabet".
                    *   The transformed `Role` document is added to the `Roles` array (the `OUT-ARRAY` of the loop).
            *   **Sub-Scenario 3.2: Only `objectId` is provided**:
                *   Condition: `%objectId% != $null` (this is the `$default` within the inner branch if the first sub-scenario didn't match).
                *   **Invoke `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByObjectId`**: This JDBC adapter queries `dbo.Sparx_API_Get_Role` using only the `objectId`. Input is mapped from `objectId` to `"Sparx System GUID"`.
                *   **Data Transformation Loop**: Similar to Sub-Scenario 3.1, a `LOOP` transforms the database results into `Role` documents, setting the `application` field to "alfabet".
            *   **Sub-Scenario 3.3: Only `roleId` is provided**:
                *   Condition: This is the `$default` if the above `objectId` conditions are not met, meaning `roleId` must be populated as per the outer branch's validation logic.
                *   **Invoke `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Role:findRoleByRoleId`**: This JDBC adapter queries `dbo.Sparx_API_Get_Role` using only the `roleId`. Input is mapped from `roleId` to `"Role ID"`.
                *   **Data Transformation Loop**: Similar to previous sub-scenarios, a `LOOP` transforms results into `Role` documents, setting `application` to "alfabet".

2.  **Final Cleanup and Response Preparation**:
    *   After the appropriate database query and data transformation loop have completed, a final `MAP STANDALONE` step labeled "cleanup" is executed.
    *   This step removes all intermediate variables and adapter outputs (`roleId`, `objectId`, `token`, `ReportArgs`, `ObjectByReportResponse`, `Response`, various `findRoleBy...Output` documents) from the pipeline.
    *   Crucially, it copies the accumulated `Roles` array into the `_generatedResponse/Roles` field and the total `count` into `_generatedResponse/count`, preparing the final output for the calling `roleFindById` service.

### `cms.eadg.utils.api:handleError`

*   **Purpose**: This is a generic utility service designed to centralize and standardize error handling across various APIs. It takes raw error information and transforms it into a consistent API error response format.
*   **Integration**: It is invoked by the main `roleFindById` service's `CATCH` block when an exception occurs.
*   **Logic**:
    *   It checks if a `SetResponse` document (pre-configured error details) is already present in the pipeline.
    *   If `SetResponse` is `$null`, it implies an unexpected error. In this case, it sets a default `SetResponse` with `responseCode` 500 ("Internal Server Error"), `result` "error", `format` "application/json", and sets the `message` to the actual `error` message from `lastError`.
    *   If `SetResponse` is not `$null`, it uses the pre-existing error details (e.g., from an input validation failure handled upstream).
    *   It then invokes `cms.eadg.utils.api:setResponse` to finalize the response string and set the HTTP response headers.
    *   Finally, it cleans up the `lastError` and `SetResponse` documents from the pipeline.

### `cms.eadg.utils.api:setResponse`

*   **Purpose**: This is another utility service responsible for taking a structured response document (`SetResponse` or `Response`) and transforming it into the final HTTP response, including setting the content type and HTTP status code.
*   **Integration**: It is invoked by `cms.eadg.utils.api:handleError` (for error responses) and would be invoked directly by other services for successful responses (though not directly shown for success in the `roleFindById` main flow's current fragment, it's typical for the service framework).
*   **Logic**:
    *   It maps the `result` and `message` fields from the input `SetResponse` into a standard `Response` document.
    *   It `BRANCH`es based on the desired `format` specified in `SetResponse` (e.g., "application/json" or "application/xml").
        *   If `application/json`, it calls `pub.json:documentToJSONString` to serialize the `Response` document into a JSON string.
        *   If `application/xml`, it first maps the `Response` into a `ResponseRooted` document (a wrapper for XML output), then calls `pub.xml:documentToXMLString` to serialize it into an XML string.
    *   It then uses `pub.flow:setResponseCode` to set the HTTP status code and reason phrase based on `SetResponse/responseCode` and `SetResponse/responsePhrase`.
    *   Finally, `pub.flow:setResponse2` is called to write the generated JSON or XML string to the HTTP response body and set the `Content-Type` header (from `SetResponse/format`).
    *   It cleans up temporary variables from the pipeline.

## Data Structures and Types

The service heavily relies on predefined document types (Webmethods' equivalent of data structures or schemas) to define its inputs and outputs.

*   **Input Data Model for `roleFindById` service**:
    *   `application` (string): Mandatory.
    *   `roleId` (string): Optional.
    *   `objectId` (string): Optional.
    *   `roleTypeId` (string): Optional.

*   **Main Output Data Model: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:RoleFindResponse`**:
    *   `count` (object / `java.math.BigInteger`): Represents the total number of role assignments found. This field is optional in the response, suggesting that a successful response might omit it if, for example, no roles are found.
    *   `Roles` (array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Role`): An array containing the detailed information for each found role assignment. This array is optional, meaning it might be empty or null if no roles match the criteria.

*   **Nested Data Model: `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Role`**:
    This document type represents a single role assignment and is the primary data structure for the retrieved role information. All fields are string type unless otherwise noted.
    *   `application`: The application where the role assignment exists.
    *   `objectId`: ID of the object the role is assigned to.
    *   `objectType` (optional): The type of object the role is assigned to.
    *   `roleId` (optional): ID of the role assignment.
    *   `roleTypeId`: ID of the role type.
    *   `roleTypeName` (optional): Name of the role type.
    *   `roleTypeDesc` (optional): Description of the role type.
    *   `assigneeId` (optional): ID of the role assignee, if a person.
    *   `assigneeUserName` (optional): Username of the role assignee, if a person.
    *   `assigneeIsDeleted` (optional): Indicates if the person assigned the role is marked for deletion.
    *   `assigneeFirstName` (optional): First name of the assignee.
    *   `assigneeLastName` (optional): Last name of the assignee.
    *   `assigneeEmail` (optional): Email of the assignee.
    *   `assigneePhone` (optional): Phone number of the assignee.
    *   `assigneeOrgId` (optional): ID of the role assignee, if an organization.
    *   `assigneeOrgName` (optional): Name of the assignee organization.
    *   `assigneeDesc` (optional): Description of the assignee.
    *   `assigneeType` (optional): Type of assignee.

*   **Utility Data Models for Responses (`cms.eadg.utils.api.docs`)**:
    *   `Response`: A generic response structure with `result` (string, e.g., "success" or "error") and `message` (string array). Used for the actual body of simple responses.
    *   `SetResponse`: A control document for setting response parameters: `responseCode`, `responsePhrase`, `result`, `message`, `format` (e.g., "application/json"). This isn't directly part of the API payload but guides how the HTTP response is constructed.
    *   `ResponseRooted`: A simple wrapper document (`ResponseRooted` contains a `Response` field) used specifically for XML output to provide a root element.

*   **Data Transformation Logic**:
    As described in the "Database Interactions" section, the core transformation logic involves mapping columns from the `Sparx_API_Get_Role` database view into the fields of the `Role` document type. This is done repeatedly inside `LOOP` blocks. A specific transformation involves setting the `application` field of the output `Role` object to a hardcoded string "alfabet". This direct, field-by-field mapping is a common pattern in Webmethods for translating flat database results into structured API responses. For the TypeScript porting project, these explicit mappings represent direct property assignments in your DTO/interface definitions.

## Error Handling and Response Codes

The service incorporates a centralized error handling mechanism to provide consistent and informative responses in case of failures.

*   **Different Error Scenarios Covered**:
    1.  **Invalid Application**: If the input `application` parameter is not "alfabet", the service immediately returns a `400 Bad Request`. This is an explicit validation rule.
    2.  **Missing Search Criteria**: If neither `roleId` nor `objectId` is provided, the service returns a `400 Bad Request`. This ensures that a meaningful search criterion is always present.
    3.  **Conflicting Search Criteria**: If `roleId` is provided along with `objectId` or `roleTypeId`, a `400 Bad Request` is returned. This enforces the rule that `roleId` is for a precise lookup, while `objectId` (with optional `roleTypeId`) is for broader searches.
    4.  **Unhandled Exceptions**: Any other unexpected runtime errors (e.g., database connectivity issues, SQL execution errors, unexpected data types, null pointer exceptions within the flow logic) are caught by the main `TRY/CATCH` block.

*   **HTTP Response Codes Used**:
    *   `400 Bad Request`: Used for all input validation failures, as detailed in the scenarios above. The service explicitly sets this code along with a relevant phrase and message.
    *   `500 Internal Server Error`: This is the default response code used by the `cms.eadg.utils.api:handleError` service for any unhandled or unexpected exceptions caught during processing. It indicates a server-side problem that prevented the request from being fulfilled.
    *   Implicit `200 OK`: For successful responses, although not explicitly set to 200 in the flow logic, `pub.flow:setResponseCode` is always invoked, and typically it defaults to `200 OK` if no other code is specified for success.

*   **Error Message Formats**:
    Error messages are standardized using the `cms.eadg.utils.api.docs:Response` document type, which is then serialized to `application/json` (or `application/xml` if requested, though JSON is the default set for errors here). This document type contains:
    *   `result`: A string indicating the outcome, which will be "error" for all failure scenarios.
    *   `message`: An array of strings providing human-readable details about the error. For unhandled exceptions, this array will contain the exception message.

*   **Fallback Behaviors**:
    The `cms.eadg.utils.api:handleError` service provides a crucial fallback mechanism. If an exception occurs and no specific `SetResponse` document (pre-configured error details) is available in the pipeline, `handleError` automatically populates a default "500 Internal Server Error" response using information from `pub.flow:getLastError`. This ensures that even unexpected system failures result in a structured and predictable error response, rather than a raw exception dump or an unresponsive API.