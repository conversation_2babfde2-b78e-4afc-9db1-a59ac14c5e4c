# Webmethods Service Explanation: CmsEadgCedarCoreApi exchangeFindById

This document provides a detailed explanation of the `exchangeFindById` service within the `CmsEadgCedarCoreApi` package, designed for experienced software developers who are new to Webmethods. It covers the service's purpose, its operational flow, interactions with the database, and the intricate data mapping processes crucial for your TypeScript porting project.

The `exchangeFindById` service is part of the `CmsEadgCedarCoreApi` package and resides at `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `exchangeFindById`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `exchangeFindById` service serves the business purpose of retrieving a single data exchange record from the Alfabet system, which is backed by a database. It functions as a lookup mechanism, providing a detailed view of a specific data exchange.

*   **Business Purpose:** To fetch a single data exchange record from the underlying database, likely representing a data integration point or connection in an enterprise architecture management (EAM) system like Alfabet.
*   **Input Parameters:** The service takes a single input parameter:
    *   `id` (string): This is the unique identifier (GUID) of the data exchange record to be retrieved.
*   **Expected Outputs:**
    *   On success, the service is expected to return a JSON object representing a single `Exchange` record. This object contains a comprehensive set of details about the data exchange, including information about its sender, receiver, format, security aspects, and more.
    *   On failure, the service returns a standardized error response, indicating issues such as missing input or internal server errors.
*   **Side Effects:** As a "find by ID" operation, this service is designed to be read-only and is not expected to have any side effects on the data store.
*   **Key Validation Rules:** The primary validation performed by the service is to ensure that the `id` input parameter is provided and is not an empty string.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm based on "flows." These flows are sequences of predefined or custom steps. Understanding a few core elements will help in comprehending the service logic:

*   **SEQUENCE:** Similar to a code block or function body in conventional programming. Steps within a `SEQUENCE` are executed in order. If any step within a `SEQUENCE` fails (e.g., throws an exception), the entire `SEQUENCE` typically fails. The `FORM="TRY"` attribute indicates the beginning of a try-catch block, where subsequent `SEQUENCE` elements with `FORM="CATCH"` handle exceptions.
*   **BRANCH:** Analogous to `if-else if-else` or `switch` statements. It directs the flow based on conditions. When `LABELEXPRESSIONS="true"`, the `NAME` attribute of child `SEQUENCE` blocks defines a condition to be evaluated. The first `SEQUENCE` whose condition evaluates to `true` is executed. A `$default` `SEQUENCE` acts as the `else` block if no other conditions are met.
*   **MAP:** This is a crucial element for data transformation. It allows you to define how data fields are moved, transformed, or initialized within the "pipeline" (Webmethods' term for the data context shared between steps).
    *   **MAPSET:** Used to assign a literal value or an expression result to a specific field.
    *   **MAPCOPY:** Copies the value from a source field to a target field. This is frequently used for data mapping.
    *   **MAPDELETE:** Removes a field from the pipeline. This is used for cleanup, ensuring only necessary data is passed between services or returned.
*   **INVOKE:** This element is used to call other services. These can be:
    *   **Built-in services:** Such as `pub.flow:getLastError` (to retrieve details of the last error) or `pub.math:addInts` (for arithmetic operations).
    *   **Custom flow services:** Like `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindById:findExchange`.
    *   **Adapter services:** Like `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues`, which typically interact with external systems like databases.
*   **Error Handling (TRY/CATCH blocks):** Webmethods uses `SEQUENCE` elements with `FORM="TRY"` and `FORM="CATCH"` to implement error handling. Operations within the `TRY` block are monitored; if an error occurs, control immediately transfers to the `CATCH` block.
*   **Input Validation and Branching Logic:** The `BRANCH` element, especially with `LABELEXPRESSIONS`, is fundamental for implementing conditional logic. Conditions are expressed using a syntax similar to variable interpolation and comparison (e.g., `%id% == $null || %id% == ''`).

## Database Interactions

The `exchangeFindById` service primarily interacts with a Microsoft SQL Server database to retrieve the data exchange information.

*   **Database Operations Performed:** The service executes a `SELECT` query to retrieve data.
*   **Database Connection Configuration:** The service utilizes the JDBC Adapter connection named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.
    *   **Database Name:** `Sparx_Support`
    *   **Server Name:** `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port Number:** `1433`
    *   **User:** `sparx_dbuser`
*   **SQL Tables, Views, and Stored Procedures Used:**
    *   **Views:** `Sparx_System_DataExchange` (aliased as `t1` in the query).
    *   No explicit stored procedures are called within the provided files.
*   **SQL Query Details:** The `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues` adapter service executes a SQL `SELECT` statement against the `Sparx_System_DataExchange` view.
    *   The query selects 43 columns from the `Sparx_System_DataExchange` view.
    *   The `WHERE` clause filters records based on the input `id`: `t1."Sparx Sendor GUID" = ? OR t1."Sparx Receiver GUID" = ? OR t1."Connection GUID" = ?`. This indicates that the provided `id` could match the sender's GUID, the receiver's GUID, or the connection's GUID.
*   **Data Mapping (Service Inputs to Database Parameters):**
    *   The `id` input parameter of the `exchangeFindById` service is mapped to the placeholder (`?`) for the `"Connection GUID"`, `"Sparx Sendor GUID"`, and `"Sparx Receiver GUID"` columns in the `WHERE` clause of the `getExchangeValues` adapter query.

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke external REST or SOAP APIs outside of the Webmethods Integration Server environment. All service calls identified are to other internal Webmethods services or database adapters.

## Main Service Flow

The main service flow for `exchangeFindById` is defined in `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/exchangeFindById/flow.xml`.

1.  **Try Block Initialization:** The entire service logic is enclosed within a `SEQUENCE` with `FORM="TRY"`, indicating that any errors within this block will be caught by the subsequent `CATCH` block. An initial `MAP` step is present for variable initialization, although it currently performs no operations.

2.  **Input Validation:**
    *   A `BRANCH` statement is used to check for required parameters.
    *   **Condition:** `%id% == $null || %id% == ''` (if the `id` input is null or an empty string).
    *   **Outcome if true:** A `SEQUENCE` block is executed.
        *   A `MAP` step (`map error`) sets the `SetResponse` document type with:
            *   `responseCode`: "400"
            *   `responsePhrase`: "Bad Request"
            *   `result`: "error"
            *   `format`: "application/json"
            *   `message`: ["Please provide required parameter 'id' "]
        *   An `EXIT` step is encountered, which terminates the flow with a `FAILURE` signal, preventing further execution and indicating a client-side error.

3.  **Fetch Exchange Data:**
    *   If the `id` validation passes, the service `INVOKE`s a dependency service: `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindById:findExchange`. This service is responsible for retrieving the actual exchange data from the database.
    *   After the invocation, a `MAP` step cleans up the pipeline by `MAPDELETE`ing the `token` and `id` input fields, as they are no longer needed.

4.  **Check for Native Error:**
    *   Another `BRANCH` step checks if `%SetResponse% != $null`. This condition is important because the `findExchange` dependency service might itself populate `SetResponse` in case of an internal error or if no record is found.
    *   **Outcome if true:** An `EXIT` step is executed, again terminating the flow with a `FAILURE` signal, indicating an error occurred in a sub-service.

5.  **Response Generation and Cleanup:**
    *   If no errors have occurred, a final `MAP` step (`cleanup`) is executed.
    *   It `MAPCOPY`s the `Exchange` document (which is the result from `findExchange`) to `_generatedResponse`. This renames the main output field to the expected response name for the API.
    *   It then `MAPDELETE`s any lingering `SetResponse` and the original `Exchange` document from the pipeline to ensure a clean output.

6.  **Catch Block (General Error Handling):**
    *   If any unhandled exception occurs in the `TRY` block (e.g., a database connectivity issue, or an unexpected data format), the flow transfers to this `SEQUENCE` block with `FORM="CATCH"`.
    *   `INVOKE pub.flow:getLastError`: Retrieves detailed information about the exception that occurred.
    *   `INVOKE cms.eadg.utils.api:handleError`: This service standardizes the error response. It typically sets an HTTP 500 Internal Server Error status and formats the error message from `lastError` into a consistent structure for the API client.

## Dependency Service Flows

The `exchangeFindById` service relies on several other services to perform its function.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindById:findExchange`:**
    *   **Purpose:** This service acts as an intermediary, orchestrating the data retrieval from the database and preliminary data transformation. It's designed to abstract the direct database interaction from the main API service.
    *   **Integration with Main Flow:** It is invoked by `exchangeFindById` to obtain the core data exchange record.
    *   **Input/Output Contract:** Takes `id` and `token` as input. Produces a single `Exchange` document type (`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`) or populates `SetResponse` if an error occurs during its execution.
    *   **Specialized Processing:**
        *   **Mapping Input:** The input `id` is mapped to a `ReportArgs/refstr` field, preparing it for the database adapter.
        *   **Database Call:** It `INVOKE`s `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues` to query the database.
        *   **Result Handling:**
            *   It checks the `Selected` field from the database adapter output (likely indicating if a record was found). If no record is found (e.g., `Selected` is "0"), it proceeds down a branch that doesn't map any data, resulting in a `null` `Exchange` output.
            *   If records are found (`$default` branch), it iterates through the `results` returned by `getExchangeValues` using a `LOOP` (though for "findById", only one result is expected).
            *   Within the loop, it performs a crucial `MAP` operation (`map values`) that transforms the raw database column names into fields of the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeValues` document type. This is the first stage of data shaping.
            *   It then `INVOKE`s `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindById:mapExchange` to perform the final complex mapping and type conversions.
        *   **Cleanup:** Deletes intermediate variables like `getExchangeValuesInput` and `ReportArgs`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:getExchangeValues`:**
    *   **Purpose:** This is a JDBC adapter service that directly interacts with the database. It executes the SQL `SELECT` query to fetch raw data exchange records.
    *   **Integration with `findExchange`:** It's a low-level data access component, providing the raw data from the database.
    *   **Input/Output Contract:** Takes parameters like `"Sparx Sendor GUID"`, `"Sparx Receiver GUID"`, and `"Connection GUID"` (all mapped from the input `id`). Returns a list of `results`, where each result is a flat record corresponding to a row from the `Sparx_System_DataExchange` view, along with a `Selected` field.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeFindById:mapExchange`:**
    *   **Purpose:** This service is responsible for transforming the database-agnostic `ExchangeValues` document type into the API-specific `Exchange` document type, including handling complex data transformations like type conversions and array population. It also calculates the total count of exchanges found.
    *   **Integration with `findExchange`:** It receives the raw `ExchangeValues` from `findExchange` and outputs the fully mapped `ExchangeFindResponse`.
    *   **Input/Output Contract:** Takes an array of `Exchange` (which are `ExchangeValues` at this stage). Outputs `ExchangeFindResponse` (containing the final `Exchange` object and a `count`).
    *   **Specialized Processing:**
        *   Initializes internal counters: `tempExchangeId`, `dataTypeIndex`, `exchangeIndex`, `count`.
        *   **Looping and Aggregation:** It `LOOP`s through the `Exchange` (representing `ExchangeValues` from the DB). It uses a `BRANCH` (`%tempExchangeId% != %Exchange/exchangeId%` or `$default`) to determine if it's processing a new logical exchange record or an additional data type for an existing one. This structure is common when a single logical entity might be represented by multiple rows in a denormalized database view (e.g., if a many-to-many relationship is flattened).
            *   For each *new* logical exchange, it increments `exchangeIndex` and `count`, then performs direct `MAPCOPY` of most fields from the `ExchangeValues` record to the `ExchangeFindResponse/Exchanges` document type.
            *   It uses `cms.eadg.utils.date:dateTimeStringToObject` to convert string date fields (e.g., "Retire Date") to `java.util.Date` objects.
            *   It uses `cms.eadg.utils.string:tokenize` to convert pipe- or space-separated strings into string arrays (e.g., "Exchange Frequency", "Beneficiary Address Purpose", "Exchange Network Protocol").
            *   It uses `cms.eadg.utils.map:convertBoolean` to convert string representations of booleans (e.g., "Yes"/"No", "True"/"False") into `java.lang.Boolean` objects.
            *   If it's an *existing* logical exchange (`%tempExchangeId% == %Exchange/exchangeId%`), it only processes nested array fields like `typeOfData`, ensuring all related data types for a single exchange are aggregated.
        *   **Count Calculation:** After the loop, it converts the internal `count` (string) to `java.math.BigInteger` using `cms.eadg.utils.math:toNumberIf` and sets it in the `ExchangeFindResponse/count`.
        *   **Default Array Initialization:** A final loop ensures that array fields in the output `Exchange` document (like `typeOfData`, `connectionFrequency`, `businessPurposeOfAddress`) are initialized as empty arrays if they are missing or null in the source data. This is a best practice for consistent JSON output.

*   **`cms.eadg.utils.api:handleError`:**
    *   **Purpose:** This is a utility service for standardizing API error responses.
    *   **Integration:** It's invoked by the main `exchangeFindById` service's `CATCH` block.
    *   **Input/Output:** Takes an optional `SetResponse` (if an error is already partially formulated) and the `lastError` object from Webmethods' internal error pipeline. It cleans up these inputs and uses `cms.eadg.utils.api:setResponse` to format the final HTTP response.
    *   **Logic:** If no `SetResponse` is provided, it defaults to a 500 Internal Server Error with the raw exception message. Otherwise, it uses the provided `SetResponse` details.

*   **`cms.eadg.utils.api:setResponse`:**
    *   **Purpose:** This utility service is responsible for formatting the final HTTP response body (JSON or XML) and setting the HTTP response code and content type headers.
    *   **Integration:** Called by `handleError` and potentially other services preparing the final response.
    *   **Input/Output:** Takes a `SetResponse` document (containing `responseCode`, `responsePhrase`, `result`, `message`, `format`). Its primary effect is setting HTTP headers and the response body.
    *   **Logic:** It converts the `SetResponse` information into a generic `Response` document. It then uses a `BRANCH` statement based on the `format` field (`application/json` or `application/xml`) to select the appropriate serialization service (`pub.json:documentToJSONString` or `pub.xml:documentToXMLString`). Finally, it calls `pub.flow:setResponseCode` and `pub.flow:setResponse2` to send the response.

## Data Structures and Types

Understanding the data structures involved is crucial for mapping the Webmethods pipeline to TypeScript interfaces.

*   **Input Data Model:**
    *   `id` (string): The identifier for the exchange.
*   **Intermediate Data Models:**
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeValues`: This document type acts as a direct representation of the columns returned from the `Sparx_System_DataExchange` database view. All its fields are `string` types, reflecting the raw data from the database. This acts as the bridge between the database adapter and the application-specific data models.
    *   `pub.event:exceptionInfo`: A standard Webmethods document type containing details about an exception, including the error message.
    *   `cms.eadg.utils.api.docs:SetResponse`: An internal utility document type used to standardize error responses and control HTTP response details like status code, phrase, content type, and message.
*   **Output Data Models:**
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`: This is the primary target document type for the API's successful response. It represents a single data exchange object and includes transformations of data types (e.g., string dates to `Date` objects, string booleans to `Boolean` objects) and structured fields (e.g., nested objects, arrays).
        *   Many fields in this document type are optional (`field_opt=true`, `nillable=true`).
        *   It contains an array of `typeOfData` objects, each with `id` and `name`.
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeFindResponse`: This is a wrapper document type for the final response, especially useful when an API might return multiple `Exchange` objects or a count. For `exchangeFindById`, it will contain `count` and an array `Exchanges` that should hold at most one `Exchange` object.
    *   `cms.eadg.utils.api.docs:Response`: A generic response document type used for both successful (e.g., `result: "success"`) and error (`result: "error"`) messages, often containing a `message` array.
    *   `cms.eadg.utils.api.docs:ResponseRooted`: A wrapper for `cms.eadg.utils.api.docs:Response` specifically for XML serialization.
*   **Field Validation Rules & Transformations:**
    *   Input `id` must not be null or empty.
    *   String fields from the database are often converted to richer data types in the final `Exchange` output:
        *   String representations of dates (e.g., "2023-01-01") are converted to `java.util.Date` objects (using `cms.eadg.utils.date:dateTimeStringToObject`).
        *   String representations of booleans (e.g., "true", "false", "Yes", "No") are converted to `java.lang.Boolean` objects (using `cms.eadg.utils.map:convertBoolean`).
        *   String fields containing delimited values (e.g., "Frequency1 Frequency2") are tokenized into string arrays (using `cms.eadg.utils.string:tokenize`).
        *   Numerical strings are converted to `java.lang.Long` where appropriate (e.g., for `count` using `cms.eadg.utils.math:toNumberIf`).
*   **Optional vs. Required Fields:** Many fields within the output `Exchange` document are marked as optional (`field_opt=true` or `nillable=true`), meaning they might be absent if the source data does not contain a value. The `mapExchange` service explicitly handles some optional array fields by setting them to empty arrays (`[]`) if the source is null, which is a good practice for API consistency.

## Error Handling and Response Codes

The service implements a robust error handling strategy to provide clear feedback to API consumers.

*   **Different Error Scenarios Covered:**
    *   **Bad Request (Client Error):** This occurs when the client provides an invalid or missing `id` input.
    *   **Internal Server Error (System Error):** This covers any unexpected exceptions that occur during the service's execution, such as database connectivity issues, unexpected data formats, or unhandled logic errors.
    *   **No Data Found (Implicit):** While not explicitly an error, if `findExchange` returns no data for a given ID, the `_generatedResponse` would be empty or null. The current flow doesn't explicitly return a 404 Not Found, but rather an empty response body if the data record isn't there, or falls into the general error handling if an exception is thrown.
*   **HTTP Response Codes Used:**
    *   `400 Bad Request`: Used specifically when the mandatory `id` parameter is missing or empty.
    *   `500 Internal Server Error`: Used as a catch-all for any unhandled exceptions in the service's execution.
    *   A successful response, implicitly, would typically be `200 OK`.
*   **Error Message Formats:** Error responses adhere to a standardized format defined by `cms.eadg.utils.api.docs:Response`. This typically includes:
    *   `result`: A string indicating the outcome (e.g., "error").
    *   `message`: An array of strings providing details about the error.
    *   The `format` field in `SetResponse` dictates whether the error body is `application/json` or `application/xml`.
*   **Fallback Behaviors:** The `cms.eadg.utils.api:handleError` service acts as a fallback, ensuring that even if an unexpected error occurs, a consistent 500 Internal Server Error response is returned to the client, preventing raw exception details from being exposed.

## Detailed Source Database Column to Output Object Properties Mapping

This section details the journey of data from the `Sparx_System_DataExchange` database view columns to the properties of the final `Exchange` JSON object, including intermediate mappings and transformations.

**Source View:** `Sparx_System_DataExchange`

**Intermediate Document Type:** `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeValues` (raw string values from DB)

**Final Output Object:** `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` (transformed types for API response)

*   `Sparx_System_DataExchange."Connection GUID"`: `exchangeId` (string)
*   `Sparx_System_DataExchange."Connection Name"`: `exchangeName` (string)
*   `Sparx_System_DataExchange."Sparx Exchange Description"`: `exchangeDescription` (string)
*   `Sparx_System_DataExchange."Object State"`: `exchangeState` (string)
*   `Sparx_System_DataExchange."Retire Date"`: `exchangeRetiredDate` (string converted to `java.util.Date` using `yyyy-MM-dd` format)
*   `Sparx_System_DataExchange."Sparx Sendor GUID"`: `fromOwnerId` (string)
*   `Sparx_System_DataExchange."Sender Name"`: `fromOwnerName` (string)
*   `Sparx_System_DataExchange."Sender Type"`: `fromOwnerType` (string)
*   `Sparx_System_DataExchange."Sparx Receiver GUID"`: `toOwnerId` (string)
*   `Sparx_System_DataExchange."Receiver Name"`: `toOwnerName` (string)
*   `Sparx_System_DataExchange."Receiver Type"`: `toOwnerType` (string)
*   `Sparx_System_DataExchange."Sparx Exchange Frequency"`: `connectionFrequency` (string tokenized by space into string array)
*   `Sparx_System_DataExchange."IE Agreement"`: `dataExchangeAgreement` (string)
*   `Sparx_System_DataExchange."Exchange includes Beneficiary Address Data"`: `containsBeneficiaryAddress` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Beneficiary Address Purpose"`: `businessPurposeOfAddress` (string tokenized by space into string array)
*   `Sparx_System_DataExchange."Address Data Editable"`: `isAddressEditable` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange Contains PII"`: `containsPii` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange Contains PHI"`: `containsPhi` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Contains Health Disparity Data"`: `containsHealthDisparityData` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange Includes Banking Data"`: `containsBankingData` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange Supports Mailing to Beneficiaries"`: `isBeneficiaryMailingFile` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Data Shared via API"`: `sharedViaApi` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."API Ownership"`: `apiOwnership` (string)
*   `Sparx_System_DataExchange."Type of Data ID"`: `typeOfData.id` (string nested in `typeOfData` object array)
*   `Sparx_System_DataExchange."Type of Data"`: `typeOfData.name` (string nested in `typeOfData` object array)
*   `Sparx_System_DataExchange."Number of Records Exchanged"`: `numOfRecords` (string)
*   `Sparx_System_DataExchange."Exchange Format"`: `dataFormat` (string)
*   `Sparx_System_DataExchange."Exchange Format Other"`: `dataFormatOther` (string)
*   `Sparx_System_DataExchange."Exchange Contains CUI"`: `exchangeContainsCUI` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange CUI Description"`: `exchangeCUIDescription` (string)
*   `Sparx_System_DataExchange."Exchange CUI Type"`: `exchangeCUIType` (string)
*   `Sparx_System_DataExchange."Exchange Connection Authenticated"`: `exchangeConnectionAuthenticated` (string converted to `java.lang.Boolean`)
*   `Sparx_System_DataExchange."Exchange Network Protocol"`: `exchangeNetworkProtocol` (string tokenized by `|` into string array)
*   `Sparx_System_DataExchange."Exchange Network Protocol Other"`: `exchangeNetworkProtocolOther` (string)
*   `Sparx_System_DataExchange."Exchange Start Date"`: `exchangeStartDate` (string converted to `java.util.Date` using `yyyy-MM-dd` format)
*   `Sparx_System_DataExchange."Exchange End Date"`: `exchangeEndDate` (string converted to `java.util.Date` using `yyyy-MM-dd` format)
*   `Sparx_System_DataExchange."Exchange Version"`: `exchangeVersion` (string)

Additionally, the `ExchangeFindResponse` wrapper also outputs:
*   `count`: The total count of exchanges found (string converted to `java.math.BigInteger`).