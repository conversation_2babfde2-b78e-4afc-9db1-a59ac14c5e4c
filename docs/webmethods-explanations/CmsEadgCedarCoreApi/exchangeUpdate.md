# Webmethods Service Explanation: CmsEadgCedarCoreApi exchangeUpdate

The `exchangeUpdate` service is a core component within the `CmsEadgCedarCoreApi` package, specifically located at `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`. Its primary business purpose is to facilitate the update of data exchange records within the "Alfabet" system, which it achieves by interacting with a backend SQL Server database through a stored procedure.

The service accepts an array of data exchange records as input. It transforms these records into a specific JSON structure compliant with the target system's requirements and then passes this JSON to a database stored procedure for the actual update operation. The expected output is a simple success or failure response. Key validation rules primarily revolve around the structure and types of the input data, with the underlying database stored procedure handling data integrity and business rule validations.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `exchangeUpdate`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Webmethods Concepts for Non-Webmethods Developers

Webmethods flow services use a visual programming paradigm, where different operations are represented as "steps" within a control flow. Here are some key concepts encountered in this service:

*   **FLOW**: This is the top-level container for an integration service. It defines the overall sequence and structure of operations.
*   **SEQUENCE**: A `SEQUENCE` block executes its child steps in the order they appear. It can have `TIMEOUT` settings and an `EXIT-ON` condition (e.g., `FAILURE`, which causes the sequence to stop on the first error). When `FORM="TRY"`, it functions as a "try" block in traditional programming, where errors caught within it can be handled by a corresponding `CATCH` block.
*   **BRANCH**: Similar to a `switch` or `if-else if-else` statement in other languages, a `BRANCH` step directs the flow based on the value of a specified variable (`SWITCH`). Each branch has a `NAME` (the value to match) and executes its own `SEQUENCE` of steps. A `$default` branch handles cases where no explicit match is found.
*   **MAP**: A fundamental data transformation step. It allows manipulating data within the Webmethods "pipeline" (a shared memory space for data flowing through the service).
    *   `MAPCOPY`: Copies data from a source field to a target field.
    *   `MAPSET`: Sets a literal value to a target field.
    *   `MAPDELETE`: Removes a field from the pipeline.
    *   `MODE="INPUT"`: Defines how data is mapped into a service being invoked.
    *   `MODE="OUTPUT"`: Defines how data from a service invocation's output is mapped into the current service's pipeline.
    *   `MODE="STANDALONE"`: Defines data transformations within the current flow step without involving service invocation inputs/outputs.
*   **INVOKE**: This step calls another Webmethods service, which can be a built-in utility, another flow service, or an adapter service (e.g., for database interaction). `VALIDATE-IN="$none"` and `VALIDATE-OUT="$none"` indicate that input and output schemas are not strictly validated at runtime for this invocation.
*   **LOOP**: This step iterates over elements in an array. `IN-ARRAY` specifies the input array to iterate over, and `OUT-ARRAY` (though not used in this service's main loop) specifies an array to collect results.
*   **EXIT**: This step terminates the execution of the current flow or a parent flow (specified by `FROM="$parent"`). It can signal a `SUCCESS` or `FAILURE` state.
*   **CATCH**: Paired with a `TRY` block, the `CATCH` block executes if an error occurs within the corresponding `TRY` block. It is typically used for centralized error handling and cleanup.
*   **Document Types (recref)**: Webmethods uses "document types" (often referenced as `recref` in XML) to define complex data structures, analogous to JSON schemas, TypeScript interfaces, or classes. `IData` is the internal representation of these documents in the pipeline, similar to a dynamic map or dictionary.

## Database Interactions

The `exchangeUpdate` service interacts with a SQL Server database to persist the transformed data.

*   **Database Connection**: The service utilizes the JDBC connection named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.
    *   **Server Name**: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port**: `1433`
    *   **Database Name**: `Sparx_Support`
    *   **User**: `sparx_dbuser` (password is configured separately)
    *   **JDBC Driver**: `com.microsoft.sqlserver.jdbc.SQLServerDataSource`
    *   **Transaction Type**: `NO_TRANSACTION` (meaning each database operation commits independently).
*   **SQL Queries or Stored Procedures Called**:
    *   The service invokes the stored procedure `SP_Update_SystemDataExchange_json;1`. The `;1` typically denotes a version or overload of the stored procedure.
    *   This stored procedure accepts two parameters:
        *   `@jsonInput` (Input, NVARCHAR): This parameter receives a JSON string containing the data to be updated.
        *   `@jsonOutput` (Input/Output, NVARCHAR): This parameter can potentially return JSON data from the stored procedure, although its output is not used by this Webmethods service.
        *   `@RETURN_VALUE` (Output, INTEGER): This is the return value of the stored procedure, indicating success (0) or failure (non-zero).
*   **Data Mapping between Service Inputs and Database Parameters**:
    *   The service first transforms its input `Exchange` documents into a `UpdateRequest` document.
    *   This `UpdateRequest` document is then converted into a single JSON string using `pub.json:documentToJSONString`.
    *   This `jsonString` is passed directly as the `@jsonInput` parameter to the `SP_Update_SystemDataExchange_json` stored procedure. The actual mapping from this JSON string's fields to specific database tables and columns happens entirely within the stored procedure's logic.

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke external HTTP/REST APIs. The "Alfabet" system, which is the conceptual target for data updates, is interfaced via a database stored procedure (`SP_Update_SystemDataExchange_json`) rather than a separate HTTP API call. Document types like `cms.eadg.alfabet.api.v01.resources.update.docs:UpdateRequest` are internal data models used to structure the JSON payload sent to the database adapter, not for direct external API communication within this service flow. A previously existing (or future) external API interaction for "Alfabet" may have been disabled (`DISABLED="true"` on one `INVOKE` to `cms.eadg.alfabet.api.v01.resources.update.operations:updateAlfabet`) or is handled in a different service.

## Main Service Flow

The `exchangeUpdate` service's flow outlines the following sequence of operations:

1.  **Try Block Initialization**: The entire main logic is encapsulated within a `SEQUENCE` block configured as a `TRY` block. This ensures that any errors occurring during execution are caught and handled by the `CATCH` block.
2.  **Initialize Variables (MAP)**: An initial `MAP` step is used to set up variables for the flow, notably initializing the `UpdateRequest` document to "API User" for the `CurrentProfile` and an `indexRelations` counter to "0".
3.  **Map Exchange Data (`mapExchange` INVOKE)**:
    *   The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapExchange`. This crucial sub-service transforms the input array of `Exchange` documents (from `_generatedInput/Exchanges`) into a `UpdateRequest` document structure.
    *   After the `mapExchange` service completes, its original `Exchanges` input is removed from the pipeline, but the generated `UpdateRequest` is retained for subsequent steps.
4.  **Convert to JSON String (`documentToJSONString` INVOKE)**:
    *   The `UpdateRequest` document is then converted into a JSON string using the built-in `pub.json:documentToJSONString` service. This JSON string is the payload that will be sent to the database stored procedure.
    *   The original `UpdateRequest` document is removed from the pipeline, and only the `jsonString` remains.
5.  **Execute Database Stored Procedure (`updateExchangeSP` INVOKE)**:
    *   The service calls the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:updateExchangeSP` adapter service. This adapter is configured to invoke the `SP_Update_SystemDataExchange_json` stored procedure in the `Sparx_Support` database.
    *   The `jsonString` generated in the previous step is passed as the `@jsonInput` parameter to this stored procedure.
    *   Upon completion, the `jsonString` and other temporary input variables for the adapter are removed from the pipeline. The `updateExchangeSPOutput` (containing the `@RETURN_VALUE` from the stored procedure) is retained.
6.  **Branch on Stored Procedure Return Value (BRANCH)**:
    *   A `BRANCH` step evaluates the `@RETURN_VALUE` received from the `updateExchangeSP` stored procedure.
    *   **Success Path (`NAME="0"`)**: If the `@RETURN_VALUE` is "0" (indicating success from the stored procedure), the flow proceeds to:
        *   A `MAP` step which sets the `result` field of the overall `Response` to "success". It also performs some cleanup of unrelated variables (`jsonString`, `addBudgetInputOutput`).
    *   **Failure Path (`$default`)**: If the `@RETURN_VALUE` is anything other than "0" (indicating a failure from the stored procedure), the `$default` branch is executed. This branch contains an `EXIT FROM="$parent" SIGNAL="FAILURE"` step, which immediately terminates the main `exchangeUpdate` service and signals a failure to the calling process.
7.  **Catch Block (Error Handling)**:
    *   If any error occurs during the execution of the `TRY` block (either through an explicit `EXIT SIGNAL="FAILURE"` or an unhandled exception), control is transferred to the `CATCH` block.
    *   Within the `CATCH` block, `pub.flow:getLastError` is invoked to retrieve detailed information about the error that occurred.
    *   Subsequently, `cms.eadg.utils.api:handleError` is called. This utility service is responsible for processing the error information and setting up an appropriate HTTP error response.

## Dependency Service Flows

The `exchangeUpdate` service relies on several other services to perform its operations. These dependency services handle data transformation and generalized error reporting.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeUpdate:mapExchange`**:
    *   **Purpose**: This flow service is responsible for transforming the incoming `Exchange` data into the `UpdateRequest` format, which is a nested structure compatible with the `SP_Update_SystemDataExchange_json` stored procedure. This involves creating a main `InformationFlow` object (which becomes `UpdateRequest.Objects.Values`), setting its `ClassName` to "InformationFlow", and establishing several `Relations` objects for various associations.
    *   **Integration with Main Flow**: It's called early in the `exchangeUpdate` service to prepare the data for the database interaction.
    *   **Input/Output Contract**: Takes an array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` as input and produces a `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest` as output.
    *   **Specialized Processing**:
        *   Initializes `CurrentProfile` to "API User" and an `indexRelations` counter.
        *   It iterates through each `Exchange` object in the input array.
        *   **Field Mapping and Type Conversion**: It performs extensive mapping of fields from the `Exchange` document type to the `InformationFlow` document type. This includes:
            *   Converting boolean fields (e.g., `isAddressEditable`, `containsPii`) from Webmethods' internal boolean type to string representations ("Yes" or "No") using `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:convertBooleanToString`.
            *   Concatenating array fields (e.g., `businessPurposeOfAddress`, `connectionFrequency`, `exchangeNetworkProtocol`, `exchangeCUIType`) into single string fields, using a pipe (`|`) as a separator, via `pub.string:makeString`.
            *   Formatting date fields (`exchangeRetiredDate`) to "MM/dd/yyyy" string format using `cms.eadg.utils.date:formatDateIf`.
            *   Constructing a compound `name` for the `InformationFlow` object using `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:createExchangeName` based on `fromOwnerName`, `toOwnerName`, and `exchangeVersion`.
        *   **Relationship Creation**: For each `Exchange` object, it creates up to four standard `Relations` (Property: "From", "To", "FromOwner", "ToOwner") linking the `exchangeId` to `fromOwnerId` and `toOwnerId`. It also dynamically creates additional `Relations` for each entry in `typeOfData`, setting the `Property` to "cms_data_exch_type_of_data" and `ToRef` to the `typeOfData.id`. These relations are added to the `UpdateRequest.Relations` array.
*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This service provides a standardized way to handle errors across various APIs. It takes detailed error information and constructs a generic error response.
    *   **Integration with Main Flow**: It is invoked by the `CATCH` block of the `exchangeUpdate` service when any failure occurs, ensuring a consistent error response format.
    *   **Input/Output Contract**: Takes a `lastError` document (containing error details from `pub.flow:getLastError`) and an optional `SetResponse` document (which can pre-configure response details). It doesn't return data directly but sets HTTP response properties.
    *   **Specialized Processing**: If no `SetResponse` is provided or if it's null, it defaults to a "500 Internal Server Error" status code with a generic "error" result and includes the actual error message from `lastError`. It then calls `cms.eadg.utils.api:setResponse` to finalize the HTTP response.
*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is a utility for setting the HTTP response code and body. It supports both JSON and XML response formats.
    *   **Integration with Main Flow**: It's called by `cms.eadg.utils.api:handleError` (and potentially by other success paths, though not directly observed in the `exchangeUpdate` main flow).
    *   **Input/Output Contract**: Takes a `cms.eadg.utils.api.docs:SetResponse` document as input, which specifies the response code, phrase, result message, and desired format (JSON or XML). It manipulates the HTTP response.
    *   **Specialized Processing**:
        *   Maps the input `SetResponse` into a `Response` document.
        *   Uses a `BRANCH` to determine the output content type:
            *   For "application/json", it uses `pub.json:documentToJSONString` to convert the `Response` document into a JSON string.
            *   For "application/xml", it first wraps the `Response` in a `ResponseRooted` document (to ensure a root element for XML) and then uses `pub.xml:documentToXMLString` to convert it into an XML string.
        *   Finally, it uses `pub.flow:setResponseCode` to set the HTTP status code and `pub.flow:setResponse2` to set the response body and content type.

## Data Structures and Types

The service heavily relies on predefined document types (analogous to schema definitions) to structure its input, intermediate data, and output.

*   **Input to `exchangeUpdate`**:
    *   `_generatedInput` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeUpdateRequest`): A wrapper document containing an array of `Exchanges`.
        *   `Exchanges`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` objects.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`**: Represents a single data exchange record.
    *   `exchangeId` (string, optional): Unique identifier for the exchange.
    *   `exchangeName` (string, optional): Name of the exchange.
    *   `exchangeDescription` (string, optional): Description of the exchange.
    *   `exchangeVersion` (string, optional): Version of the exchange.
    *   `exchangeState` (string, optional): Current state (e.g., Active, Retired).
    *   `exchangeStartDate` (Date object, optional): Start date of the exchange.
    *   `exchangeEndDate` (Date object, optional): End date of the exchange.
    *   `exchangeRetiredDate` (Date object, optional): Retirement date of the exchange.
    *   `fromOwnerId` (string, optional): ID of the originating owner system/entity.
    *   `fromOwnerName` (string, optional): Name of the originating owner.
    *   `fromOwnerType` (string, optional): Type of the originating owner.
    *   `toOwnerId` (string, optional): ID of the receiving owner system/entity.
    *   `toOwnerName` (string, optional): Name of the receiving owner.
    *   `toOwnerType` (string, optional): Type of the receiving owner.
    *   `connectionFrequency` (string array, optional): How often data is exchanged.
    *   `dataExchangeAgreement` (string, optional): Related data exchange agreement.
    *   `containsBeneficiaryAddress` (Boolean object, optional): Flag if data includes beneficiary addresses.
    *   `businessPurposeOfAddress` (string array, optional): Business purpose(s) for address data.
    *   `isAddressEditable` (Boolean object, optional): Flag if address data is editable.
    *   `containsPii` (Boolean object, optional): Flag if data includes Personally Identifiable Information (PII).
    *   `containsPhi` (Boolean object, optional): Flag if data includes Protected Health Information (PHI).
    *   `containsHealthDisparityData` (Boolean object, optional): Flag if data includes health disparity information.
    *   `containsBankingData` (Boolean object, optional): Flag if data includes banking information.
    *   `isBeneficiaryMailingFile` (Boolean object, optional): Flag if it's a beneficiary mailing file.
    *   `sharedViaApi` (Boolean object, optional): Flag if shared via API.
    *   `apiOwnership` (string, optional): Ownership details if shared via API.
    *   `typeOfData` (record array, optional): Array of records describing data types, each with:
        *   `id` (string, optional): ID of the data type.
        *   `name` (string, optional): Name of the data type.
    *   `numOfRecords` (string, optional): Number of records involved.
    *   `dataFormat` (string, optional): Format of the data (e.g., JSON, XML).
    *   `dataFormatOther` (string, optional): Other data format details.
    *   `exchangeContainsCUI` (Boolean object, optional): Flag if exchange contains Controlled Unclassified Information (CUI).
    *   `exchangeCUIDescription` (string, optional): Description of CUI.
    *   `exchangeCUIType` (string array, optional): Type(s) of CUI.
    *   `exchangeConnectionAuthenticated` (Boolean object, optional): Flag if connection is authenticated.
    *   `exchangeNetworkProtocol` (string array, optional): Network protocol(s) used.
    *   `exchangeNetworkProtocolOther` (string, optional): Other network protocol details.
*   **Intermediate `UpdateRequest` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`)**: This is the JSON structure sent to the database stored procedure.
    *   `CurrentProfile` (string): Set to "API User".
    *   `APICulture` (string, optional).
    *   `Objects`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object` records.
    *   `Relations`: An array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations` records.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object`**: Represents a single "object" (in Alfabet terms) to be updated.
    *   `RefStr` (string): A reference string, derived from `Exchange.exchangeId`.
    *   `ClassName` (string): Set to "InformationFlow".
    *   `Id` (string): Not explicitly mapped in this flow.
    *   `Values`: A dynamic record (IData) populated by fields from `InformationFlow`. This holds the actual properties of the InformationFlow object in key-value pairs.
    *   `GenericAttributes` (record array): Not used in this mapping, but part of the general object structure.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:InformationFlow`**: This document type serves as the primary data model for the `Values` field within the `Object` document type, which is then serialized to JSON for the stored procedure. Its fields often correspond to custom attributes (`cms_`) within the Alfabet system.
    *   `id` (string) - Not directly mapped from input.
    *   `alfa_license_policy` (string) - Not directly mapped from input.
    *   `baseid` (string) - Not directly mapped from input.
    *   `cms_adress_data_edits` (string)
    *   `cms_api_owner` (string)
    *   `cms_beneficiary_address_pur` (string)
    *   `cms_data_exch_api_data_share` (string)
    *   `cms_data_exch_benef_mailing` (string)
    *   `cms_data_exch_beneficiary_add` (string)
    *   `cms_data_exch_format` (string)
    *   `cms_data_exch_format_other` (string)
    *   `cms_data_exch_num_recs` (string)
    *   `cms_data_exch_type_of_data` (string array) - (Not explicitly mapped as an array into InformationFlow, but individual `typeOfData.id`s are used to create relations).
    *   `cms_data_exchange_banking` (string)
    *   `cms_data_exchange_phi` (string)
    *   `cms_data_exchange_pii` (string)
    *   `cms_exchange` (string)
    *   `cms_exchange_contains_cui` (string)
    *   `cms_exchange_cui_description` (string)
    *   `cms_exchange_cui_type` (string)
    *   `cms_exchange_connection_authenticated` (string)
    *   `cms_exchange_network_protocol` (string)
    *   `cms_exchange_network_protocol_other` (string)
    *   `cms_exchange_frequency` (string)
    *   `cms_exchange_retire_date` (string)
    *   `cms_exchangecomments` (string) - Not mapped.
    *   `cms_exchangeconfidencelevel` (string) - Not mapped.
    *   `cms_exchangeretiredateconfirm` (string) - Not mapped.
    *   `cms_exchangeuuid` (string) - Not mapped.
    *   `cms_health_disparity_data` (string)
    *   `cms_ie_agreement` (string)
    *   `color` (string) - Not mapped.
    *   `connectiondataformat` (string) - Not mapped.
    *   `connectionfrequency` (string) - Not mapped.
    *   `connectionmethod` (string) - Not mapped.
    *   `connectiontype` (string) - Not mapped.
    *   `creation_date` (string) - Not mapped.
    *   `creation_user` (string) - Not mapped.
    *   `creation_user_ref` (string) - Not mapped.
    *   `data` (string) - Not mapped.
    *   `description` (string)
    *   `documents` (string) - Not mapped.
    *   `enddate` (string)
    *   `fea_primary_reference` (string) - Not mapped.
    *   `forecolor` (string) - Not mapped.
    *   `from` (string) - Not mapped.
    *   `fromowner` (string)
    *   `icon` (string) - Not mapped.
    *   `last_update` (string) - Not mapped.
    *   `last_update_user` (string) - Not mapped.
    *   `leadlanguage` (string) - Not mapped.
    *   `name` (string)
    *   `namesuffix` (string) - Not mapped.
    *   `objectstate` (string)
    *   `project` (string) - Not mapped.
    *   `responsibleorganization` (string) - Not mapped.
    *   `responsibleuser` (string) - Not mapped.
    *   `responsibleusergroups` (string array) - Not mapped.
    *   `sag_imp_id` (string) - Not mapped.
    *   `sag_middleware` (string) - Not mapped.
    *   `samplerecordforusecases` (string) - Not mapped.
    *   `shortname` (string) - Not mapped.
    *   `sourceinterface` (string) - Not mapped.
    *   `startdate` (string)
    *   `status` (string)
    *   `statushistory` (string) - Not mapped.
    *   `targetinterface` (string) - Not mapped.
    *   `targetservice` (string) - Not mapped.
    *   `to` (string) - Not mapped.
    *   `toowner` (string)
    *   `version` (string)
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations`**: Represents a relationship between two objects in Alfabet.
    *   `FromRef` (string): Reference to the source object (e.g., `exchangeId`).
    *   `FromId` (string, optional).
    *   `Property` (string): The type of relationship (e.g., "From", "To", "FromOwner", "ToOwner", "cms_data_exch_type_of_data").
    *   `ToRef` (string): Reference to the target object (e.g., `fromOwnerId`, `toOwnerId`, `typeOfData.id`).
    *   `ToId` (string, optional).
*   **Output of `exchangeUpdate`**:
    *   `_generatedResponse` (type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`): A general API response structure.
        *   `result` (string): Indicates "success" or "error".
        *   `message` (string array, optional): Contains error messages if `result` is "error".
    *   Additional optional fields for HTTP error codes (400, 401, 500) which would contain a `Response` document if those error conditions are met.

**Detailed Input (Exchange Document) to Output (InformationFlow Properties in UpdateRequest) Mapping**:

This mapping outlines how fields from the input `Exchange` document are transformed into the `InformationFlow` object and `Relations` objects that form the JSON payload sent to the `SP_Update_SystemDataExchange_json` stored procedure.

*   `Exchange.exchangeId`: `UpdateRequest.Objects.RefStr`
*   `Exchange.exchangeName`: `InformationFlow.cms_exchange`
*   `Exchange.exchangeDescription`: `InformationFlow.description`
*   `Exchange.exchangeVersion`: `InformationFlow.version`
*   `Exchange.exchangeState`: `InformationFlow.objectstate`
*   `Exchange.exchangeState`: `InformationFlow.status`
*   `Exchange.exchangeStartDate`: `InformationFlow.startdate`
*   `Exchange.exchangeEndDate`: `InformationFlow.enddate`
*   `Exchange.exchangeRetiredDate`: `InformationFlow.cms_exchange_retire_date` (formatted as "MM/dd/yyyy")
*   `Exchange.fromOwnerId`: `InformationFlow.fromowner`
*   `Exchange.toOwnerId`: `InformationFlow.toowner`
*   `Exchange.connectionFrequency`: `InformationFlow.cms_exchange_frequency` (elements joined by `|`)
*   `Exchange.dataExchangeAgreement`: `InformationFlow.cms_ie_agreement`
*   `Exchange.containsBeneficiaryAddress`: `InformationFlow.cms_data_exch_beneficiary_add` ("Yes" / "No")
*   `Exchange.businessPurposeOfAddress`: `InformationFlow.cms_beneficiary_address_pur` (elements joined by `|`)
*   `Exchange.isAddressEditable`: `InformationFlow.cms_adress_data_edits` ("Yes" / "No")
*   `Exchange.containsPii`: `InformationFlow.cms_data_exchange_pii` ("Yes" / "No")
*   `Exchange.containsPhi`: `InformationFlow.cms_data_exchange_phi` ("Yes" / "No")
*   `Exchange.containsHealthDisparityData`: `InformationFlow.cms_health_disparity_data` ("Yes" / "No")
*   `Exchange.containsBankingData`: `InformationFlow.cms_data_exchange_banking` ("Yes" / "No")
*   `Exchange.isBeneficiaryMailingFile`: `InformationFlow.cms_data_exch_benef_mailing` ("Yes" / "No")
*   `Exchange.sharedViaApi`: `InformationFlow.cms_data_exch_api_data_share` ("Yes" / "No")
*   `Exchange.apiOwnership`: `InformationFlow.cms_api_owner`
*   `Exchange.typeOfData.id`: `UpdateRequest.Relations[].ToRef` (for `Property` "cms_data_exch_type_of_data")
*   `Exchange.numOfRecords`: `InformationFlow.cms_data_exch_num_recs`
*   `Exchange.dataFormat`: `InformationFlow.cms_data_exch_format`
*   `Exchange.dataFormatOther`: `InformationFlow.cms_data_exch_format_other`
*   `Exchange.exchangeContainsCUI`: `InformationFlow.cms_exchange_contains_cui` ("Yes" / "No")
*   `Exchange.exchangeCUIDescription`: `InformationFlow.cms_exchange_cui_description`
*   `Exchange.exchangeCUIType`: `InformationFlow.cms_exchange_cui_type` (elements joined by `|`)
*   `Exchange.exchangeConnectionAuthenticated`: `InformationFlow.cms_exchange_connection_authenticated` ("Yes" / "No")
*   `Exchange.exchangeNetworkProtocol`: `InformationFlow.cms_exchange_network_protocol` (elements joined by `|`)
*   `Exchange.exchangeNetworkProtocolOther`: `InformationFlow.cms_exchange_network_protocol_other`
*   (Derived from `Exchange.fromOwnerName`, `Exchange.toOwnerName`, `Exchange.exchangeVersion`): `InformationFlow.name`
*   (Derived from `Exchange.exchangeId`, `Exchange.fromOwnerId`): `UpdateRequest.Relations[].FromRef` and `UpdateRequest.Relations[].ToRef` for `Property` "From"
*   (Derived from `Exchange.exchangeId`, `Exchange.toOwnerId`): `UpdateRequest.Relations[].FromRef` and `UpdateRequest.Relations[].ToRef` for `Property` "To"
*   (Derived from `Exchange.exchangeId`, `Exchange.fromOwnerId`): `UpdateRequest.Relations[].FromRef` and `UpdateRequest.Relations[].ToRef` for `Property` "FromOwner"
*   (Derived from `Exchange.exchangeId`, `Exchange.toOwnerId`): `UpdateRequest.Relations[].FromRef` and `UpdateRequest.Relations[].ToRef` for `Property` "ToOwner"

## Error Handling and Response Codes

The service employs a comprehensive error handling strategy to provide clear feedback in case of issues.

*   **Different Error Scenarios Covered**: The service is designed to catch any errors that occur within its primary execution path. This includes issues during data transformation (e.g., if a sub-service fails), problems with the database connection, or a non-successful return value from the invoked stored procedure.
*   **Error Handling Mechanism**:
    *   The main service logic is enclosed in a `TRY` block. If any step within this block (or its invoked sub-services) fails, control is transferred to the `CATCH` block.
    *   Within the `CATCH` block, the `pub.flow:getLastError` service retrieves details about the exception, including the error message and stack trace.
    *   This error information is then passed to the `cms.eadg.utils.api:handleError` service, which is a centralized error processing utility.
    *   If the database stored procedure (`SP_Update_SystemDataExchange_json`) returns a non-zero value, the service explicitly `EXIT`s with a `FAILURE` signal, which also triggers the `CATCH` block.
*   **HTTP Response Codes Used**:
    *   **Success**: If the service executes successfully (i.e., the stored procedure returns `0`), the service sets the `result` to "success" in its `Response` document. While no explicit `pub.flow:setResponseCode` is called for success, Webmethods typically defaults to `200 OK` for successful service executions.
    *   **Failure**: In case of an error, the `cms.eadg.utils.api:handleError` service, via `cms.eadg.utils.api:setResponse`, will set the HTTP response code. By default, if no specific `SetResponse` input is provided to `handleError`, it sets the `responseCode` to "500" and the `responsePhrase` to "Internal Server Error". The service's `node.ndf` explicitly lists `400`, `401`, and `500` as potential error outputs, indicating a possibility for `handleError` or other internal logic to map specific error types to these codes if desired.
*   **Error Message Formats**: Error responses are structured as a `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` document (which is also used in `cms.eadg.utils.api.docs:Response`). This document contains a `result` field (set to "error") and a `message` field (an array of strings) populated with the captured error details from `pub.flow:getLastError`. The `cms.eadg.utils.api:setResponse` service converts this `Response` document into either JSON (`application/json`) or XML (`application/xml`) based on the requested format, ensuring a consistent output structure for error messages.
*   **Fallback Behaviors**: The `TRY-CATCH` block acts as a robust fallback. Any unhandled exception during the main processing flow is caught, and a standardized 500-level error response is returned to the client, preventing ungraceful service termination and providing a consistent error payload.