This document explains the Webmethods service `exchangeAdd` within the `CmsEadgCedarCoreApi` package, based on the provided Webmethods XML files.

Please note: Your initial request mentioned `CmsEadgCensusCoreApi systemPropertyAdd`. However, the provided files clearly indicate the service name is `exchangeAdd` and the package is `CmsEadgCedarCoreApi`. This explanation will proceed based on the provided file contents for `CmsEadgCedarCoreApi exchangeAdd`.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `exchangeAdd`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `exchangeAdd` service is designed to add one or more data exchange records to an external system, identified as "Alfabet" based on dependency names. It acts as an intermediary, transforming incoming data into a format suitable for the external system and then persisting it.

*   **Business Purpose:** To create or add new data exchange entries within the "Alfabet" system, which likely serves as a central repository or management tool for data exchanges. The service handles the mapping of input data to the external system's required format and manages the database interaction.
*   **Input Parameters:** The service expects a single input parameter named `_generatedInput`, which is of type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeAddRequest`. This document type contains an array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` objects, representing the data exchanges to be added.
*   **Expected Outputs:** The service returns a `_generatedResponse` of type `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`. In case of success, this response will have a "result" of "success" and its "message" field will contain an array of GUIDs for the newly created objects. In case of failure (partial or total), it will have a "result" of "error" and the "message" will contain relevant error descriptions.
*   **Side Effects:** The primary side effect is the insertion of new records into a database via a stored procedure (`SP_Insert_SystemDataExchange_json`). In cases of partial failure, it also attempts a rollback by calling an external "Alfabet" API (`deleteObjectsByRef`) to delete any objects that were successfully inserted before an error occurred.
*   **Key Validation Rules:**
    *   The service checks if the number of successfully inserted objects (from the database response) matches the number of objects sent in the request.
    *   During potential rollback (in `deleteObjectsByRef`), it validates that the class ID extracted from the reference string (`RefStr`) matches an expected `className` (e.g., "InformationFlow"), ensuring it attempts to delete objects of the correct type.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming paradigm known as "Flow services" to define business logic. Here's how some key elements in these XML files translate to common programming concepts:

*   **SEQUENCE:** Represents a block of code.
    *   `SEQUENCE TIMEOUT="" EXIT-ON="FAILURE"`: Similar to a `try` block in other languages. If any step within this sequence fails (throws an exception), the entire sequence immediately exits, and control is passed to the next available error handler (a `CATCH` block).
    *   `FORM="TRY"` or `FORM="CATCH"`: Explicitly defines `try` and `catch` blocks for error handling.
*   **BRANCH:** A control flow statement for conditional execution, similar to a `switch` statement in Java/TypeScript or `if-else if-else` chain.
    *   `SWITCH="/path/to/variable"`: The value of the specified variable is used to determine which `SEQUENCE` (case) to execute.
    *   `LABELEXPRESSIONS="true"`: Allows the `NAME` attribute of a `SEQUENCE` within a `BRANCH` to be a dynamic expression, enabling flexible conditional logic (e.g., `%requestTotalCount% == %responseCount%`).
    *   `NAME="$default"`: The default case if no other `SEQUENCE`'s `NAME` (condition) matches, similar to a `default` case in a `switch` statement.
*   **MAP:** The core data transformation step in Webmethods Flow services. It allows mapping data from input variables to output variables, renaming fields, setting default values, and performing simple data manipulation.
    *   `MAPCOPY`: Copies the value of a field from the source side of the map to a field on the target side.
    *   `MAPSET`: Assigns a static literal value to a target field. Can also set dynamic values using expressions (`VARIABLES="true"`).
    *   `MAPDELETE`: Removes a field from the pipeline (the in-memory data structure). This is crucial for pipeline management and preventing sensitive data from being unnecessarily carried forward.
*   **INVOKE:** Calls another service (which could be another Flow service, a Java service, an Adapter service, or a built-in `pub` service). This is analogous to calling a function or method.
    *   `VALIDATE-IN="$none"` / `VALIDATE-OUT="$none"`: These attributes control whether the Integration Server should validate the input/output signatures of the invoked service. `$none` means no validation.
*   **LOOP:** Iterates over an array in the pipeline.
    *   `IN-ARRAY="/path/to/array"`: Specifies the array to iterate over. Each element of the array is temporarily available as `_current` (or directly by field name if the array contains records).
    *   `OUT-ARRAY="/path/to/outputArray"`: Allows mapping the results of each iteration to a new array.
*   **Error Handling (TRY/CATCH):** As mentioned under `SEQUENCE`, this is structured as a `TRY` block and a `CATCH` block. When an error occurs in the `TRY` block, execution jumps to the associated `CATCH` block. The `pub.flow:getLastError` service is typically called in the `CATCH` block to retrieve details about the error (like the error message and stack trace).
*   **`pub` Services:** These are built-in, core services provided by Webmethods (e.g., `pub.json:documentToJSONString`, `pub.string:tokenize`, `pub.math:addInts`). They offer common utility functions.
*   **Global Variables:** Referenced with `%variableName%` in `MAPSET` or expressions. `cms.eadg.utils.globalVariables:getGlobalVariable` is used to retrieve their values. These are server-level configurations, similar to environment variables or global constants.

## Database Interactions

The `exchangeAdd` service interacts with a Microsoft SQL Server database using a JDBC adapter.

*   **Database Operations Performed:** The primary database operation is an insertion, mediated by a stored procedure call.
*   **Database Connection Configuration:** The service uses the JDBC connection named `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`.
    *   **Server Name:** `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   **Port:** `1433`
    *   **Database Name:** `Sparx_Support`
    *   **User:** `sparx_dbuser` (password retrieved from `password.cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`)
    *   **Transaction Type:** `NO_TRANSACTION` - This is a crucial detail. It means that the adapter service itself does *not* participate in a distributed transaction. Each call to the stored procedure is an independent transaction. This implies that if a partial failure occurs during batch insertion, manual rollback (like calling `deleteObjectsByRef`) is necessary, as seen in `mapResponse`.
*   **SQL Queries or Stored Procedures Called:**
    *   **Stored Procedure:** `SP_Insert_SystemDataExchange_json;1`
    *   **Input Parameters to Stored Procedure:**
        *   `@jsonInput` (NVARCHAR): A JSON string containing the data for the data exchange (derived from the `InformationFlow` document type).
        *   `@jsonOutput` (NVARCHAR, INOUT): A JSON string that is also an output, likely containing success information or GUIDs of inserted records.
    *   **Output Parameter from Stored Procedure:**
        *   `@RETURN_VALUE` (INTEGER): An integer return code indicating the success or failure of the stored procedure execution.

*   **Data Mapping Between Service Inputs and Database Parameters:**
    The `UpdateRequest` document (populated from the input `Exchange` objects) is converted into a single JSON string using `pub.json:documentToJSONString`. This JSON string is then passed as the `@jsonInput` parameter to the `SP_Insert_SystemDataExchange_json` stored procedure. The structure of this JSON string conforms to the `InformationFlow` document type.

## External API Interactions

The service includes logic for interacting with an external "Alfabet API", primarily for rollback purposes in case of partial failures during database insertion.

*   **External Services Called:**
    *   `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`
*   **Request/Response Formats:**
    *   For `deleteObjectsByRef`, the request is built as an `ObjectByRefRequest` document (which contains an array of `Refs` or reference strings, usually GUIDs). This document is then converted to a JSON string and sent in the HTTP request body.
    *   The response from the external Alfabet API is expected to be a JSON string, which is then parsed into an `ObjectByRefResponse` document.
*   **Authentication Mechanisms:**
    *   The `deleteObjectsByRef` service uses a `Bearer` token for authentication when making the HTTP call to the Alfabet API. This token is passed as an input (`token`) to `deleteObjectsByRef`.
    *   The URL for the Alfabet API is retrieved from a global variable `alfabet.api.url`.
*   **Error Handling for External Calls:**
    *   The `deleteObjectsByRef` service itself checks the HTTP status code of the response from the Alfabet API. If it's `200` (OK), it processes the response. If it's `403` (Forbidden), it internally remaps it to a `500 Internal Server Error`. For any other error status, it propagates the HTTP status code and message as an error.
    *   Messages from rejected objects (if the external API reports partial success/failure) are extracted by `cms.eadg.alfabet.api.v01.resources.update.utils:extractRejectedObjectsMessages` and appended to the main service's error message.

## Main Service Flow (`CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_/services/exchangeAdd/flow.xml`)

The main service orchestrates the steps to add data exchange records.

1.  **Initialization:** A `MAP` step initializes internal variables `indexRelations` to `0` and `indexId` to `1`. It also sets the `CurrentProfile` field within the `UpdateRequest` document (which will be built later) to "API User".
2.  **Input Mapping (`mapExchange`):**
    *   The service invokes `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:mapExchange`.
    *   This sub-service takes the input `_generatedInput/Exchanges` array (of type `Exchange`) and transforms it into a `UpdateRequest` document. This `UpdateRequest` is structured for the external Alfabet API, containing `Objects` (each representing an `InformationFlow`) and `Relations`.
    *   Detailed mapping and transformations occur within `mapExchange` (see "Dependency Service Flows" section).
3.  **JSON Conversion:**
    *   `pub.json:documentToJSONString` converts the generated `UpdateRequest` document into a JSON string. This JSON string is the payload that will be sent to the database stored procedure.
4.  **Database Insertion (`addExchangeSP`):**
    *   The service calls the adapter service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:addExchangeSP`.
    *   This adapter executes the `SP_Insert_SystemDataExchange_json` stored procedure in the configured SQL Server database, passing the JSON string as the `@jsonInput` parameter.
    *   The stored procedure is expected to return an integer (`@RETURN_VALUE`) and a JSON string (`@jsonOutput`) containing details like the GUIDs of the newly created objects.
5.  **Result Processing (BRANCH on `addExchangeSPOutput/@RETURN_VALUE`):**
    *   **Success (`0`):** If `@RETURN_VALUE` is 0 (indicating success from the stored procedure):
        *   `pub.json:jsonStringToDocument` converts the `@jsonOutput` JSON string from the stored procedure into an `ExchangeAddGUIDS` document.
        *   A `MAP` step sets the final `_generatedResponse/result` to "success" and populates `_generatedResponse/message` with the `GUID`s extracted from `ExchangeAddGUIDS/NewObjects`.
    *   **Default (Any other value):** If `@RETURN_VALUE` is not 0 (indicating an error or unexpected outcome from the stored procedure), the service immediately exits with a `FAILURE` signal. This failure will be caught by the main service's `CATCH` block.
6.  **Error Handling (CATCH block):**
    *   If any step within the main `TRY` block (steps 1-5) fails, control is transferred to the `CATCH` block.
    *   `pub.flow:getLastError` retrieves the details of the error that occurred.
    *   `cms.eadg.utils.api:handleError` is then invoked. This utility service is responsible for determining the appropriate HTTP response code and message based on the error and setting these into the pipeline for the final HTTP response.

## Dependency Service Flows

The `exchangeAdd` service relies on several other services to perform its tasks.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:mapExchange`:**
    *   **Purpose:** This service transforms the high-level `Exchange` input into the detailed `UpdateRequest` structure expected by the "Alfabet API". This includes mapping fields, handling array-to-string conversions, and setting up relations.
    *   **Integration:** Called early in the main flow to prepare the data before sending it to the database.
    *   **Input Contract:** `Exchanges` (an array of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange` documents).
    *   **Output Contract:** `UpdateRequest` (a single `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest` document).
    *   **Specialized Processing:**
        *   Uses `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:convertBooleanToString` to convert boolean fields (e.g., `isAddressEditable`, `sharedViaApi`) from `Exchange` to string representations (likely "true"/"false" or "yes"/"no") for fields like `cms_adress_data_edits` in `InformationFlow`.
        *   Uses `pub.string:makeString` with `|` as a separator to convert arrays of strings (e.g., `connectionFrequency`, `exchangeNetworkProtocol`, `exchangeCUIType`) into single pipe-separated strings suitable for database columns that store multiple values.
        *   Uses `cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:createExchangeName` to generate a canonical `name` for the `InformationFlow` based on `fromOwnerName`, `toOwnerName`, and `exchangeVersion`.
        *   Constructs `Object` records with `ClassName` "InformationFlow" and maps the transformed `InformationFlow` data into their `Values` field.
        *   Generates four `Relations` for each `Exchange` object: "From", "To", "FromOwner", and "ToOwner", linking the newly created "InformationFlow" object (referenced by `indexId`) to the `fromOwnerId` and `toOwnerId`. It also maps a `typeOfData` relation.
        *   Incrementally updates `indexId` and `indexRelations` counters to manage array indices within the `UpdateRequest`.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.Exchange:addExchangeSP`:**
    *   **Purpose:** This is the JDBC Adapter service responsible for calling the stored procedure `SP_Insert_SystemDataExchange_json`.
    *   **Integration:** Directly invoked by the main service to perform the database insertion.
    *   **Input Contract:** `addExchangeSPInput` containing `@jsonInput` (JSON string of `UpdateRequest`) and an optional `$connectionName` for credential override.
    *   **Output Contract:** `addExchangeSPOutput` containing `@RETURN_VALUE` (integer) and `@jsonOutput` (JSON string).
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.exchangeAdd:mapResponse`:**
    *   **Purpose:** This service processes the outcome of the database insertion and constructs the final response, including handling partial failures and initiating rollback if necessary.
    *   **Integration:** Called immediately after `addExchangeSP` in the main flow.
    *   **Input Contract:** `UpdateRequest`, `UpdateResponse` (from the database adapter), and `token` (for potential rollback calls).
    *   **Output Contract:** `Response` (the final output for the main service).
    *   **Specialized Processing:**
        *   Compares `requestTotalCount` (total objects + relations in the original request) with `responseCount` (objects inserted into Alfabet, derived from `UpdateResponse/Count`).
        *   If `responseCount == requestTotalCount`, it signifies full success. The `Response/result` is set to "success", and `Response/message` gets the GUIDs of the new objects.
        *   If `responseCount == 0`, it signifies a total failure. It sets `responseCode` to `400` ("Bad Request") and `result` to "error" with a message "No updates made.".
        *   If `responseCount` is not 0 but `requestTotalCount` is not equal to `responseCount` (partial failure), it attempts a rollback:
            *   It calls `cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef` using the `refstrs` (GUIDs) extracted from `UpdateResponse/NewObjects`.
            *   Sets `responseCode` to `400` ("Bad Request") and `result` to "error" with a message "Partial failure. Rollback was attempted.".
            *   Appends messages from `extractRejectedObjectsMessages` (if any were returned by the Alfabet API during the initial insert attempt).
*   **`cms.eadg.alfabet.api.v01.resources.objects.byRef.services:deleteObjectsByRef`:**
    *   **Purpose:** This service is used to delete objects from the external "Alfabet API" by their reference strings (GUIDs). It's primarily used here for rollback.
    *   **Integration:** Called by `mapResponse` in case of partial failure.
    *   **Input Contract:** `token` (Bearer token for authentication), `ObjectByRefRequest` (containing `Refs` to delete), optional `validateClassName` and `className`.
    *   **Output Contract:** `ObjectByRefResponse` (details of deletion outcome) or `Response` (error details).
    *   **Specialized Processing:** Constructs and executes an HTTP POST request to the Alfabet API `/v2/delete` endpoint. It performs validation to ensure the class ID from the `Refs` matches the expected `className` (e.g., "InformationFlow").
*   **`cms.eadg.alfabet.api.v01.resources.update.utils:extractRejectedObjectsMessages`:**
    *   **Purpose:** Extracts only the `Message` fields from an array of `RejectedObjects` (which are returned by the external Alfabet API if some objects in a batch update/insert fail).
    *   **Integration:** Called by `mapResponse` during partial failure to collect specific error messages.
    *   **Input Contract:** `RejectedObjects` (an array of records, each with `RefStr` and `Message`).
    *   **Output Contract:** `messages` (an array of strings, containing only the error messages).
*   **`cms.eadg.utils.api:handleError`:**
    *   **Purpose:** A reusable utility service to standardize error responses across different APIs.
    *   **Integration:** Called by the `CATCH` block of the main service (`exchangeAdd`) and other sub-services.
    *   **Logic:** If a `SetResponse` document is already present in the pipeline (meaning a more specific error has already been defined), it uses that. Otherwise, it sets a default `500 Internal Server Error` response, using the `lastError` details from the Webmethods flow.
*   **`cms.eadg.utils.api:setResponse`:**
    *   **Purpose:** Another reusable utility service responsible for setting the HTTP response headers and body based on a standardized `SetResponse` document.
    *   **Integration:** Called by `handleError` and other services that need to directly influence the HTTP response.
    *   **Logic:** Takes a `SetResponse` document (with `responseCode`, `responsePhrase`, `result`, `message`, `format`) and uses `pub.flow:setResponseCode` and `pub.flow:setResponse2` to configure the HTTP response. It also handles marshalling the response content to either JSON or XML based on the `format` field.

## Data Structures and Types

The service heavily relies on predefined document types (schemas) for its inputs, outputs, and internal processing.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeAddRequest`:** The top-level input document for `exchangeAdd`. It contains an array of `Exchanges`.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Exchange`:** Represents a single data exchange. This is the source structure provided by the API caller. It includes fields such as:
    *   `exchangeId` (string, optional)
    *   `exchangeName` (string, optional)
    *   `exchangeDescription` (string, optional)
    *   `exchangeVersion` (string, optional)
    *   `exchangeState` (string, optional)
    *   `exchangeStartDate`, `exchangeEndDate`, `exchangeRetiredDate` (object, type `java.util.Date`, optional)
    *   `fromOwnerId`, `fromOwnerName`, `fromOwnerType` (strings, optional)
    *   `toOwnerId`, `toOwnerName`, `toOwnerType` (strings, optional)
    *   `connectionFrequency` (string array, optional)
    *   `dataExchangeAgreement` (string, optional)
    *   Boolean flags (`containsBeneficiaryAddress`, `isAddressEditable`, `containsPii`, `containsPhi`, `containsHealthDisparityData`, `containsBankingData`, `isBeneficiaryMailingFile`, `sharedViaApi`, `exchangeContainsCUI`, `exchangeConnectionAuthenticated`) (object, type `java.lang.Boolean`, optional)
    *   `businessPurposeOfAddress` (string array, optional)
    *   `apiOwnership` (string, optional)
    *   `typeOfData` (array of records, each with `id` and `name`, optional)
    *   `numOfRecords` (string, optional)
    *   `dataFormat`, `dataFormatOther` (strings, optional)
    *   `exchangeCUIDescription` (string, optional)
    *   `exchangeCUIType` (string array, optional)
    *   `exchangeNetworkProtocol` (string array, optional)
    *   `exchangeNetworkProtocolOther` (string, optional)
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:InformationFlow`:** This document type represents the internal data model used for the "Alfabet" system, likely mapping directly to fields within its database or API. Fields are often prefixed with `cms_` or `alfa_`.
    *   **Source Database Column to Output Object Property (from `Exchange` to `InformationFlow` mapping in `mapExchange`):**
        *   `exchangeDescription`: `description`
        *   `exchangeName`: `cms_exchange`
        *   `fromOwnerId`: `fromowner`
        *   `toOwnerId`: `toowner`
        *   `exchangeStartDate`: `startdate`
        *   `exchangeEndDate`: `enddate`
        *   `exchangeState`: `objectstate`
        *   `exchangeVersion`: `version`
        *   `apiOwnership`: `cms_api_owner`
        *   `dataFormat`: `cms_data_exch_format`
        *   `dataFormatOther`: `cms_data_exch_format_other`
        *   `numOfRecords`: `cms_data_exch_num_recs`
        *   `dataExchangeAgreement`: `cms_ie_agreement`
        *   `exchangeCUIDescription`: `cms_exchange_cui_description`
        *   `exchangeNetworkProtocolOther`: `cms_exchange_network_protocol_other`
        *   `exchangeRetiredDate`: `cms_exchange_retire_date` (formatted to `MM/dd/yyyy`)
        *   `isAddressEditable`: `cms_adress_data_edits` (converted boolean to string)
        *   `sharedViaApi`: `cms_data_exch_api_data_share` (converted boolean to string)
        *   `isBeneficiaryMailingFile`: `cms_data_exch_benef_mailing` (converted boolean to string)
        *   `containsPii`: `cms_data_exchange_pii` (converted boolean to string)
        *   `containsHealthDisparityData`: `cms_health_disparity_data` (converted boolean to string)
        *   `containsBeneficiaryAddress`: `cms_data_exch_beneficiary_add` (converted boolean to string)
        *   `containsPhi`: `cms_data_exchange_phi` (converted boolean to string)
        *   `containsBankingData`: `cms_data_exchange_banking` (converted boolean to string)
        *   `exchangeConnectionAuthenticated`: `cms_exchange_connection_authenticated` (converted boolean to string)
        *   `businessPurposeOfAddress` (array): `cms_beneficiary_address_pur` (joined by `|`)
        *   `connectionFrequency` (array): `cms_exchange_frequency` (joined by `|`)
        *   `exchangeCUIType` (array): `cms_exchange_cui_type` (joined by `|`)
        *   Generated `name`: `name` (from `createExchangeName` service)
        *   `typeOfData` (array of objects with `id`, `name`): `cms_data_exch_type_of_data` (this mapping is more complex, as `Relations` are created to link to `typeOfData`'s `id` field).
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UpdateRequest`:** A generic request document for updates to the "Alfabet API". It contains arrays of `Objects` and `Relations`.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Object`:** Represents a single object within the "Alfabet" system. Contains `ClassName`, `Id`, and a `Values` field which holds the actual data structure (e.g., `InformationFlow`).
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Relations`:** Represents relationships between objects in "Alfabet". Contains `FromRef`, `FromId`, `Property` (type of relationship), `ToRef`, `ToId`.
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:ExchangeAddGUIDS`:** Used to capture the GUIDs of newly created objects returned by the stored procedure. Contains `NewObjects/GUID` (an array of strings).
*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`:** The final simplified output document for the `exchangeAdd` service, containing a `result` string ("success" or "error") and a `message` string array.
*   **`cms.eadg.utils.api.docs:SetResponse`:** A utility document type used by generic error/response handling services to hold standardized HTTP response details (code, phrase, result, messages, format).

## Error Handling and Response Codes

The `exchangeAdd` service implements a robust error handling strategy using Webmethods' `TRY-CATCH` blocks and utility services to ensure consistent and informative responses.

*   **Different Error Scenarios Covered:**
    *   **Database Stored Procedure Errors:** If `SP_Insert_SystemDataExchange_json` returns a non-zero `@RETURN_VALUE`, the main flow's `BRANCH` (labeled `$default`) triggers a `FAILURE` signal.
    *   **Total Failure during Insertion:** If `mapResponse` determines that zero objects were successfully inserted despite a request, it explicitly sets a `400 Bad Request` response.
    *   **Partial Failure during Insertion:** If some objects were inserted but others failed (detected by `mapResponse` comparing request count to response count), a `400 Bad Request` is set. In this case, a rollback attempt (`deleteObjectsByRef`) is initiated for the successful inserts.
    *   **External API Errors (e.g., during Rollback):** The `deleteObjectsByRef` service handles HTTP errors from the "Alfabet API". For a `403 Forbidden` response, it internally transforms it to a `500 Internal Server Error` before propagating it. Other HTTP errors from the external API are generally passed through as is.
    *   **Unexpected System Errors:** Any unhandled exceptions within the main `TRY` block are caught by the `CATCH` block.
    *   **Missing Global Variables:** If `deleteObjectsByRef` cannot retrieve a necessary global variable (like the class ID for validation), it exits with a `FAILURE` signal.
    *   **Input Data Validation (Implicit):** While explicit input validation rules aren't detailed in the `flow.xml` itself (beyond mapping to strong types), issues like invalid dates or non-boolean values for boolean fields would likely cause errors during mapping or database insertion, leading to the general error handling flow.
*   **HTTP Response Codes Used:**
    *   `200 OK`: For successful processing of the request where all exchanges are added.
    *   `400 Bad Request`: Used for logical business errors, specifically when the database insertion results in total or partial failure of adding the exchanges.
    *   `500 Internal Server Error`: Used for unexpected system errors or issues originating from external dependencies (e.g., Alfabet API returning a `403` which is remapped, or other uncaught exceptions).
*   **Error Message Formats:** Error messages are conveyed through the `Response` document's `result` field ("error") and the `message` string array. For partial failures, the `message` array may contain combined general and specific messages from rejected objects.
*   **Fallback Behaviors:** The primary fallback mechanism is the partial failure rollback. If a batch of inserts partially succeeds, the service attempts to `deleteObjectsByRef` to undo the successful inserts, aiming for an "all or nothing" outcome from the client's perspective, or at least a clear indication of non-completion. The use of a `NO_TRANSACTION` JDBC connection explicitly delegates transactional integrity of the batch to the stored procedure or requires manual compensation (like the `deleteObjectsByRef` call).