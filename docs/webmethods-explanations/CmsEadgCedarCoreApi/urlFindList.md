# Webmethods Service Explanation: CmsEadgCedarCoreApi urlFindList

This document provides a comprehensive explanation of the Webmethods service `urlFindList`, including its business purpose, technical implementation details, data flow, and error handling. This service is part of a larger system aimed at retrieving and presenting data related to URLs associated with various objects within the CEDAR environment.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `urlFindList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## Service Overview

The `urlFindList` service is designed to retrieve a list of URLs associated with a specific object (e.g., a System or an Authority To Operate - ATO) from a database. Its primary business purpose is to provide an API endpoint for applications to query URL details linked to various entities in the CEDAR system.

The service takes a single input parameter: an `id` (string), which represents the unique identifier (GUID) of the object for which URLs are to be retrieved.

Upon successful execution, the service is expected to output a JSON or XML response containing a `UrlFindResponse` object. This object includes a `count` of the URLs found and a `UrlList`, which is an array of `Url` objects, each detailing the associated URL's properties. If no URLs are found for the provided `id`, the service returns a successful response with a `count` of 0 and an empty `UrlList`.

Key validation rules implemented in this service primarily involve checking whether the database query returns any records. If a "native error" occurs during database interaction, it's caught and handled as an internal server error.

## Webmethods Concepts for Non-Webmethods Developers

Webmethods Integration Server uses a visual programming model called "Flow Services" to define business logic. Here are some key concepts encountered in this service:

*   **FLOW**: The entire service itself is a "flow". It defines the sequence of steps and logical constructs that constitute the service's execution. In other programming languages, this is equivalent to the main function or a top-level API handler.
*   **SEQUENCE**: A `SEQUENCE` block executes its contained steps in sequential order. If any step within a `SEQUENCE` fails (throws an exception), the entire `SEQUENCE` fails unless specific error handling is in place.
    *   **TRY/CATCH**: A `SEQUENCE` can be marked as a `TRY` block. This allows for structured error handling, similar to `try...catch` blocks in languages like Java or TypeScript. If an error occurs within the `TRY` block, execution transfers to a designated `CATCH` `SEQUENCE`.
*   **BRANCH**: A `BRANCH` step provides conditional logic, allowing the flow to execute different paths based on a condition. It often uses a `SWITCH` expression, where the value of a variable determines which named `SEQUENCE` (or "label expression") is executed. If no matching label is found, a `$default` `SEQUENCE` can be executed. This is analogous to `switch` statements or chained `if/else if/else` blocks.
*   **INVOKE**: An `INVOKE` step calls another Webmethods service or a built-in function. This promotes modularity and reusability. It's like calling a function or method from a library or another module in conventional programming.
*   **MAP**: A `MAP` step is used for data transformation and manipulation. It allows copying, deleting, or setting values of fields in the "pipeline" (the in-memory data structure representing the service's current context).
    *   **MAPCOPY**: Copies the value from a source field to a target field.
    *   **MAPDELETE**: Removes a field from the pipeline. This is crucial for pipeline optimization and security, preventing sensitive data from persisting unnecessarily.
    *   **MAPSET**: Assigns a literal value to a field.
    *   **MAPINVOKE**: Allows invoking a service directly within a `MAP` step, typically used for simple data transformations where the invoked service's input and output can be directly mapped within the context of the larger mapping. This is like calling a utility function inline during data processing.
*   **LOOP**: A `LOOP` step iterates over elements in an array. For each element, the steps within the `LOOP` block are executed. This is equivalent to a `for` or `forEach` loop.

## Database Interactions

The `urlFindList` service interacts with a database to retrieve URL information.

The primary database interaction is performed by the `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.URL:getURL` service, which is a JDBC Adapter service. This service connects to the `Sparx_Support` database instance located at `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com` on port `1433`. The database user for this connection is `sparx_dbuser`. The connection is configured as `NO_TRANSACTION`, meaning that each database operation is treated as an independent transaction.

The `getURL` adapter service executes a `SELECT` query against the database.

*   **Database View Used**: `Sparx_System_URL` (aliased as `t1` in the SQL definition).
*   **SQL Query Details**: The service selects multiple columns from the `Sparx_System_URL` view. The `WHERE` clause filters records based on the `"Sparx System GUID"` column, matching it with the `id` input provided to the `urlFindList` service.

*   **Source Database Column to Output Object Property Mappings**:
    The `getURL` adapter retrieves numerous columns from the `Sparx_System_URL` view. These raw database results are then processed and mapped to a structured `Url` object within the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UrlFindResponse/UrlList` array by the `mapResponse` service. Note that some columns from the database query are not mapped to the final `v2.cedarCore_.docTypes:Url` output structure and are therefore dropped.

    *   `"Sparx URL GUID"`: `urlId`
    *   `URLLink`: `address`
    *   `"URL API Endpoint"`: `isApiEndpoint` (Boolean conversion is applied)
    *   `"URL API AWF"`: `isBehindWebApplicationFirewall` (Boolean conversion is applied)
    *   `"Provides Version Code Repository Access"`: `isVersionCodeRepository` (Boolean conversion is applied)
    *   `"Hosting Environment"`: `urlHostingEnv`

    The following columns are retrieved from the `Sparx_System_URL` view but are not present in the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Url` document type, and thus will not appear in the final `_generatedResponse/UrlList` objects:

    *   `"System ID"`
    *   `"Sparx System ID"`
    *   `"System Name"`
    *   `"URL ID"`
    *   `"Sparx URL ID"`
    *   `"Connection GUID"`
    *   `"Connection Name"`
    *   `"URL Name"` (Although mapped to an intermediate `v1` structure, it is likely dropped in the final `v2` output)
    *   `"Confidence Level"`
    *   `"Is Intranet Only"`
    *   `"Portal Services Used"`
    *   `"Used for Beneficiary"`
    *   `"Uses HTTPS"`

## External API Interactions

Based on the provided Webmethods files, this service does not directly invoke any external third-party APIs. All `INVOKE` statements observed are calls to other internal Webmethods services (`pub.flow`, `pub.json`, `pub.xml`, `pub.math`, `cms.eadg.utils.api`, `cms.eadg.utils.map`) or custom adapter services that interact with internal databases.

## Main Service Flow

The `urlFindList` service flow is designed to fetch and format URL data, incorporating error handling.

1.  **Start (TRY Block)**: The entire main logic is encapsulated within a `TRY` block. This ensures that any unhandled errors during execution will be caught and managed gracefully by the `CATCH` block.
2.  **Invoke `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.URL:getURL`**:
    *   The service first calls the `getURL` adapter service, passing its `id` input parameter (representing the "Sparx System GUID") to the adapter's input.
    *   After the database query, intermediate input parameters (`getURLInput`, `id`) are deleted from the pipeline to keep it clean.
3.  **Branch for Result Check**: A `BRANCH` step evaluates the `Selected` field from the `getURLOutput` (which indicates the number of records returned by the database query).
    *   **Condition: `%getURLOutput/Selected% > 0` (Records Found)**:
        *   **Invoke `cms.eadg.cedar.core.api.v2.cedarCore_.operations.urlFind:mapResponse`**: If URLs are found, this service is invoked. Its purpose is to transform the raw database results into the structured `UrlFindResponse` document type expected by the API.
        *   After `mapResponse` completes, its output (`UrlFindResponse`) is copied to `_generatedResponse`, which is the final output variable of the `urlFindList` service. The intermediate `UrlFindResponse` variable is then deleted.
    *   **Condition: `%getURLOutput/Selected% == 0` (No Records Found)**:
        *   A `MAP` step is executed to explicitly set the `count` field of the `_generatedResponse` to `0`. This ensures that even when no URLs are found, a valid (though empty) response structure is returned with an accurate count. The `pub.math:toNumber` service is used for this conversion.
    *   **Default (`$default`) (Native Error)**: If neither of the above conditions is met (indicating an error during the `getURL` invocation itself, not just zero results), an `EXIT FROM="$parent" SIGNAL="FAILURE"` step is executed. This immediately terminates the `TRY` block and transfers control to the `CATCH` block.
4.  **Cleanup**: After the `BRANCH` logic, a `MAP` step is used to delete the `getURLOutput` (raw database results) from the pipeline, clearing intermediate data.

## Dependency Service Flows

The `urlFindList` service relies on several other services to perform its functions:

1.  **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.URL:getURL`**:
    *   **Purpose**: This is a core database adapter service. Its sole purpose is to query the `Sparx_System_URL` database view and retrieve all URL-related columns for a given system GUID.
    *   **Integration with Main Flow**: The `urlFindList` service invokes `getURL` early in its execution to fetch the raw data that will then be processed.
    *   **Input/Output Contract**: It takes a `Sparx System GUID` as input and returns a `results` array (containing rows of database columns) and a `Selected` count.
    *   **Specialized Processing**: This service encapsulates the direct SQL query execution against the `Sparx_Support` database.

2.  **`cms.eadg.cedar.core.api.v2.cedarCore_.operations.urlFind:mapResponse`**:
    *   **Purpose**: This service acts as a data transformer. Its responsibility is to take the raw, flat list of database records returned by `getURL` and map them into the structured `UrlFindResponse` document type that the API exposes. It also converts string-based boolean values from the database into proper boolean types.
    *   **Integration with Main Flow**: It's called after `getURL` successfully returns data, but only if the data is not empty.
    *   **Input/Output Contract**: It receives `getURLOutput` as input and produces a `UrlFindResponse` document.
    *   **Specialized Processing**:
        *   It maps the `Selected` count from `getURLOutput` to the `count` field of `UrlFindResponse` using `pub.math:toNumber` for type conversion.
        *   It then iterates (`LOOP`) through each `results` record from `getURLOutput`. For each record, it maps specific database columns to the fields of a `Url` object (an instance of `cms.eadg.alfabet.api.v01.docs.reports.cedar.core:Url`).
        *   Crucially, for boolean fields (`isApiEndpoint`, `isBehindWebApplicationFirewall`, `isVersionCodeRepository`), it uses `cms.eadg.utils.map:convertBoolean` to convert the string values (likely "True"/"False" or "1"/"0") from the database into proper Boolean objects. It includes a fallback copy if the conversion results in null.
        *   Finally, it copies the populated `Url` objects into the `UrlList` array within the `UrlFindResponse`.
        *   It cleans up intermediate `Url` and `getURLOutput` variables.

3.  **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: This is a general-purpose error handling utility service. It receives details about an error that occurred and formats a standardized error response, including setting appropriate HTTP status codes.
    *   **Integration with Main Flow**: It is invoked within the `CATCH` block of `urlFindList` when any unhandled exception occurs in the `TRY` block.
    *   **Input/Output Contract**: It takes `lastError` (from `pub.flow:getLastError`) and an optional `SetResponse` document (for custom error messages). It prepares internal variables for the response.
    *   **Specialized Processing**: If no custom `SetResponse` is provided, it defaults to a `500 Internal Server Error` response code and "Internal Server Error" phrase. It extracts the error message from `lastError` and sets it into the response. It then delegates the actual HTTP response setting to `cms.eadg.utils.api:setResponse`.

4.  **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This service is responsible for converting the structured response object into a serializable string (JSON or XML) and setting the HTTP response headers (status code, content type).
    *   **Integration with Main Flow**: It's called by `handleError` to finalize error responses, and would be implicitly called by the REST API Gateway for successful responses if `_generatedResponse` is set.
    *   **Input/Output Contract**: It takes a `SetResponse` document (containing response code, phrase, result, message, and format).
    *   **Specialized Processing**:
        *   It converts the `result` and `message` from the `SetResponse` into a `Response` document type.
        *   It uses a `BRANCH` on the `format` field (`application/json` or `application/xml`) to determine the serialization method.
            *   For JSON: It invokes `pub.json:documentToJSONString` to convert the `Response` document into a JSON string.
            *   For XML: It first wraps the `Response` in a `ResponseRooted` document (to ensure a single root element for XML), then invokes `pub.xml:documentToXMLString` to convert it into an XML string.
        *   Finally, it calls `pub.flow:setResponseCode` and `pub.flow:setResponse2` to explicitly set the HTTP status code and body of the API response.

## Data Structures and Types

The service heavily relies on predefined Webmethods "Document Types" to structure its data. These are analogous to classes or interfaces in object-oriented programming.

*   **Input (`urlFindList` service)**:
    *   `id`: A `string` field, representing the unique identifier of the object whose URLs are being sought. It is `nillable`, meaning it can be null or empty.

*   **Output (`urlFindList` service)**:
    *   `_generatedResponse`: This is the primary success output. It is a reference (`recref`) to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UrlFindResponse`.
    *   `400`, `401`, `500`: These fields represent potential error responses, each being a reference (`recref`) to `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`. They are `optional`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:UrlFindResponse`**: This document type encapsulates the overall response for the URL search.
    *   `count`: An `object` of type `java.math.BigInteger`, representing the total number of URLs found.
    *   `UrlList`: An `array` of `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Url` objects (`recref`). This is the list of individual URL details. It is `optional`.

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Url`**: This document type defines the structure for each individual URL entry in the `UrlList`.
    *   `urlId`: A `string` that uniquely identifies the URL in the database.
    *   `address`: A `string` containing the full URL. It is `optional`.
    *   `isApiEndpoint`: An `object` of type `java.lang.Boolean`, indicating if the URL is an API endpoint. It is `optional`.
    *   `isBehindWebApplicationFirewall`: An `object` of type `java.lang.Boolean`, indicating if the application is behind a Web Application Firewall. It is `optional`.
    *   `isVersionCodeRepository`: An `object` of type `java.lang.Boolean`, indicating if the URL provides access to a versioned code repository. It is `optional`.
    *   `urlHostingEnv`: A `string` representing the hosting environment of the URL. It is `optional`.

*   **`cms.eadg.utils.api.docs:Response`**: A generic document type used for error responses.
    *   `result`: A `string` indicating the outcome (e.g., "error"). It is `optional`.
    *   `message`: An `array` of `string`, containing error messages. It is `optional`.

*   **`cms.eadg.utils.api.docs:SetResponse`**: An internal document type used by `cms.eadg.utils.api:setResponse` to configure the HTTP response.
    *   `responseCode`: A `string` for the HTTP status code (e.g., "200", "500").
    *   `responsePhrase`: A `string` for the HTTP reason phrase (e.g., "OK", "Internal Server Error").
    *   `result`: A `string` (e.g., "success", "error").
    *   `message`: An `array` of `string` for response messages.
    *   `format`: A `string` specifying the content type (e.g., "application/json", "application/xml").

## Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` flow control:

*   **Global Error Capture**: The entire main flow is enclosed in a `SEQUENCE` marked as `FORM="TRY"`. This means any unhandled exception that occurs during the execution of the main logic will automatically transfer control to the `CATCH` `SEQUENCE`.
*   **Database Query Errors**: Inside the `TRY` block, if the `getURL` adapter service encounters a "native error" (e.g., database connection issues, malformed SQL, etc.), the `BRANCH` step's `$default` path is executed. This path contains an `EXIT FROM="$parent" SIGNAL="FAILURE"` step, which explicitly causes the `urlFindList` service to fail and triggers the `CATCH` block.
*   **No Results Found**: If the database query executes successfully but returns no records (`getURLOutput/Selected == 0`), this is *not* treated as an error. Instead, a specific `SEQUENCE` handles this by setting the `count` in the `_generatedResponse` to `0`, leading to a successful `200 OK` response with an empty `UrlList`.
*   **Generic Error Handling (`cms.eadg.utils.api:handleError`)**:
    *   When the `CATCH` block is activated, `pub.flow:getLastError` is immediately invoked to retrieve detailed information about the exception that occurred.
    *   This error information is then passed to `cms.eadg.utils.api:handleError`. This utility service is designed to standardize error responses. By default, if no specific `SetResponse` is passed to it, it configures the response for a `500 Internal Server Error`. It populates the `message` field of the error response with the error details obtained from `lastError`.
*   **Response Formatting and HTTP Status Setting (`cms.eadg.utils.api:setResponse`)**:
    *   The `handleError` service, in turn, invokes `cms.eadg.utils.api:setResponse`. This service takes the formatted error details and converts them into the appropriate output format (JSON or XML) based on the `format` specified (which `handleError` sets to `application/json` by default).
    *   Finally, `pub.flow:setResponseCode` is called to set the HTTP status code (e.g., `500`) and `pub.flow:setResponse2` sends the serialized error message as the HTTP response body.
*   **Expected HTTP Response Codes**:
    *   **200 OK**: For successful requests, including those where no URLs are found for the given `id`. The response body will contain the `UrlFindResponse` object.
    *   **500 Internal Server Error**: For any unhandled exceptions or "native errors" during database interaction. The response body will contain a generic `Response` object with an "error" result and relevant messages.