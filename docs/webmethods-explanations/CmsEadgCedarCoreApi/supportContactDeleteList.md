# Webmethods Service Explanation: CmsEadgCedarCoreApi supportContactDeleteList

This document provides a comprehensive explanation of the Webmethods service `supportContactDeleteList`, focusing on its business purpose, technical implementation, and interactions with external systems and databases. As an experienced software developer new to Webmethods, this explanation aims to bridge the gap by clarifying Webmethods-specific concepts and highlighting architectural patterns relevant for porting to TypeScript.

*   Package Name: `CmsEadgCedarCoreApi`
*   Service Name: `supportContactDeleteList`
*   Service Package Path: `CmsEadgCedarCoreApi/ns/cms/eadg/cedar/core/api/v2/cedarCore_`

## 1. Service Overview

The `supportContactDeleteList` service is designed to delete one or more support contact records from an external system, identified as "Alfabet" (inferred from package names like `sparx.api.services` and `alfabet.api`). The service handles the deletion of both the "Support Contact to System Connector" and the "Support Contact Object" itself for each provided ID.

*   **Business Purpose**: To facilitate the bulk deletion of support contact records within the Alfabet system, ensuring related connector data is also removed to maintain data integrity.
*   **Input Parameters**:
    *   `id` (string array): An array of unique identifiers (GUIDs) for the support contacts to be deleted.
*   **Expected Outputs or Side Effects**:
    *   If all deletions are successful, the service returns a success message indicating the number of objects deleted.
    *   If no objects are found for deletion or if some deletions fail, an error response is returned with an appropriate HTTP status code (e.g., 400 Bad Request) and a descriptive message.
    *   The primary side effect is the permanent removal of support contact records and their associated connectors from the underlying Alfabet database.
*   **Key Validation Rules**:
    *   The service checks whether any objects were successfully deleted.
    *   It also verifies if the number of successfully deleted objects matches the total number of IDs provided in the input list. Discrepancies lead to specific error responses.

## 2. Webmethods Concepts for Non-Webmethods Developers

Webmethods flow services use a visual programming paradigm with specific elements to define logic.

*   **SEQUENCE**: Analogous to a block of code in procedural programming (e.g., `{...}` in JavaScript/TypeScript). Statements within a sequence are executed in order. A `TRY` sequence is similar to a `try` block, where exceptions are caught by a `CATCH` sequence. An `EXIT-ON="FAILURE"` attribute means the sequence will stop execution if any step within it fails.
*   **BRANCH**: Similar to a `switch` statement or an `if-else if-else` chain. It evaluates a specified variable or expression and directs the flow to a labeled sub-sequence that matches the evaluation. `$default` acts as the `default` case. `LABELEXPRESSIONS="true"` allows for dynamic evaluation of branch conditions.
*   **MAP**: This element is used for data transformation and manipulation within the pipeline (the in-memory data structure representing the service's current state). It allows for:
    *   **MAPCOPY**: Copying data from one field to another.
    *   **MAPSET**: Setting a literal value to a field.
    *   **MAPDELETE**: Removing a field from the pipeline.
*   **INVOKE**: Used to call another service (either built-in, custom, or an adapter service). It's comparable to calling a function or method in other languages. `VALIDATE-IN="$none"` and `VALIDATE-OUT="$none"` indicate that input/output validation is not explicitly defined at this invocation point.
*   **LOOP**: Iterates over an array or list within the pipeline, executing the child steps for each element. The current element in the iteration is usually available under a specific variable name (e.g., `id` in this service's loop).
*   **Error Handling (TRY/CATCH blocks)**: Webmethods flow services employ `TRY` and `CATCH` sequences for exception handling, similar to `try-catch` blocks in Java or TypeScript. If an error occurs within a `TRY` block, execution immediately jumps to the corresponding `CATCH` block, allowing for graceful error processing. `pub.flow:getLastError` is a common built-in service used within `CATCH` blocks to retrieve details of the exception.

## 3. Database Interactions

This service interacts with an MS SQL Server database to retrieve information about the "Support Contact to System Connector" before attempting deletion.

*   **Database Operations Performed**: A `SELECT` operation is performed to retrieve the `Connection GUID` based on the `Sparx Support Contact GUID`. No direct `DELETE` SQL commands are issued by this specific service; deletion operations are handled via the `cms.eadg.sparx.api.services:deleteResource` service, which likely encapsulates its own database or external API interactions.
*   **Database Connection Configuration**:
    *   Connection Name: `cms.eadg.cedar.common.v1.connections.jdbc.support:sparxSupportJdbcConnectionNoTrans`
    *   Database Name: `Sparx_Support`
    *   Database Type: MS SQL Server (via `com.microsoft.sqlserver.jdbc.SQLServerDataSource`)
    *   Server Name: `cedar-dev-apps-shared-sparx-dbinstance.c03dk21i90ve.us-east-1.rds.amazonaws.com`
    *   Port: `1433`
    *   User: `sparx_dbuser`
*   **SQL Queries or Stored Procedures Called**:
    The adapter service `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:getSupportContactConnector` executes a `SELECT` query against a database view.
    *   **Tables/Views Used**: `dbo.Sparx_System_SupportContact` (a SQL Server View).
    *   **Conceptual SQL Query**:
        ```sql
        SELECT
            T1."Connection GUID"
        FROM
            dbo.Sparx_System_SupportContact T1
        WHERE
            T1."Sparx Support Contact GUID" = ?
        ```
*   **Data Mapping Between Service Inputs and Database Parameters**:
    *   **Input to Database Parameter**: The input `id` (which represents the "Sparx Support Contact GUID") is mapped to the `?` parameter in the SQL `WHERE` clause.
    *   **Database Column to Intermediate Pipeline Field**:
        *   `Sparx_System_SupportContact."Connection GUID"`: `getSupportContactConnectorOutput.results."Connection GUID"`

## 4. External API Interactions

The primary external interactions (from the perspective of this service) are with the `cms.eadg.sparx.api.services:deleteResource` service. While these are internal Webmethods services, they act as an API to an external system, likely Alfabet.

*   **External Services Called**: `cms.eadg.sparx.api.services:deleteResource`
*   **Request/Response Formats**: This service takes `resourceIdentifier` (the ID of the resource to delete) and `type` (either "Object" or "Connector") as input. The response structure is not detailed in the provided XML for `deleteResource` itself, but the calling service expects a `responseCode` to determine success.
*   **Authentication Mechanisms**: Not explicitly detailed in the provided files for the `deleteResource` service, but typically handled by the underlying connection or session management within Webmethods.
*   **Error Handling for External Calls**: The `supportContactDeleteList` service checks the `responseCode` from `deleteResource`. If it's not `200`, the current deletion iteration is considered a failure, and the main service flow signals a failure, which is then handled by the CATCH block.

## 5. Main Service Flow

The service executes within a `TRY` block, ensuring that any unhandled errors are caught and processed.

1.  **Initialize Variables**:
    *   The service first calculates the `listSize` by invoking `pub.list:sizeOfList` on the input `id` array.
    *   It initializes a counter `numDeleted` to "0".
2.  **Iterate Through Support Contact IDs (LOOP)**:
    For each `id` in the input `id` array, the following steps are performed:
    *   **Retrieve Connector GUID**: It invokes `cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:getSupportContactConnector`. This calls the JDBC adapter to query the `dbo.Sparx_System_SupportContact` view using the current `id` (Support Contact GUID) to retrieve the associated `Connection GUID`.
    *   **Delete Connector**: It then invokes `cms.eadg.sparx.api.services:deleteResource` with `type` set to "Connector" and `resourceIdentifier` set to the `Connection GUID` obtained in the previous step. This attempts to delete the relationship between the support contact and its connected system.
    *   **Check Connector Deletion Status (BRANCH)**:
        *   If the `deleteResource` service (for the connector) returns a `responseCode` of "200" (Success):
            *   **Delete Support Contact Object**: It calls `cms.eadg.sparx.api.services:deleteResource` again, this time with `type` set to "Object" and `resourceIdentifier` set to the original `id` (Support Contact GUID). This deletes the support contact itself.
            *   **Increment Counter**: `pub.math:addInts` is invoked to increment the `numDeleted` counter by 1.
        *   If the `deleteResource` service (for the connector) does *not* return "200":
            *   The flow `EXIT`s the current loop iteration and signals a `FAILURE` for the main `TRY` block. This means subsequent deletions are halted, and control moves to the CATCH block.
3.  **Post-Loop Validation (BRANCH with Label Expressions)**:
    After processing all IDs in the loop, the service evaluates the total `numDeleted` against the `listSize`:
    *   **No Objects Deleted (`%numDeleted% == 0`)**:
        *   Sets `SetResponse` with `responseCode` "400", `responsePhrase` "Bad Request", `result` "error", `format` "application/json", and `message` "Object(s) could not be found".
        *   `EXIT`s the main `TRY` block with a `FAILURE` signal.
    *   **Some Objects Deleted, But Not All (`%numDeleted% < %listSize%`)**:
        *   Sets `SetResponse` with `responseCode` "400", `responsePhrase` "Bad Request", `result` "error", `format` "application/json", and `message` "One or more objects could not be deleted. Please re-pull object list.".
        *   `EXIT`s the main `TRY` block with a `FAILURE` signal.
    *   **All Objects Deleted (`%numDeleted% == %listSize%`)**:
        *   Sets the final output document `_generatedResponse` with `result` "success" and a dynamic `message` like "X object(s) successfully deleted".
    *   **Default (Unexpected Deletion Count)**:
        *   Sets `SetResponse` with `responseCode` "500", `responsePhrase` "Internal Server Error", `result` "error", `format` "application/json", and `message` "One or more objects could not be deleted. Please re-pull object list.".
        *   `EXIT`s the main `TRY` block with a `FAILURE` signal.
4.  **Error Handling (CATCH Block)**:
    If any step within the main `TRY` block (including the `EXIT` statements that signal `FAILURE`) causes an error, control transfers here:
    *   `pub.flow:getLastError` is invoked to get details about the error.
    *   `cms.eadg.utils.api:handleError` is then called to process the error, which internally uses `cms.eadg.utils.api:setResponse` to format the final HTTP response.

## 6. Dependency Service Flows

The main service relies on several utility and adapter services:

*   **`cms.eadg.cedar.core.api.v2.cedarCore_.adapters.SupportContact:getSupportContactConnector`**:
    *   **Purpose**: This is a JDBC Adapter service designed to retrieve the `Connection GUID` for a given Support Contact GUID from the `dbo.Sparx_System_SupportContact` database view. This connector GUID is essential because the deletion process in Alfabet requires deleting the connector object before deleting the main support contact object.
    *   **Integration with Main Flow**: It's invoked once per `id` in the main service's loop. The output (`Connection GUID`) is immediately used as input for the subsequent `deleteResource` call.
    *   **Input/Output Contracts**: Takes a Support Contact GUID as input and returns a record containing the associated `Connection GUID`.
*   **`cms.eadg.sparx.api.services:deleteResource`**:
    *   **Purpose**: This service encapsulates the logic for deleting resources (either "Objects" or "Connectors") in the Alfabet/Sparx system. It abstracts the underlying API calls or direct database manipulations.
    *   **Integration with Main Flow**: It's invoked twice per `id` within the main loop: first for the "Connector" (using the retrieved `Connection GUID`) and then for the "Object" (using the original `id`).
    *   **Input/Output Contracts**: Takes `resourceIdentifier` (the ID of the item to delete) and `type` ("Object" or "Connector"). It's expected to return a `responseCode` (like "200" for success) in its output, which the calling service checks.
*   **`cms.eadg.utils.api:handleError`**:
    *   **Purpose**: Provides a standardized way to format and prepare error responses for API calls. It takes the `lastError` (from `pub.flow:getLastError`) and an optional pre-set `SetResponse` document (if the error was intentionally set earlier in the flow). If `SetResponse` is not present, it defaults to a generic "500 Internal Server Error".
    *   **Integration with Main Flow**: It's called in the main service's `CATCH` block. It centralizes common error response logic, avoiding duplication across multiple services.
*   **`cms.eadg.utils.api:setResponse`**:
    *   **Purpose**: This utility service is responsible for converting the internal `SetResponse` document into the final HTTP response payload (either JSON or XML) and setting the appropriate HTTP status code and content type headers.
    *   **Integration with Main Flow**: It's called by `handleError` (for error responses) and would be implicitly called if the main service were to set `_generatedResponse` directly (though the current flow's success path directly maps to `_generatedResponse` which then becomes the top-level output of the `supportContactDeleteList` service). It ensures consistent API response formatting.
    *   **Specialized Processing**: It uses `pub.json:documentToJSONString` for JSON responses and `pub.xml:documentToXMLString` for XML responses, based on the `format` field in the `SetResponse` document. It also leverages `pub.flow:setResponseCode` and `pub.flow:setResponse2` to send the HTTP response.

## 7. Data Structures and Types

The service heavily utilizes Webmethods Document Types (recrefs) to define its input and output structures.

*   **Input Data Model**:
    *   `id` (string array): This is the direct input, representing a list of GUIDs for the support contacts. It's a required field for the service.
*   **Output Data Models**:
    *   `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response`: This is the standard success response structure.
        *   `result` (string, optional): Indicates "success" or "error".
        *   `message` (string array, optional): Contains human-readable messages about the operation's outcome.
    *   Error Responses (e.g., `400`, `500`): These also adhere to a `Response` structure, providing consistent error information.
    *   `cms.eadg.utils.api.docs:SetResponse`: This is an internal document type used by utility services (`handleError`, `setResponse`) to formulate the HTTP response details.
        *   `responseCode` (string): HTTP status code (e.g., "200", "400", "500").
        *   `responsePhrase` (string): HTTP reason phrase (e.g., "OK", "Bad Request", "Internal Server Error").
        *   `result` (string): "success" or "error".
        *   `message` (string array): Details about the response.
        *   `format` (string): Content type for the response body (e.g., "application/json", "application/xml").
*   **Field Validation Rules**: The service implicitly validates the `id` array by attempting to process each ID. If an object corresponding to an ID is not found or cannot be deleted, this is reflected in the final response messages and HTTP status codes.
*   **Data Transformation Logic**: The main service's output is not a direct transformation of input data or database rows into a complex object. Instead, it aggregates the success/failure of individual deletion attempts into a simple `result` and `message` structure. The `setResponse` utility handles the serialization of this internal data model into the final JSON or XML string format for the HTTP response.

## 8. Error Handling and Response Codes

The service implements a robust error handling strategy using Webmethods' `TRY`/`CATCH` blocks and utility services to ensure consistent error responses.

*   **Different Error Scenarios Covered**:
    *   **Deletion of Connector Fails**: If `cms.eadg.sparx.api.services:deleteResource` fails for the connector, the loop is exited, and the service transitions to the main CATCH block.
    *   **No Objects Found/Deleted**: If `numDeleted` is 0 after the loop, it indicates that none of the provided IDs corresponded to deletable objects.
    *   **Partial Deletion**: If `numDeleted` is less than `listSize` but greater than 0, it means some objects were deleted, but others failed (e.g., due to not being found, or issues with the external system).
    *   **General System Error**: Any other unhandled exceptions during the main processing or in called services will be caught by the main `CATCH` block.
*   **HTTP Response Codes Used**:
    *   `200 OK`: Returned when all specified support contacts and their connectors are successfully deleted.
    *   `400 Bad Request`: Returned in cases of client-side errors, specifically when:
        *   No objects were found to be deleted (`numDeleted == 0`).
        *   Only some objects were successfully deleted, implying issues with the client's request or the existence of some IDs (`numDeleted < listSize`).
    *   `500 Internal Server Error`: A generic server-side error, typically used as a default if an unexpected exception occurs or if the internal processing of partial deletion encounters an unmapped scenario.
*   **Error Message Formats**: Error responses follow the `cms.eadg.cedar.core.api.v2.cedarCore_.docTypes:Response` structure, with `result` set to "error" and detailed messages in the `message` array. The `SetResponse` utility ensures these are formatted as "application/json" or "application/xml" based on configuration.
*   **Fallback Behaviors**: The `cms.eadg.utils.api:handleError` service acts as a centralized fallback. If a specific error handling branch hasn't already populated the `SetResponse` document with a custom error, `handleError` will default to a 500 status code and a generic "Internal Server Error" message, ensuring that an error response is always generated.