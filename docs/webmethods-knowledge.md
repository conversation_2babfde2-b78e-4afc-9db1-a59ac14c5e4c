# Webmethods Knowledge

## Fields Exist in Designer / Explanation but Are Missing in Response
During some past development when webmethods was switched from
Alphabet to Sparx, some fields were removed on one side of the
mappings, but not from the other side.  It appears at first glance
that these fields should be included in the response, but when
looked at closely, the fields aren't being mapped.

Webmethods output should always be consistent in terms of the
JSON properties in objects from one call to the next.

Verified this with <PERSON>.
